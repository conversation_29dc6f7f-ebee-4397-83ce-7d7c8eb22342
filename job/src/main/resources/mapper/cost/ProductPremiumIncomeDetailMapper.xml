<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.cost.mapper.ProductPremiumIncomeDetailMapper">

    <resultMap type="com.xl.alm.job.cost.entity.ProductPremiumIncomeDetailEntity" id="ProductPremiumIncomeDetailEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="actuarialCode" column="actuarial_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="productName" column="product_name"/>
        <result property="designType" column="design_type"/>
        <result property="currentSinglePremium" column="current_single_premium"/>
        <result property="currentRegularPremium" column="current_regular_premium"/>
        <result property="currentRenewalPremium" column="current_renewal_premium"/>
        <result property="currentTotalPremium" column="current_total_premium"/>
        <result property="currentUlSingle" column="current_ul_single"/>
        <result property="currentUlRegular" column="current_ul_regular"/>
        <result property="currentUlRenewal" column="current_ul_renewal"/>
        <result property="currentUlInitialFee" column="current_ul_initial_fee"/>
        <result property="currentUlTotal" column="current_ul_total"/>
        <result property="currentScalePremium" column="current_scale_premium"/>
        <result property="currentInvestmentBalance" column="current_investment_balance"/>
        <result property="currentSurrender" column="current_surrender"/>
        <result property="currentUlWithdraw" column="current_ul_withdraw"/>
        <result property="currentClaim" column="current_claim"/>
        <result property="currentMedical" column="current_medical"/>
        <result property="currentMaturity" column="current_maturity"/>
        <result property="currentAnnuity" column="current_annuity"/>
        <result property="currentUlClaim" column="current_ul_claim"/>
        <result property="currentUlMedical" column="current_ul_medical"/>
        <result property="currentUlMaturity" column="current_ul_maturity"/>
        <result property="currentUlAnnuity" column="current_ul_annuity"/>
        <result property="currentTotalClaim" column="current_total_claim"/>
        <result property="ytdSinglePremium" column="ytd_single_premium"/>
        <result property="ytdRegularPremium" column="ytd_regular_premium"/>
        <result property="ytdRenewalPremium" column="ytd_renewal_premium"/>
        <result property="ytdTotalPremium" column="ytd_total_premium"/>
        <result property="ytdUlSingle" column="ytd_ul_single"/>
        <result property="ytdUlRegular" column="ytd_ul_regular"/>
        <result property="ytdUlRenewal" column="ytd_ul_renewal"/>
        <result property="ytdUlInitialFee" column="ytd_ul_initial_fee"/>
        <result property="ytdUlTotal" column="ytd_ul_total"/>
        <result property="ytdScalePremium" column="ytd_scale_premium"/>
        <result property="ytdInvestmentBalance" column="ytd_investment_balance"/>
        <result property="ytdSurrender" column="ytd_surrender"/>
        <result property="ytdUlWithdraw" column="ytd_ul_withdraw"/>
        <result property="ytdClaim" column="ytd_claim"/>
        <result property="ytdMedical" column="ytd_medical"/>
        <result property="ytdMaturity" column="ytd_maturity"/>
        <result property="ytdAnnuity" column="ytd_annuity"/>
        <result property="ytdUlClaim" column="ytd_ul_claim"/>
        <result property="ytdUlMedical" column="ytd_ul_medical"/>
        <result property="ytdUlMaturity" column="ytd_ul_maturity"/>
        <result property="ytdUlAnnuity" column="ytd_ul_annuity"/>
        <result property="ytdTotalClaim" column="ytd_total_claim"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectProductPremiumIncomeDetailVo">
        select id, accounting_period, actuarial_code, business_code, product_name, design_type,
               current_single_premium, current_regular_premium, current_renewal_premium, current_total_premium,
               current_ul_single, current_ul_regular, current_ul_renewal, current_ul_initial_fee, current_ul_total,
               current_scale_premium, current_investment_balance, current_surrender, current_ul_withdraw,
               current_claim, current_medical, current_maturity, current_annuity,
               current_ul_claim, current_ul_medical, current_ul_maturity, current_ul_annuity, current_total_claim,
               ytd_single_premium, ytd_regular_premium, ytd_renewal_premium, ytd_total_premium,
               ytd_ul_single, ytd_ul_regular, ytd_ul_renewal, ytd_ul_initial_fee, ytd_ul_total,
               ytd_scale_premium, ytd_investment_balance, ytd_surrender, ytd_ul_withdraw,
               ytd_claim, ytd_medical, ytd_maturity, ytd_annuity,
               ytd_ul_claim, ytd_ul_medical, ytd_ul_maturity, ytd_ul_annuity, ytd_total_claim,
               remark, create_time, create_by, update_time, update_by, is_del
        from t_cost_product_premium_income_detail
    </sql>

    <!-- 查询分产品保费收入列表 -->
    <select id="selectProductPremiumIncomeDetailList" parameterType="com.xl.alm.job.cost.entity.ProductPremiumIncomeDetailEntity" resultMap="ProductPremiumIncomeDetailEntityResult">
        <include refid="selectProductPremiumIncomeDetailVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code = #{actuarialCode}</if>
            <if test="businessCode != null and businessCode != ''">and business_code = #{businessCode}</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据统计期间查询分产品保费收入列表 -->
    <select id="selectProductPremiumIncomeDetailByPeriod" resultMap="ProductPremiumIncomeDetailEntityResult">
        <include refid="selectProductPremiumIncomeDetailVo"/>
        where accounting_period = #{accountingPeriod} and is_del = 0
        order by actuarial_code
    </select>

    <!-- 根据统计期间和产品精算代码查询分产品保费收入 -->
    <select id="selectProductPremiumIncomeDetailByCondition" resultMap="ProductPremiumIncomeDetailEntityResult">
        <include refid="selectProductPremiumIncomeDetailVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code = #{actuarialCode}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增分产品保费收入 -->
    <insert id="insertProductPremiumIncomeDetail" parameterType="com.xl.alm.job.cost.entity.ProductPremiumIncomeDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cost_product_premium_income_detail (
            accounting_period, actuarial_code, business_code, product_name, design_type,
            current_single_premium, current_regular_premium, current_renewal_premium, current_total_premium,
            current_ul_single, current_ul_regular, current_ul_renewal, current_ul_initial_fee, current_ul_total,
            current_scale_premium, current_investment_balance, current_surrender, current_ul_withdraw,
            current_claim, current_medical, current_maturity, current_annuity,
            current_ul_claim, current_ul_medical, current_ul_maturity, current_ul_annuity, current_total_claim,
            ytd_single_premium, ytd_regular_premium, ytd_renewal_premium, ytd_total_premium,
            ytd_ul_single, ytd_ul_regular, ytd_ul_renewal, ytd_ul_initial_fee, ytd_ul_total,
            ytd_scale_premium, ytd_investment_balance, ytd_surrender, ytd_ul_withdraw,
            ytd_claim, ytd_medical, ytd_maturity, ytd_annuity,
            ytd_ul_claim, ytd_ul_medical, ytd_ul_maturity, ytd_ul_annuity, ytd_total_claim,
            remark, create_by, update_by
        ) values (
            #{accountingPeriod}, #{actuarialCode}, #{businessCode}, #{productName}, #{designType},
            #{currentSinglePremium}, #{currentRegularPremium}, #{currentRenewalPremium}, #{currentTotalPremium},
            #{currentUlSingle}, #{currentUlRegular}, #{currentUlRenewal}, #{currentUlInitialFee}, #{currentUlTotal},
            #{currentScalePremium}, #{currentInvestmentBalance}, #{currentSurrender}, #{currentUlWithdraw},
            #{currentClaim}, #{currentMedical}, #{currentMaturity}, #{currentAnnuity},
            #{currentUlClaim}, #{currentUlMedical}, #{currentUlMaturity}, #{currentUlAnnuity}, #{currentTotalClaim},
            #{ytdSinglePremium}, #{ytdRegularPremium}, #{ytdRenewalPremium}, #{ytdTotalPremium},
            #{ytdUlSingle}, #{ytdUlRegular}, #{ytdUlRenewal}, #{ytdUlInitialFee}, #{ytdUlTotal},
            #{ytdScalePremium}, #{ytdInvestmentBalance}, #{ytdSurrender}, #{ytdUlWithdraw},
            #{ytdClaim}, #{ytdMedical}, #{ytdMaturity}, #{ytdAnnuity},
            #{ytdUlClaim}, #{ytdUlMedical}, #{ytdUlMaturity}, #{ytdUlAnnuity}, #{ytdTotalClaim},
            #{remark}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量插入分产品保费收入数据 -->
    <insert id="batchInsertProductPremiumIncomeDetail" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_cost_product_premium_income_detail (
            accounting_period, actuarial_code, business_code, product_name, design_type,
            current_single_premium, current_regular_premium, current_renewal_premium, current_total_premium,
            current_ul_single, current_ul_regular, current_ul_renewal, current_ul_initial_fee, current_ul_total,
            current_scale_premium, current_investment_balance, current_surrender, current_ul_withdraw,
            current_claim, current_medical, current_maturity, current_annuity,
            current_ul_claim, current_ul_medical, current_ul_maturity, current_ul_annuity, current_total_claim,
            ytd_single_premium, ytd_regular_premium, ytd_renewal_premium, ytd_total_premium,
            ytd_ul_single, ytd_ul_regular, ytd_ul_renewal, ytd_ul_initial_fee, ytd_ul_total,
            ytd_scale_premium, ytd_investment_balance, ytd_surrender, ytd_ul_withdraw,
            ytd_claim, ytd_medical, ytd_maturity, ytd_annuity,
            ytd_ul_claim, ytd_ul_medical, ytd_ul_maturity, ytd_ul_annuity, ytd_total_claim,
            remark, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod}, #{item.actuarialCode}, #{item.businessCode}, #{item.productName}, #{item.designType},
            #{item.currentSinglePremium}, #{item.currentRegularPremium}, #{item.currentRenewalPremium}, #{item.currentTotalPremium},
            #{item.currentUlSingle}, #{item.currentUlRegular}, #{item.currentUlRenewal}, #{item.currentUlInitialFee}, #{item.currentUlTotal},
            #{item.currentScalePremium}, #{item.currentInvestmentBalance}, #{item.currentSurrender}, #{item.currentUlWithdraw},
            #{item.currentClaim}, #{item.currentMedical}, #{item.currentMaturity}, #{item.currentAnnuity},
            #{item.currentUlClaim}, #{item.currentUlMedical}, #{item.currentUlMaturity}, #{item.currentUlAnnuity}, #{item.currentTotalClaim},
            #{item.ytdSinglePremium}, #{item.ytdRegularPremium}, #{item.ytdRenewalPremium}, #{item.ytdTotalPremium},
            #{item.ytdUlSingle}, #{item.ytdUlRegular}, #{item.ytdUlRenewal}, #{item.ytdUlInitialFee}, #{item.ytdUlTotal},
            #{item.ytdScalePremium}, #{item.ytdInvestmentBalance}, #{item.ytdSurrender}, #{item.ytdUlWithdraw},
            #{item.ytdClaim}, #{item.ytdMedical}, #{item.ytdMaturity}, #{item.ytdAnnuity},
            #{item.ytdUlClaim}, #{item.ytdUlMedical}, #{item.ytdUlMaturity}, #{item.ytdUlAnnuity}, #{item.ytdTotalClaim},
            #{item.remark}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 更新分产品保费收入 -->
    <update id="updateProductPremiumIncomeDetail" parameterType="com.xl.alm.job.cost.entity.ProductPremiumIncomeDetailEntity">
        update t_cost_product_premium_income_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code = #{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="currentSinglePremium != null">current_single_premium = #{currentSinglePremium},</if>
            <if test="currentRegularPremium != null">current_regular_premium = #{currentRegularPremium},</if>
            <if test="currentRenewalPremium != null">current_renewal_premium = #{currentRenewalPremium},</if>
            <if test="currentTotalPremium != null">current_total_premium = #{currentTotalPremium},</if>
            <if test="currentUlSingle != null">current_ul_single = #{currentUlSingle},</if>
            <if test="currentUlRegular != null">current_ul_regular = #{currentUlRegular},</if>
            <if test="currentUlRenewal != null">current_ul_renewal = #{currentUlRenewal},</if>
            <if test="currentUlInitialFee != null">current_ul_initial_fee = #{currentUlInitialFee},</if>
            <if test="currentUlTotal != null">current_ul_total = #{currentUlTotal},</if>
            <if test="currentScalePremium != null">current_scale_premium = #{currentScalePremium},</if>
            <if test="currentInvestmentBalance != null">current_investment_balance = #{currentInvestmentBalance},</if>
            <if test="currentSurrender != null">current_surrender = #{currentSurrender},</if>
            <if test="currentUlWithdraw != null">current_ul_withdraw = #{currentUlWithdraw},</if>
            <if test="currentClaim != null">current_claim = #{currentClaim},</if>
            <if test="currentMedical != null">current_medical = #{currentMedical},</if>
            <if test="currentMaturity != null">current_maturity = #{currentMaturity},</if>
            <if test="currentAnnuity != null">current_annuity = #{currentAnnuity},</if>
            <if test="currentUlClaim != null">current_ul_claim = #{currentUlClaim},</if>
            <if test="currentUlMedical != null">current_ul_medical = #{currentUlMedical},</if>
            <if test="currentUlMaturity != null">current_ul_maturity = #{currentUlMaturity},</if>
            <if test="currentUlAnnuity != null">current_ul_annuity = #{currentUlAnnuity},</if>
            <if test="currentTotalClaim != null">current_total_claim = #{currentTotalClaim},</if>
            <if test="ytdSinglePremium != null">ytd_single_premium = #{ytdSinglePremium},</if>
            <if test="ytdRegularPremium != null">ytd_regular_premium = #{ytdRegularPremium},</if>
            <if test="ytdRenewalPremium != null">ytd_renewal_premium = #{ytdRenewalPremium},</if>
            <if test="ytdTotalPremium != null">ytd_total_premium = #{ytdTotalPremium},</if>
            <if test="ytdUlSingle != null">ytd_ul_single = #{ytdUlSingle},</if>
            <if test="ytdUlRegular != null">ytd_ul_regular = #{ytdUlRegular},</if>
            <if test="ytdUlRenewal != null">ytd_ul_renewal = #{ytdUlRenewal},</if>
            <if test="ytdUlInitialFee != null">ytd_ul_initial_fee = #{ytdUlInitialFee},</if>
            <if test="ytdUlTotal != null">ytd_ul_total = #{ytdUlTotal},</if>
            <if test="ytdScalePremium != null">ytd_scale_premium = #{ytdScalePremium},</if>
            <if test="ytdInvestmentBalance != null">ytd_investment_balance = #{ytdInvestmentBalance},</if>
            <if test="ytdSurrender != null">ytd_surrender = #{ytdSurrender},</if>
            <if test="ytdUlWithdraw != null">ytd_ul_withdraw = #{ytdUlWithdraw},</if>
            <if test="ytdClaim != null">ytd_claim = #{ytdClaim},</if>
            <if test="ytdMedical != null">ytd_medical = #{ytdMedical},</if>
            <if test="ytdMaturity != null">ytd_maturity = #{ytdMaturity},</if>
            <if test="ytdAnnuity != null">ytd_annuity = #{ytdAnnuity},</if>
            <if test="ytdUlClaim != null">ytd_ul_claim = #{ytdUlClaim},</if>
            <if test="ytdUlMedical != null">ytd_ul_medical = #{ytdUlMedical},</if>
            <if test="ytdUlMaturity != null">ytd_ul_maturity = #{ytdUlMaturity},</if>
            <if test="ytdUlAnnuity != null">ytd_ul_annuity = #{ytdUlAnnuity},</if>
            <if test="ytdTotalClaim != null">ytd_total_claim = #{ytdTotalClaim},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除指定统计期间的分产品保费收入数据 -->
    <delete id="deleteProductPremiumIncomeDetailByPeriod" parameterType="String">
        delete from t_cost_product_premium_income_detail
        where accounting_period = #{accountingPeriod}
    </delete>

    <!-- 物理删除指定统计期间的分产品保费收入数据 -->
    <delete id="physicalDeleteProductPremiumIncomeDetailByPeriod" parameterType="String">
        delete from t_cost_product_premium_income_detail
        where accounting_period = #{accountingPeriod}
    </delete>

    <!-- 逻辑删除指定统计期间的分产品保费收入数据 -->
    <update id="logicalDeleteProductPremiumIncomeDetailByPeriod" parameterType="String">
        update t_cost_product_premium_income_detail set is_del = 1, update_time = now()
        where accounting_period = #{accountingPeriod} and is_del = 0
    </update>

    <!-- 删除指定id的分产品保费收入数据 -->
    <update id="deleteProductPremiumIncomeDetailById" parameterType="Long">
        update t_cost_product_premium_income_detail set is_del = 1, update_time = now() 
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除分产品保费收入数据 -->
    <update id="deleteProductPremiumIncomeDetailByIds" parameterType="Long">
        update t_cost_product_premium_income_detail set is_del = 1, update_time = now() 
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
