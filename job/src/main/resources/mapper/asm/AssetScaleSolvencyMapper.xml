<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.asm.mapper.AssetScaleSolvencyMapper">

    <resultMap type="com.xl.alm.job.asm.entity.AssetScaleSolvencyEntity" id="AssetScaleSolvencyEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="lastYearEnd" column="last_year_end"/>
        <result property="lastQuarterEnd" column="last_quarter_end"/>
        <result property="currentQuarterEnd" column="current_quarter_end"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetScaleSolvencyVo">
        select id, accounting_period, item_name, last_year_end, last_quarter_end, current_quarter_end, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_asm_asset_scale_solvency
    </sql>

    <!-- 批量插入资产规模与偿付能力表数据 -->
    <insert id="batchInsertAssetScaleSolvency" parameterType="java.util.List">
        insert into t_asm_asset_scale_solvency(accounting_period, item_name, last_year_end, last_quarter_end, current_quarter_end, remark, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.lastYearEnd}, #{item.lastQuarterEnd}, #{item.currentQuarterEnd}, #{item.remark}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 物理删除指定账期的资产规模与偿付能力表数据 -->
    <delete id="deleteAssetScaleSolvencyByPeriod">
        delete from t_asm_asset_scale_solvency where accounting_period = #{accountingPeriod}
    </delete>

    <!-- 根据账期查询资产规模与偿付能力表列表 -->
    <select id="selectAssetScaleSolvencyListByPeriod" resultMap="AssetScaleSolvencyEntityResult">
        <include refid="selectAssetScaleSolvencyVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            and is_del = 0
        </where>
        order by item_name
    </select>

    <!-- 根据账期和项目名称查询资产规模与偿付能力表 -->
    <select id="selectAssetScaleSolvencyByPeriodAndItem" resultMap="AssetScaleSolvencyEntityResult">
        <include refid="selectAssetScaleSolvencyVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 更新资产规模与偿付能力表 -->
    <update id="updateAssetScaleSolvency" parameterType="com.xl.alm.job.asm.entity.AssetScaleSolvencyEntity">
        update t_asm_asset_scale_solvency
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="lastYearEnd != null">last_year_end = #{lastYearEnd},</if>
            <if test="lastQuarterEnd != null">last_quarter_end = #{lastQuarterEnd},</if>
            <if test="currentQuarterEnd != null">current_quarter_end = #{currentQuarterEnd},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 插入资产规模与偿付能力表 -->
    <insert id="insertAssetScaleSolvency" parameterType="com.xl.alm.job.asm.entity.AssetScaleSolvencyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_asset_scale_solvency
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="lastYearEnd != null">last_year_end,</if>
            <if test="lastQuarterEnd != null">last_quarter_end,</if>
            <if test="currentQuarterEnd != null">current_quarter_end,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="lastYearEnd != null">#{lastYearEnd},</if>
            <if test="lastQuarterEnd != null">#{lastQuarterEnd},</if>
            <if test="currentQuarterEnd != null">#{currentQuarterEnd},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

</mapper>
