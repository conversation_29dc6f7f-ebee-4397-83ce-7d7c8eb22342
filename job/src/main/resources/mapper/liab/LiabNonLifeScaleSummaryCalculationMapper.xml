<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.liab.mapper.LiabNonLifeScaleSummaryCalculationMapper">

    <!-- 按账期和险种主类分组汇总非寿险负债规模明细数据 -->
    <select id="selectNonLifeScaleSummaryByInsuranceType" resultType="java.util.Map">
        SELECT 
            accounting_period,
            insurance_main_type,
            SUM(COALESCE(unearned_premium_reserve, 0)) as unearned_premium_reserve,
            SUM(COALESCE(outstanding_claim_reserve_s, 0)) as outstanding_claim_reserve
        FROM t_liab_scale_detail
        WHERE accounting_period = #{accountingPeriod}
        AND insurance_main_type IN ('04', '06')  -- 04-短期寿险, 06-短期意外险
        AND is_del = 0
        GROUP BY accounting_period, insurance_main_type
        ORDER BY 
            CASE insurance_main_type 
                WHEN '04' THEN 1  -- 短期寿险
                WHEN '06' THEN 2  -- 短期意外险
                ELSE 3
            END
    </select>

    <!-- 获取普通账户汇总数据 -->
    <select id="selectGeneralAccountSummaryData" resultType="java.util.Map">
        SELECT 
            accounting_period,
            SUM(COALESCE(unearned_premium_reserve, 0)) as unearned_premium_reserve,
            SUM(COALESCE(outstanding_claim_reserve_s, 0)) as outstanding_claim_reserve
        FROM t_liab_scale_detail
        WHERE accounting_period = #{accountingPeriod}
        AND design_type = '05'  -- 05-普通账户
        AND is_del = 0
        GROUP BY accounting_period
    </select>

    <!-- 批量插入非寿险负债规模汇总数据 -->
    <insert id="batchInsertLiabNonLifeScaleSummary">
        INSERT INTO t_liab_non_life_scale_summary (
            accounting_period, insurance_main_type, unearned_premium_reserve, outstanding_claim_reserve, remark
        ) VALUES
        <foreach collection="summaryDataList" item="item" separator=",">
            (
                #{item.accounting_period}, #{item.insurance_main_type}, 
                #{item.unearned_premium_reserve}, #{item.outstanding_claim_reserve}, #{item.remark}
            )
        </foreach>
    </insert>

    <!-- 删除指定账期的非寿险负债规模汇总数据 -->
    <delete id="deleteLiabNonLifeScaleSummaryByPeriod">
        DELETE FROM t_liab_non_life_scale_summary 
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 检查负债规模明细表中非寿险数据是否存在 -->
    <select id="countNonLifeScaleDetailByPeriod" resultType="int">
        SELECT COUNT(1)
        FROM t_liab_scale_detail
        WHERE accounting_period = #{accountingPeriod}
        AND insurance_main_type IN ('04', '05', '06')  -- 04-短期寿险, 05-短期健康险, 06-短期意外险
        AND is_del = 0
    </select>

</mapper>
