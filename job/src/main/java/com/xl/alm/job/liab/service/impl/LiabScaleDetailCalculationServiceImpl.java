package com.xl.alm.job.liab.service.impl;

import com.xl.alm.job.liab.mapper.LiabScaleDetailCalculationMapper;
import com.xl.alm.job.liab.service.LiabScaleDetailCalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 负债规模明细数据计算服务实现类
 * 对应UC0002：计算负债规模明细数据
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class LiabScaleDetailCalculationServiceImpl implements LiabScaleDetailCalculationService {

    @Autowired
    private LiabScaleDetailCalculationMapper liabScaleDetailCalculationMapper;

    /**
     * 执行负债规模明细数据计算
     *
     * @param accountingPeriod 账期，格式：YYYYMM（如202506）
     * @return 处理结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateLiabScaleDetail(String accountingPeriod) {
        log.info("开始执行负债规模明细数据计算，账期：{}", accountingPeriod);

        // 参数校验
        if (!StringUtils.hasText(accountingPeriod)) {
            log.error("账期参数不能为空");
            return false;
        }

        if (accountingPeriod.length() != 6) {
            log.error("账期格式错误，应为YYYYMM格式，当前值：{}", accountingPeriod);
            return false;
        }

        try {
            // 步骤1：数据准备
            log.info("步骤1：数据准备 - 读取产品属性表和会计准备金明细表数据");

            // 读取产品属性表数据（主数据源）
            List<Map<String, Object>> productAttributeList = liabScaleDetailCalculationMapper.selectProductAttributeData(accountingPeriod);
            if (productAttributeList == null || productAttributeList.isEmpty()) {
                log.warn("未找到账期{}的产品属性数据", accountingPeriod);
                return false;
            }
            log.info("成功读取{}条产品属性数据", productAttributeList.size());

            // 读取会计准备金明细表数据
            List<Map<String, Object>> accountingReserveList = liabScaleDetailCalculationMapper.selectAccountingReserveDetailData(accountingPeriod);
            log.info("成功读取{}条会计准备金明细数据", accountingReserveList != null ? accountingReserveList.size() : 0);

            // 构建会计准备金数据映射（以精算代码为key）
            Map<String, Map<String, Object>> reserveMap = new HashMap<>();
            if (accountingReserveList != null) {
                for (Map<String, Object> reserve : accountingReserveList) {
                    String actuarialCode = (String) reserve.get("actuarial_code");
                    if (actuarialCode != null) {
                        reserveMap.put(actuarialCode, reserve);
                    }
                }
            }

            // 步骤2：清理历史数据
            log.info("步骤2：清理账期{}的历史负债规模明细数据", accountingPeriod);
            int deletedCount = liabScaleDetailCalculationMapper.deleteLiabScaleDetailByPeriod(accountingPeriod);
            log.info("清理了{}条历史数据", deletedCount);

            // 步骤3：计算负债规模明细数据
            log.info("步骤3：开始计算负债规模明细数据");
            List<Map<String, Object>> liabScaleDetailList = new ArrayList<>();

            for (Map<String, Object> productAttr : productAttributeList) {
                Map<String, Object> liabScaleDetail = calculateSingleProductDetail(productAttr, reserveMap);
                liabScaleDetailList.add(liabScaleDetail);
            }

            // 步骤4：批量插入负债规模明细数据
            log.info("步骤4：批量插入{}条负债规模明细数据", liabScaleDetailList.size());
            int insertedCount = liabScaleDetailCalculationMapper.batchInsertLiabScaleDetail(liabScaleDetailList);
            log.info("成功插入{}条负债规模明细数据", insertedCount);

            // 步骤5：计算并插入普通账户汇总记录
            log.info("步骤5：计算并插入普通账户汇总记录");
            Map<String, Object> generalAccountSummary = liabScaleDetailCalculationMapper.calculateGeneralAccountSummary(accountingPeriod);
            if (generalAccountSummary != null) {
                int summaryInserted = liabScaleDetailCalculationMapper.insertGeneralAccountSummary(generalAccountSummary);
                log.info("成功插入{}条普通账户汇总记录", summaryInserted);
            }

            log.info("负债规模明细数据计算完成，账期：{}，共处理{}条产品数据", accountingPeriod, productAttributeList.size());
            return true;

        } catch (Exception e) {
            log.error("负债规模明细数据计算失败，账期：{}", accountingPeriod, e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 计算单个产品的负债规模明细数据
     *
     * @param productAttr 产品属性数据
     * @param reserveMap 会计准备金数据映射
     * @return 负债规模明细数据
     */
    private Map<String, Object> calculateSingleProductDetail(Map<String, Object> productAttr, Map<String, Map<String, Object>> reserveMap) {
        Map<String, Object> detail = new HashMap<>();

        // 基础字段直接取自产品属性表
        detail.put("accounting_period", productAttr.get("accounting_period"));
        detail.put("actuarial_code", productAttr.get("actuarial_code"));
        detail.put("business_code", productAttr.get("business_code"));
        detail.put("product_name", productAttr.get("product_name"));
        detail.put("term_flag", productAttr.get("term_type"));
        detail.put("insurance_main_type", productAttr.get("insurance_main_type"));
        detail.put("insurance_sub_type", productAttr.get("insurance_sub_type"));
        detail.put("design_type", productAttr.get("design_type"));

        // 获取对应的会计准备金数据
        String actuarialCode = (String) productAttr.get("actuarial_code");
        Map<String, Object> reserveData = reserveMap.get(actuarialCode);

        if (reserveData == null) {
            // 如果匹配不到会计准备金数据，相关字段默认为0
            log.warn("产品{}未找到对应的会计准备金数据，相关字段将设为0", actuarialCode);
            setDefaultValues(detail);
        } else {
            // 计算各类负债规模字段
            calculateLiabilityFields(detail, productAttr, reserveData);
        }

        detail.put("remark", "系统计算生成");
        return detail;
    }

    /**
     * 设置默认值（当匹配不到会计准备金数据时）
     *
     * @param detail 负债规模明细数据
     */
    private void setDefaultValues(Map<String, Object> detail) {
        detail.put("reasonable_liability", BigDecimal.ZERO);
        detail.put("risk_margin", BigDecimal.ZERO);
        detail.put("residual_margin", BigDecimal.ZERO);
        detail.put("outstanding_claim_reserve_l", BigDecimal.ZERO);
        detail.put("unearned_premium_reserve", BigDecimal.ZERO);
        detail.put("outstanding_claim_reserve_s", BigDecimal.ZERO);
        detail.put("investment_linked_liability", BigDecimal.ZERO);
        detail.put("receivable_unearned_premium_reserve", BigDecimal.ZERO);
        detail.put("receivable_outstanding_claim_reserve", BigDecimal.ZERO);
        detail.put("receivable_life_insurance_reserve", BigDecimal.ZERO);
        detail.put("receivable_long_term_health_reserve", BigDecimal.ZERO);
    }

    /**
     * 计算负债规模字段
     *
     * @param detail 负债规模明细数据
     * @param productAttr 产品属性数据
     * @param reserveData 会计准备金数据
     */
    private void calculateLiabilityFields(Map<String, Object> detail, Map<String, Object> productAttr, Map<String, Object> reserveData) {
        // 获取基础数据
        String termType = (String) productAttr.get("term_type");
        String designType = (String) productAttr.get("design_type");
        String insuranceMainType = (String) productAttr.get("insurance_main_type");

        // 获取会计准备金字段（处理null值）
        BigDecimal bestEstimate = getBigDecimalValue(reserveData, "best_estimate");
        BigDecimal unmodeledReserve = getBigDecimalValue(reserveData, "unmodeled_reserve");
        BigDecimal waiverReserve = getBigDecimalValue(reserveData, "waiver_reserve");
        BigDecimal persistenceBonusReserve = getBigDecimalValue(reserveData, "persistence_bonus_reserve");
        BigDecimal lapsedPolicyValue = getBigDecimalValue(reserveData, "lapsed_policy_value");
        BigDecimal riskMargin = getBigDecimalValue(reserveData, "risk_margin");
        BigDecimal residualMargin = getBigDecimalValue(reserveData, "residual_margin");
        BigDecimal shortTermUnearned = getBigDecimalValue(reserveData, "short_term_unearned");
        BigDecimal outstandingClaimReserve = getBigDecimalValue(reserveData, "outstanding_claim_reserve");
        BigDecimal accountValue = getBigDecimalValue(reserveData, "account_value");
        BigDecimal totalAccountingReserve = getBigDecimalValue(reserveData, "total_accounting_reserve");
        BigDecimal reinsuranceUnearned = getBigDecimalValue(reserveData, "reinsurance_unearned");
        BigDecimal reinsuranceClaimTotal = getBigDecimalValue(reserveData, "reinsurance_claim_total");
        BigDecimal reinsuranceTotal = getBigDecimalValue(reserveData, "reinsurance_total");

        // b. 合理估计负债 = 最优估计 + 未建模 + 豁免准备金 + 持续奖准备金 + 失效保单现价
        BigDecimal reasonableLiability = bestEstimate.add(unmodeledReserve)
                .add(waiverReserve).add(persistenceBonusReserve).add(lapsedPolicyValue);
        detail.put("reasonable_liability", reasonableLiability);

        // c. 风险边际、剩余边际直接取自会计准备金明细表
        detail.put("risk_margin", riskMargin);
        detail.put("residual_margin", residualMargin);

        // d. 未决赔款准备金L：如果长短期标识=L，取未决赔款准备金；否则为0
        BigDecimal outstandingClaimReserveL = "L".equals(termType) ? outstandingClaimReserve : BigDecimal.ZERO;
        detail.put("outstanding_claim_reserve_l", outstandingClaimReserveL);

        // e. 未到期责任准备金：取短险未到期
        detail.put("unearned_premium_reserve", shortTermUnearned);

        // f. 未决赔款准备金S：如果长短期标识=S，取未决赔款准备金；否则为0
        BigDecimal outstandingClaimReserveS = "S".equals(termType) ? outstandingClaimReserve : BigDecimal.ZERO;
        detail.put("outstanding_claim_reserve_s", outstandingClaimReserveS);

        // g. 万能投连险负债规模：如果设计类型为投连险或万能险，取账户价值+会计准备金合计；否则为0
        BigDecimal investmentLinkedLiability = BigDecimal.ZERO;
        if ("04".equals(designType) || "03".equals(designType)) { // 04-投连险, 03-万能险
            investmentLinkedLiability = accountValue.add(totalAccountingReserve);
        }
        detail.put("investment_linked_liability", investmentLinkedLiability);

        // h. 应收分保未到期责任准备金：如果险种主类为短期意外险或短期健康险，取应收分保未到期责任准备金；否则为0
        BigDecimal receivableUnearnedPremiumReserve = BigDecimal.ZERO;
        if ("06".equals(insuranceMainType) || "05".equals(insuranceMainType)) { // 06-短期意外险, 05-短期健康险
            receivableUnearnedPremiumReserve = reinsuranceUnearned;
        }
        detail.put("receivable_unearned_premium_reserve", receivableUnearnedPremiumReserve);

        // i. 应收分保未决赔款准备金：如果险种主类为短期意外险或短期健康险，取应收分保未决合计；否则为0
        BigDecimal receivableOutstandingClaimReserve = BigDecimal.ZERO;
        if ("06".equals(insuranceMainType) || "05".equals(insuranceMainType)) { // 06-短期意外险, 05-短期健康险
            receivableOutstandingClaimReserve = reinsuranceClaimTotal;
        }
        detail.put("receivable_outstanding_claim_reserve", receivableOutstandingClaimReserve);

        // j. 应收分保寿险责任准备金：如果险种主类为短期寿险或长期寿险，取应收分保合计；否则为0
        BigDecimal receivableLifeInsuranceReserve = BigDecimal.ZERO;
        if ("04".equals(insuranceMainType) || "01".equals(insuranceMainType)) { // 04-短期寿险, 01-长期寿险
            receivableLifeInsuranceReserve = reinsuranceTotal;
        }
        detail.put("receivable_life_insurance_reserve", receivableLifeInsuranceReserve);

        // k. 应收分保长期健康险责任准备金：如果险种主类为长期健康险，取应收分保合计；否则为0
        BigDecimal receivableLongTermHealthReserve = BigDecimal.ZERO;
        if ("02".equals(insuranceMainType)) { // 02-长期健康险
            receivableLongTermHealthReserve = reinsuranceTotal;
        }
        detail.put("receivable_long_term_health_reserve", receivableLongTermHealthReserve);
    }

    /**
     * 安全获取BigDecimal值，处理null情况
     *
     * @param data 数据Map
     * @param key 字段名
     * @return BigDecimal值，null时返回0
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("字段{}的值{}无法转换为BigDecimal，使用0代替", key, value);
            return BigDecimal.ZERO;
        }
    }
}
