package com.xl.alm.job.adur.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xl.alm.job.adur.entity.AdurDurationAssetDetailEntity;
import com.xl.alm.job.adur.entity.AdurKeyDurationDiscountFactorWithSpreadEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountFactorWithSpreadEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.mapper.AdurDurationAssetDetailMapper;
import com.xl.alm.job.adur.mapper.AdurKeyDurationDiscountFactorWithSpreadMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountFactorWithSpreadMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.service.AdurDurationIndicatorCalculationService;
import com.xl.alm.job.adur.util.CashFlowUtil;
import com.xl.alm.job.adur.util.TermDataUtil;
import com.xl.alm.job.common.util.XirrCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * ADUR久期指标计算服务实现类
 * 对应用例：UC0010 计算久期指标
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurDurationIndicatorCalculationServiceImpl implements AdurDurationIndicatorCalculationService {

    @Autowired
    private AdurDurationAssetDetailMapper durationAssetDetailMapper;

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadMapper monthlyDiscountFactorWithSpreadMapper;

    @Autowired
    private AdurKeyDurationDiscountFactorWithSpreadMapper keyDurationDiscountFactorWithSpreadMapper;

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper monthlyDiscountCurveWithSpreadMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 常量定义
    private static final String CURVE_SUB_CATEGORY_2 = "2";
    private static final String CURVE_SUB_CATEGORY_3 = "3";
    private static final String CURVE_SUB_CATEGORY_4 = "4";
    private static final String CURVE_SUB_CATEGORY_5 = "5";
    private static final String STRESS_DIRECTION_UP = "01";
    private static final String STRESS_DIRECTION_DOWN = "02";
    
    // 关键期限列表
    private static final String[] KEY_TERMS = {
            "0", "0.5", "1", "2", "3", "4", "5", "6", "7", "8",
            "10", "12", "15", "20", "25", "30", "35", "40", "45", "50"
    };

    // 索引缓存，用于优化查找性能
    private Map<String, AdurMonthlyDiscountFactorWithSpreadEntity> discountFactorIndexCache = new HashMap<>();
    private Map<String, AdurMonthlyDiscountCurveWithSpreadEntity> discountCurveIndexCache = new HashMap<>();

    /**
     * 计算久期指标
     *
     * @param accountPeriod 账期，格式YYYYMM
     * @return 处理结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateDurationIndicators(String accountPeriod) {
        log.info("开始执行久期指标计算，账期：{}", accountPeriod);
        try {
            // 步骤1：加载基础数据
            List<AdurDurationAssetDetailEntity> assetDetailList = loadDurationAssetDetailData(accountPeriod);
            log.info("加载久期资产明细数据完成，共加载{}条记录", assetDetailList.size());

            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList = loadMonthlyDiscountFactorData(accountPeriod);
            log.info("加载月度折现因子含价差数据完成，共加载{}条数据", monthlyDiscountFactorList.size());

            // 构建索引缓存以优化查找性能
            buildDiscountFactorIndex(monthlyDiscountFactorList);

            // 加载月度折现曲线含价差数据
            List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveList = loadMonthlyDiscountCurveData(accountPeriod);
            log.info("加载月度折现曲线含价差数据完成，共加载{}条数据", monthlyDiscountCurveList.size());

            // 构建月度折现曲线索引缓存
            buildDiscountCurveIndex(monthlyDiscountCurveList);

            List<AdurKeyDurationDiscountFactorWithSpreadEntity> keyDurationDiscountFactorList = loadKeyDurationDiscountFactorData(accountPeriod);
            log.info("加载关键久期折现因子含价差数据完成，共加载{}条数据", keyDurationDiscountFactorList.size());

            // 步骤2：计算久期指标
            int successCount = 0;
            int failCount = 0;
            long totalCalculationTime = 0;
            long maxSingleAssetTime = 0;
            String slowestAsset = "";

            log.info("开始计算{}个资产的久期指标", assetDetailList.size());

            for (int i = 0; i < assetDetailList.size(); i++) {
                AdurDurationAssetDetailEntity assetDetail = assetDetailList.get(i);
                long assetStartTime = System.currentTimeMillis();

                try {
                    boolean result = calculateAssetDurationIndicators(assetDetail, monthlyDiscountFactorList, keyDurationDiscountFactorList, accountPeriod);
                    long assetTime = System.currentTimeMillis() - assetStartTime;
                    totalCalculationTime += assetTime;

                    if (assetTime > maxSingleAssetTime) {
                        maxSingleAssetTime = assetTime;
                        slowestAsset = assetDetail.getAssetNumber();
                    }

                    if (result) {
                        successCount++;
                    } else {
                        failCount++;
                    }

                    // 每处理100个资产输出一次进度
                    if ((i + 1) % 100 == 0) {
                        long avgTime = totalCalculationTime / (i + 1);
                        log.info("已处理{}/{}个资产，成功{}个，失败{}个，平均耗时{}ms/个，最慢资产{}耗时{}ms",
                                i + 1, assetDetailList.size(), successCount, failCount,
                                avgTime, slowestAsset, maxSingleAssetTime);
                    }

                } catch (Exception e) {
                    long assetTime = System.currentTimeMillis() - assetStartTime;
                    totalCalculationTime += assetTime;
                    log.error("计算资产{}的久期指标失败，耗时：{}ms", assetDetail.getAssetNumber(), assetTime, e);
                    failCount++;
                }
            }

            // 输出最终统计信息
            long avgTime = assetDetailList.size() > 0 ? totalCalculationTime / assetDetailList.size() : 0;
            log.info("久期指标计算完成，总计{}个资产，成功{}个，失败{}个，" +
                    "总耗时{}ms，平均{}ms/个，最慢资产{}耗时{}ms",
                    assetDetailList.size(), successCount, failCount,
                    totalCalculationTime, avgTime, slowestAsset, maxSingleAssetTime);

            // 步骤3：批量更新数据
            if (!CollectionUtils.isEmpty(assetDetailList)) {
                int updateCount = durationAssetDetailMapper.batchUpdateDurationAssetDetail(assetDetailList);
                log.info("批量更新久期资产明细数据完成，更新{}条记录", updateCount);
            }

            log.info("久期指标计算完成，账期：{}，成功{}条，失败{}条", accountPeriod, successCount, failCount);
            return true;

        } catch (Exception e) {
            log.error("久期指标计算失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    /**
     * 加载久期资产明细数据
     *
     * @param accountPeriod 账期
     * @return 久期资产明细列表
     */
    private List<AdurDurationAssetDetailEntity> loadDurationAssetDetailData(String accountPeriod) {
        List<AdurDurationAssetDetailEntity> assetDetailList = durationAssetDetailMapper.selectByAccountPeriod(accountPeriod);
        
        if (CollectionUtils.isEmpty(assetDetailList)) {
            log.warn("未找到账期{}的久期资产明细数据", accountPeriod);
            return new ArrayList<>();
        }
        
        return assetDetailList;
    }

    /**
     * 加载月度折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @return 月度折现因子数据列表
     */
    private List<AdurMonthlyDiscountFactorWithSpreadEntity> loadMonthlyDiscountFactorData(String accountPeriod) {
        List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList = monthlyDiscountFactorWithSpreadMapper.selectByAccountPeriod(accountPeriod);

        if (CollectionUtils.isEmpty(monthlyDiscountFactorList)) {
            log.warn("未找到账期{}的月度折现因子含价差数据", accountPeriod);
            return new ArrayList<>();
        }

        return monthlyDiscountFactorList;
    }

    /**
     * 加载月度折现曲线含价差数据
     *
     * @param accountPeriod 账期
     * @return 月度折现曲线数据列表
     */
    private List<AdurMonthlyDiscountCurveWithSpreadEntity> loadMonthlyDiscountCurveData(String accountPeriod) {
        List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveList = monthlyDiscountCurveWithSpreadMapper.selectByAccountPeriod(accountPeriod);

        if (CollectionUtils.isEmpty(monthlyDiscountCurveList)) {
            log.warn("未找到账期{}的月度折现曲线含价差数据", accountPeriod);
            return new ArrayList<>();
        }

        return monthlyDiscountCurveList;
    }

    /**
     * 加载关键久期折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @return 关键久期折现因子数据列表
     */
    private List<AdurKeyDurationDiscountFactorWithSpreadEntity> loadKeyDurationDiscountFactorData(String accountPeriod) {
        List<AdurKeyDurationDiscountFactorWithSpreadEntity> keyDurationDiscountFactorList = keyDurationDiscountFactorWithSpreadMapper.selectByAccountPeriod(accountPeriod);

        if (CollectionUtils.isEmpty(keyDurationDiscountFactorList)) {
            log.warn("未找到账期{}的关键久期折现因子含价差数据", accountPeriod);
            return new ArrayList<>();
        }

        return keyDurationDiscountFactorList;
    }



    /**
     * 从关键久期折现因子列表中查找匹配的记录
     *
     * @param keyDurationDiscountFactorList 关键久期折现因子列表
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return 匹配的关键久期折现因子实体，如果未找到则返回null
     */
    private AdurKeyDurationDiscountFactorWithSpreadEntity findKeyDurationDiscountFactor(
            List<AdurKeyDurationDiscountFactorWithSpreadEntity> keyDurationDiscountFactorList,
            String accountName, String securityCode, String keyTerm, String stressDirection) {

        long startTime = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(keyDurationDiscountFactorList)) {
            return null;
        }

        AdurKeyDurationDiscountFactorWithSpreadEntity result = keyDurationDiscountFactorList.stream()
                .filter(entity -> Objects.equals(entity.getAccountName(), accountName)
                        && Objects.equals(entity.getSecurityCode(), securityCode)
                        && Objects.equals(entity.getKeyTerm(), keyTerm)
                        && Objects.equals(entity.getStressDirection(), stressDirection))
                .findFirst()
                .orElse(null);

        long searchTime = System.currentTimeMillis() - startTime;
        if (searchTime > 5) { // 只记录耗时超过5ms的查找
            log.debug("查找关键久期折现因子耗时：{}ms，账户：{}，证券：{}，期限：{}，方向：{}，结果：{}",
                    searchTime, accountName, securityCode, keyTerm, stressDirection, result != null ? "找到" : "未找到");
        }

        return result;
    }

    /**
     * 计算单个资产的久期指标
     *
     * @param assetDetail 资产明细
     * @param monthlyDiscountFactorList 月度折现因子数据列表
     * @param keyDurationDiscountFactorList 关键久期折现因子数据列表
     * @param accountPeriod 账期，格式YYYYMM
     * @return 计算结果，true表示成功，false表示失败
     */
    private boolean calculateAssetDurationIndicators(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList,
            List<AdurKeyDurationDiscountFactorWithSpreadEntity> keyDurationDiscountFactorList,
            String accountPeriod) {

        long methodStartTime = System.currentTimeMillis();
        String assetNumber = assetDetail.getAssetNumber();

        try {
            // 验证基础数据
            long stepStartTime = System.currentTimeMillis();
            if (CollectionUtils.isEmpty(monthlyDiscountFactorList)) {
                log.warn("月度折现因子数据为空，无法计算资产{}的久期指标", assetNumber);
                return false;
            }

            if (CollectionUtils.isEmpty(keyDurationDiscountFactorList)) {
                log.warn("关键久期折现因子数据为空，无法计算资产{}的DV10指标", assetNumber);
                // 注：DV10指标缺失不影响其他指标计算，继续执行
            }
            long validationTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}数据验证耗时：{}ms", assetNumber, validationTime);

            // 解析现金流数据
            stepStartTime = System.currentTimeMillis();
            Map<Integer, BigDecimal> issueCashflowMap = parseCashflowSet(assetDetail.getIssueCashflowSet());
            Map<Integer, BigDecimal> evalCashflowMap = parseCashflowSet(assetDetail.getEvalCashflowSet());
            long parseTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}现金流解析耗时：{}ms", assetNumber, parseTime);

            // 步骤1.5：计算本金现金流和利息现金流
            stepStartTime = System.currentTimeMillis();
            calculatePrincipalAndInterestCashflows(assetDetail, evalCashflowMap, accountPeriod);
            long principalInterestTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}本金利息现金流计算耗时：{}ms", assetNumber, principalInterestTime);

            // 步骤2：计算价差（已注释）这个前面的任务已经实现的，这里去掉
            // stepStartTime = System.currentTimeMillis();
            // calculateSpreads(assetDetail, monthlyDiscountFactorList, issueCashflowMap, evalCashflowMap);
            // long spreadsTime = System.currentTimeMillis() - stepStartTime;
            // log.debug("资产{}价差计算耗时：{}ms", assetNumber, spreadsTime);

            // 步骤3：计算现值
            stepStartTime = System.currentTimeMillis();
            calculatePresentValues(assetDetail, monthlyDiscountFactorList, evalCashflowMap);
            long presentValuesTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}现值计算耗时：{}ms", assetNumber, presentValuesTime);

            // 步骤4：计算久期指标
            stepStartTime = System.currentTimeMillis();
            calculateDurationIndicators(assetDetail, monthlyDiscountFactorList, evalCashflowMap);
            long durationIndicatorsTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}久期指标计算耗时：{}ms", assetNumber, durationIndicatorsTime);

            // 步骤5：计算DV10指标
            stepStartTime = System.currentTimeMillis();
            calculateDV10Indicators(assetDetail, keyDurationDiscountFactorList, evalCashflowMap);
            long dv10IndicatorsTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}DV10指标计算耗时：{}ms", assetNumber, dv10IndicatorsTime);

            // 步骤6：计算DV10综合指标
            stepStartTime = System.currentTimeMillis();
            calculateDV10CombinedIndicators(assetDetail);
            long dv10CombinedTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}DV10综合指标计算耗时：{}ms", assetNumber, dv10CombinedTime);

            // 步骤7：计算其他指标
            stepStartTime = System.currentTimeMillis();
            calculateOtherIndicators(assetDetail, evalCashflowMap);
            long otherIndicatorsTime = System.currentTimeMillis() - stepStartTime;
            log.debug("资产{}其他指标计算耗时：{}ms", assetNumber, otherIndicatorsTime);

            long totalTime = System.currentTimeMillis() - methodStartTime;

            // 输出详细的性能统计
            log.info("资产{}久期指标计算完成，总耗时：{}ms，详细耗时统计：" +
                    "数据验证={}ms, 现金流解析={}ms, 本金利息计算={}ms, " +
                    "现值计算={}ms, 久期指标={}ms, DV10指标={}ms, " +
                    "DV10综合={}ms, 其他指标={}ms",
                    assetNumber, totalTime, validationTime, parseTime, principalInterestTime,
                    presentValuesTime, durationIndicatorsTime, dv10IndicatorsTime,
                    dv10CombinedTime, otherIndicatorsTime);

            return true;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - methodStartTime;
            log.error("计算资产{}的久期指标失败，总耗时：{}ms", assetNumber, totalTime, e);
            return false;
        }
    }

    /**
     * 解析现金流值集JSON
     *
     * @param cashflowSetJson 现金流值集JSON字符串
     * @return 现金流Map，key为期限索引，value为现金流值
     */
    private Map<Integer, BigDecimal> parseCashflowSet(String cashflowSetJson) {
        return CashFlowUtil.parseCashflowAmounts(cashflowSetJson);
    }

    /**
     * 计算本金现金流和利息现金流
     *
     * 根据业务需求：
     * 1. 本金现金流：等于持仓面值，仅在最后一期发生，其余期数都是0
     * 2. 利息现金流：评估时点现金流值集-本金现金流
     *
     * @param assetDetail 资产明细
     * @param evalCashflowMap 评估时点现金流Map
     * @param accountPeriod 账期，格式YYYYMM
     */
    private void calculatePrincipalAndInterestCashflows(
            AdurDurationAssetDetailEntity assetDetail,
            Map<Integer, BigDecimal> evalCashflowMap,
            String accountPeriod) {

        long methodStartTime = System.currentTimeMillis();
        String assetNumber = assetDetail.getAssetNumber();

        try {
            // 获取持仓面值
            long stepStartTime = System.currentTimeMillis();
            BigDecimal holdingFaceValue = assetDetail.getHoldingFaceValue();
            if (holdingFaceValue == null) {
                holdingFaceValue = BigDecimal.ZERO;
            }

            // 计算本金现金流：仅在最后一期发生
            Map<Integer, Map<String, Object>> principalCashflowMap = new HashMap<>();
            Map<Integer, Map<String, Object>> interestCashflowMap = new HashMap<>();

            // 根据调整到期日计算本金现金流发生的期数
            int principalPeriod = calculatePrincipalPeriod(assetDetail.getAdjustedMaturityDate(), accountPeriod);
            long initTime = System.currentTimeMillis() - stepStartTime;

            // 生成0-600期的现金流数据
            stepStartTime = System.currentTimeMillis();
            for (int period = 0; period <= 600; period++) {
                // 计算日期（月末最后一天）
                String dateStr = calculatePeriodEndDate(period, accountPeriod);

                // 本金现金流：仅在调整到期日对应的期数等于持仓面值，其余期数为0
                BigDecimal principalAmount = (period == principalPeriod) ? holdingFaceValue : BigDecimal.ZERO;

                // 利息现金流：评估时点现金流 - 本金现金流
                BigDecimal evalAmount = evalCashflowMap.getOrDefault(period, BigDecimal.ZERO);
                BigDecimal interestAmount = evalAmount.subtract(principalAmount);

                // 构建本金现金流数据
                Map<String, Object> principalData = new HashMap<>();
                principalData.put("date", dateStr);
                principalData.put("value", principalAmount.toPlainString());
                principalCashflowMap.put(period, principalData);

                // 构建利息现金流数据
                Map<String, Object> interestData = new HashMap<>();
                interestData.put("date", dateStr);
                interestData.put("value", interestAmount.toPlainString());
                interestCashflowMap.put(period, interestData);
            }
            long loopTime = System.currentTimeMillis() - stepStartTime;

            // 转换为JSON格式并设置到实体
            stepStartTime = System.currentTimeMillis();
            String principalCashflowJson = convertCashflowMapToJson(principalCashflowMap);
            String interestCashflowJson = convertCashflowMapToJson(interestCashflowMap);

            assetDetail.setPrincipalCashflowSet(principalCashflowJson);
            assetDetail.setInterestCashflowSet(interestCashflowJson);
            long jsonTime = System.currentTimeMillis() - stepStartTime;

            long totalTime = System.currentTimeMillis() - methodStartTime;
            log.debug("资产{}本金现金流和利息现金流计算完成，本金发生期数：{}，总耗时：{}ms，" +
                    "初始化：{}ms，循环计算：{}ms，JSON转换：{}ms",
                    assetNumber, principalPeriod, totalTime, initTime, loopTime, jsonTime);

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - methodStartTime;
            log.error("计算资产{}的本金现金流和利息现金流失败，耗时：{}ms", assetNumber, totalTime, e);
            // 设置默认值
            assetDetail.setPrincipalCashflowSet("{}");
            assetDetail.setInterestCashflowSet("{}");
        }
    }

    /**
     * 计算价差
     *
     * 根据设计文档UC0010步骤2：
     * 1. 发行时点价差计算：如果折现曲线标识=0则赋值为0，否则使用goseek方法计算
     * 2. 评估时点价差计算：如果利差久期资产统计标识=0则赋值为0，否则使用goseek方法计算
     *
     * @param assetDetail 资产明细
     * @param monthlyDiscountFactorList 月度折现因子数据列表
     * @param issueCashflowMap 发行时点现金流Map
     * @param evalCashflowMap 评估时点现金流Map
     */
    private void calculateSpreads(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList,
            Map<Integer, BigDecimal> issueCashflowMap,
            Map<Integer, BigDecimal> evalCashflowMap) {

        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();

        // 发行时点价差计算
        if ("0".equals(assetDetail.getCurveId())) {
            assetDetail.setIssueSpread(BigDecimal.ZERO);
        } else {
            // 使用goseek方法计算，使得发行时点现金流值集向量*月度折现因子向量=发行时点资产现值
            AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = findMonthlyDiscountFactorOptimized(
                    accountName, securityCode, CURVE_SUB_CATEGORY_2);
            if (factorEntity != null && assetDetail.getIssuePresentValue() != null) {
                BigDecimal issueSpread = calculateSpreadUsingGoSeek(
                        issueCashflowMap, factorEntity, assetDetail.getIssuePresentValue());
                assetDetail.setIssueSpread(issueSpread);
            } else {
                if (factorEntity == null) {
                    log.warn("资产{}未找到曲线细分类{}的月度折现因子，发行时点价差设为0",
                            assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_2);
                }
                if (assetDetail.getIssuePresentValue() == null) {
                    log.warn("资产{}发行时点资产现值为null，发行时点价差设为0", assetDetail.getAssetNumber());
                }
                assetDetail.setIssueSpread(BigDecimal.ZERO);
            }
        }

        // 评估时点价差计算
        if ("0".equals(assetDetail.getSpreadDurationStatFlag())) {
            assetDetail.setEvalSpread(BigDecimal.ZERO);
        } else {
            // 使用goseek方法计算，使得评估时点现金流值集向量*月度折现因子向量=市值
            // 根据需求，评估时点价差计算需要使用曲线细分类=5的月度折现因子
            AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = findMonthlyDiscountFactorOptimized(
                    accountName, securityCode, CURVE_SUB_CATEGORY_5);
            if (factorEntity != null && assetDetail.getMarketValue() != null) {
                BigDecimal evalSpread = calculateSpreadUsingGoSeek(
                        evalCashflowMap, factorEntity, assetDetail.getMarketValue());
                assetDetail.setEvalSpread(evalSpread);
            } else {
                if (factorEntity == null) {
                    log.warn("资产{}未找到曲线细分类{}的月度折现因子，评估时点价差设为0",
                            assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_5);
                }
                if (assetDetail.getMarketValue() == null) {
                    log.warn("资产{}市值为null，评估时点价差设为0", assetDetail.getAssetNumber());
                }
                assetDetail.setEvalSpread(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 计算现值
     *
     * 根据设计文档UC0010步骤3：
     * 1. 评估时点资产现值：如果折现曲线标识=0则等于市值，否则使用曲线细分类=2的折现因子计算
     * 2. 评估时点资产现值+50bp：使用曲线细分类=3的折现因子计算
     * 3. 评估时点资产现值-50bp：使用曲线细分类=4的折现因子计算
     *
     * @param assetDetail 资产明细
     * @param monthlyDiscountFactorList 月度折现因子数据列表
     * @param evalCashflowMap 评估时点现金流Map
     */
    private void calculatePresentValues(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList,
            Map<Integer, BigDecimal> evalCashflowMap) {

        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();

        // 评估时点资产现值
        if ("0".equals(assetDetail.getCurveId())) {
            assetDetail.setEvalPresentValue(assetDetail.getMarketValue());
        } else {
            // 折现因子匹配条件：曲线细分类=2 and 账户名称=资产账户名称 and 证券代码=资产证券代码
            AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = findMonthlyDiscountFactorOptimized(
                    accountName, securityCode, CURVE_SUB_CATEGORY_2);
            if (factorEntity != null) {
                BigDecimal evalPresentValue = calculatePresentValue(evalCashflowMap, factorEntity);
                assetDetail.setEvalPresentValue(evalPresentValue);
            } else {
                log.warn("资产{}未找到曲线细分类{}的月度折现因子，使用市值作为评估时点资产现值",
                        assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_2);
                assetDetail.setEvalPresentValue(assetDetail.getMarketValue());
            }
        }

        // 评估时点资产现值+50bp
        // 折现因子匹配条件：曲线细分类=3 and 账户名称=资产账户名称 and 证券代码=资产证券代码
        AdurMonthlyDiscountFactorWithSpreadEntity factorEntityPlus50bp = findMonthlyDiscountFactorOptimized(
                accountName, securityCode, CURVE_SUB_CATEGORY_3);
        if (factorEntityPlus50bp != null) {
            BigDecimal evalPresentValuePlus50bp = calculatePresentValue(evalCashflowMap, factorEntityPlus50bp);
            assetDetail.setEvalPresentValuePlus50bp(evalPresentValuePlus50bp);
        } else {
            log.warn("资产{}未找到曲线细分类{}的月度折现因子，+50bp现值设为0",
                    assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_3);
            assetDetail.setEvalPresentValuePlus50bp(BigDecimal.ZERO);
        }

        // 评估时点资产现值-50bp
        // 折现因子匹配条件：曲线细分类=4 and 账户名称=资产账户名称 and 证券代码=资产证券代码
        AdurMonthlyDiscountFactorWithSpreadEntity factorEntityMinus50bp = findMonthlyDiscountFactorOptimized(
                accountName, securityCode, CURVE_SUB_CATEGORY_4);
        if (factorEntityMinus50bp != null) {
            BigDecimal evalPresentValueMinus50bp = calculatePresentValue(evalCashflowMap, factorEntityMinus50bp);
            assetDetail.setEvalPresentValueMinus50bp(evalPresentValueMinus50bp);
        } else {
            log.warn("资产{}未找到曲线细分类{}的月度折现因子，-50bp现值设为0",
                    assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_4);
            assetDetail.setEvalPresentValueMinus50bp(BigDecimal.ZERO);
        }
    }

    /**
     * 计算久期指标
     *
     * 根据设计文档UC0010步骤4：
     * 1. 资产修正久期：使用曲线细分类=2的折现因子和折现曲线计算
     * 2. 资产有效久期：使用+50bp和-50bp现值计算
     *
     * @param assetDetail 资产明细
     * @param monthlyDiscountFactorList 月度折现因子数据列表
     * @param evalCashflowMap 评估时点现金流Map
     */
    private void calculateDurationIndicators(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList,
            Map<Integer, BigDecimal> evalCashflowMap) {

        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();

        // 资产修正久期
        // 折现因子匹配条件：曲线细分类=2 and 账户名称=资产账户名称 and 证券代码=资产证券代码
        AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = findMonthlyDiscountFactorOptimized(
                accountName, securityCode, CURVE_SUB_CATEGORY_2);

        if (factorEntity != null) {
            BigDecimal modifiedDuration = calculateModifiedDuration(evalCashflowMap,
                    factorEntity, factorEntity, // 使用同一个曲线细分类的折现因子和折现曲线
                    assetDetail.getEvalPresentValue());
            assetDetail.setAssetModifiedDuration(modifiedDuration);
        } else {
            log.warn("资产{}未找到曲线细分类{}的月度折现因子，修正久期设为0",
                    assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_2);
            assetDetail.setAssetModifiedDuration(BigDecimal.ZERO);
        }

        // 资产有效久期 = -(评估时点资产现值+50bp-评估时点资产现值-50bp)/评估时点资产现值/0.01
        BigDecimal effectiveDuration = calculateEffectiveDuration(
                assetDetail.getEvalPresentValuePlus50bp(),
                assetDetail.getEvalPresentValueMinus50bp(),
                assetDetail.getEvalPresentValue());
        assetDetail.setAssetEffectiveDuration(effectiveDuration);

        // 利差久期计算
        // 根据需求：利差久期 = [评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量*1/(1+月度折现曲线表含价差中的折现曲线向量)*月份向量/12]/评估时点资产现值
        // 匹配条件：曲线细分类=5 and 账户名称=资产账户名称 and 证券代码=资产证券代码
        AdurMonthlyDiscountFactorWithSpreadEntity spreadFactorEntity = findMonthlyDiscountFactorOptimized(
                accountName, securityCode, CURVE_SUB_CATEGORY_5);

        if (spreadFactorEntity != null) {
            BigDecimal spreadDuration = calculateSpreadDuration(evalCashflowMap,
                    spreadFactorEntity, assetDetail.getEvalPresentValue());
            assetDetail.setSpreadDuration(spreadDuration);
        } else {
            log.warn("资产{}未找到曲线细分类{}的月度折现因子，利差久期设为0",
                    assetDetail.getAssetNumber(), CURVE_SUB_CATEGORY_5);
            assetDetail.setSpreadDuration(BigDecimal.ZERO);
        }
    }

    /**
     * 计算DV10指标
     *
     * 根据要求计算各关键期限的上升和下降情景现值：
     * - DV10_X_上升 = 评估时点现金流值集向量*关键久期折现因子向量（压力方向=上升，关键期限=X）
     * - DV10_X_下降 = 评估时点现金流值集向量*关键久期折现因子向量（压力方向=下降，关键期限=X）
     *
     * @param assetDetail 资产明细
     * @param keyDurationDiscountFactorList 关键久期折现因子数据列表
     * @param evalCashflowMap 评估时点现金流Map
     */
    private void calculateDV10Indicators(
            AdurDurationAssetDetailEntity assetDetail,
            List<AdurKeyDurationDiscountFactorWithSpreadEntity> keyDurationDiscountFactorList,
            Map<Integer, BigDecimal> evalCashflowMap) {

        long methodStartTime = System.currentTimeMillis();
        String accountName = assetDetail.getAccountName();
        String securityCode = assetDetail.getSecurityCode();
        String assetNumber = assetDetail.getAssetNumber();

        int foundUpCount = 0;
        int foundDownCount = 0;
        long totalSearchTime = 0;
        long totalCalculationTime = 0;

        // 计算各关键期限的DV10上升和下降指标
        for (String keyTerm : KEY_TERMS) {
            long termStartTime = System.currentTimeMillis();

            // 计算上升情景现值
            long searchStartTime = System.currentTimeMillis();
            AdurKeyDurationDiscountFactorWithSpreadEntity upEntity = findKeyDurationDiscountFactor(
                    keyDurationDiscountFactorList, accountName, securityCode, keyTerm, STRESS_DIRECTION_UP);
            long searchTime = System.currentTimeMillis() - searchStartTime;
            totalSearchTime += searchTime;

            if (upEntity != null) {
                long calcStartTime = System.currentTimeMillis();
                BigDecimal dv10Up = calculatePresentValueWithKeyDurationFactor(evalCashflowMap, upEntity);
                setDV10UpValue(assetDetail, keyTerm, dv10Up);
                long calcTime = System.currentTimeMillis() - calcStartTime;
                totalCalculationTime += calcTime;
                foundUpCount++;
            } else {
                log.debug("资产{}(账户:{}, 证券:{})未找到关键期限{}上升方向的折现因子",
                        assetNumber, accountName, securityCode, keyTerm);
            }

            // 计算下降情景现值
            searchStartTime = System.currentTimeMillis();
            AdurKeyDurationDiscountFactorWithSpreadEntity downEntity = findKeyDurationDiscountFactor(
                    keyDurationDiscountFactorList, accountName, securityCode, keyTerm, STRESS_DIRECTION_DOWN);
            searchTime = System.currentTimeMillis() - searchStartTime;
            totalSearchTime += searchTime;

            if (downEntity != null) {
                long calcStartTime = System.currentTimeMillis();
                BigDecimal dv10Down = calculatePresentValueWithKeyDurationFactor(evalCashflowMap, downEntity);
                setDV10DownValue(assetDetail, keyTerm, dv10Down);
                long calcTime = System.currentTimeMillis() - calcStartTime;
                totalCalculationTime += calcTime;
                foundDownCount++;
            } else {
                log.debug("资产{}(账户:{}, 证券:{})未找到关键期限{}下降方向的折现因子",
                        assetNumber, accountName, securityCode, keyTerm);
            }

            long termTime = System.currentTimeMillis() - termStartTime;
            if (termTime > 10) { // 只记录耗时超过10ms的期限
                log.debug("资产{}关键期限{}DV10计算耗时：{}ms", assetNumber, keyTerm, termTime);
            }
        }

        long totalTime = System.currentTimeMillis() - methodStartTime;
        log.debug("资产{}DV10指标计算完成，总耗时：{}ms，搜索耗时：{}ms，计算耗时：{}ms，" +
                "找到上升方向{}个，下降方向{}个，总期限{}个",
                assetNumber, totalTime, totalSearchTime, totalCalculationTime,
                foundUpCount, foundDownCount, KEY_TERMS.length);
    }

    /**
     * 计算DV10综合指标
     *
     * 根据公式计算DV10字段：DV10_X = -（DV10_X_上升-DV10_X_下降）/2
     *
     * @param assetDetail 资产明细
     */
    private void calculateDV10CombinedIndicators(AdurDurationAssetDetailEntity assetDetail) {

        // DV10_0 = -（DV10_0_上升-DV10_0_下降）/2
        if (assetDetail.getDv101Up() != null && assetDetail.getDv101Down() != null) {
            BigDecimal dv101 = assetDetail.getDv101Up().subtract(assetDetail.getDv101Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv101(dv101);
        }

        // DV10_0.5 = -（DV10_0.5_上升-DV10_0.5_下降）/2
        if (assetDetail.getDv102Up() != null && assetDetail.getDv102Down() != null) {
            BigDecimal dv102 = assetDetail.getDv102Up().subtract(assetDetail.getDv102Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv102(dv102);
        }

        // DV10_1 = -（DV10_1_上升-DV10_1_下降）/2
        if (assetDetail.getDv103Up() != null && assetDetail.getDv103Down() != null) {
            BigDecimal dv103 = assetDetail.getDv103Up().subtract(assetDetail.getDv103Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv103(dv103);
        }

        // DV10_2 = -（DV10_2_上升-DV10_2_下降）/2
        if (assetDetail.getDv104Up() != null && assetDetail.getDv104Down() != null) {
            BigDecimal dv104 = assetDetail.getDv104Up().subtract(assetDetail.getDv104Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv104(dv104);
        }

        // DV10_3 = -（DV10_3_上升-DV10_3_下降）/2
        if (assetDetail.getDv105Up() != null && assetDetail.getDv105Down() != null) {
            BigDecimal dv105 = assetDetail.getDv105Up().subtract(assetDetail.getDv105Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv105(dv105);
        }

        // DV10_4 = -（DV10_4_上升-DV10_4_下降）/2
        if (assetDetail.getDv106Up() != null && assetDetail.getDv106Down() != null) {
            BigDecimal dv106 = assetDetail.getDv106Up().subtract(assetDetail.getDv106Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv106(dv106);
        }

        // DV10_5 = -（DV10_5_上升-DV10_5_下降）/2
        if (assetDetail.getDv107Up() != null && assetDetail.getDv107Down() != null) {
            BigDecimal dv107 = assetDetail.getDv107Up().subtract(assetDetail.getDv107Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv107(dv107);
        }

        // DV10_6 = -（DV10_6_上升-DV10_6_下降）/2
        if (assetDetail.getDv108Up() != null && assetDetail.getDv108Down() != null) {
            BigDecimal dv108 = assetDetail.getDv108Up().subtract(assetDetail.getDv108Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv108(dv108);
        }

        // DV10_7 = -（DV10_7_上升-DV10_7_下降）/2
        if (assetDetail.getDv109Up() != null && assetDetail.getDv109Down() != null) {
            BigDecimal dv109 = assetDetail.getDv109Up().subtract(assetDetail.getDv109Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv109(dv109);
        }

        // DV10_8 = -（DV10_8_上升-DV10_8_下降）/2
        if (assetDetail.getDv1010Up() != null && assetDetail.getDv1010Down() != null) {
            BigDecimal dv1010 = assetDetail.getDv1010Up().subtract(assetDetail.getDv1010Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1010(dv1010);
        }

        // DV10_10 = -（DV10_10_上升-DV10_10_下降）/2
        if (assetDetail.getDv1011Up() != null && assetDetail.getDv1011Down() != null) {
            BigDecimal dv1011 = assetDetail.getDv1011Up().subtract(assetDetail.getDv1011Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1011(dv1011);
        }

        // DV10_12 = -（DV10_12_上升-DV10_12_下降）/2
        if (assetDetail.getDv1012Up() != null && assetDetail.getDv1012Down() != null) {
            BigDecimal dv1012 = assetDetail.getDv1012Up().subtract(assetDetail.getDv1012Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1012(dv1012);
        }

        // DV10_15 = -（DV10_15_上升-DV10_15_下降）/2
        if (assetDetail.getDv1013Up() != null && assetDetail.getDv1013Down() != null) {
            BigDecimal dv1013 = assetDetail.getDv1013Up().subtract(assetDetail.getDv1013Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1013(dv1013);
        }

        // DV10_20 = -（DV10_20_上升-DV10_20_下降）/2
        if (assetDetail.getDv1014Up() != null && assetDetail.getDv1014Down() != null) {
            BigDecimal dv1014 = assetDetail.getDv1014Up().subtract(assetDetail.getDv1014Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1014(dv1014);
        }

        // DV10_25 = -（DV10_25_上升-DV10_25_下降）/2
        if (assetDetail.getDv1015Up() != null && assetDetail.getDv1015Down() != null) {
            BigDecimal dv1015 = assetDetail.getDv1015Up().subtract(assetDetail.getDv1015Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1015(dv1015);
        }

        // DV10_30 = -（DV10_30_上升-DV10_30_下降）/2
        if (assetDetail.getDv1016Up() != null && assetDetail.getDv1016Down() != null) {
            BigDecimal dv1016 = assetDetail.getDv1016Up().subtract(assetDetail.getDv1016Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1016(dv1016);
        }

        // DV10_35 = -（DV10_35_上升-DV10_35_下降）/2
        if (assetDetail.getDv1017Up() != null && assetDetail.getDv1017Down() != null) {
            BigDecimal dv1017 = assetDetail.getDv1017Up().subtract(assetDetail.getDv1017Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1017(dv1017);
        }

        // DV10_40 = -（DV10_40_上升-DV10_40_下降）/2
        if (assetDetail.getDv1018Up() != null && assetDetail.getDv1018Down() != null) {
            BigDecimal dv1018 = assetDetail.getDv1018Up().subtract(assetDetail.getDv1018Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1018(dv1018);
        }

        // DV10_45 = -（DV10_45_上升-DV10_45_下降）/2
        if (assetDetail.getDv1019Up() != null && assetDetail.getDv1019Down() != null) {
            BigDecimal dv1019 = assetDetail.getDv1019Up().subtract(assetDetail.getDv1019Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1019(dv1019);
        }

        // DV10_50 = -（DV10_50_上升-DV10_50_下降）/2
        if (assetDetail.getDv1020Up() != null && assetDetail.getDv1020Down() != null) {
            BigDecimal dv1020 = assetDetail.getDv1020Up().subtract(assetDetail.getDv1020Down())
                    .divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP).negate();
            assetDetail.setDv1020(dv1020);
        }
    }

    /**
     * 计算其他指标
     *
     * 根据设计文档UC0010步骤6：
     * 1. 评估时点到期收益率：使用XirrCalculator工具类计算XIRR
     * 2. 账面价值σ系列：根据评估时点利差和利差久期计算
     *
     * @param assetDetail 资产明细
     * @param evalCashflowMap 评估时点现金流Map
     */
    private void calculateOtherIndicators(
            AdurDurationAssetDetailEntity assetDetail,
            Map<Integer, BigDecimal> evalCashflowMap) {


        // 账面价值σ系列
        BigDecimal bookValue = assetDetail.getBookValue();
        BigDecimal evalSpread = assetDetail.getEvalSpread();
        BigDecimal spreadDuration = assetDetail.getSpreadDuration();

        if (bookValue != null && evalSpread != null && spreadDuration != null) {
            // 账面价值σ=9%
            BigDecimal bookValueSigma9 = bookValue.multiply(
                    BigDecimal.ONE.subtract(new BigDecimal("0.09").multiply(evalSpread).multiply(spreadDuration))
            );
            assetDetail.setBookValueSigma9(bookValueSigma9);

            // 账面价值σ=17%
            BigDecimal bookValueSigma17 = bookValue.multiply(
                    BigDecimal.ONE.subtract(new BigDecimal("0.17").multiply(evalSpread).multiply(spreadDuration))
            );
            assetDetail.setBookValueSigma17(bookValueSigma17);

            // 账面价值σ=77%
            BigDecimal bookValueSigma77 = bookValue.multiply(
                    BigDecimal.ONE.subtract(new BigDecimal("0.77").multiply(evalSpread).multiply(spreadDuration))
            );
            assetDetail.setBookValueSigma77(bookValueSigma77);
        }
    }

    /**
     * 使用GoSeek方法计算价差
     *
     * 改进的GoSeek实现：
     * 1. 扩大搜索范围
     * 2. 先检查边界值
     * 3. 使用更精确的容差
     * 4. 添加收敛检查
     *
     * @param cashflowMap 现金流Map
     * @param discountFactorEntity 折现因子实体
     * @param targetValue 目标值
     * @return 计算得到的价差
     */
    private BigDecimal calculateSpreadUsingGoSeek(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity,
            BigDecimal targetValue) {

        if (cashflowMap == null || cashflowMap.isEmpty() ||
            discountFactorEntity == null || targetValue == null ||
            targetValue.compareTo(BigDecimal.ZERO) == 0) {
            log.debug("GoSeek计算参数不完整，返回0价差");
            return BigDecimal.ZERO;
        }

        // 扩大搜索范围，从-50%到+50%
        BigDecimal lowSpread = new BigDecimal("-0.5");
        BigDecimal highSpread = new BigDecimal("0.5");
        BigDecimal tolerance = new BigDecimal("0.0001"); // 更精确的容差

        // 先检查边界值，确保解在搜索范围内
        BigDecimal lowValue = calculatePresentValueWithSpread(cashflowMap, discountFactorEntity, lowSpread);
        BigDecimal highValue = calculatePresentValueWithSpread(cashflowMap, discountFactorEntity, highSpread);

        BigDecimal lowDiff = lowValue.subtract(targetValue);
        BigDecimal highDiff = highValue.subtract(targetValue);

        // 如果边界值的符号相同，说明解可能不在当前范围内
        if (lowDiff.signum() == highDiff.signum()) {
            log.debug("目标值{}可能不在搜索范围内，低值：{}，高值：{}", targetValue, lowValue, highValue);

            // 尝试扩大搜索范围
            if (lowDiff.signum() > 0) {
                // 两个值都大于目标值，需要更负的价差
                lowSpread = new BigDecimal("-1.0");
                lowValue = calculatePresentValueWithSpread(cashflowMap, discountFactorEntity, lowSpread);
                lowDiff = lowValue.subtract(targetValue);
            } else {
                // 两个值都小于目标值，需要更正的价差
                highSpread = new BigDecimal("1.0");
                highValue = calculatePresentValueWithSpread(cashflowMap, discountFactorEntity, highSpread);
                highDiff = highValue.subtract(targetValue);
            }

            // 如果扩大范围后仍然同号，返回0
            if (lowDiff.signum() == highDiff.signum()) {
                log.warn("无法找到合适的价差范围，返回0");
                return BigDecimal.ZERO;
            }
        }

        BigDecimal previousMid = null;
        int stagnantCount = 0;

        for (int i = 0; i < 200; i++) { // 增加最大迭代次数
            BigDecimal midSpread = lowSpread.add(highSpread).divide(new BigDecimal("2"), 12, RoundingMode.HALF_UP);

            // 检查是否收敛停滞
            if (previousMid != null && midSpread.subtract(previousMid).abs().compareTo(new BigDecimal("0.000001")) < 0) {
                stagnantCount++;
                if (stagnantCount > 5) {
                    log.debug("GoSeek算法收敛停滞，当前价差：{}", midSpread);
                    break;
                }
            } else {
                stagnantCount = 0;
            }
            previousMid = midSpread;

            BigDecimal calculatedValue = calculatePresentValueWithSpread(cashflowMap, discountFactorEntity, midSpread);
            BigDecimal diff = calculatedValue.subtract(targetValue);

            // 检查是否达到容差要求
            if (diff.abs().compareTo(tolerance) <= 0) {
                log.debug("GoSeek收敛成功，迭代{}次，价差：{}，目标值：{}，计算值：{}",
                        i + 1, midSpread, targetValue, calculatedValue);
                return midSpread.setScale(10, RoundingMode.HALF_UP);
            }

            // 更新搜索范围
            if (diff.compareTo(BigDecimal.ZERO) > 0) {
                highSpread = midSpread;
            } else {
                lowSpread = midSpread;
            }

            // 每50次迭代输出调试信息
            if (i % 50 == 49) {
                log.debug("GoSeek迭代{}次，当前范围：[{}, {}]，中值：{}，差异：{}",
                        i + 1, lowSpread, highSpread, midSpread, diff);
            }
        }

        // 如果未收敛，返回最后的中值
        BigDecimal finalSpread = lowSpread.add(highSpread).divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP);
        log.warn("GoSeek未完全收敛，返回最后计算的价差：{}", finalSpread);
        return finalSpread;
    }

    /**
     * 计算现值
     *
     * @param cashflowMap 现金流Map
     * @param discountFactorEntity 折现因子实体
     * @return 现值
     */
    private BigDecimal calculatePresentValue(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity) {

        if (discountFactorEntity == null) {
            return BigDecimal.ZERO;
        }

        // 解析月度折现因子的期限数据
        Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(discountFactorEntity.getMonthlyDiscountFactorSet());

        BigDecimal presentValue = BigDecimal.ZERO;

        for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal cashflow = entry.getValue();
            BigDecimal discountFactor = discountFactorMap.get(termIndex);

            if (cashflow != null && discountFactor != null) {
                presentValue = presentValue.add(cashflow.multiply(discountFactor));
            }
        }

        return presentValue.setScale(10, RoundingMode.HALF_UP);
    }

    /**
     * 计算带价差的现值
     *
     * @param cashflowMap 现金流Map
     * @param discountFactorEntity 折现因子实体
     * @param spread 价差
     * @return 现值
     */
    private BigDecimal calculatePresentValueWithSpread(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity,
            BigDecimal spread) {

        if (discountFactorEntity == null) {
            return BigDecimal.ZERO;
        }

        // 解析月度折现因子的期限数据
        Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(discountFactorEntity.getMonthlyDiscountFactorSet());

        BigDecimal presentValue = BigDecimal.ZERO;

        for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal cashflow = entry.getValue();
            BigDecimal baseFactor = discountFactorMap.get(termIndex);

            if (cashflow != null && baseFactor != null) {
                // 直接将价差加到折现因子上作为调整后的折现因子
                BigDecimal adjustedFactor = baseFactor.add(spread);
                presentValue = presentValue.add(cashflow.multiply(adjustedFactor));
            }
        }

        return presentValue.setScale(8, RoundingMode.HALF_UP);
    }

    /**
     * 使用关键久期折现因子计算现值
     *
     * @param cashflowMap 现金流Map
     * @param keyDurationFactorEntity 关键久期折现因子实体
     * @return 现值
     */
    private BigDecimal calculatePresentValueWithKeyDurationFactor(
            Map<Integer, BigDecimal> cashflowMap,
            AdurKeyDurationDiscountFactorWithSpreadEntity keyDurationFactorEntity) {

        if (keyDurationFactorEntity == null) {
            return BigDecimal.ZERO;
        }

        // 解析关键久期折现因子的期限数据
        Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(keyDurationFactorEntity.getKeyDurationFactorWithSpreadSet());

        BigDecimal presentValue = BigDecimal.ZERO;

        for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal cashflow = entry.getValue();
            BigDecimal discountFactor = discountFactorMap.get(termIndex);

            if (cashflow != null && discountFactor != null) {
                presentValue = presentValue.add(cashflow.multiply(discountFactor));
            }
        }

        return presentValue.setScale(10, RoundingMode.HALF_UP);
    }

    /**
     * 计算修正久期
     *
     * 根据公式计算：
     * 资产修正久期 = [评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量*1/（1+月度折现曲线表含价差中的折现曲线向量）*久期资产明细表中的月份向量/12]/评估时点资产现值
     *
     * @param cashflowMap 现金流Map
     * @param discountFactorEntity 折现因子实体
     * @param discountCurveEntity 折现曲线实体（用于获取收益率）
     * @param presentValue 现值
     * @return 修正久期
     */
    private BigDecimal calculateModifiedDuration(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity,
            AdurMonthlyDiscountFactorWithSpreadEntity discountCurveEntity,
            BigDecimal presentValue) {

        if (discountFactorEntity == null || discountCurveEntity == null ||
            presentValue == null || presentValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        try {
            // 解析月度折现因子的期限数据
            Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(discountFactorEntity.getMonthlyDiscountFactorSet());
            // 解析月度折现曲线的期限数据（收益率）
            Map<Integer, BigDecimal> discountCurveMap = TermDataUtil.parseTermValues(discountCurveEntity.getMonthlyDiscountFactorSet());

            // 向量化计算优化
            BigDecimal weightedDuration = BigDecimal.ZERO;

            // 预计算常用值，避免重复计算
            Map<Integer, BigDecimal> monthWeightCache = new HashMap<>();
            Map<Integer, BigDecimal> inverseRateCache = new HashMap<>();

            // 只对有现金流的期限进行计算，避免无效循环
            for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal cashflow = entry.getValue();

                // 快速跳过零现金流
                if (cashflow == null || cashflow.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                BigDecimal discountFactor = discountFactorMap.get(termIndex);
                BigDecimal yieldRate = discountCurveMap.get(termIndex);

                if (discountFactor != null && yieldRate != null) {
                    // 获取或计算月份权重（缓存避免重复计算）
                    BigDecimal monthWeight = monthWeightCache.computeIfAbsent(termIndex,
                        k -> new BigDecimal(k).divide(new BigDecimal("12"), 10, RoundingMode.HALF_UP));

                    // 获取或计算1/(1+收益率)（缓存避免重复计算）
                    BigDecimal inverseOnePlusRate = inverseRateCache.computeIfAbsent(termIndex,
                        k -> BigDecimal.ONE.divide(BigDecimal.ONE.add(yieldRate), 10, RoundingMode.HALF_UP));

                    // 向量化乘积计算：现金流 * 折现因子 * 1/(1+收益率) * 月份/12
                    BigDecimal termDuration = cashflow
                            .multiply(discountFactor)
                            .multiply(inverseOnePlusRate)
                            .multiply(monthWeight);

                    weightedDuration = weightedDuration.add(termDuration);
                }
            }

            // 最终计算
            BigDecimal modifiedDuration = weightedDuration.divide(presentValue, 10, RoundingMode.HALF_UP);

            return modifiedDuration;

        } catch (Exception e) {
            log.error("修正久期计算失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算修正久期（兼容性方法）
     *
     * @param cashflowMap 现金流Map
     * @param discountFactorEntity 折现因子实体
     * @param presentValue 现值
     * @return 修正久期
     */
    private BigDecimal calculateModifiedDuration(
            Map<Integer, BigDecimal> cashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity,
            BigDecimal presentValue) {

        if (discountFactorEntity == null || presentValue == null || presentValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // 解析月度折现因子的期限数据
        Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(discountFactorEntity.getMonthlyDiscountFactorSet());

        BigDecimal weightedDuration = BigDecimal.ZERO;

        for (Map.Entry<Integer, BigDecimal> entry : cashflowMap.entrySet()) {
            Integer termIndex = entry.getKey();
            BigDecimal cashflow = entry.getValue();
            BigDecimal discountFactor = discountFactorMap.get(termIndex);
            BigDecimal yieldRate = getYieldRateFromDiscountFactor(discountFactor, termIndex);

            if (cashflow != null && discountFactor != null && yieldRate != null) {
                // 计算：现金流 * 折现因子 * 1/(1+收益率) * 月份/12
                BigDecimal termDuration = cashflow
                        .multiply(discountFactor)
                        .multiply(BigDecimal.ONE.divide(BigDecimal.ONE.add(yieldRate), 10, RoundingMode.HALF_UP))
                        .multiply(new BigDecimal(termIndex))
                        .divide(new BigDecimal("12"), 10, RoundingMode.HALF_UP);

                weightedDuration = weightedDuration.add(termDuration);
            }
        }

        return weightedDuration.divide(presentValue, 6, RoundingMode.HALF_UP);
    }

    /**
     * 计算有效久期
     *
     * @param presentValuePlus50bp +50bp现值
     * @param presentValueMinus50bp -50bp现值
     * @param presentValue 基准现值
     * @return 有效久期
     */
    private BigDecimal calculateEffectiveDuration(
            BigDecimal presentValuePlus50bp,
            BigDecimal presentValueMinus50bp,
            BigDecimal presentValue) {

        if (presentValue == null || presentValue.compareTo(BigDecimal.ZERO) == 0 ||
                presentValuePlus50bp == null || presentValueMinus50bp == null) {
            return BigDecimal.ZERO;
        }

        // 有效久期 = -(PV+50bp - PV-50bp) / PV / 0.01
        BigDecimal numerator = presentValuePlus50bp.subtract(presentValueMinus50bp).negate();
        BigDecimal denominator = presentValue.multiply(new BigDecimal("0.01"));

        return numerator.divide(denominator, 6, RoundingMode.HALF_UP);
    }



    /**
     * 从折现因子反推收益率
     *
     * @param discountFactor 折现因子
     * @param termIndex 期限索引
     * @return 收益率
     */
    private BigDecimal getYieldRateFromDiscountFactor(BigDecimal discountFactor, int termIndex) {
        if (termIndex == 0 || discountFactor == null || discountFactor.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        try {
            // 收益率 = (1/折现因子)^(12/期限) - 1
            double exponent = 12.0 / termIndex;
            double yieldRate = Math.pow(BigDecimal.ONE.divide(discountFactor, 10, RoundingMode.HALF_UP).doubleValue(), exponent) - 1;
            return BigDecimal.valueOf(yieldRate).setScale(10, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.warn("从折现因子反推收益率失败", e);
            return BigDecimal.ZERO;
        }
    }





    /**
     * 设置DV10上升情景值到对应字段
     *
     * 根据关键期限设置对应的DV10上升字段：
     * DV10_0_上升 -> dv10_1_up, DV10_0.5_上升 -> dv10_2_up, 等等
     *
     * @param assetDetail 资产明细
     * @param keyTerm 关键期限
     * @param dv10UpValue DV10上升值
     */
    private void setDV10UpValue(AdurDurationAssetDetailEntity assetDetail, String keyTerm, BigDecimal dv10UpValue) {
        switch (keyTerm) {
            case "0": assetDetail.setDv101Up(dv10UpValue); break;      // DV10_0_上升 -> dv10_1_up
            case "0.5": assetDetail.setDv102Up(dv10UpValue); break;   // DV10_0.5_上升 -> dv10_2_up
            case "1": assetDetail.setDv103Up(dv10UpValue); break;     // DV10_1_上升 -> dv10_3_up
            case "2": assetDetail.setDv104Up(dv10UpValue); break;     // DV10_2_上升 -> dv10_4_up
            case "3": assetDetail.setDv105Up(dv10UpValue); break;     // DV10_3_上升 -> dv10_5_up
            case "4": assetDetail.setDv106Up(dv10UpValue); break;     // DV10_4_上升 -> dv10_6_up
            case "5": assetDetail.setDv107Up(dv10UpValue); break;     // DV10_5_上升 -> dv10_7_up
            case "6": assetDetail.setDv108Up(dv10UpValue); break;     // DV10_6_上升 -> dv10_8_up
            case "7": assetDetail.setDv109Up(dv10UpValue); break;     // DV10_7_上升 -> dv10_9_up
            case "8": assetDetail.setDv1010Up(dv10UpValue); break;    // DV10_8_上升 -> dv10_10_up
            case "10": assetDetail.setDv1011Up(dv10UpValue); break;   // DV10_10_上升 -> dv10_11_up
            case "12": assetDetail.setDv1012Up(dv10UpValue); break;   // DV10_12_上升 -> dv10_12_up
            case "15": assetDetail.setDv1013Up(dv10UpValue); break;   // DV10_15_上升 -> dv10_13_up
            case "20": assetDetail.setDv1014Up(dv10UpValue); break;   // DV10_20_上升 -> dv10_14_up
            case "25": assetDetail.setDv1015Up(dv10UpValue); break;   // DV10_25_上升 -> dv10_15_up
            case "30": assetDetail.setDv1016Up(dv10UpValue); break;   // DV10_30_上升 -> dv10_16_up
            case "35": assetDetail.setDv1017Up(dv10UpValue); break;   // DV10_35_上升 -> dv10_17_up
            case "40": assetDetail.setDv1018Up(dv10UpValue); break;   // DV10_40_上升 -> dv10_18_up
            case "45": assetDetail.setDv1019Up(dv10UpValue); break;   // DV10_45_上升 -> dv10_19_up
            case "50": assetDetail.setDv1020Up(dv10UpValue); break;   // DV10_50_上升 -> dv10_20_up
            default:
                log.warn("未知的关键期限：{}", keyTerm);
                break;
        }
    }

    /**
     * 设置DV10下降情景值到对应字段
     *
     * 根据关键期限设置对应的DV10下降字段：
     * DV10_0_下降 -> dv10_1_down, DV10_0.5_下降 -> dv10_2_down, 等等
     *
     * @param assetDetail 资产明细
     * @param keyTerm 关键期限
     * @param dv10DownValue DV10下降值
     */
    private void setDV10DownValue(AdurDurationAssetDetailEntity assetDetail, String keyTerm, BigDecimal dv10DownValue) {
        switch (keyTerm) {
            case "0": assetDetail.setDv101Down(dv10DownValue); break;      // DV10_0_下降 -> dv10_1_down
            case "0.5": assetDetail.setDv102Down(dv10DownValue); break;   // DV10_0.5_下降 -> dv10_2_down
            case "1": assetDetail.setDv103Down(dv10DownValue); break;     // DV10_1_下降 -> dv10_3_down
            case "2": assetDetail.setDv104Down(dv10DownValue); break;     // DV10_2_下降 -> dv10_4_down
            case "3": assetDetail.setDv105Down(dv10DownValue); break;     // DV10_3_下降 -> dv10_5_down
            case "4": assetDetail.setDv106Down(dv10DownValue); break;     // DV10_4_下降 -> dv10_6_down
            case "5": assetDetail.setDv107Down(dv10DownValue); break;     // DV10_5_下降 -> dv10_7_down
            case "6": assetDetail.setDv108Down(dv10DownValue); break;     // DV10_6_下降 -> dv10_8_down
            case "7": assetDetail.setDv109Down(dv10DownValue); break;     // DV10_7_下降 -> dv10_9_down
            case "8": assetDetail.setDv1010Down(dv10DownValue); break;    // DV10_8_下降 -> dv10_10_down
            case "10": assetDetail.setDv1011Down(dv10DownValue); break;   // DV10_10_下降 -> dv10_11_down
            case "12": assetDetail.setDv1012Down(dv10DownValue); break;   // DV10_12_下降 -> dv10_12_down
            case "15": assetDetail.setDv1013Down(dv10DownValue); break;   // DV10_15_下降 -> dv10_13_down
            case "20": assetDetail.setDv1014Down(dv10DownValue); break;   // DV10_20_下降 -> dv10_14_down
            case "25": assetDetail.setDv1015Down(dv10DownValue); break;   // DV10_25_下降 -> dv10_15_down
            case "30": assetDetail.setDv1016Down(dv10DownValue); break;   // DV10_30_下降 -> dv10_16_down
            case "35": assetDetail.setDv1017Down(dv10DownValue); break;   // DV10_35_下降 -> dv10_17_down
            case "40": assetDetail.setDv1018Down(dv10DownValue); break;   // DV10_40_下降 -> dv10_18_down
            case "45": assetDetail.setDv1019Down(dv10DownValue); break;   // DV10_45_下降 -> dv10_19_down
            case "50": assetDetail.setDv1020Down(dv10DownValue); break;   // DV10_50_下降 -> dv10_20_down
            default:
                log.warn("未知的关键期限：{}", keyTerm);
                break;
        }
    }

    /**
     * 根据调整到期日计算本金现金流发生的期数
     *
     * @param adjustedMaturityDate 调整到期日
     * @param accountPeriod 账期，格式YYYYMM
     * @return 本金发生的期数，如果计算失败则返回600
     */
    private int calculatePrincipalPeriod(Date adjustedMaturityDate, String accountPeriod) {
        if (adjustedMaturityDate == null) {
            log.warn("调整到期日为空，使用默认期数600");
            return 600;
        }

        try {
            // 获取账期月末日期作为基准日期
            LocalDate baseDate = getAccountPeriodEndDate(accountPeriod);

            // 将调整到期日转换为LocalDate
            LocalDate maturityDate = adjustedMaturityDate.toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDate();

            // 计算月份差
            long monthsBetween = java.time.temporal.ChronoUnit.MONTHS.between(
                    baseDate.withDayOfMonth(1),
                    maturityDate.withDayOfMonth(1)
            );

            // 确保期数在0-600范围内
            int period = (int) Math.max(0, Math.min(600, monthsBetween));

            log.debug("账期：{}，调整到期日：{}，计算得到本金发生期数：{}", accountPeriod, maturityDate, period);
            return period;

        } catch (Exception e) {
            log.error("计算本金发生期数失败，账期：{}，调整到期日：{}", accountPeriod, adjustedMaturityDate, e);
            return 600; // 默认返回最后一期
        }
    }

    /**
     * 计算指定期数对应的月末日期
     *
     * @param period 期数（0表示账期当月，1表示账期下个月，以此类推）
     * @param accountPeriod 账期，格式YYYYMM
     * @return 日期字符串，格式为yyyy-MM-dd
     */
    private String calculatePeriodEndDate(int period, String accountPeriod) {
        try {
            // 解析账期获取基准日期
            LocalDate baseDate = getAccountPeriodEndDate(accountPeriod);

            // 加上对应的月份数，并获取该月的最后一天
            LocalDate targetDate = baseDate.plusMonths(period).withDayOfMonth(1)
                    .withDayOfMonth(baseDate.plusMonths(period).lengthOfMonth());

            return targetDate.toString();
        } catch (Exception e) {
            log.warn("计算期数{}对应的日期失败，账期：{}，使用默认日期", period, accountPeriod, e);
            return LocalDate.now().toString();
        }
    }

    /**
     * 根据账期获取账期月末日期
     *
     * @param accountPeriod 账期，格式YYYYMM
     * @return 账期月末日期
     */
    private LocalDate getAccountPeriodEndDate(String accountPeriod) {
        try {
            if (accountPeriod == null || accountPeriod.length() != 6) {
                log.warn("账期格式错误：{}，使用当前日期", accountPeriod);
                return LocalDate.now();
            }

            int year = Integer.parseInt(accountPeriod.substring(0, 4));
            int month = Integer.parseInt(accountPeriod.substring(4, 6));

            // 返回该月的最后一天
            return LocalDate.of(year, month, 1).plusMonths(1).minusDays(1);

        } catch (Exception e) {
            log.error("解析账期失败：{}", accountPeriod, e);
            return LocalDate.now();
        }
    }

    /**
     * 将现金流Map转换为JSON字符串
     *
     * @param cashflowMap 现金流Map
     * @return JSON字符串
     */
    private String convertCashflowMapToJson(Map<Integer, Map<String, Object>> cashflowMap) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(cashflowMap);
        } catch (Exception e) {
            log.error("转换现金流Map为JSON失败", e);
            return "{}";
        }
    }

    /**
     * 计算利差久期
     *
     * 根据需求计算公式：
     * 利差久期 = [评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量*1/(1+月度折现曲线表含价差中的折现曲线向量)*月份向量/12]/评估时点资产现值
     *
     * 匹配条件：
     * 1. 折现因子匹配条件：月度折现因子表含价差.曲线细分类=5 and 账户名称=久期资产明细表.账户名称 and 证券代码=久期资产明细表.证券代码
     * 2. 折现曲线匹配条件：月度折现曲线表含价差.曲线细分类=5 and 账户名称=久期资产明细表.账户名称 and 证券代码=久期资产明细表.证券代码
     *
     * @param evalCashflowMap 评估时点现金流Map
     * @param discountFactorEntity 月度折现因子实体（曲线细分类=5）
     * @param evalPresentValue 评估时点资产现值
     * @return 利差久期
     */
    private BigDecimal calculateSpreadDuration(
            Map<Integer, BigDecimal> evalCashflowMap,
            AdurMonthlyDiscountFactorWithSpreadEntity discountFactorEntity,
            BigDecimal evalPresentValue) {

        if (evalCashflowMap == null || evalCashflowMap.isEmpty() ||
            discountFactorEntity == null || evalPresentValue == null ||
            evalPresentValue.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("利差久期计算参数不完整，返回0");
            return BigDecimal.ZERO;
        }

        try {
            // 从缓存中查找对应的月度折现曲线表含价差数据（曲线细分类=5）
            AdurMonthlyDiscountCurveWithSpreadEntity curveEntity = findMonthlyDiscountCurveOptimized(
                    discountFactorEntity.getAccountName(),
                    discountFactorEntity.getSecurityCode(),
                    CURVE_SUB_CATEGORY_5);

            if (curveEntity == null) {
                log.warn("未找到对应的月度折现曲线表含价差数据，账户：{}，证券：{}，曲线细分类：{}",
                        discountFactorEntity.getAccountName(),
                        discountFactorEntity.getSecurityCode(),
                        CURVE_SUB_CATEGORY_5);
                return BigDecimal.ZERO;
            }

            // 解析折现因子和折现曲线的期限数据
            Map<Integer, BigDecimal> discountFactorMap = TermDataUtil.parseTermValues(
                    discountFactorEntity.getMonthlyDiscountFactorSet());
            Map<Integer, BigDecimal> discountCurveMap = TermDataUtil.parseTermValues(
                    curveEntity.getMonthlyDiscountRateWithSpreadSet());

            // 向量化计算优化：预计算常用值，减少重复计算
            BigDecimal numerator = BigDecimal.ZERO; // 分子

            // 预计算月份权重，避免重复除法运算
            Map<Integer, BigDecimal> monthWeightCache = new HashMap<>();

            // 只对有现金流的期限进行计算，避免无效循环
            for (Map.Entry<Integer, BigDecimal> entry : evalCashflowMap.entrySet()) {
                Integer termIndex = entry.getKey();
                BigDecimal cashflow = entry.getValue();

                // 快速跳过零现金流
                if (cashflow == null || cashflow.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                BigDecimal discountFactor = discountFactorMap.get(termIndex);
                BigDecimal discountRate = discountCurveMap.get(termIndex);

                if (discountFactor != null && discountRate != null) {
                    // 获取或计算月份权重（缓存避免重复计算）
                    BigDecimal monthWeight = monthWeightCache.computeIfAbsent(termIndex,
                        k -> new BigDecimal(k).divide(new BigDecimal("12"), 10, RoundingMode.HALF_UP));

                    // 计算 1/(1+折现曲线) - 优化：避免创建临时BigDecimal对象
                    BigDecimal onePlusRate = BigDecimal.ONE.add(discountRate);
                    BigDecimal inverseOnePlusRate = BigDecimal.ONE.divide(onePlusRate, 10, RoundingMode.HALF_UP);

                    // 向量化乘积计算：现金流 * 折现因子 * 1/(1+折现曲线) * 月份/12
                    BigDecimal product = cashflow
                            .multiply(discountFactor)
                            .multiply(inverseOnePlusRate)
                            .multiply(monthWeight);

                    numerator = numerator.add(product);
                }
            }

            // 计算利差久期：分子 / 评估时点资产现值
            BigDecimal spreadDuration = numerator.divide(evalPresentValue, 10, RoundingMode.HALF_UP);

            return spreadDuration;

        } catch (Exception e) {
            log.error("利差久期计算失败", e);
            return BigDecimal.ZERO;
        }
    }



    /**
     * 构建折现因子索引缓存
     *
     * @param monthlyDiscountFactorList 月度折现因子列表
     */
    private void buildDiscountFactorIndex(List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorList) {
        long startTime = System.currentTimeMillis();

        // 清空之前的缓存
        discountFactorIndexCache.clear();

        if (!CollectionUtils.isEmpty(monthlyDiscountFactorList)) {
            for (AdurMonthlyDiscountFactorWithSpreadEntity entity : monthlyDiscountFactorList) {
                String key = buildIndexKey(entity.getAccountName(), entity.getSecurityCode(), entity.getCurveSubCategory());
                discountFactorIndexCache.put(key, entity);
            }
        }

        long buildTime = System.currentTimeMillis() - startTime;
        log.info("构建折现因子索引缓存完成，耗时:{}ms，缓存条目数:{}", buildTime, discountFactorIndexCache.size());
    }

    /**
     * 构建索引键
     *
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param curveSubCategory 曲线细分类
     * @return 索引键
     */
    private String buildIndexKey(String accountName, String securityCode, String curveSubCategory) {
        return accountName + "|" + securityCode + "|" + curveSubCategory;
    }

    /**
     * 构建月度折现曲线索引缓存
     *
     * @param monthlyDiscountCurveList 月度折现曲线列表
     */
    private void buildDiscountCurveIndex(List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveList) {
        long startTime = System.currentTimeMillis();

        // 清空之前的缓存
        discountCurveIndexCache.clear();

        if (!CollectionUtils.isEmpty(monthlyDiscountCurveList)) {
            for (AdurMonthlyDiscountCurveWithSpreadEntity entity : monthlyDiscountCurveList) {
                String key = buildIndexKey(entity.getAccountName(), entity.getSecurityCode(), entity.getCurveSubCategory());
                discountCurveIndexCache.put(key, entity);
            }
        }

        long buildTime = System.currentTimeMillis() - startTime;
        log.info("构建月度折现曲线索引缓存完成，耗时:{}ms，缓存条目数:{}", buildTime, discountCurveIndexCache.size());
    }

    /**
     * 使用索引缓存快速查找月度折现因子（优化版本）
     *
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param curveSubCategory 曲线细分类
     * @return 匹配的月度折现因子实体
     */
    private AdurMonthlyDiscountFactorWithSpreadEntity findMonthlyDiscountFactorOptimized(
            String accountName, String securityCode, String curveSubCategory) {

        long startTime = System.currentTimeMillis();

        String key = buildIndexKey(accountName, securityCode, curveSubCategory);
        AdurMonthlyDiscountFactorWithSpreadEntity result = discountFactorIndexCache.get(key);

        long searchTime = System.currentTimeMillis() - startTime;
        if (searchTime > 1) { // 只记录耗时超过1ms的查找
            log.debug("索引查找月度折现因子耗时：{}ms，账户：{}，证券：{}，曲线细分类：{}，结果：{}",
                    searchTime, accountName, securityCode, curveSubCategory, result != null ? "找到" : "未找到");
        }

        return result;
    }

    /**
     * 使用索引缓存快速查找月度折现曲线（优化版本）
     *
     * @param accountName 账户名称
     * @param securityCode 证券代码
     * @param curveSubCategory 曲线细分类
     * @return 匹配的月度折现曲线实体
     */
    private AdurMonthlyDiscountCurveWithSpreadEntity findMonthlyDiscountCurveOptimized(
            String accountName, String securityCode, String curveSubCategory) {

        long startTime = System.currentTimeMillis();

        String key = buildIndexKey(accountName, securityCode, curveSubCategory);
        AdurMonthlyDiscountCurveWithSpreadEntity result = discountCurveIndexCache.get(key);

        long searchTime = System.currentTimeMillis() - startTime;
        if (searchTime > 1) { // 只记录耗时超过1ms的查找
            log.debug("索引查找月度折现曲线耗时：{}ms，账户：{}，证券：{}，曲线细分类：{}，结果：{}",
                    searchTime, accountName, securityCode, curveSubCategory, result != null ? "找到" : "未找到");
        }

        return result;
    }
}
