package com.xl.alm.job.cost.mapper;

import com.xl.alm.job.cost.entity.PremiumIncomeDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保费收入明细表 Mapper 接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface PremiumIncomeDetailMapper {

    /**
     * 根据统计期间查询保费收入明细列表
     *
     * @param accountingPeriod 统计期间
     * @return 保费收入明细列表
     */
    List<PremiumIncomeDetailEntity> selectPremiumIncomeDetailByPeriod(@Param("accountingPeriod") String accountingPeriod);

    /**
     * 根据统计期间和产品编码查询保费收入明细列表
     *
     * @param accountingPeriod 统计期间
     * @param productCode 产品编码
     * @return 保费收入明细列表
     */
    List<PremiumIncomeDetailEntity> selectPremiumIncomeDetailByProductCode(
            @Param("accountingPeriod") String accountingPeriod,
            @Param("productCode") String productCode);

    /**
     * 根据统计期间和产品编码汇总保费收入数据
     *
     * @param accountingPeriod 统计期间
     * @param productCode 产品编码
     * @return 汇总的保费收入数据
     */
    PremiumIncomeDetailEntity selectPremiumIncomeSummaryByProductCode(
            @Param("accountingPeriod") String accountingPeriod,
            @Param("productCode") String productCode);
}
