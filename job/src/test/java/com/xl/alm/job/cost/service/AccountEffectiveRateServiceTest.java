package com.xl.alm.job.cost.service;

import com.xl.alm.job.cost.entity.AccountEffectiveRateEntity;
import com.xl.alm.job.cost.mapper.AccountEffectiveRateMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * 分账户有效成本率服务测试类
 * 测试UC0012：计算分账户有效成本率功能
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
public class AccountEffectiveRateServiceTest {

    @Autowired
    private AccountEffectiveRateService accountEffectiveRateService;

    @Autowired
    private AccountEffectiveRateMapper accountEffectiveRateMapper;

    /**
     * 测试计算分账户有效成本率
     * 验证按照设计文档UC0012要求：
     * 1. 按账期、设计类型维度对TB0010表数据进行分组
     * 2. 汇总现金流值集，将同一设计类型的所有产品的现金流值集按对应序号累加
     * 3. 使用内部收益率(IRR)方法计算账户有效成本率
     */
    @Test
    public void testCalculateAccountEffectiveRate() {
        String accountingPeriod = "202412"; // 测试账期

        log.info("开始测试分账户有效成本率计算，账期：{}", accountingPeriod);

        // 执行计算
        boolean result = accountEffectiveRateService.calculateAccountEffectiveRate(accountingPeriod);
        log.info("分账户有效成本率计算结果：{}", result);

        // 验证计算结果
        if (result) {
            List<AccountEffectiveRateEntity> resultList =
                accountEffectiveRateMapper.selectAccountEffectiveRateListByPeriod(accountingPeriod);

            log.info("计算完成，共生成{}条分账户有效成本率记录", resultList.size());

            for (AccountEffectiveRateEntity entity : resultList) {
                log.info("设计类型：{}，有效成本率：{}，现金流数据长度：{}",
                        entity.getDesignType(),
                        entity.getEffectiveCostRate(),
                        entity.getCashFlowSet() != null ? entity.getCashFlowSet().length() : 0);
            }
        }
    }


}
