package com.xl.alm.job.liab;

import com.xl.alm.job.liab.processor.LiabNonLifeScaleSummaryCalculationProcessor;
import com.xl.alm.job.liab.service.LiabNonLifeScaleSummaryCalculationService;
import com.xl.alm.job.liab.task.LiabNonLifeScaleSummaryCalculationTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;

/**
 * 非寿险负债规模汇总数据计算功能测试
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class LiabNonLifeScaleSummaryCalculationTest {

    @Autowired
    private LiabNonLifeScaleSummaryCalculationService liabNonLifeScaleSummaryCalculationService;

    @Autowired
    private LiabNonLifeScaleSummaryCalculationTask liabNonLifeScaleSummaryCalculationTask;

    @Autowired
    private LiabNonLifeScaleSummaryCalculationProcessor liabNonLifeScaleSummaryCalculationProcessor;

    /**
     * 测试Service层计算功能
     */
    @Test
    public void testServiceCalculation() {
        log.info("开始测试Service层非寿险负债规模汇总数据计算功能");

        String accountingPeriod = "202506";
        
        try {
            boolean result = liabNonLifeScaleSummaryCalculationService.calculateLiabNonLifeScaleSummary(accountingPeriod);
            log.info("Service层计算结果：{}", result ? "成功" : "失败");
            
            // 验证结果
            assert result : "Service层计算应该成功";
            
        } catch (Exception e) {
            log.error("Service层计算异常", e);
            throw e;
        }
    }

    /**
     * 测试Task层执行功能
     */
    @Test
    public void testTaskExecution() {
        log.info("开始测试Task层非寿险负债规模汇总数据计算功能");

        String accountingPeriod = "202506";
        
        try {
            // 测试execute方法
            liabNonLifeScaleSummaryCalculationTask.execute(accountingPeriod);
            log.info("Task层execute方法执行完成");
            
            // 测试executeWithPeriod方法
            boolean result = liabNonLifeScaleSummaryCalculationTask.executeWithPeriod(accountingPeriod);
            log.info("Task层executeWithPeriod方法执行结果：{}", result ? "成功" : "失败");
            
        } catch (Exception e) {
            log.error("Task层执行异常", e);
            throw e;
        }
    }

    /**
     * 测试Processor层处理功能
     */
    @Test
    public void testProcessorExecution() {
        log.info("开始测试Processor层非寿险负债规模汇总数据计算功能");

        // 准备任务上下文
        TaskContext taskContext = new TaskContext();
        taskContext.setJobParams("{\"accountingPeriod\":\"202506\"}");
        
        try {
            ProcessResult result = liabNonLifeScaleSummaryCalculationProcessor.process(taskContext);
            log.info("Processor层处理结果：{}", result);
            
            // 验证结果
            assert result != null : "Processor处理结果不能为空";
            assert result.isSuccess() : "Processor处理应该成功";
            
        } catch (Exception e) {
            log.error("Processor层处理异常", e);
            throw e;
        }
    }

    /**
     * 测试参数校验功能
     */
    @Test
    public void testParameterValidation() {
        log.info("开始测试参数校验功能");

        // 测试空参数
        try {
            TaskContext emptyContext = new TaskContext();
            emptyContext.setJobParams("{}");
            ProcessResult result = liabNonLifeScaleSummaryCalculationProcessor.process(emptyContext);
            assert !result.isSuccess() : "空参数应该返回错误";
            log.info("空参数校验通过：{}", result.getMsg());
        } catch (Exception e) {
            log.error("空参数测试异常", e);
        }

        // 测试无效账期格式
        try {
            TaskContext invalidContext = new TaskContext();
            invalidContext.setJobParams("{\"accountingPeriod\":\"2025\"}");
            ProcessResult result = liabNonLifeScaleSummaryCalculationProcessor.process(invalidContext);
            assert !result.isSuccess() : "无效账期格式应该返回错误";
            log.info("无效账期格式校验通过：{}", result.getMsg());
        } catch (Exception e) {
            log.error("无效账期格式测试异常", e);
        }

        // 测试非数字账期
        try {
            TaskContext invalidContext = new TaskContext();
            invalidContext.setJobParams("{\"accountingPeriod\":\"20250A\"}");
            ProcessResult result = liabNonLifeScaleSummaryCalculationProcessor.process(invalidContext);
            assert !result.isSuccess() : "非数字账期应该返回错误";
            log.info("非数字账期校验通过：{}", result.getMsg());
        } catch (Exception e) {
            log.error("非数字账期测试异常", e);
        }
    }

    /**
     * 测试差额计算逻辑
     */
    @Test
    public void testDifferenceCalculation() {
        log.info("开始测试差额计算逻辑");

        String accountingPeriod = "202506";
        
        try {
            // 执行计算，验证短期健康险的差额计算逻辑
            boolean result = liabNonLifeScaleSummaryCalculationService.calculateLiabNonLifeScaleSummary(accountingPeriod);
            
            log.info("差额计算测试结果：{}", result ? "成功" : "失败");
            
            // 这里可以添加更详细的验证逻辑，比如：
            // 1. 验证短期健康险的数据是否等于普通账户总额减去短期寿险和短期意外险
            // 2. 验证各个险种的数据是否合理
            
        } catch (Exception e) {
            log.error("差额计算测试异常", e);
            throw e;
        }
    }

    /**
     * 性能测试
     */
    @Test
    public void testPerformance() {
        log.info("开始性能测试");

        String accountingPeriod = "202506";
        TaskContext taskContext = new TaskContext();
        taskContext.setJobParams("{\"accountingPeriod\":\"" + accountingPeriod + "\"}");

        long startTime = System.currentTimeMillis();
        
        try {
            ProcessResult result = liabNonLifeScaleSummaryCalculationProcessor.process(taskContext);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("性能测试结果：");
            log.info("- 执行结果：{}", result.isSuccess() ? "成功" : "失败");
            log.info("- 执行耗时：{}ms", duration);
            log.info("- 结果信息：{}", result.getMsg());
            
            // 性能要求：执行时间不超过30秒
            assert duration < 30000 : "执行时间不应超过30秒";
            
        } catch (Exception e) {
            log.error("性能测试异常", e);
            throw e;
        }
    }
}
