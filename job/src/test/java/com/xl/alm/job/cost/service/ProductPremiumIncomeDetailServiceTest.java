package com.xl.alm.job.cost.service;

import com.xl.alm.job.cost.entity.ProductPremiumIncomeDetailEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分产品保费收入服务测试类
 * 
 * 功能描述：
 * 1. 测试分产品保费收入计算逻辑
 * 2. 测试数据查询功能
 * 3. 测试数据清理功能
 * 4. 测试业务逻辑正确性
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
//@Transactional
//@Rollback
public class ProductPremiumIncomeDetailServiceTest {

    @Autowired
    private ProductPremiumIncomeDetailService productPremiumIncomeDetailService;

    private static final String TEST_ACCOUNTING_PERIOD = "202412";
    private static final String TEST_OPERATOR = "testUser";


    /**
     * 测试分产品保费收入计算功能
     * 使用真实数据库数据进行计算
     */
    @Test
    public void testCalculateProductPremiumIncomeDetail() {
        log.info("开始测试分产品保费收入计算功能");

        try {
            // 执行计算
            long startTime = System.currentTimeMillis();
            boolean result = productPremiumIncomeDetailService.calculateProductPremiumIncomeDetail(
                    TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);
            long endTime = System.currentTimeMillis();

            // 验证计算结果
          //  Assertions.assertTrue(result, "分产品保费收入计算应该成功");

            // 查询计算结果
            List<ProductPremiumIncomeDetailEntity> resultList =
                productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);

            log.info("分产品保费收入计算完成，期间：{}，计算结果数量：{}，耗时：{}毫秒",
                    TEST_ACCOUNTING_PERIOD, resultList.size(), (endTime - startTime));

            // 验证数据结构和完整性
            /*if (!resultList.isEmpty()) {
                validateCalculationResults(resultList);
            }*/

        } catch (Exception e) {
            log.error("分产品保费收入计算测试异常", e);
            Assertions.fail("计算过程发生异常：" + e.getMessage());
        }
    }

    /**
     * 测试重复执行计算功能（验证数据清理是否正常）
     */
    @Test
    public void testRepeatedCalculation() {
        log.info("开始测试重复执行计算功能");

        try {
            // 第一次执行计算
            log.info("执行第一次计算");
            boolean result1 = productPremiumIncomeDetailService.calculateProductPremiumIncomeDetail(
                    TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);

            List<ProductPremiumIncomeDetailEntity> resultList1 =
                productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
            log.info("第一次计算完成，结果数量：{}", resultList1.size());

            // 第二次执行计算（测试重跑任务）
            log.info("执行第二次计算（重跑任务）");
            boolean result2 = productPremiumIncomeDetailService.calculateProductPremiumIncomeDetail(
                    TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);

            List<ProductPremiumIncomeDetailEntity> resultList2 =
                productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
            log.info("第二次计算完成，结果数量：{}", resultList2.size());

            // 验证两次计算结果数量应该一致（说明数据清理正常）
            Assertions.assertEquals(resultList1.size(), resultList2.size(),
                "重跑任务后数据数量应该一致，说明数据清理正常");

            log.info("重复执行计算测试通过，数据清理功能正常");

        } catch (Exception e) {
            log.error("重复执行计算测试异常", e);
            Assertions.fail("重复执行计算测试发生异常：" + e.getMessage());
        }
    }

    /**
     * 测试物理删除和逻辑删除功能
     */
    @Test
    public void testDeleteMethods() {
        log.info("开始测试删除方法功能");

        try {
            // 先执行一次计算，确保有数据
            productPremiumIncomeDetailService.calculateProductPremiumIncomeDetail(
                    TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);

            List<ProductPremiumIncomeDetailEntity> beforeDelete =
                productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
            log.info("删除前数据数量：{}", beforeDelete.size());

            if (beforeDelete.size() > 0) {
                // 测试逻辑删除
                int logicalDeleteCount = productPremiumIncomeDetailService.logicalDeleteProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
                log.info("逻辑删除记录数：{}", logicalDeleteCount);

                List<ProductPremiumIncomeDetailEntity> afterLogicalDelete =
                    productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
                log.info("逻辑删除后查询到的数据数量：{}", afterLogicalDelete.size());

                // 重新插入数据
                productPremiumIncomeDetailService.calculateProductPremiumIncomeDetail(
                        TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);

                // 测试物理删除
                int physicalDeleteCount = productPremiumIncomeDetailService.physicalDeleteProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
                log.info("物理删除记录数：{}", physicalDeleteCount);

                List<ProductPremiumIncomeDetailEntity> afterPhysicalDelete =
                    productPremiumIncomeDetailService.selectProductPremiumIncomeDetailByPeriod(TEST_ACCOUNTING_PERIOD);
                log.info("物理删除后查询到的数据数量：{}", afterPhysicalDelete.size());

                Assertions.assertEquals(0, afterPhysicalDelete.size(), "物理删除后应该没有数据");
            }

            log.info("删除方法测试完成");

        } catch (Exception e) {
            log.error("删除方法测试异常", e);
            Assertions.fail("删除方法测试发生异常：" + e.getMessage());
        }
    }


}
