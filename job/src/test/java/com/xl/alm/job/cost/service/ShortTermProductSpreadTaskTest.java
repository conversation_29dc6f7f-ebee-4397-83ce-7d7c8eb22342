package com.xl.alm.job.cost.service;

import com.xl.alm.job.cost.entity.*;
import com.xl.alm.job.cost.mapper.*;
import com.xl.alm.job.cost.task.ShortTermProductSpreadTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 中短存续期产品利差计算任务测试类
 * 
 * 测试UC0016：计算中短存续期产品利差表
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
//@ActiveProfiles("dev")
//@Transactional
public class ShortTermProductSpreadTaskTest {

    @Autowired
    private ShortTermProductSpreadTask shortTermProductSpreadTask;

    @Autowired
    private ShortTermProductSpreadService shortTermProductSpreadService;

    @Autowired
    private ProductAttributeMapper productAttributeMapper;

    @Autowired
    private ProductPremiumIncomeDetailMapper productPremiumIncomeDetailMapper;

    @Autowired
    private SubAccountYieldRateMapper subAccountYieldRateMapper;

    @Autowired
    private AccountingReserveDetailMapper accountingReserveDetailMapper;

    @Autowired
    private ProductStatisticsMapper productStatisticsMapper;

    @Autowired
    private ProductEffectiveRateMapper productEffectiveRateMapper;

    private static final String TEST_ACCOUNTING_PERIOD = "202412";
    private static final String TEST_OPERATOR = "testUser";



    /**
     * 测试中短存续期产品利差计算任务执行
     */
    @Test
    void testExecute() {
        log.info("开始测试中短存续期产品利差计算任务执行");

        // 执行计算任务
        boolean result = shortTermProductSpreadTask.execute(TEST_ACCOUNTING_PERIOD, TEST_OPERATOR);

        // 验证执行结果
        assertTrue(result, "中短存续期产品利差计算任务应该执行成功");

        // 验证计算结果
        List<ShortTermProductSpreadEntity> resultList = shortTermProductSpreadService
                .selectShortTermProductSpreadByPeriod(TEST_ACCOUNTING_PERIOD);

        log.info("中短存续期产品利差计算任务执行测试完成，计算结果数量：{}", resultList.size());
    }


}
