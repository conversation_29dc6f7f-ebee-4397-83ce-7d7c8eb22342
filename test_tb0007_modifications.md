# TB0007 月度折现因子表含价差修改测试 - 修正版

## 问题说明
之前修改了错误的文件。正确的TB0007对应的URL是：
`http://localhost:8899/#/adur/adurmonthlydiscountfactor`

对应的文件是：
- 前端：`web/src/views/adur/monthly/discount/factor/index.vue`
- 后端：`app/src/main/java/com/xl/alm/app/controller/MonthlyDiscountFactorController.java`

## 修改内容总结

### 1. 移除导入功能
- ✅ **前端修改**：
  - 移除了导入按钮
  - 移除了导入对话框HTML
  - 移除了upload配置对象
  - 移除了导入相关的JavaScript方法：`handleImport()`, `importTemplate()`, `handleFileUploadProgress()`, `handleFileSuccess()`, `submitFileForm()`
  - 移除了`getToken`的导入

- ✅ **后端修改**：
  - 移除了Controller中的导入相关方法：`importTemplate()`, `importData()`
  - 移除了Service接口中的导入方法：`importMonthlyDiscountFactorDto()`
  - 移除了Service实现类中的导入方法实现
  - 移除了不再使用的import语句：`import org.springframework.web.multipart.MultipartFile;`

### 2. 价差类型列显示
- ✅ 前端表格中已经存在价差类型列，无需添加

### 3. 完善导出Excel字典翻译
- ✅ 在Controller的导出方法中添加了字典转换调用
- ✅ 添加了`convertDictValueToLabel()`方法，包含所有字典字段转换：
  - 久期类型 (adur_duration_type)
  - 基点类型 (adur_basis_point_type)
  - 日期类型 (adur_date_type)
  - 价差类型 (adur_spread_type)
  - 账户名称 (adur_account_name)

## 测试验证点

### 前端测试
1. 访问TB0007页面，确认：
   - 表格中显示价差类型列
   - 价差类型列正确显示字典翻译
   - 没有导入按钮
   - 导出按钮正常工作

### 后端测试
1. 确认Controller编译无错误
2. 确认Service接口和实现类编译无错误
3. 测试导出功能，验证Excel中字典值已转换为中文标签

### API测试
1. 测试导出接口：`POST /adur/monthly/discount/factor/with/spread/export`
2. 确认导入相关接口已移除：
   - `/adur/monthly/discount/factor/with/spread/importTemplate` (应返回404)
   - `/adur/monthly/discount/factor/with/spread/importData` (应返回404)

## 字典配置确认
确认以下字典类型在系统中已配置：
- adur_duration_type (久期类型)
- adur_basis_point_type (基点类型)
- adur_date_type (日期类型)
- adur_spread_type (价差类型)
- adur_account_name (账户名称)
- adur_curve_sub_category (曲线细分类)

## 修改文件清单
1. `web/src/views/adur/monthly/discount/factor/index.vue` - 移除导入按钮和相关功能
2. `app/src/main/java/com/xl/alm/app/controller/MonthlyDiscountFactorController.java` - 移除导入方法，添加字典转换
3. `app/src/main/java/com/xl/alm/app/service/MonthlyDiscountFactorService.java` - 移除导入方法接口
4. `app/src/main/java/com/xl/alm/app/service/impl/MonthlyDiscountFactorServiceImpl.java` - 移除导入方法实现

## 测试步骤

### 1. 前端测试
1. 访问 `http://localhost:8899/#/adur/adurmonthlydiscountfactor`
2. 确认页面中没有导入按钮
3. 确认表格中显示价差类型列（已存在）

### 2. 字典转换测试
1. 访问测试接口：`http://localhost:8095/adur/monthly/discount/factor/testDictConversion`
2. 查看控制台日志，确认字典转换是否正常工作
3. 如果字典转换失败，请执行 `check_dict_data.sql` 中的查询语句检查字典数据

### 3. 导出测试
1. 在页面中点击导出按钮
2. 或直接访问：`http://localhost:8095/adur/monthly/discount/factor/export`
3. 确认Excel中字典值已转换为中文标签

### 4. 数据库检查
如果导出时字典值没有转换，请执行以下步骤：
1. 运行 `check_dict_data.sql` 中的查询语句
2. 确认字典类型和字典数据是否存在
3. 确认表中的实际数据值与字典值是否匹配
4. 如果字典数据不存在，执行 `check_dict_data.sql` 中的插入语句

## 可能的问题和解决方案

### 问题1：字典数据不存在
**解决方案**：执行 `docs/sql/adur_program_dict.sql` 文件中的字典数据插入语句

### 问题2：表中数据值与字典值不匹配
**解决方案**：检查表中的实际数据，如果是中文值需要转换为字典值，或者修改字典配置

### 问题3：字典转换工具类不工作
**解决方案**：检查 `DictConvertUtil` 类和 `ISysDictTypeService` 服务是否正常工作

## 清理工作
测试完成后，请删除测试方法：
- 从 `MonthlyDiscountFactorController` 中删除 `testDictConversion()` 方法
