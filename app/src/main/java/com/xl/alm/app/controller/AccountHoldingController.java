package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AccountHoldingDTO;
import com.xl.alm.app.dto.AccountHoldingImportDTO;
import com.xl.alm.app.query.AccountHoldingQuery;
import com.xl.alm.app.service.AccountHoldingService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * TB0001-组合持仓表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/account/holding")
public class AccountHoldingController extends BaseController {

    @Autowired
    private AccountHoldingService accountHoldingService;

    /**
     * 查询组合持仓列表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:list')")
    @GetMapping("/list")
    public TableDataInfo list(AccountHoldingQuery accountHoldingQuery) {
        startPage();
        List<AccountHoldingDTO> list = accountHoldingService.selectAccountHoldingDtoList(accountHoldingQuery);
        return getDataTable(list);
    }

    /**
     * 导出组合持仓列表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:export')")
    @Log(title = "组合持仓", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountHoldingQuery accountHoldingQuery) {
        List<AccountHoldingDTO> list = accountHoldingService.selectAccountHoldingDtoList(accountHoldingQuery);
        ExcelUtil<AccountHoldingDTO> util = new ExcelUtil<>(AccountHoldingDTO.class);
        util.exportExcel(list, "组合持仓数据", response);
    }

    /**
     * 获取组合持仓详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(accountHoldingService.selectAccountHoldingDtoById(id));
    }

    /**
     * 新增组合持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:add')")
    @Log(title = "组合持仓", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody AccountHoldingDTO accountHoldingDTO) {
        return toAjax(accountHoldingService.insertAccountHoldingDto(accountHoldingDTO));
    }

    /**
     * 修改组合持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:edit')")
    @Log(title = "组合持仓", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody AccountHoldingDTO accountHoldingDTO) {
        return toAjax(accountHoldingService.updateAccountHoldingDto(accountHoldingDTO));
    }

    /**
     * 删除组合持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:remove')")
    @Log(title = "组合持仓", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(accountHoldingService.deleteAccountHoldingDtoByIds(ids));
    }

    /**
     * 获取组合持仓导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AccountHoldingImportDTO> util = new ExcelUtil<>(AccountHoldingImportDTO.class);
        util.exportTemplateExcel(response, "组合持仓");
    }

    /**
     * 导入组合持仓数据
     * @param file 上传的Excel文件
     * @param updateSupport 是否支持更新
     * @param accountingPeriod 账期，格式YYYYMM（如202406）
     */
    @PreAuthorize("@ss.hasPermi('ast:account:holding:import')")
    @Log(title = "组合持仓", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport, String accountingPeriod) throws Exception {
        // 验证账期格式
        if (StringUtils.isEmpty(accountingPeriod) || !accountingPeriod.matches("^\\d{6}$")) {
            return Result.error("账期格式错误，请输入YYYYMM格式（如202406）");
        }

        ExcelUtil<AccountHoldingImportDTO> util = new ExcelUtil<>(AccountHoldingImportDTO.class);
        List<AccountHoldingImportDTO> importList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = accountHoldingService.importAccountHoldingDto(importList, updateSupport, operName, accountingPeriod);
        return Result.success(message);
    }
}
