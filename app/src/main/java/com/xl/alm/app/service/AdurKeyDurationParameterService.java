package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationParameterDTO;
import com.xl.alm.app.query.AdurKeyDurationParameterQuery;

import java.util.List;

/**
 * ADUR关键久期参数表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurKeyDurationParameterService {

    /**
     * 查询ADUR关键久期参数列表
     *
     * @param adurKeyDurationParameterQuery ADUR关键久期参数查询条件
     * @return ADUR关键久期参数列表
     */
    List<AdurKeyDurationParameterDTO> selectAdurKeyDurationParameterDtoList(AdurKeyDurationParameterQuery adurKeyDurationParameterQuery);

    /**
     * 用id查询ADUR关键久期参数
     *
     * @param id id
     * @return ADUR关键久期参数
     */
    AdurKeyDurationParameterDTO selectAdurKeyDurationParameterDtoById(Long id);

    /**
     * 根据账期和关键期限点查询ADUR关键久期参数
     *
     * @param accountPeriod 账期
     * @param keyDuration 关键期限点
     * @return ADUR关键久期参数
     */
    AdurKeyDurationParameterDTO selectAdurKeyDurationParameterDtoByCondition(String accountPeriod, String keyDuration);

    /**
     * 新增ADUR关键久期参数
     *
     * @param adurKeyDurationParameterDTO ADUR关键久期参数
     * @return 结果
     */
    int insertAdurKeyDurationParameterDto(AdurKeyDurationParameterDTO adurKeyDurationParameterDTO);

    /**
     * 批量插入ADUR关键久期参数数据
     *
     * @param adurKeyDurationParameterDtoList ADUR关键久期参数列表
     * @return 影响行数
     */
    int batchInsertAdurKeyDurationParameterDto(List<AdurKeyDurationParameterDTO> adurKeyDurationParameterDtoList);

    /**
     * 更新ADUR关键久期参数数据
     *
     * @param dto ADUR关键久期参数
     * @return 结果
     */
    int updateAdurKeyDurationParameterDto(AdurKeyDurationParameterDTO dto);

    /**
     * 删除指定id的ADUR关键久期参数数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurKeyDurationParameterDtoById(Long id);

    /**
     * 批量删除ADUR关键久期参数
     *
     * @param ids 需要删除的ADUR关键久期参数主键
     * @return 结果
     */
    int deleteAdurKeyDurationParameterDtoByIds(Long[] ids);

    /**
     * 导入ADUR关键久期参数
     *
     * @param dtoList       ADUR关键久期参数数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAdurKeyDurationParameterDto(List<AdurKeyDurationParameterDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 从老表迁移数据到新表
     *
     * @param username 操作用户
     * @return 迁移结果
     */
    String migrateDataFromOldTable(String username);

    /**
     * 分析老表数据（调试用）
     *
     * @return 分析结果
     */
    String analyzeOldTableData();

    /**
     * 根据账期删除ADUR关键久期参数
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurKeyDurationParameterDtoByAccountPeriod(String accountPeriod);
}
