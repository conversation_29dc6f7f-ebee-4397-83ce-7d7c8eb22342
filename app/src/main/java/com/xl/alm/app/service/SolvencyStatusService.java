package com.xl.alm.app.service;

import com.xl.alm.app.dto.SolvencyStatusDTO;
import com.xl.alm.app.query.SolvencyStatusQuery;

import java.util.List;

/**
 * 偿付能力状况表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface SolvencyStatusService {

    /**
     * 查询偿付能力状况表列表
     *
     * @param solvencyStatusQuery 偿付能力状况表查询条件
     * @return 偿付能力状况表列表
     */
    List<SolvencyStatusDTO> selectSolvencyStatusDtoList(SolvencyStatusQuery solvencyStatusQuery);

    /**
     * 用id查询偿付能力状况表
     *
     * @param id id
     * @return 偿付能力状况表
     */
    SolvencyStatusDTO selectSolvencyStatusDtoById(Long id);

    /**
     * 根据账期和项目名称查询偿付能力状况表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return 偿付能力状况表
     */
    SolvencyStatusDTO selectSolvencyStatusDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增偿付能力状况表
     *
     * @param solvencyStatusDTO 偿付能力状况表
     * @return 结果
     */
    int insertSolvencyStatusDto(SolvencyStatusDTO solvencyStatusDTO);

    /**
     * 批量新增偿付能力状况表
     *
     * @param solvencyStatusDtoList 偿付能力状况表列表
     * @return 影响行数
     */
    int batchInsertSolvencyStatusDto(List<SolvencyStatusDTO> solvencyStatusDtoList);

    /**
     * 更新偿付能力状况表数据
     *
     * @param solvencyStatusDTO 偿付能力状况表
     * @return 结果
     */
    int updateSolvencyStatusDto(SolvencyStatusDTO solvencyStatusDTO);

    /**
     * 删除指定id的偿付能力状况表数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteSolvencyStatusDtoById(Long id);

    /**
     * 批量删除偿付能力状况表
     *
     * @param ids 需要删除的偿付能力状况表主键集合
     * @return 结果
     */
    int deleteSolvencyStatusDtoByIds(Long[] ids);

    /**
     * 删除指定账期的偿付能力状况表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteSolvencyStatusDtoByPeriod(String accountingPeriod);

    /**
     * 导入偿付能力状况表
     *
     * @param dtoList       偿付能力状况表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importSolvencyStatusDto(List<SolvencyStatusDTO> dtoList, Boolean updateSupport, String username);
}
