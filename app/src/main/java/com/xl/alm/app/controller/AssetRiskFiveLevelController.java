package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.AssetRiskFiveLevelDTO;
import com.xl.alm.app.query.AssetRiskFiveLevelQuery;
import com.xl.alm.app.service.AssetRiskFiveLevelService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 保险资产风险五级分类状况表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/asset/risk/five/level")
public class AssetRiskFiveLevelController extends BaseController {
    @Autowired
    private AssetRiskFiveLevelService assetRiskFiveLevelService;

    /**
     * 查询保险资产风险五级分类状况表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetRiskFiveLevelQuery query) {
        startPage();
        List<AssetRiskFiveLevelDTO> list = assetRiskFiveLevelService.selectAssetRiskFiveLevelDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出保险资产风险五级分类状况表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:export')")
    @Log(title = "保险资产风险五级分类状况表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetRiskFiveLevelQuery query) {
        List<AssetRiskFiveLevelDTO> list = assetRiskFiveLevelService.selectAssetRiskFiveLevelDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        ExcelUtil<AssetRiskFiveLevelDTO> util = new ExcelUtil<AssetRiskFiveLevelDTO>(AssetRiskFiveLevelDTO.class);
        util.exportExcel(list, "保险资产风险五级分类状况表数据", response);
    }

    /**
     * 获取保险资产风险五级分类状况表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetRiskFiveLevelService.selectAssetRiskFiveLevelDtoById(id));
    }

    /**
     * 新增保险资产风险五级分类状况表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:add')")
    @Log(title = "保险资产风险五级分类状况表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AssetRiskFiveLevelDTO assetRiskFiveLevelDto) {
        return toAjax(assetRiskFiveLevelService.addAssetRiskFiveLevelDto(assetRiskFiveLevelDto));
    }

    /**
     * 修改保险资产风险五级分类状况表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:edit')")
    @Log(title = "保险资产风险五级分类状况表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AssetRiskFiveLevelDTO assetRiskFiveLevelDto) {
        return toAjax(assetRiskFiveLevelService.updateAssetRiskFiveLevelDto(assetRiskFiveLevelDto));
    }

    /**
     * 删除保险资产风险五级分类状况表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:remove')")
    @Log(title = "保险资产风险五级分类状况表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetRiskFiveLevelService.deleteAssetRiskFiveLevelDtoByIds(ids));
    }

    /**
     * 获取保险资产风险五级分类状况表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetRiskFiveLevelDTO> util = new ExcelUtil<AssetRiskFiveLevelDTO>(AssetRiskFiveLevelDTO.class);
        util.exportTemplateExcel(response, "保险资产风险五级分类状况表");
    }

    /**
     * 导入保险资产风险五级分类状况表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:five:level:import')")
    @Log(title = "保险资产风险五级分类状况表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetRiskFiveLevelDTO> util = new ExcelUtil<AssetRiskFiveLevelDTO>(AssetRiskFiveLevelDTO.class);
        List<AssetRiskFiveLevelDTO> assetRiskFiveLevelList = util.importExcel(file.getInputStream());
        // 转换字典标签为值用于导入
        convertDictLabelToValue(assetRiskFiveLevelList);
        String operName = getUsername();
        String message = assetRiskFiveLevelService.importAssetRiskFiveLevelDto(assetRiskFiveLevelList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 保险资产风险五级分类状况表数据列表
     */
    private void convertDictValueToLabel(List<AssetRiskFiveLevelDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetRiskFiveLevelDTO dto : list) {
            // 转换项目名称字典值
            if (dto.getItemName() != null) {
                dto.setItemName(DictConvertUtil.convertValueToLabel(dto.getItemName(), "acm_risk_item_name"));
            }
            // 转换五级分类资产统计标识字典值
            if (dto.getFiveLevelStatisticsFlag() != null) {
                dto.setFiveLevelStatisticsFlag(DictConvertUtil.convertValueToLabel(dto.getFiveLevelStatisticsFlag(), "ast_five_level_statistics_flag"));
            }
            // 转换五级分类字典值
            if (dto.getFiveLevelClassification() != null) {
                dto.setFiveLevelClassification(DictConvertUtil.convertValueToLabel(dto.getFiveLevelClassification(), "ast_five_level_classification"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 保险资产风险五级分类状况表数据列表
     */
    private void convertDictLabelToValue(List<AssetRiskFiveLevelDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetRiskFiveLevelDTO dto : list) {
            // 转换项目名称字典标签
            if (dto.getItemName() != null) {
                dto.setItemName(DictConvertUtil.convertLabelToValue(dto.getItemName(), "acm_risk_item_name"));
            }
            // 转换五级分类资产统计标识字典标签
            if (dto.getFiveLevelStatisticsFlag() != null) {
                dto.setFiveLevelStatisticsFlag(DictConvertUtil.convertLabelToValue(dto.getFiveLevelStatisticsFlag(), "ast_five_level_statistics_flag"));
            }
            // 转换五级分类字典标签
            if (dto.getFiveLevelClassification() != null) {
                dto.setFiveLevelClassification(DictConvertUtil.convertLabelToValue(dto.getFiveLevelClassification(), "ast_five_level_classification"));
            }
        }
    }
}
