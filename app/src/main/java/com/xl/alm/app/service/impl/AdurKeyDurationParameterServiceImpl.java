package com.xl.alm.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationParameterDTO;
import com.xl.alm.app.entity.AdurKeyDurationParameterEntity;
import com.xl.alm.app.entity.KeyDurationParameterEntity;
import com.xl.alm.app.mapper.AdurKeyDurationParameterMapper;
import com.xl.alm.app.query.AdurKeyDurationParameterQuery;
import com.xl.alm.app.service.AdurKeyDurationParameterService;
import com.xl.alm.app.util.AdurDataMigrationUtil;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期参数表 Service 业务层处理
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationParameterServiceImpl implements AdurKeyDurationParameterService {
    @Autowired
    private AdurKeyDurationParameterMapper adurKeyDurationParameterMapper;

    /**
     * 查询ADUR关键久期参数列表
     *
     * @param adurKeyDurationParameterQuery ADUR关键久期参数查询条件
     * @return ADUR关键久期参数列表
     */
    @Override
    public List<AdurKeyDurationParameterDTO> selectAdurKeyDurationParameterDtoList(AdurKeyDurationParameterQuery adurKeyDurationParameterQuery) {
        List<AdurKeyDurationParameterEntity> entityList = adurKeyDurationParameterMapper.selectAdurKeyDurationParameterEntityList(adurKeyDurationParameterQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        List<AdurKeyDurationParameterDTO> dtoList = new ArrayList<>();
        for (AdurKeyDurationParameterEntity entity : entityList) {
            dtoList.add(EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationParameterDTO.class));
        }
        return dtoList;
    }

    /**
     * 用id查询ADUR关键久期参数
     *
     * @param id id
     * @return ADUR关键久期参数
     */
    @Override
    public AdurKeyDurationParameterDTO selectAdurKeyDurationParameterDtoById(Long id) {
        AdurKeyDurationParameterEntity entity = adurKeyDurationParameterMapper.selectAdurKeyDurationParameterEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationParameterDTO.class);
    }

    /**
     * 根据账期和关键期限点查询ADUR关键久期参数
     *
     * @param accountPeriod 账期
     * @param keyDuration 关键期限点
     * @return ADUR关键久期参数
     */
    @Override
    public AdurKeyDurationParameterDTO selectAdurKeyDurationParameterDtoByCondition(String accountPeriod, String keyDuration) {
        AdurKeyDurationParameterEntity entity = adurKeyDurationParameterMapper.selectAdurKeyDurationParameterEntityByCondition(accountPeriod, keyDuration);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationParameterDTO.class);
    }

    /**
     * 新增ADUR关键久期参数
     *
     * @param adurKeyDurationParameterDTO ADUR关键久期参数
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationParameterDto(AdurKeyDurationParameterDTO adurKeyDurationParameterDTO) {
        AdurKeyDurationParameterEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationParameterDTO, AdurKeyDurationParameterEntity.class);
        return adurKeyDurationParameterMapper.insertAdurKeyDurationParameterEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期参数数据
     *
     * @param adurKeyDurationParameterDtoList ADUR关键久期参数列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationParameterDto(List<AdurKeyDurationParameterDTO> adurKeyDurationParameterDtoList) {
        if (adurKeyDurationParameterDtoList == null || adurKeyDurationParameterDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationParameterEntity> entityList = new ArrayList<>();
        for (AdurKeyDurationParameterDTO dto : adurKeyDurationParameterDtoList) {
            entityList.add(EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationParameterEntity.class));
        }
        return adurKeyDurationParameterMapper.batchInsertAdurKeyDurationParameterEntity(entityList);
    }

    /**
     * 更新ADUR关键久期参数数据
     *
     * @param dto ADUR关键久期参数
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationParameterDto(AdurKeyDurationParameterDTO dto) {
        AdurKeyDurationParameterEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationParameterEntity.class);
        return adurKeyDurationParameterMapper.updateAdurKeyDurationParameterEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期参数数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationParameterDtoById(Long id) {
        return adurKeyDurationParameterMapper.deleteAdurKeyDurationParameterEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期参数
     *
     * @param ids 需要删除的ADUR关键久期参数主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationParameterDtoByIds(Long[] ids) {
        return adurKeyDurationParameterMapper.deleteAdurKeyDurationParameterEntityByIds(ids);
    }

    /**
     * 导入ADUR关键久期参数
     *
     * @param dtoList       ADUR关键久期参数数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurKeyDurationParameterDto(List<AdurKeyDurationParameterDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.isEmpty()) {
            throw new RuntimeException("导入ADUR关键久期参数数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurKeyDurationParameterDTO dto : dtoList) {
            try {
                // 验证是否存在这个ADUR关键久期参数
                AdurKeyDurationParameterDTO existDto = this.selectAdurKeyDurationParameterDtoByCondition(dto.getAccountPeriod(), dto.getKeyDuration());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    dto.setUpdateBy(username);
                    this.insertAdurKeyDurationParameterDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountPeriod())
                            .append(" 关键期限点 ").append(dto.getKeyDuration()).append(" 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    dto.setUpdateBy(username);
                    this.updateAdurKeyDurationParameterDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountPeriod())
                            .append(" 关键期限点 ").append(dto.getKeyDuration()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期 ").append(dto.getAccountPeriod())
                            .append(" 关键期限点 ").append(dto.getKeyDuration()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 关键期限点 " + dto.getKeyDuration() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 从老表迁移数据到新表
     *
     * @param username 操作用户
     * @return 迁移结果
     */
    @Override
    @Transactional
    public String migrateDataFromOldTable(String username) {
        try {
            log.info("开始从老表t_dur_key_duration_parameter迁移数据到新表t_adur_key_duration_parameter");
            
            // 1. 清空新表数据
            adurKeyDurationParameterMapper.truncateAdurKeyDurationParameterEntity();
            log.info("已清空新表数据");
            
            // 2. 从老表查询数据
            List<KeyDurationParameterEntity> oldDataList = adurKeyDurationParameterMapper.selectOldKeyDurationParameterEntityList();
            if (oldDataList == null || oldDataList.isEmpty()) {
                return "老表中没有数据需要迁移";
            }
            log.info("从老表查询到{}条数据", oldDataList.size());
            
            // 3. 处理数据并插入新表
            List<AdurKeyDurationParameterEntity> newDataList = new ArrayList<>();
            int successNum = 0;
            int failureNum = 0;
            StringBuilder failureMsg = new StringBuilder();
            
            for (KeyDurationParameterEntity oldEntity : oldDataList) {
                try {
                    log.info("开始处理关键期限：{}，账期：{}", oldEntity.getKeyDuration(), oldEntity.getAccountPeriod());

                    AdurKeyDurationParameterEntity newEntity = new AdurKeyDurationParameterEntity();
                    newEntity.setAccountPeriod(oldEntity.getAccountPeriod());
                    newEntity.setKeyDuration(oldEntity.getKeyDuration());

                    // 记录原始JSON数据的基本信息
                    String originalJson = oldEntity.getParameterValSet();
                    if (originalJson != null && !originalJson.trim().isEmpty()) {
                        log.info("原始JSON长度：{}，前100字符：{}",
                                originalJson.length(),
                                originalJson.length() > 100 ? originalJson.substring(0, 100) + "..." : originalJson);

                        // 分析原始JSON的键范围
                        try {
                            JSONObject originalJsonObj = JSON.parseObject(originalJson);
                            int totalKeys = originalJsonObj.size();

                            // 统计不同范围的键数量
                            int count0to99 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 0, 99);
                            int count100to199 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 100, 199);
                            int count200to299 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 200, 299);
                            int count300to399 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 300, 399);
                            int count400to499 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 400, 499);
                            int count500to599 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 500, 599);
                            int count600to699 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 600, 699);
                            int count700to799 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 700, 799);
                            int count800to899 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 800, 899);
                            int count900to999 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 900, 999);
                            int count1000to1099 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 1000, 1099);
                            int count1100to1199 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 1100, 1199);
                            int count1200to1273 = AdurDataMigrationUtil.getJsonKeyCountInRange(originalJson, 1200, 1273);

                            log.info("关键期限 {} 原始JSON分析 - 总键数：{}，0-99：{}，100-199：{}，200-299：{}，300-399：{}，400-499：{}，500-599：{}，600-699：{}，700-799：{}，800-899：{}，900-999：{}，1000-1099：{}，1100-1199：{}，1200-1273：{}",
                                    oldEntity.getKeyDuration(), totalKeys, count0to99, count100to199, count200to299,
                                    count300to399, count400to499, count500to599, count600to699, count700to799,
                                    count800to899, count900to999, count1000to1099, count1100to1199, count1200to1273);
                        } catch (Exception e) {
                            log.warn("分析原始JSON失败：{}", e.getMessage());
                        }
                    } else {
                        log.warn("关键期限 {} 的原始JSON为空", oldEntity.getKeyDuration());
                    }

                    // 处理parameter_val_set字段：截取JSON中标识0-600的数据
                    String processedParameterValSet = AdurDataMigrationUtil.processKeyDurationParameterValSet(originalJson);
                    newEntity.setParameterValSet(processedParameterValSet);

                    // 记录处理后的结果
                    if (processedParameterValSet != null && !processedParameterValSet.equals("{}")) {
                        int processedCount = AdurDataMigrationUtil.getJsonKeyCountInRange(processedParameterValSet, 0, 600);
                        log.info("关键期限 {} 处理完成，提取到0-600范围内的键数：{}", oldEntity.getKeyDuration(), processedCount);

                        // 验证JSON顺序
                        String orderReport = AdurDataMigrationUtil.validateJsonOrder(processedParameterValSet);
                        log.debug("关键期限 {} JSON顺序验证：\n{}", oldEntity.getKeyDuration(), orderReport);
                    } else {
                        log.warn("关键期限 {} 处理后为空", oldEntity.getKeyDuration());
                    }

                    newEntity.setCreateBy(username);
                    newEntity.setUpdateBy(username);
                    newEntity.setIsDel(0);

                    newDataList.add(newEntity);
                    successNum++;
                } catch (Exception e) {
                    failureNum++;
                    String msg = "处理关键期限 " + oldEntity.getKeyDuration() + " 数据失败：" + e.getMessage();
                    failureMsg.append("<br/>").append(failureNum).append("、").append(msg);
                    log.error(msg, e);
                }
            }
            
            // 4. 批量插入新表
            if (!newDataList.isEmpty()) {
                adurKeyDurationParameterMapper.batchInsertAdurKeyDurationParameterEntity(newDataList);
                log.info("成功插入{}条数据到新表", newDataList.size());
            }
            
            String result = "数据迁移完成！成功迁移 " + successNum + " 条数据";
            if (failureNum > 0) {
                result += "，失败 " + failureNum + " 条：" + failureMsg.toString();
            }
            
            log.info("数据迁移完成，成功：{}条，失败：{}条", successNum, failureNum);
            return result;
            
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            throw new RuntimeException("数据迁移失败：" + e.getMessage());
        }
    }

    /**
     * 分析老表数据（调试用）
     *
     * @return 分析结果
     */
    @Override
    public String analyzeOldTableData() {
        StringBuilder report = new StringBuilder();
        report.append("=== 老表数据分析报告 ===\n");

        try {
            // 从老表查询数据
            List<KeyDurationParameterEntity> oldDataList = adurKeyDurationParameterMapper.selectOldKeyDurationParameterEntityList();
            if (oldDataList == null || oldDataList.isEmpty()) {
                report.append("老表中没有数据\n");
                return report.toString();
            }

            report.append("老表总记录数：").append(oldDataList.size()).append("\n\n");

            // 分析每条记录的JSON数据
            for (int i = 0; i < Math.min(oldDataList.size(), 5); i++) { // 只分析前5条记录
                KeyDurationParameterEntity entity = oldDataList.get(i);
                report.append("--- 记录 ").append(i + 1).append(" ---\n");
                report.append("账期：").append(entity.getAccountPeriod()).append("\n");
                report.append("关键期限：").append(entity.getKeyDuration()).append("\n");

                String json = entity.getParameterValSet();
                if (json != null && !json.trim().isEmpty()) {
                    try {
                        JSONObject jsonObj = JSONObject.parseObject(json);
                        report.append("JSON总键数：").append(jsonObj.size()).append("\n");

                        // 分析键的范围分布
                        int[] ranges = {0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1273};
                        for (int j = 0; j < ranges.length - 1; j++) {
                            int count = AdurDataMigrationUtil.getJsonKeyCountInRange(json, ranges[j], ranges[j + 1] - 1);
                            if (count > 0) {
                                report.append("  范围 ").append(ranges[j]).append("-").append(ranges[j + 1] - 1)
                                      .append("：").append(count).append(" 项\n");
                            }
                        }

                        // 检查特定键是否存在
                        int[] checkKeys = {0, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1273};
                        StringBuilder keyExists = new StringBuilder("存在的关键键：");
                        for (int key : checkKeys) {
                            if (jsonObj.containsKey(String.valueOf(key))) {
                                keyExists.append(key).append(" ");
                            }
                        }
                        report.append("  ").append(keyExists.toString()).append("\n");

                        // 测试处理后的结果
                        String processed = AdurDataMigrationUtil.processKeyDurationParameterValSet(json);
                        JSONObject processedObj = JSONObject.parseObject(processed);
                        report.append("  处理后键数：").append(processedObj.size()).append("\n");

                        int count0to600 = AdurDataMigrationUtil.getJsonKeyCountInRange(processed, 0, 600);
                        report.append("  0-600范围键数：").append(count0to600).append("\n");

                    } catch (Exception e) {
                        report.append("JSON解析失败：").append(e.getMessage()).append("\n");
                    }
                } else {
                    report.append("JSON为空\n");
                }
                report.append("\n");
            }

            if (oldDataList.size() > 5) {
                report.append("... 还有 ").append(oldDataList.size() - 5).append(" 条记录未显示\n");
            }

        } catch (Exception e) {
            report.append("分析失败：").append(e.getMessage()).append("\n");
            log.error("分析老表数据失败", e);
        }

        return report.toString();
    }

    /**
     * 根据账期删除ADUR关键久期参数
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAdurKeyDurationParameterDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationParameterMapper.deleteAdurKeyDurationParameterEntityByAccountPeriod(accountPeriod);
    }
    

}
