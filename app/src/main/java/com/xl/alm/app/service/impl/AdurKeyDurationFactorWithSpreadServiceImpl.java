package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationFactorWithSpreadDTO;
import com.xl.alm.app.entity.AdurKeyDurationFactorWithSpreadEntity;
import com.xl.alm.app.mapper.AdurKeyDurationFactorWithSpreadMapper;
import com.xl.alm.app.query.AdurKeyDurationFactorWithSpreadQuery;
import com.xl.alm.app.service.AdurKeyDurationFactorWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现因子表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationFactorWithSpreadServiceImpl implements AdurKeyDurationFactorWithSpreadService {

    @Autowired
    private AdurKeyDurationFactorWithSpreadMapper adurKeyDurationFactorWithSpreadMapper;

    /**
     * 查询ADUR关键久期折现因子表含价差列表
     *
     * @param adurKeyDurationFactorWithSpreadQuery ADUR关键久期折现因子表含价差查询条件
     * @return ADUR关键久期折现因子表含价差列表
     */
    @Override
    public List<AdurKeyDurationFactorWithSpreadDTO> selectAdurKeyDurationFactorWithSpreadDtoList(AdurKeyDurationFactorWithSpreadQuery adurKeyDurationFactorWithSpreadQuery) {
        List<AdurKeyDurationFactorWithSpreadEntity> entityList = adurKeyDurationFactorWithSpreadMapper.selectAdurKeyDurationFactorWithSpreadEntityList(adurKeyDurationFactorWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurKeyDurationFactorWithSpreadDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现因子表含价差
     *
     * @param id id
     * @return ADUR关键久期折现因子表含价差
     */
    @Override
    public AdurKeyDurationFactorWithSpreadDTO selectAdurKeyDurationFactorWithSpreadDtoById(Long id) {
        AdurKeyDurationFactorWithSpreadEntity entity = adurKeyDurationFactorWithSpreadMapper.selectAdurKeyDurationFactorWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationFactorWithSpreadDTO.class);
    }

    /**
     * 根据账期、资产编号、关键期限和压力方向查询ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现因子表含价差
     */
    @Override
    public AdurKeyDurationFactorWithSpreadDTO selectAdurKeyDurationFactorWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection) {
        AdurKeyDurationFactorWithSpreadEntity entity = adurKeyDurationFactorWithSpreadMapper.selectAdurKeyDurationFactorWithSpreadEntityByCondition(accountPeriod, assetNumber, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationFactorWithSpreadDTO.class);
    }

    /**
     * 新增ADUR关键久期折现因子表含价差
     *
     * @param adurKeyDurationFactorWithSpreadDTO ADUR关键久期折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationFactorWithSpreadDto(AdurKeyDurationFactorWithSpreadDTO adurKeyDurationFactorWithSpreadDTO) {
        AdurKeyDurationFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationFactorWithSpreadDTO, AdurKeyDurationFactorWithSpreadEntity.class);
        return adurKeyDurationFactorWithSpreadMapper.insertAdurKeyDurationFactorWithSpreadEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现因子表含价差数据
     *
     * @param adurKeyDurationFactorWithSpreadDtoList ADUR关键久期折现因子表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationFactorWithSpreadDto(List<AdurKeyDurationFactorWithSpreadDTO> adurKeyDurationFactorWithSpreadDtoList) {
        if (adurKeyDurationFactorWithSpreadDtoList == null || adurKeyDurationFactorWithSpreadDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationFactorWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurKeyDurationFactorWithSpreadDtoList, AdurKeyDurationFactorWithSpreadEntity.class);
        return adurKeyDurationFactorWithSpreadMapper.batchInsertAdurKeyDurationFactorWithSpreadEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现因子表含价差数据
     *
     * @param dto ADUR关键久期折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationFactorWithSpreadDto(AdurKeyDurationFactorWithSpreadDTO dto) {
        AdurKeyDurationFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationFactorWithSpreadEntity.class);
        return adurKeyDurationFactorWithSpreadMapper.updateAdurKeyDurationFactorWithSpreadEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationFactorWithSpreadDtoById(Long id) {
        return adurKeyDurationFactorWithSpreadMapper.deleteAdurKeyDurationFactorWithSpreadEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现因子表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现因子表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationFactorWithSpreadDtoByIds(Long[] ids) {
        return adurKeyDurationFactorWithSpreadMapper.deleteAdurKeyDurationFactorWithSpreadEntityByIds(ids);
    }

    /**
     * 根据账期删除ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationFactorWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationFactorWithSpreadMapper.deleteAdurKeyDurationFactorWithSpreadEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 导入ADUR关键久期折现因子表含价差
     *
     * @param dtoList       ADUR关键久期折现因子表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurKeyDurationFactorWithSpreadDto(List<AdurKeyDurationFactorWithSpreadDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurKeyDurationFactorWithSpreadDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                AdurKeyDurationFactorWithSpreadDTO existDto = this.selectAdurKeyDurationFactorWithSpreadDtoByCondition(dto.getAccountPeriod(), dto.getAssetNumber(), dto.getKeyTerm(), dto.getStressDirection());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAdurKeyDurationFactorWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 导入成功");
                } else if (updateSupport) {
                    dto.setUpdateBy(username);
                    dto.setId(existDto.getId());
                    this.updateAdurKeyDurationFactorWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 关键期限 " + dto.getKeyTerm() + " 压力方向 " + dto.getStressDirection() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
