package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetDetailOverallDTO;
import com.xl.alm.app.query.AssetDetailOverallQuery;

import java.util.List;

/**
 * 整体资产明细表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
public interface AssetDetailOverallService {

    /**
     * 查询整体资产明细表列表
     *
     * @param query 查询条件
     * @return 整体资产明细表集合
     */
    List<AssetDetailOverallDTO> selectAssetDetailOverallList(AssetDetailOverallQuery query);

    /**
     * 根据主键查询整体资产明细表
     *
     * @param id 主键
     * @return 整体资产明细表
     */
    AssetDetailOverallDTO selectAssetDetailOverallById(Long id);

    /**
     * 新增整体资产明细表
     *
     * @param dto 整体资产明细表
     * @return 结果
     */
    int insertAssetDetailOverall(AssetDetailOverallDTO dto);

    /**
     * 修改整体资产明细表
     *
     * @param dto 整体资产明细表
     * @return 结果
     */
    int updateAssetDetailOverall(AssetDetailOverallDTO dto);

    /**
     * 删除整体资产明细表
     *
     * @param id 主键
     * @return 结果
     */
    int deleteAssetDetailOverallById(Long id);

    /**
     * 批量删除整体资产明细表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAssetDetailOverallByIds(Long[] ids);

    /**
     * 导入整体资产明细表数据
     *
     * @param dtoList 整体资产明细表数据列表
     * @param isUpdateSupport 是否支持更新，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importAssetDetailOverall(List<AssetDetailOverallDTO> dtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导出整体资产明细表数据
     *
     * @param query 查询条件
     * @return 整体资产明细表数据列表
     */
    List<AssetDetailOverallDTO> exportAssetDetailOverall(AssetDetailOverallQuery query);

    /**
     * 检查账期、资产编号、账户名称、证券标识代码组合是否存在
     *
     * @param dto 整体资产明细表
     * @return 结果
     */
    boolean checkUniqueKey(AssetDetailOverallDTO dto);
}
