package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetDefinitionDTO;
import com.xl.alm.app.query.AssetDefinitionQuery;

import java.util.List;

/**
 * 资产定义表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetDefinitionService {

    /**
     * 查询资产定义表列表
     *
     * @param assetDefinitionQuery 资产定义表查询条件
     * @return 资产定义表列表
     */
    List<AssetDefinitionDTO> selectAssetDefinitionDtoList(AssetDefinitionQuery assetDefinitionQuery);

    /**
     * 根据主键查询资产定义表
     *
     * @param id 主键
     * @return 资产定义表
     */
    AssetDefinitionDTO selectAssetDefinitionDtoById(Long id);

    /**
     * 新增资产定义表
     *
     * @param assetDefinitionDTO 资产定义表
     * @return 影响行数
     */
    int insertAssetDefinitionDto(AssetDefinitionDTO assetDefinitionDTO);

    /**
     * 修改资产定义表
     *
     * @param assetDefinitionDTO 资产定义表
     * @return 影响行数
     */
    int updateAssetDefinitionDto(AssetDefinitionDTO assetDefinitionDTO);

    /**
     * 批量删除资产定义表
     *
     * @param ids 需要删除的资产定义表主键集合
     * @return 影响行数
     */
    int deleteAssetDefinitionDtoByIds(Long[] ids);

    /**
     * 删除资产定义表信息
     *
     * @param id 资产定义表主键
     * @return 影响行数
     */
    int deleteAssetDefinitionDtoById(Long id);

    /**
     * 批量新增资产定义表
     *
     * @param assetDefinitionDtoList 资产定义表列表
     * @return 影响行数
     */
    int batchInsertAssetDefinitionDto(List<AssetDefinitionDTO> assetDefinitionDtoList);

    /**
     * 根据账期删除资产定义表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAssetDefinitionDtoByPeriod(String accountingPeriod);

    /**
     * 导入资产定义表数据
     *
     * @param assetDefinitionDtoList 资产定义表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importAssetDefinitionDto(List<AssetDefinitionDTO> assetDefinitionDtoList, Boolean isUpdateSupport, String operName);

    /**
     * 精确匹配查找证券代码
     *
     * @param accountingPeriod 账期
     * @param accountName 账户名称
     * @param assetName 资产名称
     * @return 证券代码
     */
    String findSecurityCodeByExactMatch(String accountingPeriod, String accountName, String assetName);

    /**
     * 模糊匹配查找证券代码
     *
     * @param accountingPeriod 账期
     * @param accountName 账户名称
     * @param cleanAssetName 清理后的资产名称
     * @return 证券代码
     */
    String findSecurityCodeByFuzzyMatch(String accountingPeriod, String accountName, String cleanAssetName);

    /**
     * 根据账期查找所有资产定义
     *
     * @param accountingPeriod 账期
     * @return 资产定义列表
     */
    List<AssetDefinitionDTO> findByAccountingPeriod(String accountingPeriod);
}
