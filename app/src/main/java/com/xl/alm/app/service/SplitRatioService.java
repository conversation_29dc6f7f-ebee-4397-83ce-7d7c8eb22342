package com.xl.alm.app.service;

import com.xl.alm.app.dto.SplitRatioDTO;
import com.xl.alm.app.query.SplitRatioQuery;

import java.util.List;

/**
 * 拆分比例表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface SplitRatioService {

    /**
     * 查询拆分比例表列表
     *
     * @param splitRatioQuery 拆分比例表查询条件
     * @return 拆分比例表列表
     */
    List<SplitRatioDTO> selectSplitRatioDtoList(SplitRatioQuery splitRatioQuery);

    /**
     * 根据ID查询拆分比例表
     *
     * @param id 主键ID
     * @return 拆分比例表
     */
    SplitRatioDTO selectSplitRatioDtoById(Long id);

    /**
     * 新增拆分比例表
     *
     * @param splitRatioDto 拆分比例表
     * @return 影响行数
     */
    int insertSplitRatioDto(SplitRatioDTO splitRatioDto);

    /**
     * 修改拆分比例表
     *
     * @param splitRatioDto 拆分比例表
     * @return 影响行数
     */
    int updateSplitRatioDto(SplitRatioDTO splitRatioDto);

    /**
     * 删除拆分比例表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteSplitRatioDtoById(Long id);

    /**
     * 批量删除拆分比例表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteSplitRatioDtoByIds(Long[] ids);

    /**
     * 批量新增拆分比例表
     *
     * @param splitRatioDtoList 拆分比例表列表
     * @return 影响行数
     */
    int batchInsertSplitRatioDto(List<SplitRatioDTO> splitRatioDtoList);

    /**
     * 根据账期删除拆分比例表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteSplitRatioDtoByPeriod(String accountingPeriod);

    /**
     * 根据条件查询拆分比例表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param businessType 业务类型
     * @param designType 设计类型
     * @param splitRatioType 拆分比例类型
     * @return 拆分比例表
     */
    SplitRatioDTO selectSplitRatioDtoByCondition(String accountingPeriod, String scenarioName, String businessType, String designType, String splitRatioType);

    /**
     * 导入拆分比例表数据
     *
     * @param splitRatioDtoList 拆分比例表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importSplitRatioData(List<SplitRatioDTO> splitRatioDtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导入拆分比例表数据（Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importSplitRatioDataFromExcel(java.io.InputStream inputStream, Boolean isUpdateSupport, String operName);

    /**
     * 导出拆分比例表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 导出拆分比例表
     *
     * @param response HTTP响应
     * @param query 查询条件
     */
    void exportSplitRatioHorizontal(javax.servlet.http.HttpServletResponse response, SplitRatioQuery query) throws Exception;
}
