package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 固定收益类投资资产外部评级剩余期限分布表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class FixedIncomeRatingTermDistEntity extends BaseEntity {
    private Long id;

    /**
     * 账期，格式：YYYYMM（如202406）
     */
    private String accountingPeriod;

    /**
     * 境内外标识，引用字典ast_domestic_foreign
     */
    private String domesticForeign;

    /**
     * 信用评级分类，引用字典ast_credit_rating
     */
    private String creditRatingCategory;

    /**
     * 固收资产剩余期限资产分类，引用字典ast_fixed_income_term_category
     */
    private String fixedIncomeTermCategory;

    /**
     * 账面余额金额
     */
    private BigDecimal bookBalance;

    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
