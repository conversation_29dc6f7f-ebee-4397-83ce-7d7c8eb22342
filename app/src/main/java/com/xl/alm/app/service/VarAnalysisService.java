package com.xl.alm.app.service;

import com.xl.alm.app.dto.VarAnalysisDTO;
import com.xl.alm.app.query.VarAnalysisQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * TB0003-VaR值分析表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface VarAnalysisService {

    /**
     * 查询VaR值分析列表
     *
     * @param varAnalysisQuery VaR值分析查询条件
     * @return VaR值分析列表
     */
    List<VarAnalysisDTO> selectVarAnalysisDtoList(VarAnalysisQuery varAnalysisQuery);

    /**
     * 根据ID查询VaR值分析
     *
     * @param id 主键ID
     * @return VaR值分析
     */
    VarAnalysisDTO selectVarAnalysisDtoById(Long id);

    /**
     * 新增VaR值分析
     *
     * @param varAnalysisDTO VaR值分析
     * @return 结果
     */
    int insertVarAnalysisDto(VarAnalysisDTO varAnalysisDTO);

    /**
     * 修改VaR值分析
     *
     * @param varAnalysisDTO VaR值分析
     * @return 结果
     */
    int updateVarAnalysisDto(VarAnalysisDTO varAnalysisDTO);

    /**
     * 批量删除VaR值分析
     *
     * @param ids 需要删除的VaR值分析主键集合
     * @return 结果
     */
    int deleteVarAnalysisDtoByIds(Long[] ids);

    /**
     * 删除VaR值分析信息
     *
     * @param id VaR值分析主键
     * @return 结果
     */
    int deleteVarAnalysisDtoById(Long id);

    /**
     * 导出VaR值分析数据
     *
     * @param varAnalysisQuery 查询条件
     * @return VaR值分析数据列表
     */
    List<VarAnalysisDTO> exportVarAnalysis(VarAnalysisQuery varAnalysisQuery);

    /**
     * 获取导入模板
     *
     * @param response HTTP响应
     */
    void importTemplate(HttpServletResponse response);

    /**
     * 债基1年VAR数据导入
     *
     * @param file 上传的文件
     * @param accountingPeriod 账期
     * @return 导入结果
     */
    String importBond1YearData(MultipartFile file, String accountingPeriod);

    /**
     * 债基3年VAR数据导入
     *
     * @param file 上传的文件
     * @param accountingPeriod 账期
     * @return 导入结果
     */
    String importBond3YearData(MultipartFile file, String accountingPeriod);

    /**
     * 权益1年VAR数据导入
     *
     * @param file 上传的文件
     * @param accountingPeriod 账期
     * @return 导入结果
     */
    String importEquity1YearData(MultipartFile file, String accountingPeriod);

    /**
     * 权益3年VAR数据导入
     *
     * @param file 上传的文件
     * @param accountingPeriod 账期
     * @return 导入结果
     */
    String importEquity3YearData(MultipartFile file, String accountingPeriod);

    /**
     * 通用VAR数据导入方法
     *
     * @param file 上传的文件
     * @param dataType 数据类型
     * @param accountingPeriod 账期
     * @return 导入结果
     */
    String importVarData(MultipartFile file, String dataType, String accountingPeriod);
}
