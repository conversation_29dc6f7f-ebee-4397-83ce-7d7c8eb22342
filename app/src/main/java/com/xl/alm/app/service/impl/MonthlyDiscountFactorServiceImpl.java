package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.MonthlyDiscountFactorDTO;
import com.xl.alm.app.entity.MonthlyDiscountFactorEntity;
import com.xl.alm.app.mapper.MonthlyDiscountFactorMapper;
import com.xl.alm.app.query.MonthlyDiscountFactorQuery;
import com.xl.alm.app.service.MonthlyDiscountFactorService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 月度折现因子表含价差 Service业务层处理
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class MonthlyDiscountFactorServiceImpl implements MonthlyDiscountFactorService {

    @Autowired
    private MonthlyDiscountFactorMapper monthlyDiscountFactorMapper;

    /**
     * 查询月度折现因子含价差列表
     *
     * @param monthlyDiscountFactorQuery 月度折现因子含价差查询条件
     * @return 月度折现因子含价差列表
     */
    @Override
    public List<MonthlyDiscountFactorDTO> selectMonthlyDiscountFactorDtoList(MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        List<MonthlyDiscountFactorEntity> entityList = monthlyDiscountFactorMapper.selectMonthlyDiscountFactorEntityList(monthlyDiscountFactorQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, MonthlyDiscountFactorDTO.class);
    }

    /**
     * 根据ID查询月度折现因子含价差
     *
     * @param id 主键ID
     * @return 月度折现因子含价差
     */
    @Override
    public MonthlyDiscountFactorDTO selectMonthlyDiscountFactorDtoById(Long id) {
        MonthlyDiscountFactorEntity entity = monthlyDiscountFactorMapper.selectMonthlyDiscountFactorEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, MonthlyDiscountFactorDTO.class);
    }

    /**
     * 新增月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        MonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(monthlyDiscountFactorDto, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.insertMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 修改月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        MonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(monthlyDiscountFactorDto, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.updateMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 批量删除月度折现因子含价差
     *
     * @param ids 需要删除的月度折现因子含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoByIds(Long[] ids) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityByIds(ids);
    }

    /**
     * 删除月度折现因子含价差信息
     *
     * @param id 月度折现因子含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoById(Long id) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityById(id);
    }



    /**
     * 根据账期删除月度折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteMonthlyDiscountFactorDtoByAccountPeriod(String accountPeriod) {
        return monthlyDiscountFactorMapper.deleteMonthlyDiscountFactorEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 批量插入月度折现因子含价差数据
     *
     * @param monthlyDiscountFactorList 月度折现因子含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertMonthlyDiscountFactorDto(List<MonthlyDiscountFactorDTO> monthlyDiscountFactorList) {
        if (monthlyDiscountFactorList == null || monthlyDiscountFactorList.isEmpty()) {
            return 0;
        }
        List<MonthlyDiscountFactorEntity> entityList = EntityDtoConvertUtil.convertToEntityList(monthlyDiscountFactorList, MonthlyDiscountFactorEntity.class);
        return monthlyDiscountFactorMapper.batchInsertMonthlyDiscountFactorEntity(entityList);
    }
}
