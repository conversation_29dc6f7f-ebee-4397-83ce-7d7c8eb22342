package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AdurKeyDurationDiscountCurveDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurKeyDurationDiscountCurveQuery;
import com.xl.alm.app.service.AdurKeyDurationDiscountCurveService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.ValueSetExcelExporter;
import com.xl.alm.app.util.TermDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/adur/key/duration/discount/curve")
public class AdurKeyDurationDiscountCurveController extends BaseController {

    @Autowired
    private AdurKeyDurationDiscountCurveService adurKeyDurationDiscountCurveService;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        startPage();
        List<AdurKeyDurationDiscountCurveDTO> list = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoList(adurKeyDurationDiscountCurveQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期折现曲线表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:export')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        List<AdurKeyDurationDiscountCurveDTO> list = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoList(adurKeyDurationDiscountCurveQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        // 使用ValueSetExcelExporter导出，处理keyDurationCurveWithSpreadSet字段
        // 将关键久期折现曲线表含价差值集JSON字段展开为多列，key作为表头，value作为值
        // 生成带时间戳的中文文件名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "关键久期折现曲线表含价差_" + timestamp;
        ValueSetExcelExporter.exportExcel(list, fileName, response, "keyDurationCurveWithSpreadSet");
    }

    /**
     * 获取ADUR关键久期折现曲线表含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id));
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:add')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        adurKeyDurationDiscountCurveDTO.setCreateBy(getUsername());
        return toAjax(adurKeyDurationDiscountCurveService.insertAdurKeyDurationDiscountCurveDto(adurKeyDurationDiscountCurveDTO));
    }

    /**
     * 修改ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:edit')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        adurKeyDurationDiscountCurveDTO.setUpdateBy(getUsername());
        return toAjax(adurKeyDurationDiscountCurveService.updateAdurKeyDurationDiscountCurveDto(adurKeyDurationDiscountCurveDTO));
    }

    /**
     * 删除ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:remove')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurKeyDurationDiscountCurveService.deleteAdurKeyDurationDiscountCurveDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期折现曲线表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:remove')")
    @Log(title = "ADUR关键久期折现曲线表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurKeyDurationDiscountCurveService.deleteAdurKeyDurationDiscountCurveDtoByAccountPeriod(accountPeriod));
    }



    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurKeyDurationDiscountCurveDTO dto = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getKeyDurationCurveWithSpreadSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:curve:edit')")
    @Log(title = "关键久期折现曲线期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurKeyDurationDiscountCurveDTO dto = adurKeyDurationDiscountCurveService.selectAdurKeyDurationDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setKeyDurationCurveWithSpreadSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurKeyDurationDiscountCurveService.updateAdurKeyDurationDiscountCurveDto(dto));
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list ADUR关键久期折现曲线表含价差数据列表
     */
    private void convertDictValueToLabel(List<AdurKeyDurationDiscountCurveDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        log.info("开始转换字典值为中文标签，共{}条记录", list.size());

        // 为每条记录转换字典值为中文标签
        for (AdurKeyDurationDiscountCurveDTO dto : list) {
            // 转换久期类型：字典值转为中文标签
            if (dto.getDurationType() != null) {
                String originalValue = dto.getDurationType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_duration_type");
                dto.setDurationType(convertedValue);
                log.debug("久期类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换基点类型：字典值转为中文标签
            if (dto.getBasisPointType() != null) {
                String originalValue = dto.getBasisPointType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_basis_point_type");
                dto.setBasisPointType(convertedValue);
                log.debug("基点类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换关键期限：字典值转为中文标签
            if (dto.getKeyTerm() != null) {
                String originalValue = dto.getKeyTerm();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_key_term");
                dto.setKeyTerm(convertedValue);
                log.debug("关键期限转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换压力方向：字典值转为中文标签
            if (dto.getStressDirection() != null) {
                String originalValue = dto.getStressDirection();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_stress_direction");
                dto.setStressDirection(convertedValue);
                log.debug("压力方向转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换日期类型：字典值转为中文标签
            if (dto.getDateType() != null) {
                String originalValue = dto.getDateType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_date_type");
                dto.setDateType(convertedValue);
                log.debug("日期类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换价差类型：字典值转为中文标签
            if (dto.getSpreadType() != null) {
                String originalValue = dto.getSpreadType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_spread_type");
                dto.setSpreadType(convertedValue);
                log.debug("价差类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换曲线细分类：字典值转为中文标签
            if (dto.getCurveSubCategory() != null) {
                String originalValue = dto.getCurveSubCategory();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_curve_sub_category");
                dto.setCurveSubCategory(convertedValue);
                log.debug("曲线细分类转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换账户名称：字典值转为中文标签
            if (dto.getAccountName() != null) {
                String originalValue = dto.getAccountName();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_account_name");
                dto.setAccountName(convertedValue);
                log.debug("账户名称转换: {} -> {}", originalValue, convertedValue);
            }
        }

        log.info("字典值转换完成");
    }
}
