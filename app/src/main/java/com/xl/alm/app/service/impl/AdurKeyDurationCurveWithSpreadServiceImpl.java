package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationCurveWithSpreadDTO;
import com.xl.alm.app.entity.AdurKeyDurationCurveWithSpreadEntity;
import com.xl.alm.app.mapper.AdurKeyDurationCurveWithSpreadMapper;
import com.xl.alm.app.query.AdurKeyDurationCurveWithSpreadQuery;
import com.xl.alm.app.service.AdurKeyDurationCurveWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationCurveWithSpreadServiceImpl implements AdurKeyDurationCurveWithSpreadService {

    @Autowired
    private AdurKeyDurationCurveWithSpreadMapper adurKeyDurationCurveWithSpreadMapper;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationCurveWithSpreadQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    @Override
    public List<AdurKeyDurationCurveWithSpreadDTO> selectAdurKeyDurationCurveWithSpreadDtoList(AdurKeyDurationCurveWithSpreadQuery adurKeyDurationCurveWithSpreadQuery) {
        List<AdurKeyDurationCurveWithSpreadEntity> entityList = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityList(adurKeyDurationCurveWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoById(Long id) {
        AdurKeyDurationCurveWithSpreadEntity entity = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 根据账期、资产编号、久期类型、关键期限和压力方向查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String keyTerm, String stressDirection) {
        AdurKeyDurationCurveWithSpreadEntity entity = adurKeyDurationCurveWithSpreadMapper.selectAdurKeyDurationCurveWithSpreadEntityByCondition(accountPeriod, assetNumber, durationType, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationCurveWithSpreadDTO.class);
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationCurveWithSpreadDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO adurKeyDurationCurveWithSpreadDTO) {
        AdurKeyDurationCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationCurveWithSpreadDTO, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.insertAdurKeyDurationCurveWithSpreadEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationCurveWithSpreadDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationCurveWithSpreadDto(List<AdurKeyDurationCurveWithSpreadDTO> adurKeyDurationCurveWithSpreadDtoList) {
        if (adurKeyDurationCurveWithSpreadDtoList == null || adurKeyDurationCurveWithSpreadDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationCurveWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurKeyDurationCurveWithSpreadDtoList, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.batchInsertAdurKeyDurationCurveWithSpreadEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO dto) {
        AdurKeyDurationCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationCurveWithSpreadEntity.class);
        return adurKeyDurationCurveWithSpreadMapper.updateAdurKeyDurationCurveWithSpreadEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoById(Long id) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoByIds(Long[] ids) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityByIds(ids);
    }

    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationCurveWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationCurveWithSpreadMapper.deleteAdurKeyDurationCurveWithSpreadEntityByAccountPeriod(accountPeriod);
    }


}
