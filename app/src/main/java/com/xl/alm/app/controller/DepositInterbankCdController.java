package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.DepositInterbankCdDTO;
import com.xl.alm.app.query.DepositInterbankCdQuery;
import com.xl.alm.app.service.DepositInterbankCdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 存款及同业存单表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/deposit/interbank/cd")
public class DepositInterbankCdController extends BaseController {

    @Autowired
    private DepositInterbankCdService depositInterbankCdService;

    /**
     * 查询存款及同业存单表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:list')")
    @GetMapping("/list")
    public TableDataInfo list(DepositInterbankCdQuery depositInterbankCdQuery) {
        startPage();
        List<DepositInterbankCdDTO> list = depositInterbankCdService.selectDepositInterbankCdDtoList(depositInterbankCdQuery);
        return getDataTable(list);
    }

    /**
     * 导出存款及同业存单表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:export')")
    @Log(title = "存款及同业存单表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DepositInterbankCdQuery depositInterbankCdQuery) {
        List<DepositInterbankCdDTO> list = depositInterbankCdService.selectDepositInterbankCdDtoList(depositInterbankCdQuery);
        convertDictValueToLabel(list);
        ExcelUtil<DepositInterbankCdDTO> util = new ExcelUtil<>(DepositInterbankCdDTO.class);
        util.exportExcel(list, "存款及同业存单表", response);
    }

    /**
     * 获取存款及同业存单表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(depositInterbankCdService.selectDepositInterbankCdDtoById(id));
    }

    /**
     * 根据条件查询存款及同业存单表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod, 
                                 @RequestParam String assetSubSubCategory, 
                                 @RequestParam String bankClassification) {
        return Result.success(depositInterbankCdService.selectDepositInterbankCdDtoByCondition(accountingPeriod, assetSubSubCategory, bankClassification));
    }

    /**
     * 新增存款及同业存单表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:add')")
    @Log(title = "存款及同业存单表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody DepositInterbankCdDTO depositInterbankCdDTO) {
        return toAjax(depositInterbankCdService.insertDepositInterbankCdDto(depositInterbankCdDTO));
    }

    /**
     * 修改存款及同业存单表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:edit')")
    @Log(title = "存款及同业存单表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody DepositInterbankCdDTO depositInterbankCdDTO) {
        return toAjax(depositInterbankCdService.updateDepositInterbankCdDto(depositInterbankCdDTO));
    }

    /**
     * 删除存款及同业存单表
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:remove')")
    @Log(title = "存款及同业存单表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(depositInterbankCdService.deleteDepositInterbankCdDtoByIds(ids));
    }

    /**
     * 导入存款及同业存单表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:deposit:interbank:cd:import')")
    @Log(title = "存款及同业存单表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DepositInterbankCdDTO> util = new ExcelUtil<>(DepositInterbankCdDTO.class);
        List<DepositInterbankCdDTO> depositInterbankCdList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(depositInterbankCdList);
        String operName = getUsername();
        try {
            String message = depositInterbankCdService.importDepositInterbankCdDto(depositInterbankCdList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载存款及同业存单表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DepositInterbankCdDTO> util = new ExcelUtil<>(DepositInterbankCdDTO.class);
        util.exportExcel(new ArrayList<>(), "存款及同业存单表数据", response);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 存款及同业存单表数据列表
     */
    private void convertDictValueToLabel(List<DepositInterbankCdDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (DepositInterbankCdDTO dto : list) {
            // 转换资产小小类字典值
            if (dto.getAssetSubSubCategory() != null) {
                dto.setAssetSubSubCategory(DictConvertUtil.convertValueToLabel(dto.getAssetSubSubCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换银行分类字典值
            if (dto.getBankClassification() != null) {
                dto.setBankClassification(DictConvertUtil.convertValueToLabel(dto.getBankClassification(), "ast_bank_classification"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 存款及同业存单表数据列表
     */
    private void convertDictLabelToValue(List<DepositInterbankCdDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (DepositInterbankCdDTO dto : list) {
            // 转换资产小小类字典标签
            if (dto.getAssetSubSubCategory() != null) {
                dto.setAssetSubSubCategory(DictConvertUtil.convertLabelToValue(dto.getAssetSubSubCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换银行分类字典标签
            if (dto.getBankClassification() != null) {
                dto.setBankClassification(DictConvertUtil.convertLabelToValue(dto.getBankClassification(), "ast_bank_classification"));
            }
        }
    }
}
