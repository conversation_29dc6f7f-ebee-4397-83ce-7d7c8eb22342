package com.xl.alm.app.service;

import com.xl.alm.app.dto.DurationAssetDetailDTO;
import com.xl.alm.app.query.DurationAssetDetailQuery;

import java.util.List;

/**
 * 久期资产明细表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface DurationAssetDetailService {

    /**
     * 查询久期资产明细列表
     *
     * @param durationAssetDetailQuery 久期资产明细查询条件
     * @return 久期资产明细列表
     */
    List<DurationAssetDetailDTO> selectDurationAssetDetailDtoList(DurationAssetDetailQuery durationAssetDetailQuery);

    /**
     * 根据ID查询久期资产明细
     *
     * @param id 主键ID
     * @return 久期资产明细
     */
    DurationAssetDetailDTO selectDurationAssetDetailDtoById(Long id);

    /**
     * 新增久期资产明细
     *
     * @param durationAssetDetailDto 久期资产明细
     * @return 结果
     */
    int insertDurationAssetDetailDto(DurationAssetDetailDTO durationAssetDetailDto);

    /**
     * 修改久期资产明细
     *
     * @param durationAssetDetailDto 久期资产明细
     * @return 结果
     */
    int updateDurationAssetDetailDto(DurationAssetDetailDTO durationAssetDetailDto);

    /**
     * 批量删除久期资产明细
     *
     * @param ids 需要删除的久期资产明细主键集合
     * @return 结果
     */
    int deleteDurationAssetDetailDtoByIds(Long[] ids);

    /**
     * 删除久期资产明细信息
     *
     * @param id 久期资产明细主键
     * @return 结果
     */
    int deleteDurationAssetDetailDtoById(Long id);

    /**
     * 导入久期资产明细
     *
     * @param durationAssetDetailList 久期资产明细数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    String importDurationAssetDetailDto(List<DurationAssetDetailDTO> durationAssetDetailList, Boolean updateSupport, String operName);

    /**
     * 根据账期删除久期资产明细数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteDurationAssetDetailDtoByAccountPeriod(String accountPeriod);

    /**
     * 批量插入久期资产明细数据
     *
     * @param durationAssetDetailList 久期资产明细列表
     * @return 影响行数
     */
    int batchInsertDurationAssetDetailDto(List<DurationAssetDetailDTO> durationAssetDetailList);
}
