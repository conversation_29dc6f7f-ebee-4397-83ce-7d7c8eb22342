package com.xl.alm.app.controller;

import com.xl.alm.app.dto.VarAnalysisDTO;
import com.xl.alm.app.dto.VarAnalysisImportDTO;
import com.xl.alm.app.query.VarAnalysisQuery;
import com.xl.alm.app.service.VarAnalysisService;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * TB0003-VaR值分析表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/var/analysis")
public class VarAnalysisController extends BaseController {

    @Autowired
    private VarAnalysisService varAnalysisService;

    /**
     * 查询VaR值分析列表
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:list')")
    @GetMapping("/list")
    public TableDataInfo list(VarAnalysisQuery varAnalysisQuery) {
        startPage();
        List<VarAnalysisDTO> list = varAnalysisService.selectVarAnalysisDtoList(varAnalysisQuery);
        return getDataTable(list);
    }

    /**
     * 导出VaR值分析列表
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:export')")
    @Log(title = "VaR值分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VarAnalysisQuery varAnalysisQuery) {
        List<VarAnalysisDTO> list = varAnalysisService.exportVarAnalysis(varAnalysisQuery);
        ExcelUtil<VarAnalysisDTO> util = new ExcelUtil<>(VarAnalysisDTO.class);
        util.exportExcel(list, "VaR值分析数据", response);
    }

    /**
     * 获取VaR值分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(varAnalysisService.selectVarAnalysisDtoById(id));
    }

    /**
     * 新增VaR值分析
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:add')")
    @Log(title = "VaR值分析", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody VarAnalysisDTO varAnalysisDTO) {
        return toAjax(varAnalysisService.insertVarAnalysisDto(varAnalysisDTO));
    }

    /**
     * 修改VaR值分析
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:edit')")
    @Log(title = "VaR值分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody VarAnalysisDTO varAnalysisDTO) {
        return toAjax(varAnalysisService.updateVarAnalysisDto(varAnalysisDTO));
    }

    /**
     * 删除VaR值分析
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:remove')")
    @Log(title = "VaR值分析", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(varAnalysisService.deleteVarAnalysisDtoByIds(ids));
    }

    /**
     * 获取VaR值分析导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        varAnalysisService.importTemplate(response);
    }

    /**
     * 债基1年VAR数据导入
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:import')")
    @Log(title = "债基1年VAR数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/bond-1year")
    public Result importBond1YearData(@RequestParam("file") MultipartFile file,
                                     @RequestParam("accountingPeriod") String accountingPeriod) {
        try {
            String message = varAnalysisService.importBond1YearData(file, accountingPeriod);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 债基3年VAR数据导入
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:import')")
    @Log(title = "债基3年VAR数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/bond-3year")
    public Result importBond3YearData(@RequestParam("file") MultipartFile file,
                                     @RequestParam("accountingPeriod") String accountingPeriod) {
        try {
            String message = varAnalysisService.importBond3YearData(file, accountingPeriod);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 权益1年VAR数据导入
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:import')")
    @Log(title = "权益1年VAR数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/equity-1year")
    public Result importEquity1YearData(@RequestParam("file") MultipartFile file,
                                       @RequestParam("accountingPeriod") String accountingPeriod) {
        try {
            String message = varAnalysisService.importEquity1YearData(file, accountingPeriod);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 权益3年VAR数据导入
     */
    @PreAuthorize("@ss.hasPermi('ast:var:analysis:import')")
    @Log(title = "权益3年VAR数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/import/equity-3year")
    public Result importEquity3YearData(@RequestParam("file") MultipartFile file,
                                       @RequestParam("accountingPeriod") String accountingPeriod) {
        try {
            String message = varAnalysisService.importEquity3YearData(file, accountingPeriod);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
