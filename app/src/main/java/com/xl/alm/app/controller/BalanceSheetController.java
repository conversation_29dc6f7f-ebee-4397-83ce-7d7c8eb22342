package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.BalanceSheetDTO;
import com.xl.alm.app.query.BalanceSheetQuery;
import com.xl.alm.app.service.BalanceSheetService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产负债表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/balance/sheet")
public class BalanceSheetController extends BaseController {

    @Autowired
    private BalanceSheetService balanceSheetService;

    /**
     * 查询资产负债表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:list')")
    @GetMapping("/list")
    public TableDataInfo list(BalanceSheetQuery balanceSheetQuery) {
        startPage();
        List<BalanceSheetDTO> list = balanceSheetService.selectBalanceSheetDtoList(balanceSheetQuery);
        return getDataTable(list);
    }

    /**
     * 获取资产负债表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(balanceSheetService.selectBalanceSheetDtoById(id));
    }

    /**
     * 新增资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:add')")
    @Log(title = "资产负债表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody BalanceSheetDTO balanceSheetDTO) {
        return toAjax(balanceSheetService.addBalanceSheetDto(balanceSheetDTO));
    }

    /**
     * 修改资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:edit')")
    @Log(title = "资产负债表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody BalanceSheetDTO balanceSheetDTO) {
        return toAjax(balanceSheetService.updateBalanceSheetDto(balanceSheetDTO));
    }

    /**
     * 删除资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:remove')")
    @Log(title = "资产负债表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(balanceSheetService.deleteBalanceSheetDtoByIds(ids));
    }

    /**
     * 导出资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:export')")
    @Log(title = "资产负债表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BalanceSheetQuery balanceSheetQuery) {
        List<BalanceSheetDTO> list = balanceSheetService.selectBalanceSheetDtoList(balanceSheetQuery);

        // 转换类别编码为中文标签用于导出
        convertCategoryCodeToLabel(list);

        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        util.exportExcel(list, "资产负债表数据", response);
    }

    /**
     * 获取资产负债表导入模板
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        util.exportTemplateExcel(response, "资产负债表");
    }

    /**
     * 导入资产负债表
     */
    @PreAuthorize("@ss.hasPermi('liab:balance:sheet:import')")
    @Log(title = "资产负债表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BalanceSheetDTO> util = new ExcelUtil<>(BalanceSheetDTO.class);
        List<BalanceSheetDTO> balanceSheetList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = balanceSheetService.importBalanceSheetDto(balanceSheetList, updateSupport, username);
        return Result.success(message);
    }

    /**
     * 将类别编码转换为中文标签用于导出
     *
     * @param list 资产负债表数据列表
     */
    private void convertCategoryCodeToLabel(List<BalanceSheetDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);
            List<SysDictData> dictDataList = dictTypeService.selectDictDataByType("liab_balance_category");

            // 为每条记录转换类别编码为中文标签
            for (BalanceSheetDTO dto : list) {
                if (StringUtils.isNotEmpty(dto.getCategory())) {
                    for (SysDictData dictData : dictDataList) {
                        if (dto.getCategory().equals(dictData.getDictValue())) {
                            dto.setCategory(dictData.getDictLabel());
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            logger.warn("转换类别编码为中文标签时发生异常: {}", e.getMessage());
        }
    }
}
