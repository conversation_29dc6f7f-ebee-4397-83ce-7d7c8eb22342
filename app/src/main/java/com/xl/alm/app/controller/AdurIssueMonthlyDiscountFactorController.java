package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.AdurIssueMonthlyDiscountFactorDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurIssueMonthlyDiscountFactorQuery;
import com.xl.alm.app.service.AdurIssueMonthlyDiscountFactorService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期折现因子表含价差(第二个)Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/issue/monthly/discount/factor")
public class AdurIssueMonthlyDiscountFactorController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(AdurIssueMonthlyDiscountFactorController.class);

    @Autowired
    private AdurIssueMonthlyDiscountFactorService adurIssueMonthlyDiscountFactorService;

    /**
     * 查询ADUR关键久期折现因子表含价差(第二个)列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery) {
        startPage();
        List<AdurIssueMonthlyDiscountFactorDTO> list = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoList(adurIssueMonthlyDiscountFactorQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期折现因子表含价差(第二个)列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:export')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery) {
        List<AdurIssueMonthlyDiscountFactorDTO> list = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoList(adurIssueMonthlyDiscountFactorQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        util.exportExcel(list, "ADUR关键久期折现因子表含价差(第二个)数据", response);
    }

    /**
     * 获取ADUR关键久期折现因子表含价差(第二个)详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id));
    }

    /**
     * 新增ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:add')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO) {
        adurIssueMonthlyDiscountFactorDTO.setCreateBy(getUsername());
        return toAjax(adurIssueMonthlyDiscountFactorService.insertAdurIssueMonthlyDiscountFactorDto(adurIssueMonthlyDiscountFactorDTO));
    }

    /**
     * 修改ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:edit')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO) {
        adurIssueMonthlyDiscountFactorDTO.setUpdateBy(getUsername());
        return toAjax(adurIssueMonthlyDiscountFactorService.updateAdurIssueMonthlyDiscountFactorDto(adurIssueMonthlyDiscountFactorDTO));
    }

    /**
     * 删除ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurIssueMonthlyDiscountFactorService.deleteAdurIssueMonthlyDiscountFactorDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期折现因子表含价差(第二个)
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurIssueMonthlyDiscountFactorService.deleteAdurIssueMonthlyDiscountFactorDtoByAccountPeriod(accountPeriod));
    }

    /**
     * 获取ADUR关键久期折现因子表含价差(第二个)导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        util.exportTemplateExcel(response, "ADUR关键久期折现因子表含价差(第二个)数据");
    }

    /**
     * 导入ADUR关键久期折现因子表含价差(第二个)数据
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:import')")
    @Log(title = "ADUR关键久期折现因子表含价差(第二个)", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AdurIssueMonthlyDiscountFactorDTO> util = new ExcelUtil<AdurIssueMonthlyDiscountFactorDTO>(AdurIssueMonthlyDiscountFactorDTO.class);
        List<AdurIssueMonthlyDiscountFactorDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = adurIssueMonthlyDiscountFactorService.importAdurIssueMonthlyDiscountFactorDto(dtoList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurIssueMonthlyDiscountFactorDTO dto = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountFactorSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:issue:monthly:discount:factor:edit')")
    @Log(title = "发行月度折现因子期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurIssueMonthlyDiscountFactorDTO dto = adurIssueMonthlyDiscountFactorService.selectAdurIssueMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountFactorSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurIssueMonthlyDiscountFactorService.updateAdurIssueMonthlyDiscountFactorDto(dto));
    }

    /**
     * 转换字典值为中文标签用于导出
     *
     * @param list 数据列表
     */
    private void convertDictValueToLabel(List<AdurIssueMonthlyDiscountFactorDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            for (AdurIssueMonthlyDiscountFactorDTO dto : list) {
                // 转换久期类型
                if (StringUtils.isNotEmpty(dto.getDurationType())) {
                    dto.setDurationType(convertDictValue(dictTypeService, "adur_duration_type", dto.getDurationType()));
                }

                // 转换基点类型
                if (StringUtils.isNotEmpty(dto.getBasisPointType())) {
                    dto.setBasisPointType(convertDictValue(dictTypeService, "adur_basis_point_type", dto.getBasisPointType()));
                }

                // 转换关键期限
                if (StringUtils.isNotEmpty(dto.getKeyTerm())) {
                    dto.setKeyTerm(convertDictValue(dictTypeService, "adur_key_term", dto.getKeyTerm()));
                }

                // 转换压力方向
                if (StringUtils.isNotEmpty(dto.getStressDirection())) {
                    dto.setStressDirection(convertDictValue(dictTypeService, "adur_stress_direction", dto.getStressDirection()));
                }

                // 转换日期类型
                if (StringUtils.isNotEmpty(dto.getDateType())) {
                    dto.setDateType(convertDictValue(dictTypeService, "adur_date_type", dto.getDateType()));
                }

                // 转换价差类型
                if (StringUtils.isNotEmpty(dto.getSpreadType())) {
                    dto.setSpreadType(convertDictValue(dictTypeService, "adur_spread_type", dto.getSpreadType()));
                }

                // 转换曲线细分类
                if (StringUtils.isNotEmpty(dto.getCurveSubCategory())) {
                    dto.setCurveSubCategory(convertDictValue(dictTypeService, "adur_curve_sub_category", dto.getCurveSubCategory()));
                }

                // 转换账户名称
                if (StringUtils.isNotEmpty(dto.getAccountName())) {
                    dto.setAccountName(convertDictValue(dictTypeService, "adur_account_name", dto.getAccountName()));
                }
            }
        } catch (Exception e) {
            log.error("转换字典值失败", e);
        }
    }

    /**
     * 将字典数据值转换为字典标签
     *
     * @param dictTypeService 字典服务
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签
     */
    private String convertDictValue(ISysDictTypeService dictTypeService, String dictType, String dictValue) {
        try {
            List<SysDictData> dictDataList = dictTypeService.selectDictDataByType(dictType);
            if (dictDataList != null) {
                for (SysDictData dictData : dictDataList) {
                    if (dictValue.equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取字典标签失败: dictType={}, dictValue={}", dictType, dictValue, e);
        }
        return dictValue;
    }
}
