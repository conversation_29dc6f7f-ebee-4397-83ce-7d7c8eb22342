package com.xl.alm.app.service;

import com.xl.alm.app.dto.BankClassificationMapDTO;
import com.xl.alm.app.query.BankClassificationMapQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 银行分类映射表 Service接口
 *
 * <AUTHOR> Assistant
 */
public interface BankClassificationMapService {

    /**
     * 查询银行分类映射表
     *
     * @param id 银行分类映射表主键
     * @return 银行分类映射表
     */
    BankClassificationMapDTO selectBankClassificationMapDtoById(Long id);

    /**
     * 查询银行分类映射表列表
     *
     * @param query 银行分类映射表查询条件
     * @return 银行分类映射表集合
     */
    List<BankClassificationMapDTO> selectBankClassificationMapDtoList(BankClassificationMapQuery query);

    /**
     * 新增银行分类映射表
     *
     * @param dto 银行分类映射表
     * @return 结果
     */
    int insertBankClassificationMapDto(BankClassificationMapDTO dto);

    /**
     * 修改银行分类映射表
     *
     * @param dto 银行分类映射表
     * @return 结果
     */
    int updateBankClassificationMapDto(BankClassificationMapDTO dto);

    /**
     * 批量删除银行分类映射表
     *
     * @param ids 需要删除的银行分类映射表主键集合
     * @return 结果
     */
    int deleteBankClassificationMapDtoByIds(Long[] ids);

    /**
     * 删除银行分类映射表信息
     *
     * @param id 银行分类映射表主键
     * @return 结果
     */
    int deleteBankClassificationMapDtoById(Long id);

    /**
     * 根据账期删除银行分类映射表
     *
     * @param accountingPeriod 账期
     * @return 结果
     */
    int deleteBankClassificationMapDtoByPeriod(String accountingPeriod);

    /**
     * 导入银行分类映射表数据
     *
     * @param file 上传的文件
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    String importBankClassificationMapDto(MultipartFile file, Boolean updateSupport, String username);

    /**
     * 导出银行分类映射表列表
     *
     * @param response 响应对象
     * @param query 查询条件
     */
    void exportBankClassificationMapDto(HttpServletResponse response, BankClassificationMapQuery query);

    /**
     * 下载银行分类映射表导入模板
     *
     * @param response 响应对象
     */
    void importTemplate(HttpServletResponse response);
}
