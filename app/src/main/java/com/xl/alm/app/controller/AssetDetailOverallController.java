package com.xl.alm.app.controller;

import com.github.pagehelper.PageInfo;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.AssetDetailOverallDTO;
import com.xl.alm.app.query.AssetDetailOverallQuery;
import com.xl.alm.app.service.AssetDetailOverallService;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 整体资产明细表Controller
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@RestController
@RequestMapping("/ast/asset/detail/overall")
public class AssetDetailOverallController extends BaseController {

    @Autowired
    private AssetDetailOverallService assetDetailOverallService;

    /**
     * 查询整体资产明细表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetDetailOverallQuery query) {
        startPage();
        List<AssetDetailOverallDTO> list = assetDetailOverallService.selectAssetDetailOverallList(query);
        return getDataTable(list);
    }

    /**
     * 导出整体资产明细表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:export')")
    @Log(title = "整体资产明细表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetDetailOverallQuery query) {
        List<AssetDetailOverallDTO> list = assetDetailOverallService.exportAssetDetailOverall(query);
        ExcelUtil<AssetDetailOverallDTO> util = new ExcelUtil<>(AssetDetailOverallDTO.class);
        util.exportExcel(list, "整体资产明细表数据", response);
    }

    /**
     * 获取整体资产明细表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        AssetDetailOverallDTO dto = assetDetailOverallService.selectAssetDetailOverallById(id);
        return Result.success(dto);
    }

    /**
     * 新增整体资产明细表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:add')")
    @Log(title = "整体资产明细表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AssetDetailOverallDTO dto) {
        dto.setCreateBy(getUsername());
        int result = assetDetailOverallService.insertAssetDetailOverall(dto);
        return toAjax(result);
    }

    /**
     * 修改整体资产明细表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:edit')")
    @Log(title = "整体资产明细表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AssetDetailOverallDTO dto) {
        dto.setUpdateBy(getUsername());
        int result = assetDetailOverallService.updateAssetDetailOverall(dto);
        return toAjax(result);
    }

    /**
     * 删除整体资产明细表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:remove')")
    @Log(title = "整体资产明细表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        int result = assetDetailOverallService.deleteAssetDetailOverallByIds(ids);
        return toAjax(result);
    }

    /**
     * 导入整体资产明细表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:import')")
    @Log(title = "整体资产明细表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetDetailOverallDTO> util = new ExcelUtil<>(AssetDetailOverallDTO.class);
        List<AssetDetailOverallDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = assetDetailOverallService.importAssetDetailOverall(dtoList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取整体资产明细表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:detail:overall:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetDetailOverallDTO> util = new ExcelUtil<>(AssetDetailOverallDTO.class);
        util.exportTemplateExcel(response, "整体资产明细表");
    }
}
