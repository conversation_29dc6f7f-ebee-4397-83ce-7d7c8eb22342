package com.xl.alm.app.service;

import com.xl.alm.app.dto.AccountHoldingDTO;
import com.xl.alm.app.dto.AccountHoldingImportDTO;
import com.xl.alm.app.query.AccountHoldingQuery;

import java.util.List;

/**
 * TB0001-组合持仓表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AccountHoldingService {

    /**
     * 查询组合持仓列表
     *
     * @param accountHoldingQuery 组合持仓查询条件
     * @return 组合持仓列表
     */
    List<AccountHoldingDTO> selectAccountHoldingDtoList(AccountHoldingQuery accountHoldingQuery);

    /**
     * 根据ID查询组合持仓
     *
     * @param id 主键ID
     * @return 组合持仓
     */
    AccountHoldingDTO selectAccountHoldingDtoById(Long id);

    /**
     * 新增组合持仓
     *
     * @param accountHoldingDTO 组合持仓
     * @return 结果
     */
    int insertAccountHoldingDto(AccountHoldingDTO accountHoldingDTO);

    /**
     * 修改组合持仓
     *
     * @param accountHoldingDTO 组合持仓
     * @return 结果
     */
    int updateAccountHoldingDto(AccountHoldingDTO accountHoldingDTO);

    /**
     * 批量删除组合持仓
     *
     * @param ids 需要删除的组合持仓主键集合
     * @return 结果
     */
    int deleteAccountHoldingDtoByIds(Long[] ids);

    /**
     * 删除组合持仓信息
     *
     * @param id 组合持仓主键
     * @return 结果
     */
    int deleteAccountHoldingDtoById(Long id);

    /**
     * 批量插入组合持仓数据
     *
     * @param accountHoldingDtoList 组合持仓列表
     * @return 影响行数
     */
    int batchInsertAccountHoldingDto(List<AccountHoldingDTO> accountHoldingDtoList);

    /**
     * 根据账期删除组合持仓数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAccountHoldingDtoByPeriod(String accountingPeriod);

    /**
     * 导入组合持仓数据
     *
     * @param importList       导入数据列表
     * @param updateSupport    是否更新支持，如果已存在，是否更新
     * @param username         操作用户
     * @param accountingPeriod 账期
     * @return 结果
     */
    String importAccountHoldingDto(List<AccountHoldingImportDTO> importList, Boolean updateSupport, String username, String accountingPeriod);
}
