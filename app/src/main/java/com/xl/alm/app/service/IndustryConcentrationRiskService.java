package com.xl.alm.app.service;

import com.xl.alm.app.dto.IndustryConcentrationRiskDTO;
import com.xl.alm.app.query.IndustryConcentrationRiskQuery;

import java.util.List;

/**
 * 行业集中度风险表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface IndustryConcentrationRiskService {

    /**
     * 查询行业集中度风险表列表
     *
     * @param industryConcentrationRiskQuery 行业集中度风险表查询条件
     * @return 行业集中度风险表列表
     */
    List<IndustryConcentrationRiskDTO> selectIndustryConcentrationRiskDtoList(IndustryConcentrationRiskQuery industryConcentrationRiskQuery);

    /**
     * 用id查询行业集中度风险表
     *
     * @param id id
     * @return 行业集中度风险表
     */
    IndustryConcentrationRiskDTO selectIndustryConcentrationRiskDtoById(Long id);

    /**
     * 根据账期和行业名称查询行业集中度风险表
     *
     * @param accountingPeriod 账期
     * @param industryName 行业名称
     * @return 行业集中度风险表
     */
    IndustryConcentrationRiskDTO selectIndustryConcentrationRiskDtoByCondition(
            String accountingPeriod,
            String industryName);

    /**
     * 新增行业集中度风险表
     *
     * @param dto 行业集中度风险表
     * @return 结果
     */
    int addIndustryConcentrationRiskDto(IndustryConcentrationRiskDTO dto);

    /**
     * 修改行业集中度风险表
     *
     * @param dto 行业集中度风险表
     * @return 结果
     */
    int updateIndustryConcentrationRiskDto(IndustryConcentrationRiskDTO dto);

    /**
     * 批量删除行业集中度风险表
     *
     * @param ids 需要删除的行业集中度风险表主键集合
     * @return 结果
     */
    int deleteIndustryConcentrationRiskDtoByIds(Long[] ids);

    /**
     * 删除行业集中度风险表信息
     *
     * @param id 行业集中度风险表主键
     * @return 结果
     */
    int deleteIndustryConcentrationRiskDtoById(Long id);

    /**
     * 批量插入行业集中度风险表数据
     *
     * @param industryConcentrationRiskDtoList 行业集中度风险表列表
     * @return 影响行数
     */
    int batchInsertIndustryConcentrationRiskDto(List<IndustryConcentrationRiskDTO> industryConcentrationRiskDtoList);

    /**
     * 删除指定账期的行业集中度风险表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteIndustryConcentrationRiskDtoByPeriod(String accountingPeriod);

    /**
     * 导入行业集中度风险表
     *
     * @param dtoList       行业集中度风险表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importIndustryConcentrationRiskDto(List<IndustryConcentrationRiskDTO> dtoList, Boolean updateSupport, String username);
}
