package com.xl.alm.app.controller;

import com.github.pagehelper.PageInfo;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.AlmNewPremiumStatisticsDTO;
import com.xl.alm.app.query.AlmNewPremiumStatisticsQuery;
import com.xl.alm.app.service.IAlmNewPremiumStatisticsService;
import com.xl.alm.app.util.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ALM新单保费统计表Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/liab/alm/new/premium/statistics")
public class AlmNewPremiumStatisticsController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(AlmNewPremiumStatisticsController.class);

    @Autowired
    private IAlmNewPremiumStatisticsService almNewPremiumStatisticsService;

    /**
     * 查询ALM新单保费统计表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlmNewPremiumStatisticsQuery query)
    {
        startPage();
        List<AlmNewPremiumStatisticsDTO> list = almNewPremiumStatisticsService.selectAlmNewPremiumStatisticsList(query);
        return getDataTable(list);
    }

    /**
     * 导出ALM新单保费统计表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:export')")
    @Log(title = "ALM新单保费统计表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AlmNewPremiumStatisticsQuery query)
    {
        List<AlmNewPremiumStatisticsDTO> list = almNewPremiumStatisticsService.selectAlmNewPremiumStatisticsList(query);

        // 转换统计类型：将编码转换为中文标签用于导出
        for (AlmNewPremiumStatisticsDTO dto : list) {
            convertStatisticsTypeForExport(dto);
        }

        ExcelUtil<AlmNewPremiumStatisticsDTO> util = new ExcelUtil<AlmNewPremiumStatisticsDTO>(AlmNewPremiumStatisticsDTO.class);
        util.exportExcel(list, "ALM新单保费统计数据", response);
    }

    /**
     * 获取ALM新单保费统计表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id)
    {
        return Result.success(almNewPremiumStatisticsService.selectAlmNewPremiumStatisticsById(id));
    }

    /**
     * 新增ALM新单保费统计表
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:add')")
    @Log(title = "ALM新单保费统计表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody AlmNewPremiumStatisticsDTO almNewPremiumStatisticsDTO)
    {
        logger.info("新增ALM新单保费统计，接收到的数据：{}", almNewPremiumStatisticsDTO);

        if (!almNewPremiumStatisticsService.checkUniqueRecord(almNewPremiumStatisticsDTO))
        {
            String errorMsg = String.format("新增ALM新单保费统计失败，账期[%s]+统计类型[%s]+统计类型细分[%s]组合已存在",
                    almNewPremiumStatisticsDTO.getAccountingPeriod(),
                    almNewPremiumStatisticsDTO.getStatisticsType(),
                    almNewPremiumStatisticsDTO.getStatisticsSubType());
            logger.warn(errorMsg);
            return Result.error(errorMsg);
        }
        almNewPremiumStatisticsDTO.setCreateBy(SecurityUtils.getUsername());
        return toAjax(almNewPremiumStatisticsService.insertAlmNewPremiumStatistics(almNewPremiumStatisticsDTO));
    }

    /**
     * 修改ALM新单保费统计表
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:edit')")
    @Log(title = "ALM新单保费统计表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody AlmNewPremiumStatisticsDTO almNewPremiumStatisticsDTO)
    {
        if (!almNewPremiumStatisticsService.checkUniqueRecord(almNewPremiumStatisticsDTO))
        {
            return Result.error("修改ALM新单保费统计失败，账期+统计类型+统计类型细分组合已存在");
        }
        almNewPremiumStatisticsDTO.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(almNewPremiumStatisticsService.updateAlmNewPremiumStatistics(almNewPremiumStatisticsDTO));
    }

    /**
     * 删除ALM新单保费统计表
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:remove')")
    @Log(title = "ALM新单保费统计表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids)
    {
        return toAjax(almNewPremiumStatisticsService.deleteAlmNewPremiumStatisticsByIds(ids));
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<AlmNewPremiumStatisticsDTO> util = new ExcelUtil<AlmNewPremiumStatisticsDTO>(AlmNewPremiumStatisticsDTO.class);
        util.exportTemplateExcel(response, "ALM新单保费统计表");
    }

    /**
     * 导入数据
     */
    @PreAuthorize("@ss.hasPermi('liab:alm:new:premium:statistics:import')")
    @Log(title = "ALM新单保费统计表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<AlmNewPremiumStatisticsDTO> util = new ExcelUtil<AlmNewPremiumStatisticsDTO>(AlmNewPremiumStatisticsDTO.class);
        List<AlmNewPremiumStatisticsDTO> almNewPremiumStatisticsList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = almNewPremiumStatisticsService.importAlmNewPremiumStatistics(almNewPremiumStatisticsList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 转换统计类型：将编码转换为中文标签用于导出
     *
     * @param dto ALM新单保费统计表数据
     */
    private void convertStatisticsTypeForExport(AlmNewPremiumStatisticsDTO dto) {
        String statisticsType = dto.getStatisticsType();
        if (statisticsType != null) {
            // 将编码转换为中文标签
            switch (statisticsType) {
                case "01":
                    dto.setStatisticsType("缴费年期");
                    break;
                case "02":
                    dto.setStatisticsType("设计类型");
                    break;
                case "03":
                    dto.setStatisticsType("设计类型区分中短");
                    break;
                case "04":
                    dto.setStatisticsType("业务类别");
                    break;
                default:
                    // 如果已经是中文标签格式，保持不变
                    logger.debug("统计类型已经是中文标签格式或未知格式：{}", statisticsType);
                    break;
            }
        }
    }
}
