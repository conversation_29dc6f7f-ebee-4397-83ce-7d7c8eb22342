package com.xl.alm.app.service;

import com.xl.alm.app.dto.CashFlowStatementDTO;
import com.xl.alm.app.query.CashFlowStatementQuery;

import java.util.List;

/**
 * 现金流量表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface CashFlowStatementService {

    /**
     * 查询现金流量表列表
     *
     * @param cashFlowStatementQuery 现金流量表查询条件
     * @return 现金流量表列表
     */
    List<CashFlowStatementDTO> selectCashFlowStatementDtoList(CashFlowStatementQuery cashFlowStatementQuery);

    /**
     * 用id查询现金流量表
     *
     * @param id id
     * @return 现金流量表
     */
    CashFlowStatementDTO selectCashFlowStatementDtoById(Long id);

    /**
     * 根据账期和项目名称查询现金流量表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return 现金流量表
     */
    CashFlowStatementDTO selectCashFlowStatementDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增现金流量表
     *
     * @param cashFlowStatementDto 现金流量表
     * @return 影响行数
     */
    int insertCashFlowStatementDto(CashFlowStatementDTO cashFlowStatementDto);

    /**
     * 批量新增现金流量表
     *
     * @param cashFlowStatementDtoList 现金流量表列表
     * @return 影响行数
     */
    int batchInsertCashFlowStatementDto(List<CashFlowStatementDTO> cashFlowStatementDtoList);

    /**
     * 更新现金流量表
     *
     * @param cashFlowStatementDto 现金流量表
     * @return 影响行数
     */
    int updateCashFlowStatementDto(CashFlowStatementDTO cashFlowStatementDto);

    /**
     * 删除现金流量表
     *
     * @param id id
     * @return 影响行数
     */
    int deleteCashFlowStatementDtoById(Long id);

    /**
     * 批量删除现金流量表
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteCashFlowStatementDtoByIds(Long[] ids);

    /**
     * 根据账期删除现金流量表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteCashFlowStatementDtoByPeriod(String accountingPeriod);

    /**
     * 导入现金流量表
     *
     * @param dtoList 现金流量表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    String importCashFlowStatementDto(List<CashFlowStatementDTO> dtoList, Boolean updateSupport, String username);
}
