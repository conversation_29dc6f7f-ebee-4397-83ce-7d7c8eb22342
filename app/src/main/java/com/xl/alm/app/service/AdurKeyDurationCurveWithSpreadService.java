package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationCurveWithSpreadDTO;
import com.xl.alm.app.query.AdurKeyDurationCurveWithSpreadQuery;

import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurKeyDurationCurveWithSpreadService {

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationCurveWithSpreadQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    List<AdurKeyDurationCurveWithSpreadDTO> selectAdurKeyDurationCurveWithSpreadDtoList(AdurKeyDurationCurveWithSpreadQuery adurKeyDurationCurveWithSpreadQuery);

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoById(Long id);

    /**
     * 根据账期、资产编号、久期类型、关键期限和压力方向查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    AdurKeyDurationCurveWithSpreadDTO selectAdurKeyDurationCurveWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String keyTerm, String stressDirection);

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationCurveWithSpreadDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    int insertAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO adurKeyDurationCurveWithSpreadDTO);

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationCurveWithSpreadDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    int batchInsertAdurKeyDurationCurveWithSpreadDto(List<AdurKeyDurationCurveWithSpreadDTO> adurKeyDurationCurveWithSpreadDtoList);

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    int updateAdurKeyDurationCurveWithSpreadDto(AdurKeyDurationCurveWithSpreadDTO dto);

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurKeyDurationCurveWithSpreadDtoById(Long id);

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    int deleteAdurKeyDurationCurveWithSpreadDtoByIds(Long[] ids);

    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurKeyDurationCurveWithSpreadDtoByAccountPeriod(String accountPeriod);


}
