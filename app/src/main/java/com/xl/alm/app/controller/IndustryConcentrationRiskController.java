package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.IndustryConcentrationRiskDTO;
import com.xl.alm.app.query.IndustryConcentrationRiskQuery;
import com.xl.alm.app.service.IndustryConcentrationRiskService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 行业集中度风险表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/industry/concentration/risk")
public class IndustryConcentrationRiskController extends BaseController {
    @Autowired
    private IndustryConcentrationRiskService industryConcentrationRiskService;

    /**
     * 查询行业集中度风险表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:list')")
    @GetMapping("/list")
    public TableDataInfo list(IndustryConcentrationRiskQuery query) {
        startPage();
        List<IndustryConcentrationRiskDTO> list = industryConcentrationRiskService.selectIndustryConcentrationRiskDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出行业集中度风险表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:export')")
    @Log(title = "行业集中度风险表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IndustryConcentrationRiskQuery query) {
        List<IndustryConcentrationRiskDTO> list = industryConcentrationRiskService.selectIndustryConcentrationRiskDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        ExcelUtil<IndustryConcentrationRiskDTO> util = new ExcelUtil<IndustryConcentrationRiskDTO>(IndustryConcentrationRiskDTO.class);
        util.exportExcel(list, "行业集中度风险表数据", response);
    }

    /**
     * 获取行业集中度风险表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(industryConcentrationRiskService.selectIndustryConcentrationRiskDtoById(id));
    }

    /**
     * 新增行业集中度风险表
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:add')")
    @Log(title = "行业集中度风险表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody IndustryConcentrationRiskDTO industryConcentrationRiskDto) {
        return toAjax(industryConcentrationRiskService.addIndustryConcentrationRiskDto(industryConcentrationRiskDto));
    }

    /**
     * 修改行业集中度风险表
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:edit')")
    @Log(title = "行业集中度风险表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody IndustryConcentrationRiskDTO industryConcentrationRiskDto) {
        return toAjax(industryConcentrationRiskService.updateIndustryConcentrationRiskDto(industryConcentrationRiskDto));
    }

    /**
     * 删除行业集中度风险表
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:remove')")
    @Log(title = "行业集中度风险表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(industryConcentrationRiskService.deleteIndustryConcentrationRiskDtoByIds(ids));
    }

    /**
     * 获取行业集中度风险表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<IndustryConcentrationRiskDTO> util = new ExcelUtil<IndustryConcentrationRiskDTO>(IndustryConcentrationRiskDTO.class);
        util.exportTemplateExcel(response, "行业集中度风险表");
    }

    /**
     * 导入行业集中度风险表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:industry:concentration:risk:import')")
    @Log(title = "行业集中度风险表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<IndustryConcentrationRiskDTO> util = new ExcelUtil<IndustryConcentrationRiskDTO>(IndustryConcentrationRiskDTO.class);
        List<IndustryConcentrationRiskDTO> industryConcentrationRiskList = util.importExcel(file.getInputStream());
        // 转换字典标签为值用于导入
        convertDictLabelToValue(industryConcentrationRiskList);
        String operName = getUsername();
        String message = industryConcentrationRiskService.importIndustryConcentrationRiskDto(industryConcentrationRiskList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 行业集中度风险表数据列表
     */
    private void convertDictValueToLabel(List<IndustryConcentrationRiskDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (IndustryConcentrationRiskDTO dto : list) {
            // 转换行业统计标识字典值
            if (dto.getIndustryStatisticsFlag() != null) {
                dto.setIndustryStatisticsFlag(DictConvertUtil.convertValueToLabel(dto.getIndustryStatisticsFlag(), "acm_industry_statistics"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 行业集中度风险表数据列表
     */
    private void convertDictLabelToValue(List<IndustryConcentrationRiskDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (IndustryConcentrationRiskDTO dto : list) {
            // 转换行业统计标识字典标签
            if (dto.getIndustryStatisticsFlag() != null) {
                dto.setIndustryStatisticsFlag(DictConvertUtil.convertLabelToValue(dto.getIndustryStatisticsFlag(), "acm_industry_statistics"));
            }
        }
    }
}
