package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetRiskFiveLevelDTO;
import com.xl.alm.app.query.AssetRiskFiveLevelQuery;

import java.util.List;

/**
 * 保险资产风险五级分类状况表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetRiskFiveLevelService {

    /**
     * 查询保险资产风险五级分类状况表列表
     *
     * @param assetRiskFiveLevelQuery 保险资产风险五级分类状况表查询条件
     * @return 保险资产风险五级分类状况表列表
     */
    List<AssetRiskFiveLevelDTO> selectAssetRiskFiveLevelDtoList(AssetRiskFiveLevelQuery assetRiskFiveLevelQuery);

    /**
     * 用id查询保险资产风险五级分类状况表
     *
     * @param id id
     * @return 保险资产风险五级分类状况表
     */
    AssetRiskFiveLevelDTO selectAssetRiskFiveLevelDtoById(Long id);

    /**
     * 根据账期、项目名称、五级分类资产统计标识和五级分类查询保险资产风险五级分类状况表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @param fiveLevelStatisticsFlag 五级分类资产统计标识
     * @param fiveLevelClassification 五级分类
     * @return 保险资产风险五级分类状况表
     */
    AssetRiskFiveLevelDTO selectAssetRiskFiveLevelDtoByCondition(
            String accountingPeriod,
            String itemName,
            String fiveLevelStatisticsFlag,
            String fiveLevelClassification);

    /**
     * 新增保险资产风险五级分类状况表
     *
     * @param dto 保险资产风险五级分类状况表
     * @return 结果
     */
    int addAssetRiskFiveLevelDto(AssetRiskFiveLevelDTO dto);

    /**
     * 修改保险资产风险五级分类状况表
     *
     * @param dto 保险资产风险五级分类状况表
     * @return 结果
     */
    int updateAssetRiskFiveLevelDto(AssetRiskFiveLevelDTO dto);

    /**
     * 批量删除保险资产风险五级分类状况表
     *
     * @param ids 需要删除的保险资产风险五级分类状况表主键集合
     * @return 结果
     */
    int deleteAssetRiskFiveLevelDtoByIds(Long[] ids);

    /**
     * 删除保险资产风险五级分类状况表信息
     *
     * @param id 保险资产风险五级分类状况表主键
     * @return 结果
     */
    int deleteAssetRiskFiveLevelDtoById(Long id);

    /**
     * 批量插入保险资产风险五级分类状况表数据
     *
     * @param assetRiskFiveLevelDtoList 保险资产风险五级分类状况表列表
     * @return 影响行数
     */
    int batchInsertAssetRiskFiveLevelDto(List<AssetRiskFiveLevelDTO> assetRiskFiveLevelDtoList);

    /**
     * 删除指定账期的保险资产风险五级分类状况表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAssetRiskFiveLevelDtoByPeriod(String accountingPeriod);

    /**
     * 导入保险资产风险五级分类状况表
     *
     * @param dtoList       保险资产风险五级分类状况表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAssetRiskFiveLevelDto(List<AssetRiskFiveLevelDTO> dtoList, Boolean updateSupport, String username);
}
