package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AlmcfActualQuarterlyDTO;
import com.xl.alm.app.query.AlmcfActualQuarterlyQuery;
import com.xl.alm.app.service.AlmcfActualQuarterlyService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ALMCF实际发生数本季度累计表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/almcf/actual/quarterly")
public class AlmcfActualQuarterlyController extends BaseController {

    @Autowired
    private AlmcfActualQuarterlyService almcfActualQuarterlyService;

    /**
     * 查询ALMCF实际发生数本季度累计表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlmcfActualQuarterlyQuery almcfActualQuarterlyQuery) {
        startPage();
        List<AlmcfActualQuarterlyDTO> list = almcfActualQuarterlyService.selectAlmcfActualQuarterlyDtoList(almcfActualQuarterlyQuery);
        return getDataTable(list);
    }

    /**
     * 导出ALMCF实际发生数本季度累计表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:export')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AlmcfActualQuarterlyQuery almcfActualQuarterlyQuery) {
        List<AlmcfActualQuarterlyDTO> list = almcfActualQuarterlyService.selectAlmcfActualQuarterlyDtoList(almcfActualQuarterlyQuery);
        ExcelUtil<AlmcfActualQuarterlyDTO> util = new ExcelUtil<>(AlmcfActualQuarterlyDTO.class);
        util.exportExcel(list, "ALMCF实际发生数本季度累计表数据", response);
    }

    /**
     * 获取ALMCF实际发生数本季度累计表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(almcfActualQuarterlyService.selectAlmcfActualQuarterlyDtoById(id));
    }

    /**
     * 新增ALMCF实际发生数本季度累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:add')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AlmcfActualQuarterlyDTO almcfActualQuarterlyDto) {
        return toAjax(almcfActualQuarterlyService.insertAlmcfActualQuarterlyDto(almcfActualQuarterlyDto));
    }

    /**
     * 修改ALMCF实际发生数本季度累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:edit')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AlmcfActualQuarterlyDTO almcfActualQuarterlyDto) {
        return toAjax(almcfActualQuarterlyService.updateAlmcfActualQuarterlyDto(almcfActualQuarterlyDto));
    }

    /**
     * 删除ALMCF实际发生数本季度累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:remove')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(almcfActualQuarterlyService.deleteAlmcfActualQuarterlyDtoByIds(ids));
    }

    /**
     * 获取ALMCF实际发生数本季度累计表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AlmcfActualQuarterlyDTO> util = new ExcelUtil<>(AlmcfActualQuarterlyDTO.class);
        util.exportTemplateExcel(response, "ALMCF实际发生数本季度累计表");
    }

    /**
     * 导入ALMCF实际发生数本季度累计表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:import')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AlmcfActualQuarterlyDTO> util = new ExcelUtil<>(AlmcfActualQuarterlyDTO.class);
        List<AlmcfActualQuarterlyDTO> almcfActualQuarterlyList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = almcfActualQuarterlyService.importAlmcfActualQuarterlyDto(almcfActualQuarterlyList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 计算ALMCF实际发生数本季度累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:quarterly:calculate')")
    @Log(title = "ALMCF实际发生数本季度累计表", businessType = BusinessType.OTHER)
    @PostMapping("/calculate")
    public Result calculate(@RequestParam String accountingPeriod) {
        String operName = getUsername();
        String message = almcfActualQuarterlyService.calculateAlmcfActualQuarterlyDto(accountingPeriod, operName);
        return Result.success(message);
    }
}
