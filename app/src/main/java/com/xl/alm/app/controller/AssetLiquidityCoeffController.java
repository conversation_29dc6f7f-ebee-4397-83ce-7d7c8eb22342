package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.AssetLiquidityCoeffDTO;
import com.xl.alm.app.query.AssetLiquidityCoeffQuery;
import com.xl.alm.app.service.AssetLiquidityCoeffService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产流动性分类及变现系数表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/asset/liquidity/coeff")
public class AssetLiquidityCoeffController extends BaseController {

    @Autowired
    private AssetLiquidityCoeffService assetLiquidityCoeffService;

    /**
     * 查询资产流动性分类及变现系数表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetLiquidityCoeffQuery assetLiquidityCoeffQuery) {
        startPage();
        List<AssetLiquidityCoeffDTO> list = assetLiquidityCoeffService.selectAssetLiquidityCoeffDtoList(assetLiquidityCoeffQuery);
        return getDataTable(list);
    }

    /**
     * 导出资产流动性分类及变现系数表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:export')")
    @Log(title = "资产流动性分类及变现系数表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetLiquidityCoeffQuery assetLiquidityCoeffQuery) {
        ExcelUtil<AssetLiquidityCoeffDTO> util = new ExcelUtil<>(AssetLiquidityCoeffDTO.class);
        List<AssetLiquidityCoeffDTO> list = assetLiquidityCoeffService.selectAssetLiquidityCoeffDtoList(assetLiquidityCoeffQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "资产流动性分类及变现系数表数据", response);
    }

    /**
     * 获取资产流动性分类及变现系数表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetLiquidityCoeffService.selectAssetLiquidityCoeffDtoById(id));
    }

    /**
     * 新增资产流动性分类及变现系数表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:add')")
    @Log(title = "资产流动性分类及变现系数表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody AssetLiquidityCoeffDTO assetLiquidityCoeffDTO) {
        return toAjax(assetLiquidityCoeffService.insertAssetLiquidityCoeffDto(assetLiquidityCoeffDTO));
    }

    /**
     * 修改资产流动性分类及变现系数表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:edit')")
    @Log(title = "资产流动性分类及变现系数表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody AssetLiquidityCoeffDTO assetLiquidityCoeffDTO) {
        return toAjax(assetLiquidityCoeffService.updateAssetLiquidityCoeffDto(assetLiquidityCoeffDTO));
    }

    /**
     * 删除资产流动性分类及变现系数表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:remove')")
    @Log(title = "资产流动性分类及变现系数表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetLiquidityCoeffService.deleteAssetLiquidityCoeffDtoByIds(ids));
    }

    /**
     * 导入资产流动性分类及变现系数表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:import')")
    @Log(title = "资产流动性分类及变现系数表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetLiquidityCoeffDTO> util = new ExcelUtil(AssetLiquidityCoeffDTO.class);
        List<AssetLiquidityCoeffDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = assetLiquidityCoeffService.importAssetLiquidityCoeffDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取资产流动性分类及变现系数表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:liquidity:coeff:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetLiquidityCoeffDTO> util = new ExcelUtil<>(AssetLiquidityCoeffDTO.class);
        util.exportTemplateExcel(response, "资产流动性分类及变现系数表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资产流动性分类及变现系数表数据列表
     */
    private void convertDictValueToLabel(List<AssetLiquidityCoeffDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetLiquidityCoeffDTO dto : list) {
            // 转换资产小小类字典值
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                dto.setAssetSubSubCategory(DictConvertUtil.convertValueToLabel(dto.getAssetSubSubCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换债券类型字典值
            if (StringUtils.isNotEmpty(dto.getBondType())) {
                dto.setBondType(DictConvertUtil.convertValueToLabel(dto.getBondType(), "ast_bond_type"));
            }
            // 转换会计分类类型字典值
            if (StringUtils.isNotEmpty(dto.getAccountingClassification())) {
                dto.setAccountingClassification(DictConvertUtil.convertValueToLabel(dto.getAccountingClassification(), "ast_accounting_classification"));
            }
            // 转换信用评级字典值
            if (StringUtils.isNotEmpty(dto.getCreditRating())) {
                dto.setCreditRating(DictConvertUtil.convertValueToLabel(dto.getCreditRating(), "ast_credit_rating"));
            }
            // 转换资产流动性分类字典值
            if (StringUtils.isNotEmpty(dto.getAssetLiquidityCategory())) {
                dto.setAssetLiquidityCategory(DictConvertUtil.convertValueToLabel(dto.getAssetLiquidityCategory(), "ast_asset_liquidity_category"));
            }
        }
    }
}
