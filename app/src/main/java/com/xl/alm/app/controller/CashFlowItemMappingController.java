package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.CashFlowItemMappingDTO;
import com.xl.alm.app.query.CashFlowItemMappingQuery;
import com.xl.alm.app.service.CashFlowItemMappingService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 现金流项目映射表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/cash/flow/item/mapping")
public class CashFlowItemMappingController extends BaseController {

    @Autowired
    private CashFlowItemMappingService cashFlowItemMappingService;

    /**
     * 查询现金流项目映射表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(CashFlowItemMappingQuery cashFlowItemMappingQuery) {
        startPage();
        List<CashFlowItemMappingDTO> list = cashFlowItemMappingService.selectCashFlowItemMappingDtoList(cashFlowItemMappingQuery);
        return getDataTable(list);
    }

    /**
     * 导出现金流项目映射表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:export')")
    @Log(title = "现金流项目映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CashFlowItemMappingQuery cashFlowItemMappingQuery) {
        List<CashFlowItemMappingDTO> list = cashFlowItemMappingService.selectCashFlowItemMappingDtoList(cashFlowItemMappingQuery);
        ExcelUtil<CashFlowItemMappingDTO> util = new ExcelUtil<>(CashFlowItemMappingDTO.class);
        util.exportExcel(list, "现金流项目映射表数据", response);
    }

    /**
     * 获取现金流项目映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(cashFlowItemMappingService.selectCashFlowItemMappingDtoById(id));
    }

    /**
     * 新增现金流项目映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:add')")
    @Log(title = "现金流项目映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody CashFlowItemMappingDTO cashFlowItemMappingDto) {
        return toAjax(cashFlowItemMappingService.insertCashFlowItemMappingDto(cashFlowItemMappingDto));
    }

    /**
     * 修改现金流项目映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:edit')")
    @Log(title = "现金流项目映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody CashFlowItemMappingDTO cashFlowItemMappingDto) {
        return toAjax(cashFlowItemMappingService.updateCashFlowItemMappingDto(cashFlowItemMappingDto));
    }

    /**
     * 删除现金流项目映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:remove')")
    @Log(title = "现金流项目映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(cashFlowItemMappingService.deleteCashFlowItemMappingDtoByIds(ids));
    }

    /**
     * 获取现金流项目映射表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CashFlowItemMappingDTO> util = new ExcelUtil<>(CashFlowItemMappingDTO.class);
        util.exportTemplateExcel(response, "现金流项目映射表");
    }

    /**
     * 导入现金流项目映射表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:item:mapping:import')")
    @Log(title = "现金流项目映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CashFlowItemMappingDTO> util = new ExcelUtil<>(CashFlowItemMappingDTO.class);
        List<CashFlowItemMappingDTO> cashFlowItemMappingList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = cashFlowItemMappingService.importCashFlowItemMappingDto(cashFlowItemMappingList, updateSupport, operName);
        return Result.success(message);
    }
}
