package com.xl.alm.app.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 年度折现曲线表DTO
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnnualDiscountCurveDTO extends BaseDTO {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账期
     */
    @NotBlank(message = "账期不能为空")
    @Size(max = 6, message = "账期长度不能超过6个字符")
    @Excel(name = "账期")
    @ExcelProperty("账期")
    private String accountPeriod;

    /**
     * 日期类型
     */
    @NotBlank(message = "日期类型不能为空")
    @Size(max = 20, message = "日期类型长度不能超过20个字符")
    @Excel(name = "日期类型", dictType = "adur_date_type")
    @ExcelProperty("日期类型")
    private String dateType;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    @Excel(name = "日期", dateFormat = "yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date date;

    /**
     * 资产编号
     */
    @NotBlank(message = "资产编号不能为空")
    @Size(max = 20, message = "资产编号长度不能超过20个字符")
    @Excel(name = "资产编号")
    @ExcelProperty("资产编号")
    private String assetNumber;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    @Size(max = 50, message = "账户名称长度不能超过50个字符")
    @Excel(name = "账户名称", dictType = "adur_account_name")
    @ExcelProperty("账户名称")
    private String accountName;

    /**
     * 资产名称
     */
    @NotBlank(message = "资产名称不能为空")
    @Size(max = 100, message = "资产名称长度不能超过100个字符")
    @Excel(name = "资产名称")
    @ExcelProperty("资产名称")
    private String assetName;

    /**
     * 证券代码
     */
    @NotBlank(message = "证券代码不能为空")
    @Size(max = 20, message = "证券代码长度不能超过20个字符")
    @Excel(name = "证券代码")
    @ExcelProperty("证券代码")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @NotBlank(message = "折现曲线标识不能为空")
    @Size(max = 10, message = "折现曲线标识长度不能超过10个字符")
    @Excel(name = "折现曲线标识")
    @ExcelProperty("折现曲线标识")
    private String curveId;

    // 期限字段 term_0 到 term_50
    @Excel(name = "期限0")
    @ExcelProperty("期限0")
    private BigDecimal term0;

    @Excel(name = "期限1")
    @ExcelProperty("期限1")
    private BigDecimal term1;

    @Excel(name = "期限2")
    @ExcelProperty("期限2")
    private BigDecimal term2;

    @Excel(name = "期限3")
    @ExcelProperty("期限3")
    private BigDecimal term3;

    @Excel(name = "期限4")
    @ExcelProperty("期限4")
    private BigDecimal term4;

    @Excel(name = "期限5")
    @ExcelProperty("期限5")
    private BigDecimal term5;

    @Excel(name = "期限6")
    @ExcelProperty("期限6")
    private BigDecimal term6;

    @Excel(name = "期限7")
    @ExcelProperty("期限7")
    private BigDecimal term7;

    @Excel(name = "期限8")
    @ExcelProperty("期限8")
    private BigDecimal term8;

    @Excel(name = "期限9")
    @ExcelProperty("期限9")
    private BigDecimal term9;

    @Excel(name = "期限10")
    @ExcelProperty("期限10")
    private BigDecimal term10;

    @Excel(name = "期限11")
    @ExcelProperty("期限11")
    private BigDecimal term11;

    @Excel(name = "期限12")
    @ExcelProperty("期限12")
    private BigDecimal term12;

    @Excel(name = "期限13")
    @ExcelProperty("期限13")
    private BigDecimal term13;

    @Excel(name = "期限14")
    @ExcelProperty("期限14")
    private BigDecimal term14;

    @Excel(name = "期限15")
    @ExcelProperty("期限15")
    private BigDecimal term15;

    @Excel(name = "期限16")
    @ExcelProperty("期限16")
    private BigDecimal term16;

    @Excel(name = "期限17")
    @ExcelProperty("期限17")
    private BigDecimal term17;

    @Excel(name = "期限18")
    @ExcelProperty("期限18")
    private BigDecimal term18;

    @Excel(name = "期限19")
    @ExcelProperty("期限19")
    private BigDecimal term19;

    @Excel(name = "期限20")
    @ExcelProperty("期限20")
    private BigDecimal term20;

    @Excel(name = "期限21")
    @ExcelProperty("期限21")
    private BigDecimal term21;

    @Excel(name = "期限22")
    @ExcelProperty("期限22")
    private BigDecimal term22;

    @Excel(name = "期限23")
    @ExcelProperty("期限23")
    private BigDecimal term23;

    @Excel(name = "期限24")
    @ExcelProperty("期限24")
    private BigDecimal term24;

    @Excel(name = "期限25")
    @ExcelProperty("期限25")
    private BigDecimal term25;

    @Excel(name = "期限26")
    @ExcelProperty("期限26")
    private BigDecimal term26;

    @Excel(name = "期限27")
    @ExcelProperty("期限27")
    private BigDecimal term27;

    @Excel(name = "期限28")
    @ExcelProperty("期限28")
    private BigDecimal term28;

    @Excel(name = "期限29")
    @ExcelProperty("期限29")
    private BigDecimal term29;

    @Excel(name = "期限30")
    @ExcelProperty("期限30")
    private BigDecimal term30;

    @Excel(name = "期限31")
    @ExcelProperty("期限31")
    private BigDecimal term31;

    @Excel(name = "期限32")
    @ExcelProperty("期限32")
    private BigDecimal term32;

    @Excel(name = "期限33")
    @ExcelProperty("期限33")
    private BigDecimal term33;

    @Excel(name = "期限34")
    @ExcelProperty("期限34")
    private BigDecimal term34;

    @Excel(name = "期限35")
    @ExcelProperty("期限35")
    private BigDecimal term35;

    @Excel(name = "期限36")
    @ExcelProperty("期限36")
    private BigDecimal term36;

    @Excel(name = "期限37")
    @ExcelProperty("期限37")
    private BigDecimal term37;

    @Excel(name = "期限38")
    @ExcelProperty("期限38")
    private BigDecimal term38;

    @Excel(name = "期限39")
    @ExcelProperty("期限39")
    private BigDecimal term39;

    @Excel(name = "期限40")
    @ExcelProperty("期限40")
    private BigDecimal term40;

    @Excel(name = "期限41")
    @ExcelProperty("期限41")
    private BigDecimal term41;

    @Excel(name = "期限42")
    @ExcelProperty("期限42")
    private BigDecimal term42;

    @Excel(name = "期限43")
    @ExcelProperty("期限43")
    private BigDecimal term43;

    @Excel(name = "期限44")
    @ExcelProperty("期限44")
    private BigDecimal term44;

    @Excel(name = "期限45")
    @ExcelProperty("期限45")
    private BigDecimal term45;

    @Excel(name = "期限46")
    @ExcelProperty("期限46")
    private BigDecimal term46;

    @Excel(name = "期限47")
    @ExcelProperty("期限47")
    private BigDecimal term47;

    @Excel(name = "期限48")
    @ExcelProperty("期限48")
    private BigDecimal term48;

    @Excel(name = "期限49")
    @ExcelProperty("期限49")
    private BigDecimal term49;

    @Excel(name = "期限50")
    @ExcelProperty("期限50")
    private BigDecimal term50;

    /**
     * 是否删除，0:否，1:是
     */
    private Integer isDel = 0;
}
