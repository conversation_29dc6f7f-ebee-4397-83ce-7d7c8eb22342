package com.xl.alm.app.service;

import com.xl.alm.app.dto.DepositInterbankCdDTO;
import com.xl.alm.app.query.DepositInterbankCdQuery;

import java.util.List;

/**
 * 存款及同业存单表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface DepositInterbankCdService {

    /**
     * 查询存款及同业存单表列表
     *
     * @param depositInterbankCdQuery 存款及同业存单表查询条件
     * @return 存款及同业存单表列表
     */
    List<DepositInterbankCdDTO> selectDepositInterbankCdDtoList(DepositInterbankCdQuery depositInterbankCdQuery);

    /**
     * 用id查询存款及同业存单表
     *
     * @param id id
     * @return 存款及同业存单表
     */
    DepositInterbankCdDTO selectDepositInterbankCdDtoById(Long id);

    /**
     * 根据账期、资产小小类和银行分类查询存款及同业存单表
     *
     * @param accountingPeriod 账期
     * @param assetSubSubCategory 资产小小类
     * @param bankClassification 银行分类
     * @return 存款及同业存单表
     */
    DepositInterbankCdDTO selectDepositInterbankCdDtoByCondition(String accountingPeriod, String assetSubSubCategory, String bankClassification);

    /**
     * 新增存款及同业存单表
     *
     * @param depositInterbankCdDTO 存款及同业存单表
     * @return 结果
     */
    int insertDepositInterbankCdDto(DepositInterbankCdDTO depositInterbankCdDTO);

    /**
     * 批量新增存款及同业存单表
     *
     * @param depositInterbankCdDtoList 存款及同业存单表列表
     * @return 影响行数
     */
    int batchInsertDepositInterbankCdDto(List<DepositInterbankCdDTO> depositInterbankCdDtoList);

    /**
     * 修改存款及同业存单表
     *
     * @param depositInterbankCdDTO 存款及同业存单表
     * @return 结果
     */
    int updateDepositInterbankCdDto(DepositInterbankCdDTO depositInterbankCdDTO);

    /**
     * 批量删除存款及同业存单表
     *
     * @param ids 需要删除的存款及同业存单表主键集合
     * @return 结果
     */
    int deleteDepositInterbankCdDtoByIds(Long[] ids);

    /**
     * 删除存款及同业存单表信息
     *
     * @param id 存款及同业存单表主键
     * @return 结果
     */
    int deleteDepositInterbankCdDtoById(Long id);

    /**
     * 导入存款及同业存单表数据
     *
     * @param depositInterbankCdDtoList 存款及同业存单表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importDepositInterbankCdDto(List<DepositInterbankCdDTO> depositInterbankCdDtoList, Boolean isUpdateSupport, String operName);
}
