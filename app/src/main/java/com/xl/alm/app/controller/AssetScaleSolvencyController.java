package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AssetScaleSolvencyDTO;
import com.xl.alm.app.query.AssetScaleSolvencyQuery;
import com.xl.alm.app.service.AssetScaleSolvencyService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 资产规模与偿付能力表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/asset/scale/solvency")
public class AssetScaleSolvencyController extends BaseController {

    @Autowired
    private AssetScaleSolvencyService assetScaleSolvencyService;

    /**
     * 查询资产规模与偿付能力表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetScaleSolvencyQuery assetScaleSolvencyQuery) {
        startPage();
        List<AssetScaleSolvencyDTO> list = assetScaleSolvencyService.selectAssetScaleSolvencyDtoList(assetScaleSolvencyQuery);
        return getDataTable(list);
    }

    /**
     * 获取资产规模与偿付能力表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetScaleSolvencyService.selectAssetScaleSolvencyDtoById(id));
    }

    /**
     * 根据条件查询资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("itemName") String itemName) {
        return Result.success(assetScaleSolvencyService.selectAssetScaleSolvencyDtoByCondition(accountingPeriod, itemName));
    }

    /**
     * 新增资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:add')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AssetScaleSolvencyDTO assetScaleSolvencyDTO) {
        return toAjax(assetScaleSolvencyService.insertAssetScaleSolvencyDto(assetScaleSolvencyDTO));
    }

    /**
     * 修改资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:edit')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AssetScaleSolvencyDTO assetScaleSolvencyDTO) {
        return toAjax(assetScaleSolvencyService.updateAssetScaleSolvencyDto(assetScaleSolvencyDTO));
    }

    /**
     * 删除资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:remove')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetScaleSolvencyService.deleteAssetScaleSolvencyDtoByIds(ids));
    }

    /**
     * 计算资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:calculate')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.OTHER)
    @PostMapping("/calculate")
    public Result calculate(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("lastYearPeriod") String lastYearPeriod,
            @RequestParam("lastQuarterPeriod") String lastQuarterPeriod) {
        String operName = getUsername();
        String message = assetScaleSolvencyService.calculateAssetScaleSolvency(accountingPeriod, lastYearPeriod, lastQuarterPeriod, operName);
        return Result.success(message);
    }

    /**
     * 导入资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:import')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetScaleSolvencyDTO> util = new ExcelUtil<>(AssetScaleSolvencyDTO.class);
        List<AssetScaleSolvencyDTO> assetScaleSolvencyList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = assetScaleSolvencyService.importAssetScaleSolvencyDto(assetScaleSolvencyList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 导出资产规模与偿付能力表
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:export')")
    @Log(title = "资产规模与偿付能力表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetScaleSolvencyQuery query) {
        List<AssetScaleSolvencyDTO> list = assetScaleSolvencyService.selectAssetScaleSolvencyDtoList(query);
        ExcelUtil<AssetScaleSolvencyDTO> util = new ExcelUtil<>(AssetScaleSolvencyDTO.class);
        util.exportExcel(list, "资产规模与偿付能力表数据", response);
    }

    /**
     * 获取资产规模与偿付能力表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('asm:asset:scale:solvency:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetScaleSolvencyDTO> util = new ExcelUtil<>(AssetScaleSolvencyDTO.class);
        util.exportTemplateExcel(response, "资产规模与偿付能力表");
    }
}
