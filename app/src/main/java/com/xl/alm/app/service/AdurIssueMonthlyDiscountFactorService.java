package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurIssueMonthlyDiscountFactorDTO;
import com.xl.alm.app.query.AdurIssueMonthlyDiscountFactorQuery;

import java.util.List;

/**
 * ADUR关键久期折现因子表含价差(第二个) Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurIssueMonthlyDiscountFactorService {

    /**
     * 查询ADUR关键久期折现因子表含价差(第二个)列表
     *
     * @param adurIssueMonthlyDiscountFactorQuery ADUR关键久期折现因子表含价差(第二个)查询条件
     * @return ADUR关键久期折现因子表含价差(第二个)列表
     */
    List<AdurIssueMonthlyDiscountFactorDTO> selectAdurIssueMonthlyDiscountFactorDtoList(AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery);

    /**
     * 用id查询ADUR关键久期折现因子表含价差(第二个)
     *
     * @param id id
     * @return ADUR关键久期折现因子表含价差(第二个)
     */
    AdurIssueMonthlyDiscountFactorDTO selectAdurIssueMonthlyDiscountFactorDtoById(Long id);

    /**
     * 根据条件查询ADUR关键久期折现因子表含价差(第二个)
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现因子表含价差(第二个)
     */
    AdurIssueMonthlyDiscountFactorDTO selectAdurIssueMonthlyDiscountFactorDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection);

    /**
     * 新增ADUR关键久期折现因子表含价差(第二个)
     *
     * @param adurIssueMonthlyDiscountFactorDTO ADUR关键久期折现因子表含价差(第二个)
     * @return 结果
     */
    int insertAdurIssueMonthlyDiscountFactorDto(AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO);

    /**
     * 批量插入ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param adurIssueMonthlyDiscountFactorDtoList ADUR关键久期折现因子表含价差(第二个)列表
     * @return 影响行数
     */
    int batchInsertAdurIssueMonthlyDiscountFactorDto(List<AdurIssueMonthlyDiscountFactorDTO> adurIssueMonthlyDiscountFactorDtoList);

    /**
     * 更新ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param dto ADUR关键久期折现因子表含价差(第二个)
     * @return 结果
     */
    int updateAdurIssueMonthlyDiscountFactorDto(AdurIssueMonthlyDiscountFactorDTO dto);

    /**
     * 删除指定id的ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurIssueMonthlyDiscountFactorDtoById(Long id);

    /**
     * 批量删除ADUR关键久期折现因子表含价差(第二个)
     *
     * @param ids 需要删除的ADUR关键久期折现因子表含价差(第二个)主键
     * @return 结果
     */
    int deleteAdurIssueMonthlyDiscountFactorDtoByIds(Long[] ids);

    /**
     * 导入ADUR关键久期折现因子表含价差(第二个)
     *
     * @param dtoList       ADUR关键久期折现因子表含价差(第二个)数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAdurIssueMonthlyDiscountFactorDto(List<AdurIssueMonthlyDiscountFactorDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 根据账期删除ADUR关键久期折现因子表含价差(第二个)
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurIssueMonthlyDiscountFactorDtoByAccountPeriod(String accountPeriod);
}
