package com.xl.alm.app.service;

import com.xl.alm.app.dto.LiabScaleSummaryDTO;
import com.xl.alm.app.query.LiabScaleSummaryQuery;

import java.util.List;

/**
 * 负债规模汇总表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface LiabScaleSummaryService {

    /**
     * 查询负债规模汇总表列表
     *
     * @param liabScaleSummaryQuery 负债规模汇总表查询条件
     * @return 负债规模汇总表列表
     */
    List<LiabScaleSummaryDTO> selectLiabScaleSummaryDtoList(LiabScaleSummaryQuery liabScaleSummaryQuery);

    /**
     * 用id查询负债规模汇总表
     *
     * @param id id
     * @return 负债规模汇总表
     */
    LiabScaleSummaryDTO selectLiabScaleSummaryDtoById(Long id);

    /**
     * 根据账期和设计类型查询负债规模汇总表
     *
     * @param accountingPeriod 账期
     * @param designType       设计类型
     * @return 负债规模汇总表
     */
    LiabScaleSummaryDTO selectLiabScaleSummaryDtoByCondition(
            String accountingPeriod,
            String designType);

    /**
     * 新增负债规模汇总表
     *
     * @param dto 负债规模汇总表
     * @return 结果
     */
    int addLiabScaleSummaryDto(LiabScaleSummaryDTO dto);

    /**
     * 修改负债规模汇总表
     *
     * @param dto 负债规模汇总表
     * @return 结果
     */
    int updateLiabScaleSummaryDto(LiabScaleSummaryDTO dto);

    /**
     * 批量删除负债规模汇总表
     *
     * @param ids 需要删除的负债规模汇总表主键集合
     * @return 结果
     */
    int deleteLiabScaleSummaryDtoByIds(Long[] ids);

    /**
     * 删除负债规模汇总表信息
     *
     * @param id 负债规模汇总表主键
     * @return 结果
     */
    int deleteLiabScaleSummaryDtoById(Long id);

    /**
     * 批量插入负债规模汇总表数据
     *
     * @param liabScaleSummaryDtoList 负债规模汇总表列表
     * @return 影响行数
     */
    int batchInsertLiabScaleSummaryDto(List<LiabScaleSummaryDTO> liabScaleSummaryDtoList);

    /**
     * 删除指定账期的负债规模汇总表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteLiabScaleSummaryDtoByPeriod(String accountingPeriod);

    /**
     * 导入负债规模汇总表
     *
     * @param dtoList       负债规模汇总表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importLiabScaleSummaryDto(List<LiabScaleSummaryDTO> dtoList, Boolean updateSupport, String username);
}
