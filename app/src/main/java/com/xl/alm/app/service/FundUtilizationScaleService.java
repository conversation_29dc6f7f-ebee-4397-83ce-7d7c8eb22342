package com.xl.alm.app.service;

import com.xl.alm.app.dto.FundUtilizationScaleDTO;
import com.xl.alm.app.query.FundUtilizationScaleQuery;

import java.util.List;

/**
 * 资金运用规模表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FundUtilizationScaleService {

    /**
     * 查询资金运用规模表列表
     *
     * @param fundUtilizationScaleQuery 资金运用规模表查询条件
     * @return 资金运用规模表列表
     */
    List<FundUtilizationScaleDTO> selectFundUtilizationScaleDtoList(FundUtilizationScaleQuery fundUtilizationScaleQuery);

    /**
     * 用id查询资金运用规模表
     *
     * @param id id
     * @return 资金运用规模表
     */
    FundUtilizationScaleDTO selectFundUtilizationScaleDtoById(Long id);

    /**
     * 根据账期、项目名称、项目分级标识和数据类型查询资金运用规模表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @param itemClassificationLevel 项目分级标识
     * @param dataType 数据类型
     * @return 资金运用规模表
     */
    FundUtilizationScaleDTO selectFundUtilizationScaleDtoByCondition(String accountingPeriod, String itemName, String itemClassificationLevel, String dataType);

    /**
     * 新增资金运用规模表
     *
     * @param fundUtilizationScaleDTO 资金运用规模表
     * @return 结果
     */
    int insertFundUtilizationScaleDto(FundUtilizationScaleDTO fundUtilizationScaleDTO);

    /**
     * 批量新增资金运用规模表
     *
     * @param fundUtilizationScaleDtoList 资金运用规模表列表
     * @return 影响行数
     */
    int batchInsertFundUtilizationScaleDto(List<FundUtilizationScaleDTO> fundUtilizationScaleDtoList);

    /**
     * 更新资金运用规模表数据
     *
     * @param fundUtilizationScaleDTO 资金运用规模表
     * @return 结果
     */
    int updateFundUtilizationScaleDto(FundUtilizationScaleDTO fundUtilizationScaleDTO);

    /**
     * 删除指定id的资金运用规模表数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteFundUtilizationScaleDtoById(Long id);

    /**
     * 批量删除资金运用规模表
     *
     * @param ids 需要删除的资金运用规模表主键集合
     * @return 结果
     */
    int deleteFundUtilizationScaleDtoByIds(Long[] ids);

    /**
     * 删除指定账期的资金运用规模表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteFundUtilizationScaleDtoByPeriod(String accountingPeriod);

    /**
     * 导入资金运用规模表
     *
     * @param dtoList       资金运用规模表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importFundUtilizationScaleDto(List<FundUtilizationScaleDTO> dtoList, Boolean updateSupport, String username);
}
