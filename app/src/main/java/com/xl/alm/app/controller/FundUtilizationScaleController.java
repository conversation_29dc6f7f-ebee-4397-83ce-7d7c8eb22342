package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.FundUtilizationScaleDTO;
import com.xl.alm.app.query.FundUtilizationScaleQuery;
import com.xl.alm.app.service.FundUtilizationScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 资金运用规模表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/fund/utilization/scale")
public class FundUtilizationScaleController extends BaseController {

    @Autowired
    private FundUtilizationScaleService fundUtilizationScaleService;

    /**
     * 查询资金运用规模表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:list')")
    @GetMapping("/list")
    public TableDataInfo list(FundUtilizationScaleQuery fundUtilizationScaleQuery) {
        startPage();
        List<FundUtilizationScaleDTO> list = fundUtilizationScaleService.selectFundUtilizationScaleDtoList(fundUtilizationScaleQuery);
        return getDataTable(list);
    }

    /**
     * 导出资金运用规模表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:export')")
    @Log(title = "资金运用规模表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FundUtilizationScaleQuery fundUtilizationScaleQuery) {
        List<FundUtilizationScaleDTO> list = fundUtilizationScaleService.selectFundUtilizationScaleDtoList(fundUtilizationScaleQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        ExcelUtil<FundUtilizationScaleDTO> util = new ExcelUtil<>(FundUtilizationScaleDTO.class);
        util.exportExcel(list, "资金运用规模表数据", response);
    }

    /**
     * 获取资金运用规模表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fundUtilizationScaleService.selectFundUtilizationScaleDtoById(id));
    }

    /**
     * 新增资金运用规模表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:add')")
    @Log(title = "资金运用规模表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FundUtilizationScaleDTO fundUtilizationScaleDTO) {
        return toAjax(fundUtilizationScaleService.insertFundUtilizationScaleDto(fundUtilizationScaleDTO));
    }

    /**
     * 修改资金运用规模表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:edit')")
    @Log(title = "资金运用规模表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FundUtilizationScaleDTO fundUtilizationScaleDTO) {
        return toAjax(fundUtilizationScaleService.updateFundUtilizationScaleDto(fundUtilizationScaleDTO));
    }

    /**
     * 删除资金运用规模表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:remove')")
    @Log(title = "资金运用规模表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fundUtilizationScaleService.deleteFundUtilizationScaleDtoByIds(ids));
    }

    /**
     * 获取导入模板
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FundUtilizationScaleDTO> util = new ExcelUtil<>(FundUtilizationScaleDTO.class);
        util.exportTemplateExcel(response, "资金运用规模表");
    }

    /**
     * 导入资金运用规模表数据
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:scale:import')")
    @Log(title = "资金运用规模表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FundUtilizationScaleDTO> util = new ExcelUtil<>(FundUtilizationScaleDTO.class);
        List<FundUtilizationScaleDTO> fundUtilizationScaleList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = fundUtilizationScaleService.importFundUtilizationScaleDto(fundUtilizationScaleList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资金运用规模表数据列表
     */
    private void convertDictValueToLabel(List<FundUtilizationScaleDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FundUtilizationScaleDTO dto : list) {
            // 转换数据类型字典值
            if (dto.getDataType() != null) {
                dto.setDataType(DictConvertUtil.convertValueToLabel(dto.getDataType(), "acm_data_type"));
            }
        }
    }
}
