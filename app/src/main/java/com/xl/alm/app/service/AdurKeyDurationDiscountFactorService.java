package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationDiscountFactorDTO;
import com.xl.alm.app.query.AdurKeyDurationDiscountFactorQuery;

import java.util.List;

/**
 * ADUR关键久期折现因子表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurKeyDurationDiscountFactorService {

    /**
     * 查询ADUR关键久期折现因子表含价差列表
     *
     * @param adurKeyDurationDiscountFactorQuery ADUR关键久期折现因子表含价差查询条件
     * @return ADUR关键久期折现因子表含价差列表
     */
    List<AdurKeyDurationDiscountFactorDTO> selectAdurKeyDurationDiscountFactorDtoList(AdurKeyDurationDiscountFactorQuery adurKeyDurationDiscountFactorQuery);

    /**
     * 用id查询ADUR关键久期折现因子表含价差
     *
     * @param id id
     * @return ADUR关键久期折现因子表含价差
     */
    AdurKeyDurationDiscountFactorDTO selectAdurKeyDurationDiscountFactorDtoById(Long id);

    /**
     * 根据条件查询ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现因子表含价差
     */
    AdurKeyDurationDiscountFactorDTO selectAdurKeyDurationDiscountFactorDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection);

    /**
     * 新增ADUR关键久期折现因子表含价差
     *
     * @param adurKeyDurationDiscountFactorDTO ADUR关键久期折现因子表含价差
     * @return 结果
     */
    int insertAdurKeyDurationDiscountFactorDto(AdurKeyDurationDiscountFactorDTO adurKeyDurationDiscountFactorDTO);

    /**
     * 批量插入ADUR关键久期折现因子表含价差数据
     *
     * @param adurKeyDurationDiscountFactorDtoList ADUR关键久期折现因子表含价差列表
     * @return 影响行数
     */
    int batchInsertAdurKeyDurationDiscountFactorDto(List<AdurKeyDurationDiscountFactorDTO> adurKeyDurationDiscountFactorDtoList);

    /**
     * 更新ADUR关键久期折现因子表含价差数据
     *
     * @param dto ADUR关键久期折现因子表含价差
     * @return 结果
     */
    int updateAdurKeyDurationDiscountFactorDto(AdurKeyDurationDiscountFactorDTO dto);

    /**
     * 删除指定id的ADUR关键久期折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurKeyDurationDiscountFactorDtoById(Long id);

    /**
     * 批量删除ADUR关键久期折现因子表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现因子表含价差主键
     * @return 结果
     */
    int deleteAdurKeyDurationDiscountFactorDtoByIds(Long[] ids);

    /**
     * 导入ADUR关键久期折现因子表含价差
     *
     * @param dtoList       ADUR关键久期折现因子表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAdurKeyDurationDiscountFactorDto(List<AdurKeyDurationDiscountFactorDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 根据账期删除ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurKeyDurationDiscountFactorDtoByAccountPeriod(String accountPeriod);
}
