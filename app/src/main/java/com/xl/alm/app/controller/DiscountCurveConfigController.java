package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.DiscountCurveConfigDTO;
import com.xl.alm.app.query.DiscountCurveConfigQuery;
import com.xl.alm.app.service.DiscountCurveConfigService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 折现曲线配置表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/discount/curve/config")
public class DiscountCurveConfigController extends BaseController {

    @Autowired
    private DiscountCurveConfigService discountCurveConfigService;

    /**
     * 查询折现曲线配置表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(DiscountCurveConfigQuery discountCurveConfigQuery) {
        startPage();
        List<DiscountCurveConfigDTO> list = discountCurveConfigService.selectDiscountCurveConfigDtoList(discountCurveConfigQuery);
        return getDataTable(list);
    }

    /**
     * 获取折现曲线配置表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(discountCurveConfigService.selectDiscountCurveConfigDtoById(id));
    }

    /**
     * 新增折现曲线配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:add')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody DiscountCurveConfigDTO discountCurveConfigDTO) {
        return toAjax(discountCurveConfigService.insertDiscountCurveConfigDto(discountCurveConfigDTO));
    }

    /**
     * 修改折现曲线配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:edit')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody DiscountCurveConfigDTO discountCurveConfigDTO) {
        return toAjax(discountCurveConfigService.updateDiscountCurveConfigDto(discountCurveConfigDTO));
    }

    /**
     * 删除折现曲线配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:remove')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(discountCurveConfigService.deleteDiscountCurveConfigDtoByIds(ids));
    }

    /**
     * 批量新增折现曲线配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:add')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<DiscountCurveConfigDTO> discountCurveConfigDTOList) {
        return toAjax(discountCurveConfigService.batchInsertDiscountCurveConfigDto(discountCurveConfigDTOList));
    }

    /**
     * 根据账期删除折现曲线配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:remove')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(discountCurveConfigService.deleteDiscountCurveConfigDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入折现曲线配置表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:import')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DiscountCurveConfigDTO> util = new ExcelUtil(DiscountCurveConfigDTO.class);
        List<DiscountCurveConfigDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = discountCurveConfigService.importDiscountCurveConfigDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出折现曲线配置表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:export')")
    @Log(title = "折现曲线配置表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DiscountCurveConfigQuery query) {
        ExcelUtil<DiscountCurveConfigDTO> util = new ExcelUtil<>(DiscountCurveConfigDTO.class);
        List<DiscountCurveConfigDTO> list = discountCurveConfigService.selectDiscountCurveConfigDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "折现曲线配置表数据", response);
    }

    /**
     * 获取折现曲线配置表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:discount:curve:config:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DiscountCurveConfigDTO> util = new ExcelUtil<>(DiscountCurveConfigDTO.class);
        util.exportTemplateExcel(response, "折现曲线配置表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 折现曲线配置表数据列表
     */
    private void convertDictValueToLabel(List<DiscountCurveConfigDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (DiscountCurveConfigDTO dto : list) {
            // 转换资产小小类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                dto.setAssetSubSubCategory(assetSubSubCategoryLabel);
            }

            // 转换折现曲线使用评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getDiscountCurveRating())) {
                String discountCurveRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDiscountCurveRating(),
                        "ast_credit_rating"
                );
                dto.setDiscountCurveRating(discountCurveRatingLabel);
            }

            // 注意：折现曲线标识字段是Integer类型，Excel注解中已经配置了dictType，
            // RuoYi框架会自动处理字典值到标签的转换，无需手动处理
        }
    }
}
