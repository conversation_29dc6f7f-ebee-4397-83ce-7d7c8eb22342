package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.PremiumIncomeDetailDTO;
import com.xl.alm.app.query.PremiumIncomeDetailQuery;
import com.xl.alm.app.service.PremiumIncomeDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 保费收入明细表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cost/premium/income/detail")
public class PremiumIncomeDetailController extends BaseController {

    @Autowired
    private PremiumIncomeDetailService premiumIncomeDetailService;

    /**
     * 查询保费收入明细列表
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(PremiumIncomeDetailQuery premiumIncomeDetailQuery) {
        startPage();
        List<PremiumIncomeDetailDTO> list = premiumIncomeDetailService.selectPremiumIncomeDetailDtoList(premiumIncomeDetailQuery);
        return getDataTable(list);
    }

    /**
     * 导出保费收入明细列表
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:export')")
    @Log(title = "保费收入明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PremiumIncomeDetailQuery premiumIncomeDetailQuery) {
        List<PremiumIncomeDetailDTO> list = premiumIncomeDetailService.selectPremiumIncomeDetailDtoList(premiumIncomeDetailQuery);
        ExcelUtil<PremiumIncomeDetailDTO> util = new ExcelUtil<PremiumIncomeDetailDTO>(PremiumIncomeDetailDTO.class);
        util.exportExcel(list, "保费收入明细数据", response);
    }

    /**
     * 获取保费收入明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(premiumIncomeDetailService.selectPremiumIncomeDetailDtoById(id));
    }

    /**
     * 新增保费收入明细
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:add')")
    @Log(title = "保费收入明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody PremiumIncomeDetailDTO premiumIncomeDetailDTO) {
        return toAjax(premiumIncomeDetailService.insertPremiumIncomeDetailDto(premiumIncomeDetailDTO));
    }

    /**
     * 批量新增保费收入明细
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:add')")
    @Log(title = "保费收入明细", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<PremiumIncomeDetailDTO> premiumIncomeDetailDtoList) {
        return toAjax(premiumIncomeDetailService.batchInsertPremiumIncomeDetailDto(premiumIncomeDetailDtoList));
    }

    /**
     * 修改保费收入明细
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:edit')")
    @Log(title = "保费收入明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody PremiumIncomeDetailDTO premiumIncomeDetailDTO) {
        return toAjax(premiumIncomeDetailService.updatePremiumIncomeDetailDto(premiumIncomeDetailDTO));
    }

    /**
     * 删除保费收入明细
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:remove')")
    @Log(title = "保费收入明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(premiumIncomeDetailService.deletePremiumIncomeDetailDtoByIds(ids));
    }

    /**
     * 删除指定账期的保费收入明细
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:remove')")
    @Log(title = "保费收入明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(premiumIncomeDetailService.deletePremiumIncomeDetailDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入保费收入明细数据
     */
    @PreAuthorize("@ss.hasPermi('cost:premium:income:detail:import')")
    @Log(title = "保费收入明细", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<PremiumIncomeDetailDTO> util = new ExcelUtil<PremiumIncomeDetailDTO>(PremiumIncomeDetailDTO.class);
        List<PremiumIncomeDetailDTO> premiumIncomeDetailList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        int result = premiumIncomeDetailService.batchInsertPremiumIncomeDetailDto(premiumIncomeDetailList);
        String message = "导入成功，共导入 " + result + " 条数据";
        return Result.success(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PremiumIncomeDetailDTO> util = new ExcelUtil<PremiumIncomeDetailDTO>(PremiumIncomeDetailDTO.class);
        util.exportTemplateExcel(response, "保费收入明细数据");
    }
}
