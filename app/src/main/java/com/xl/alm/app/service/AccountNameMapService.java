package com.xl.alm.app.service;

import com.xl.alm.app.dto.AccountNameMapDTO;
import com.xl.alm.app.query.AccountNameMapQuery;

import java.util.List;

/**
 * 账户名称映射表Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface AccountNameMapService {

    /**
     * 查询账户名称映射表
     *
     * @param id 账户名称映射表主键
     * @return 账户名称映射表
     */
    AccountNameMapDTO selectAccountNameMapDtoById(Long id);

    /**
     * 查询账户名称映射表列表
     *
     * @param accountNameMapQuery 账户名称映射表查询条件
     * @return 账户名称映射表集合
     */
    List<AccountNameMapDTO> selectAccountNameMapDtoList(AccountNameMapQuery accountNameMapQuery);

    /**
     * 新增账户名称映射表
     *
     * @param accountNameMapDTO 账户名称映射表
     * @return 结果
     */
    int insertAccountNameMapDto(AccountNameMapDTO accountNameMapDTO);

    /**
     * 修改账户名称映射表
     *
     * @param accountNameMapDTO 账户名称映射表
     * @return 结果
     */
    int updateAccountNameMapDto(AccountNameMapDTO accountNameMapDTO);

    /**
     * 批量删除账户名称映射表
     *
     * @param ids 需要删除的账户名称映射表主键集合
     * @return 结果
     */
    int deleteAccountNameMapDtoByIds(Long[] ids);

    /**
     * 删除账户名称映射表信息
     *
     * @param id 账户名称映射表主键
     * @return 结果
     */
    int deleteAccountNameMapDtoById(Long id);

    /**
     * 批量新增账户名称映射表
     *
     * @param accountNameMapDtoList 账户名称映射表列表
     * @return 结果
     */
    int batchInsertAccountNameMapDto(List<AccountNameMapDTO> accountNameMapDtoList);

    /**
     * 导入账户名称映射表数据
     *
     * @param accountNameMapDtoList 账户名称映射表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importAccountNameMapDto(List<AccountNameMapDTO> accountNameMapDtoList, Boolean isUpdateSupport, String operName);
}
