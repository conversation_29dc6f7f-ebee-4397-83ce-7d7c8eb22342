package com.xl.alm.app.service;

import com.xl.alm.app.dto.FixedIncomeTermCatDTO;
import com.xl.alm.app.query.FixedIncomeTermCatQuery;

import java.util.List;

/**
 * 固收资产剩余期限资产分类表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FixedIncomeTermCatService {

    /**
     * 查询固收资产剩余期限资产分类表列表
     *
     * @param fixedIncomeTermCatQuery 固收资产剩余期限资产分类表查询条件
     * @return 固收资产剩余期限资产分类表列表
     */
    List<FixedIncomeTermCatDTO> selectFixedIncomeTermCatDtoList(FixedIncomeTermCatQuery fixedIncomeTermCatQuery);

    /**
     * 根据主键查询固收资产剩余期限资产分类表
     *
     * @param id 主键
     * @return 固收资产剩余期限资产分类表
     */
    FixedIncomeTermCatDTO selectFixedIncomeTermCatDtoById(Long id);

    /**
     * 新增固收资产剩余期限资产分类表
     *
     * @param fixedIncomeTermCatDTO 固收资产剩余期限资产分类表
     * @return 影响行数
     */
    int insertFixedIncomeTermCatDto(FixedIncomeTermCatDTO fixedIncomeTermCatDTO);

    /**
     * 修改固收资产剩余期限资产分类表
     *
     * @param fixedIncomeTermCatDTO 固收资产剩余期限资产分类表
     * @return 影响行数
     */
    int updateFixedIncomeTermCatDto(FixedIncomeTermCatDTO fixedIncomeTermCatDTO);

    /**
     * 删除固收资产剩余期限资产分类表
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteFixedIncomeTermCatDtoById(Long id);

    /**
     * 批量删除固收资产剩余期限资产分类表
     *
     * @param ids 主键数组
     * @return 影响行数
     */
    int deleteFixedIncomeTermCatDtoByIds(Long[] ids);

    /**
     * 批量新增固收资产剩余期限资产分类表
     *
     * @param fixedIncomeTermCatDtoList 固收资产剩余期限资产分类表列表
     * @return 影响行数
     */
    int batchInsertFixedIncomeTermCatDto(List<FixedIncomeTermCatDTO> fixedIncomeTermCatDtoList);

    /**
     * 导入固收资产剩余期限资产分类表数据
     *
     * @param fixedIncomeTermCatDtoList 固收资产剩余期限资产分类表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFixedIncomeTermCatDto(List<FixedIncomeTermCatDTO> fixedIncomeTermCatDtoList, Boolean isUpdateSupport, String operName);
}
