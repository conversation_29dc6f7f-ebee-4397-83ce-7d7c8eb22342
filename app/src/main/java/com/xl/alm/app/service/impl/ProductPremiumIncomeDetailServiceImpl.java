package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.ProductPremiumIncomeDetailDTO;
import com.xl.alm.app.entity.ProductPremiumIncomeDetailEntity;
import com.xl.alm.app.mapper.ProductPremiumIncomeDetailMapper;
import com.xl.alm.app.query.ProductPremiumIncomeDetailQuery;
import com.xl.alm.app.service.ProductPremiumIncomeDetailService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 分产品保费收入表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class ProductPremiumIncomeDetailServiceImpl implements ProductPremiumIncomeDetailService {

    @Autowired
    private ProductPremiumIncomeDetailMapper productPremiumIncomeDetailMapper;

    /**
     * 查询分产品保费收入列表
     *
     * @param productPremiumIncomeDetailQuery 分产品保费收入查询条件
     * @return 分产品保费收入列表
     */
    @Override
    public List<ProductPremiumIncomeDetailDTO> selectProductPremiumIncomeDetailDtoList(ProductPremiumIncomeDetailQuery productPremiumIncomeDetailQuery) {
        List<ProductPremiumIncomeDetailEntity> entityList = productPremiumIncomeDetailMapper.selectProductPremiumIncomeDetailEntityList(productPremiumIncomeDetailQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, ProductPremiumIncomeDetailDTO.class);
    }

    /**
     * 用id查询分产品保费收入
     *
     * @param id id
     * @return 分产品保费收入
     */
    @Override
    public ProductPremiumIncomeDetailDTO selectProductPremiumIncomeDetailDtoById(Long id) {
        ProductPremiumIncomeDetailEntity entity = productPremiumIncomeDetailMapper.selectProductPremiumIncomeDetailEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, ProductPremiumIncomeDetailDTO.class);
    }

    /**
     * 根据统计期间和产品精算代码查询分产品保费收入
     *
     * @param accountingPeriod 统计期间
     * @param actuarialCode 产品精算代码
     * @return 分产品保费收入
     */
    @Override
    public ProductPremiumIncomeDetailDTO selectProductPremiumIncomeDetailDtoByCondition(
            String accountingPeriod,
            String actuarialCode) {
        ProductPremiumIncomeDetailEntity entity = productPremiumIncomeDetailMapper.selectProductPremiumIncomeDetailEntityByCondition(
                accountingPeriod,
                actuarialCode);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, ProductPremiumIncomeDetailDTO.class);
    }

    /**
     * 新增分产品保费收入
     *
     * @param dto 分产品保费收入
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertProductPremiumIncomeDetailDto(ProductPremiumIncomeDetailDTO dto) {
        ProductPremiumIncomeDetailEntity entity = EntityDtoConvertUtil.convertToEntity(dto, ProductPremiumIncomeDetailEntity.class);
        return productPremiumIncomeDetailMapper.insertProductPremiumIncomeDetailEntity(entity);
    }

    /**
     * 批量插入分产品保费收入数据
     *
     * @param productPremiumIncomeDetailDtoList 分产品保费收入列表
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertProductPremiumIncomeDetailDto(List<ProductPremiumIncomeDetailDTO> productPremiumIncomeDetailDtoList) {
        List<ProductPremiumIncomeDetailEntity> entityList = EntityDtoConvertUtil.convertToEntityList(productPremiumIncomeDetailDtoList, ProductPremiumIncomeDetailEntity.class);
        return productPremiumIncomeDetailMapper.batchInsertProductPremiumIncomeDetailEntity(entityList);
    }

    /**
     * 更新分产品保费收入数据
     *
     * @param dto 分产品保费收入
     * @return 结果
     */
    @Override
    public int updateProductPremiumIncomeDetailDto(ProductPremiumIncomeDetailDTO dto) {
        ProductPremiumIncomeDetailEntity entity = EntityDtoConvertUtil.convertToEntity(dto, ProductPremiumIncomeDetailEntity.class);
        return productPremiumIncomeDetailMapper.updateProductPremiumIncomeDetailEntity(entity);
    }

    /**
     * 删除指定统计期间的分产品保费收入数据
     *
     * @param accountingPeriod 统计期间
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProductPremiumIncomeDetailDtoByPeriod(String accountingPeriod) {
        return productPremiumIncomeDetailMapper.deleteProductPremiumIncomeDetailEntityByPeriod(accountingPeriod);
    }

    /**
     * 删除指定id的分产品保费收入数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProductPremiumIncomeDetailDtoById(Long id) {
        return productPremiumIncomeDetailMapper.deleteProductPremiumIncomeDetailEntityById(id);
    }

    /**
     * 批量删除分产品保费收入数据
     *
     * @param ids id数组
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProductPremiumIncomeDetailDtoByIds(Long[] ids) {
        return productPremiumIncomeDetailMapper.deleteProductPremiumIncomeDetailEntityByIds(ids);
    }

    /**
     * 导入分产品保费收入
     *
     * @param dtoList       分产品保费收入数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importProductPremiumIncomeDetailDto(List<ProductPremiumIncomeDetailDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.isEmpty()) {
            throw new RuntimeException("导入分产品保费收入不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductPremiumIncomeDetailDTO dto : dtoList) {
            try {
                // 验证是否存在这个分产品保费收入
                ProductPremiumIncomeDetailDTO existDto = selectProductPremiumIncomeDetailDtoByCondition(
                        dto.getAccountingPeriod(),
                        dto.getActuarialCode());

                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    dto.setUpdateBy(username);
                    insertProductPremiumIncomeDetailDto(dto);
                    successNum++;
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    dto.setUpdateBy(username);
                    updateProductPremiumIncomeDetailDto(dto);
                    successNum++;
                } else {
                    failureNum++;
                    if (failureNum <= 10) {
                        failureMsg.append("<br/>").append(failureNum).append("、统计期间 ").append(dto.getAccountingPeriod())
                                .append(" 产品精算代码 ").append(dto.getActuarialCode()).append(" 已存在");
                    }
                }
            } catch (Exception e) {
                failureNum++;
                if (failureNum <= 10) {
                    failureMsg.append("<br/>").append(failureNum).append("、统计期间 ").append(dto.getAccountingPeriod())
                            .append(" 产品精算代码 ").append(dto.getActuarialCode()).append(" 数据处理异常");
                }
                log.error("导入分产品保费收入数据异常", e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
