package com.xl.alm.app.service;

import com.xl.alm.app.dto.AnnualDiscountCurveDTO;
import com.xl.alm.app.query.AnnualDiscountCurveQuery;

import java.util.List;

/**
 * 年度折现曲线表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AnnualDiscountCurveService {

    /**
     * 查询年度折现曲线列表
     *
     * @param annualDiscountCurveQuery 年度折现曲线查询条件
     * @return 年度折现曲线列表
     */
    List<AnnualDiscountCurveDTO> selectAnnualDiscountCurveDtoList(AnnualDiscountCurveQuery annualDiscountCurveQuery);

    /**
     * 根据ID查询年度折现曲线
     *
     * @param id 主键ID
     * @return 年度折现曲线
     */
    AnnualDiscountCurveDTO selectAnnualDiscountCurveDtoById(Long id);

    /**
     * 新增年度折现曲线
     *
     * @param annualDiscountCurveDto 年度折现曲线
     * @return 结果
     */
    int insertAnnualDiscountCurveDto(AnnualDiscountCurveDTO annualDiscountCurveDto);

    /**
     * 修改年度折现曲线
     *
     * @param annualDiscountCurveDto 年度折现曲线
     * @return 结果
     */
    int updateAnnualDiscountCurveDto(AnnualDiscountCurveDTO annualDiscountCurveDto);

    /**
     * 批量删除年度折现曲线
     *
     * @param ids 需要删除的年度折现曲线主键集合
     * @return 结果
     */
    int deleteAnnualDiscountCurveDtoByIds(Long[] ids);

    /**
     * 删除年度折现曲线信息
     *
     * @param id 年度折现曲线主键
     * @return 结果
     */
    int deleteAnnualDiscountCurveDtoById(Long id);

    /**
     * 导入年度折现曲线
     *
     * @param annualDiscountCurveList 年度折现曲线数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    String importAnnualDiscountCurveDto(List<AnnualDiscountCurveDTO> annualDiscountCurveList, Boolean updateSupport, String operName);

    /**
     * 根据账期删除年度折现曲线数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteAnnualDiscountCurveDtoByAccountPeriod(String accountPeriod);

    /**
     * 批量插入年度折现曲线数据
     *
     * @param annualDiscountCurveList 年度折现曲线列表
     * @return 影响行数
     */
    int batchInsertAnnualDiscountCurveDto(List<AnnualDiscountCurveDTO> annualDiscountCurveList);
}
