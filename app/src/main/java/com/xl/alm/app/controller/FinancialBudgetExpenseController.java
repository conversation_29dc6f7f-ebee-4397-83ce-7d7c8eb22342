package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.FinancialBudgetExpenseDTO;
import com.xl.alm.app.query.FinancialBudgetExpenseQuery;
import com.xl.alm.app.service.FinancialBudgetExpenseService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 财务预算费用表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/financial/budget/expense")
public class FinancialBudgetExpenseController extends BaseController {

    @Autowired
    private FinancialBudgetExpenseService financialBudgetExpenseService;

    /**
     * 查询财务预算费用列表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialBudgetExpenseQuery financialBudgetExpenseQuery) {
        startPage();
        List<FinancialBudgetExpenseDTO> list = financialBudgetExpenseService.selectFinancialBudgetExpenseDtoList(financialBudgetExpenseQuery);
        return getDataTable(list);
    }

    /**
     * 获取财务预算费用详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(financialBudgetExpenseService.selectFinancialBudgetExpenseDtoById(id));
    }

    /**
     * 新增财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:add')")
    @Log(title = "财务预算费用", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody FinancialBudgetExpenseDTO financialBudgetExpenseDto) {
        return toAjax(financialBudgetExpenseService.insertFinancialBudgetExpenseDto(financialBudgetExpenseDto));
    }

    /**
     * 修改财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:edit')")
    @Log(title = "财务预算费用", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody FinancialBudgetExpenseDTO financialBudgetExpenseDto) {
        return toAjax(financialBudgetExpenseService.updateFinancialBudgetExpenseDto(financialBudgetExpenseDto));
    }

    /**
     * 删除财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:remove')")
    @Log(title = "财务预算费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(financialBudgetExpenseService.deleteFinancialBudgetExpenseDtoByIds(ids));
    }

    /**
     * 批量新增财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:add')")
    @Log(title = "财务预算费用", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList) {
        return toAjax(financialBudgetExpenseService.batchInsertFinancialBudgetExpenseDto(financialBudgetExpenseDtoList));
    }

    /**
     * 根据账期删除财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:remove')")
    @Log(title = "财务预算费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(financialBudgetExpenseService.deleteFinancialBudgetExpenseDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出财务预算费用
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:export')")
    @Log(title = "财务预算费用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialBudgetExpenseQuery query) {
        ExcelUtil<FinancialBudgetExpenseDTO> util = new ExcelUtil<>(FinancialBudgetExpenseDTO.class);
        List<FinancialBudgetExpenseDTO> list = financialBudgetExpenseService.selectFinancialBudgetExpenseDtoList(query);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        util.exportExcel(list, "财务预算费用数据", response);
    }

    /**
     * 获取财务预算费用模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:import')")
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        ExcelUtil<FinancialBudgetExpenseDTO> util = new ExcelUtil<>(FinancialBudgetExpenseDTO.class);
        util.exportTemplateExcel(response, "财务预算费用");
    }

    /**
     * 导入财务预算费用数据
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:import')")
    @Log(title = "财务预算费用", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FinancialBudgetExpenseDTO> util = new ExcelUtil<>(FinancialBudgetExpenseDTO.class);
        List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = financialBudgetExpenseService.importFinancialBudgetExpenseData(financialBudgetExpenseDtoList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 财务预算费用数据列表
     */
    private void convertDictValueToLabel(List<FinancialBudgetExpenseDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FinancialBudgetExpenseDTO dto : list) {
            // 转换情景名称：字典值转为中文标签
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(
                        dto.getScenarioName(),
                        "cft_scenario_name"
                );
                dto.setScenarioName(scenarioLabel);
            }

            // 转换财务费用类型：字典值转为中文标签
            if (dto.getFinancialExpenseType() != null) {
                String expenseTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getFinancialExpenseType(),
                        "cft_financial_expense_type"
                );
                dto.setFinancialExpenseType(expenseTypeLabel);
            }
        }
    }
}
