package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetBasicConfigDTO;
import com.xl.alm.app.query.AssetBasicConfigQuery;

import java.util.List;

/**
 * 资产基础配置表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetBasicConfigService {

    /**
     * 查询资产基础配置表列表
     *
     * @param assetBasicConfigQuery 资产基础配置表查询条件
     * @return 资产基础配置表列表
     */
    List<AssetBasicConfigDTO> selectAssetBasicConfigDtoList(AssetBasicConfigQuery assetBasicConfigQuery);

    /**
     * 根据主键查询资产基础配置表
     *
     * @param id 主键
     * @return 资产基础配置表
     */
    AssetBasicConfigDTO selectAssetBasicConfigDtoById(Long id);

    /**
     * 新增资产基础配置表
     *
     * @param assetBasicConfigDto 资产基础配置表
     * @return 影响行数
     */
    int insertAssetBasicConfigDto(AssetBasicConfigDTO assetBasicConfigDto);

    /**
     * 修改资产基础配置表
     *
     * @param assetBasicConfigDto 资产基础配置表
     * @return 影响行数
     */
    int updateAssetBasicConfigDto(AssetBasicConfigDTO assetBasicConfigDto);

    /**
     * 删除资产基础配置表
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteAssetBasicConfigDtoById(Long id);

    /**
     * 批量删除资产基础配置表
     *
     * @param ids 主键数组
     * @return 影响行数
     */
    int deleteAssetBasicConfigDtoByIds(Long[] ids);

    /**
     * 批量新增资产基础配置表
     *
     * @param assetBasicConfigDtoList 资产基础配置表列表
     * @return 影响行数
     */
    int batchInsertAssetBasicConfigDto(List<AssetBasicConfigDTO> assetBasicConfigDtoList);

    /**
     * 导入资产基础配置表数据
     *
     * @param assetBasicConfigDtoList 资产基础配置表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importAssetBasicConfigDto(List<AssetBasicConfigDTO> assetBasicConfigDtoList, Boolean isUpdateSupport, String operName);
}
