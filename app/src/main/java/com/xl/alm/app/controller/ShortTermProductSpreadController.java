package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.ShortTermProductSpreadDTO;
import com.xl.alm.app.query.ShortTermProductSpreadQuery;
import com.xl.alm.app.service.ShortTermProductSpreadService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 中短存续期产品利差表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cost/short/term/product/spread")
public class ShortTermProductSpreadController extends BaseController {

    @Autowired
    private ShortTermProductSpreadService shortTermProductSpreadService;

    /**
     * 查询中短存续期产品利差列表
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortTermProductSpreadQuery shortTermProductSpreadQuery) {
        startPage();
        List<ShortTermProductSpreadDTO> list = shortTermProductSpreadService.selectShortTermProductSpreadDtoList(shortTermProductSpreadQuery);
        return getDataTable(list);
    }

    /**
     * 获取中短存续期产品利差详细信息
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(shortTermProductSpreadService.selectShortTermProductSpreadDtoById(id));
    }

    /**
     * 根据条件查询中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("actuarialCode") String actuarialCode) {
        return Result.success(shortTermProductSpreadService.selectShortTermProductSpreadDtoByCondition(
                accountingPeriod, actuarialCode));
    }

    /**
     * 新增中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:add')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody ShortTermProductSpreadDTO shortTermProductSpreadDto) {
        return toAjax(shortTermProductSpreadService.insertShortTermProductSpreadDto(shortTermProductSpreadDto));
    }

    /**
     * 修改中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:edit')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody ShortTermProductSpreadDTO shortTermProductSpreadDto) {
        return toAjax(shortTermProductSpreadService.updateShortTermProductSpreadDto(shortTermProductSpreadDto));
    }

    /**
     * 批量新增中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:add')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<ShortTermProductSpreadDTO> shortTermProductSpreadEntityList) {
        return toAjax(shortTermProductSpreadService.batchInsertShortTermProductSpreadDto(shortTermProductSpreadEntityList));
    }

    /**
     * 删除中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:remove')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(shortTermProductSpreadService.deleteShortTermProductSpreadDtoByIds(ids));
    }

    /**
     * 删除指定账期的中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:remove')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(shortTermProductSpreadService.deleteShortTermProductSpreadDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出中短存续期产品利差
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:export')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortTermProductSpreadQuery shortTermProductSpreadQuery) {
        List<ShortTermProductSpreadDTO> list = shortTermProductSpreadService.selectShortTermProductSpreadDtoList(shortTermProductSpreadQuery);
        ExcelUtil<ShortTermProductSpreadDTO> util = new ExcelUtil<>(ShortTermProductSpreadDTO.class);
        util.exportExcel(list, "中短存续期产品利差数据", response);
    }

    /**
     * 获取中短存续期产品利差导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ShortTermProductSpreadDTO> util = new ExcelUtil<>(ShortTermProductSpreadDTO.class);
        util.exportTemplateExcel(response, "中短存续期产品利差数据");
    }

    /**
     * 导入中短存续期产品利差数据
     */
    @PreAuthorize("@ss.hasPermi('cost:short:term:product:spread:import')")
    @Log(title = "中短存续期产品利差", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<ShortTermProductSpreadDTO> util = new ExcelUtil<>(ShortTermProductSpreadDTO.class);
        List<ShortTermProductSpreadDTO> shortTermProductSpreadList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = shortTermProductSpreadService.importShortTermProductSpreadDto(shortTermProductSpreadList, updateSupport, username);
        return Result.success(message);
    }
}
