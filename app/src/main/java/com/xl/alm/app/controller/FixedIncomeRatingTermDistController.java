package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.FixedIncomeRatingTermDistDTO;
import com.xl.alm.app.query.FixedIncomeRatingTermDistQuery;
import com.xl.alm.app.service.FixedIncomeRatingTermDistService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 固定收益类投资资产外部评级剩余期限分布表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/fixed/income/rating/term/dist")
public class FixedIncomeRatingTermDistController extends BaseController {
    @Autowired
    private FixedIncomeRatingTermDistService fixedIncomeRatingTermDistService;

    /**
     * 查询固定收益类投资资产外部评级剩余期限分布表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:list')")
    @GetMapping("/list")
    public TableDataInfo list(FixedIncomeRatingTermDistQuery query) {
        startPage();
        List<FixedIncomeRatingTermDistDTO> list = fixedIncomeRatingTermDistService.selectFixedIncomeRatingTermDistDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出固定收益类投资资产外部评级剩余期限分布表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:export')")
    @Log(title = "固定收益类投资资产外部评级剩余期限分布表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FixedIncomeRatingTermDistQuery query) {
        List<FixedIncomeRatingTermDistDTO> list = fixedIncomeRatingTermDistService.selectFixedIncomeRatingTermDistDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        ExcelUtil<FixedIncomeRatingTermDistDTO> util = new ExcelUtil<FixedIncomeRatingTermDistDTO>(FixedIncomeRatingTermDistDTO.class);
        util.exportExcel(list, "固定收益类投资资产外部评级剩余期限分布表数据", response);
    }

    /**
     * 获取固定收益类投资资产外部评级剩余期限分布表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fixedIncomeRatingTermDistService.selectFixedIncomeRatingTermDistDtoById(id));
    }

    /**
     * 新增固定收益类投资资产外部评级剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:add')")
    @Log(title = "固定收益类投资资产外部评级剩余期限分布表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FixedIncomeRatingTermDistDTO fixedIncomeRatingTermDistDto) {
        return toAjax(fixedIncomeRatingTermDistService.addFixedIncomeRatingTermDistDto(fixedIncomeRatingTermDistDto));
    }

    /**
     * 修改固定收益类投资资产外部评级剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:edit')")
    @Log(title = "固定收益类投资资产外部评级剩余期限分布表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FixedIncomeRatingTermDistDTO fixedIncomeRatingTermDistDto) {
        return toAjax(fixedIncomeRatingTermDistService.updateFixedIncomeRatingTermDistDto(fixedIncomeRatingTermDistDto));
    }

    /**
     * 删除固定收益类投资资产外部评级剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:remove')")
    @Log(title = "固定收益类投资资产外部评级剩余期限分布表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fixedIncomeRatingTermDistService.deleteFixedIncomeRatingTermDistDtoByIds(ids));
    }

    /**
     * 获取固定收益类投资资产外部评级剩余期限分布表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FixedIncomeRatingTermDistDTO> util = new ExcelUtil<FixedIncomeRatingTermDistDTO>(FixedIncomeRatingTermDistDTO.class);
        util.exportTemplateExcel(response, "固定收益类投资资产外部评级剩余期限分布表");
    }

    /**
     * 导入固定收益类投资资产外部评级剩余期限分布表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:rating:term:dist:import')")
    @Log(title = "固定收益类投资资产外部评级剩余期限分布表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FixedIncomeRatingTermDistDTO> util = new ExcelUtil<FixedIncomeRatingTermDistDTO>(FixedIncomeRatingTermDistDTO.class);
        List<FixedIncomeRatingTermDistDTO> fixedIncomeRatingTermDistList = util.importExcel(file.getInputStream());
        // 转换字典标签为值用于导入
        convertDictLabelToValue(fixedIncomeRatingTermDistList);
        String operName = getUsername();
        String message = fixedIncomeRatingTermDistService.importFixedIncomeRatingTermDistDto(fixedIncomeRatingTermDistList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 固定收益类投资资产外部评级剩余期限分布表数据列表
     */
    private void convertDictValueToLabel(List<FixedIncomeRatingTermDistDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeRatingTermDistDTO dto : list) {
            // 转换境内外标识字典值
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertValueToLabel(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换信用评级分类字典值
            if (dto.getCreditRatingCategory() != null) {
                dto.setCreditRatingCategory(DictConvertUtil.convertValueToLabel(dto.getCreditRatingCategory(), "ast_credit_rating"));
            }
            // 转换固收资产剩余期限资产分类字典值
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertValueToLabel(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 固定收益类投资资产外部评级剩余期限分布表数据列表
     */
    private void convertDictLabelToValue(List<FixedIncomeRatingTermDistDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeRatingTermDistDTO dto : list) {
            // 转换境内外标识字典标签
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertLabelToValue(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换信用评级分类字典标签
            if (dto.getCreditRatingCategory() != null) {
                dto.setCreditRatingCategory(DictConvertUtil.convertLabelToValue(dto.getCreditRatingCategory(), "ast_credit_rating"));
            }
            // 转换固收资产剩余期限资产分类字典标签
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertLabelToValue(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
        }
    }
}
