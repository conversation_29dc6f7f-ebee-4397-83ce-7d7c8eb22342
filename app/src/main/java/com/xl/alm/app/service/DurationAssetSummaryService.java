package com.xl.alm.app.service;

import com.xl.alm.app.dto.DurationAssetSummaryDTO;
import com.xl.alm.app.query.DurationAssetSummaryQuery;

import java.util.List;

/**
 * 久期资产结果汇总表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface DurationAssetSummaryService {

    /**
     * 查询久期资产结果汇总表列表
     *
     * @param durationAssetSummaryQuery 久期资产结果汇总表查询条件
     * @return 久期资产结果汇总表列表
     */
    List<DurationAssetSummaryDTO> selectDurationAssetSummaryDtoList(DurationAssetSummaryQuery durationAssetSummaryQuery);

    /**
     * 用id查询久期资产结果汇总表
     *
     * @param id id
     * @return 久期资产结果汇总表
     */
    DurationAssetSummaryDTO selectDurationAssetSummaryDtoById(Long id);

    /**
     * 根据账期和账户名称查询久期资产结果汇总表
     *
     * @param accountPeriod 账期
     * @param accountName 账户名称
     * @return 久期资产结果汇总表
     */
    DurationAssetSummaryDTO selectDurationAssetSummaryDtoByCondition(
            String accountPeriod,
            String accountName);

    /**
     * 新增久期资产结果汇总表
     *
     * @param durationAssetSummaryDTO 久期资产结果汇总表
     * @return 结果
     */
    int insertDurationAssetSummaryDto(DurationAssetSummaryDTO durationAssetSummaryDTO);

    /**
     * 批量插入久期资产结果汇总表数据
     *
     * @param durationAssetSummaryDtoList 久期资产结果汇总表列表
     * @return 影响行数
     */
    int batchInsertDurationAssetSummaryDto(List<DurationAssetSummaryDTO> durationAssetSummaryDtoList);

    /**
     * 更新久期资产结果汇总表数据
     *
     * @param dto 久期资产结果汇总表
     * @return 结果
     */
    int updateDurationAssetSummaryDto(DurationAssetSummaryDTO dto);

    /**
     * 删除指定账期的久期资产结果汇总表数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteDurationAssetSummaryDtoByPeriod(String accountPeriod);

    /**
     * 删除指定id的久期资产结果汇总表数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteDurationAssetSummaryDtoById(Long id);

    /**
     * 批量删除久期资产结果汇总表
     *
     * @param ids 需要删除的久期资产结果汇总表主键
     * @return 结果
     */
    int deleteDurationAssetSummaryDtoByIds(Long[] ids);

    /**
     * 导入久期资产结果汇总表
     *
     * @param dtoList       久期资产结果汇总表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importDurationAssetSummaryDto(List<DurationAssetSummaryDTO> dtoList, Boolean updateSupport, String username);
}
