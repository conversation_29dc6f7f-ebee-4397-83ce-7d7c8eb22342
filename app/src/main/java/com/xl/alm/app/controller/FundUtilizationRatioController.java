package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.FundUtilizationRatioDTO;
import com.xl.alm.app.query.FundUtilizationRatioQuery;
import com.xl.alm.app.service.FundUtilizationRatioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 资金运用比例监管表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/fund/utilization/ratio")
public class FundUtilizationRatioController extends BaseController {

    @Autowired
    private FundUtilizationRatioService fundUtilizationRatioService;

    /**
     * 查询资金运用比例监管表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:list')")
    @GetMapping("/list")
    public TableDataInfo list(FundUtilizationRatioQuery fundUtilizationRatioQuery) {
        startPage();
        List<FundUtilizationRatioDTO> list = fundUtilizationRatioService.selectFundUtilizationRatioDtoList(fundUtilizationRatioQuery);
        return getDataTable(list);
    }

    /**
     * 导出资金运用比例监管表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:export')")
    @Log(title = "资金运用比例监管表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FundUtilizationRatioQuery fundUtilizationRatioQuery) {
        List<FundUtilizationRatioDTO> list = fundUtilizationRatioService.selectFundUtilizationRatioDtoList(fundUtilizationRatioQuery);
        convertDictValueToLabel(list);
        ExcelUtil<FundUtilizationRatioDTO> util = new ExcelUtil<>(FundUtilizationRatioDTO.class);
        util.exportExcel(list, "资金运用比例监管表", response);
    }

    /**
     * 获取资金运用比例监管表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fundUtilizationRatioService.selectFundUtilizationRatioDtoById(id));
    }

    /**
     * 根据条件查询资金运用比例监管表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod,
                                 @RequestParam String assetSubSubCategory,
                                 @RequestParam String accountName) {
        return Result.success(fundUtilizationRatioService.selectFundUtilizationRatioDtoByCondition(accountingPeriod, assetSubSubCategory, accountName));
    }

    /**
     * 新增资金运用比例监管表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:add')")
    @Log(title = "资金运用比例监管表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FundUtilizationRatioDTO fundUtilizationRatioDTO) {
        return toAjax(fundUtilizationRatioService.insertFundUtilizationRatioDto(fundUtilizationRatioDTO));
    }

    /**
     * 修改资金运用比例监管表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:edit')")
    @Log(title = "资金运用比例监管表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FundUtilizationRatioDTO fundUtilizationRatioDTO) {
        return toAjax(fundUtilizationRatioService.updateFundUtilizationRatioDto(fundUtilizationRatioDTO));
    }

    /**
     * 删除资金运用比例监管表
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:remove')")
    @Log(title = "资金运用比例监管表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fundUtilizationRatioService.deleteFundUtilizationRatioDtoByIds(ids));
    }

    /**
     * 导入资金运用比例监管表数据
     */
    @PreAuthorize("@ss.hasPermi('asm:fund:utilization:ratio:import')")
    @Log(title = "资金运用比例监管表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FundUtilizationRatioDTO> util = new ExcelUtil<>(FundUtilizationRatioDTO.class);
        List<FundUtilizationRatioDTO> fundUtilizationRatioList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(fundUtilizationRatioList);
        String operName = getUsername();
        try {
            String message = fundUtilizationRatioService.importFundUtilizationRatioDto(fundUtilizationRatioList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载资金运用比例监管表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FundUtilizationRatioDTO> util = new ExcelUtil<>(FundUtilizationRatioDTO.class);
        util.exportExcel(new ArrayList<>(), "资金运用比例监管表数据", response);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资金运用比例监管表数据列表
     */
    private void convertDictValueToLabel(List<FundUtilizationRatioDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FundUtilizationRatioDTO dto : list) {
            // 转换资产小小类字典值
            if (dto.getAssetSubSubCategory() != null) {
                dto.setAssetSubSubCategory(DictConvertUtil.convertValueToLabel(dto.getAssetSubSubCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换账户名称字典值
            if (dto.getAccountName() != null) {
                dto.setAccountName(DictConvertUtil.convertValueToLabel(dto.getAccountName(), "ast_account_name_mapping"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 资金运用比例监管表数据列表
     */
    private void convertDictLabelToValue(List<FundUtilizationRatioDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FundUtilizationRatioDTO dto : list) {
            // 转换资产小小类字典标签
            if (dto.getAssetSubSubCategory() != null) {
                dto.setAssetSubSubCategory(DictConvertUtil.convertLabelToValue(dto.getAssetSubSubCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换账户名称字典标签
            if (dto.getAccountName() != null) {
                dto.setAccountName(DictConvertUtil.convertLabelToValue(dto.getAccountName(), "ast_account_name_mapping"));
            }
        }
    }
}
