package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.MonthlyDiscountFactorDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.MonthlyDiscountFactorQuery;
import com.xl.alm.app.service.MonthlyDiscountFactorService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月度折现因子表含价差 Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/adur/monthly/discount/factor")
public class MonthlyDiscountFactorController extends BaseController {

    @Autowired
    private MonthlyDiscountFactorService monthlyDiscountFactorService;

    /**
     * 查询月度折现因子含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        startPage();
        List<MonthlyDiscountFactorDTO> list = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoList(monthlyDiscountFactorQuery);
        return getDataTable(list);
    }

    /**
     * 获取月度折现因子含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id));
    }

    /**
     * 新增月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:add')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        return toAjax(monthlyDiscountFactorService.insertMonthlyDiscountFactorDto(monthlyDiscountFactorDto));
    }

    /**
     * 修改月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:edit')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody MonthlyDiscountFactorDTO monthlyDiscountFactorDto) {
        return toAjax(monthlyDiscountFactorService.updateMonthlyDiscountFactorDto(monthlyDiscountFactorDto));
    }

    /**
     * 删除月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:remove')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(monthlyDiscountFactorService.deleteMonthlyDiscountFactorDtoByIds(ids));
    }

    /**
     * 导出月度折现因子含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:export')")
    @Log(title = "月度折现因子含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyDiscountFactorQuery monthlyDiscountFactorQuery) {
        List<MonthlyDiscountFactorDTO> list = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoList(monthlyDiscountFactorQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        ExcelUtil<MonthlyDiscountFactorDTO> util = new ExcelUtil<>(MonthlyDiscountFactorDTO.class);
        util.exportExcel(list, "月度折现因子含价差数据", response);
    }



    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        MonthlyDiscountFactorDTO dto = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountFactorSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:factor:edit')")
    @Log(title = "月度折现因子期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        MonthlyDiscountFactorDTO dto = monthlyDiscountFactorService.selectMonthlyDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountFactorSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(monthlyDiscountFactorService.updateMonthlyDiscountFactorDto(dto));
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 月度折现因子含价差数据列表
     */
    private void convertDictValueToLabel(List<MonthlyDiscountFactorDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        log.info("开始转换字典值为中文标签，共{}条记录", list.size());

        // 为每条记录转换字典值为中文标签
        for (MonthlyDiscountFactorDTO dto : list) {
            // 转换久期类型：字典值转为中文标签
            if (dto.getDurationType() != null) {
                String originalValue = dto.getDurationType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_duration_type");
                dto.setDurationType(convertedValue);
                log.debug("久期类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换基点类型：字典值转为中文标签
            if (dto.getBasisPointType() != null) {
                String originalValue = dto.getBasisPointType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_basis_point_type");
                dto.setBasisPointType(convertedValue);
                log.debug("基点类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换日期类型：字典值转为中文标签
            if (dto.getDateType() != null) {
                String originalValue = dto.getDateType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_date_type");
                dto.setDateType(convertedValue);
                log.debug("日期类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换价差类型：字典值转为中文标签
            if (dto.getSpreadType() != null) {
                String originalValue = dto.getSpreadType();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_spread_type");
                dto.setSpreadType(convertedValue);
                log.debug("价差类型转换: {} -> {}", originalValue, convertedValue);
            }

            // 转换账户名称：字典值转为中文标签
            if (dto.getAccountName() != null) {
                String originalValue = dto.getAccountName();
                String convertedValue = DictConvertUtil.convertValueToLabel(originalValue, "adur_account_name");
                dto.setAccountName(convertedValue);
                log.debug("账户名称转换: {} -> {}", originalValue, convertedValue);
            }
        }

        log.info("字典值转换完成");
    }

    /**
     * 测试字典转换功能
     */
    @GetMapping("/testDictConversion")
    public Result testDictConversion() {
        log.info("开始测试字典转换功能");

        // 测试各个字典类型的转换
        String[] testValues = {"01", "02", "03", "04"};
        String[] dictTypes = {
            "adur_duration_type",
            "adur_basis_point_type",
            "adur_date_type",
            "adur_spread_type",
            "adur_account_name"
        };

        for (String dictType : dictTypes) {
            log.info("测试字典类型: {}", dictType);
            for (String value : testValues) {
                String label = DictConvertUtil.convertValueToLabel(value, dictType);
                log.info("  {} -> {}", value, label);
            }
        }

        return Result.success("字典转换测试完成，请查看日志");
    }
}
