package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.LiabNonLifeScaleSummaryDTO;
import com.xl.alm.app.query.LiabNonLifeScaleSummaryQuery;
import com.xl.alm.app.service.LiabNonLifeScaleSummaryService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 非寿险负债规模汇总表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/non/life/scale/summary")
public class LiabNonLifeScaleSummaryController extends BaseController {

    @Autowired
    private LiabNonLifeScaleSummaryService liabNonLifeScaleSummaryService;

    /**
     * 查询非寿险负债规模汇总表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(LiabNonLifeScaleSummaryQuery liabNonLifeScaleSummaryQuery) {
        startPage();
        List<LiabNonLifeScaleSummaryDTO> list = liabNonLifeScaleSummaryService.selectLiabNonLifeScaleSummaryDtoList(liabNonLifeScaleSummaryQuery);
        return getDataTable(list);
    }

    /**
     * 获取非寿险负债规模汇总表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(liabNonLifeScaleSummaryService.selectLiabNonLifeScaleSummaryDtoById(id));
    }

    /**
     * 新增非寿险负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:add')")
    @Log(title = "非寿险负债规模汇总表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody LiabNonLifeScaleSummaryDTO liabNonLifeScaleSummaryDTO) {
        return toAjax(liabNonLifeScaleSummaryService.addLiabNonLifeScaleSummaryDto(liabNonLifeScaleSummaryDTO));
    }

    /**
     * 修改非寿险负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:edit')")
    @Log(title = "非寿险负债规模汇总表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody LiabNonLifeScaleSummaryDTO liabNonLifeScaleSummaryDTO) {
        return toAjax(liabNonLifeScaleSummaryService.updateLiabNonLifeScaleSummaryDto(liabNonLifeScaleSummaryDTO));
    }

    /**
     * 删除非寿险负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:remove')")
    @Log(title = "非寿险负债规模汇总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(liabNonLifeScaleSummaryService.deleteLiabNonLifeScaleSummaryDtoByIds(ids));
    }

    /**
     * 导出非寿险负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:export')")
    @Log(title = "非寿险负债规模汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiabNonLifeScaleSummaryQuery liabNonLifeScaleSummaryQuery) {
        List<LiabNonLifeScaleSummaryDTO> list = liabNonLifeScaleSummaryService.selectLiabNonLifeScaleSummaryDtoList(liabNonLifeScaleSummaryQuery);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<LiabNonLifeScaleSummaryDTO> util = new ExcelUtil<>(LiabNonLifeScaleSummaryDTO.class);
        util.exportExcel(list, "非寿险负债规模汇总表数据", response);
    }

    /**
     * 获取非寿险负债规模汇总表导入模板
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<LiabNonLifeScaleSummaryDTO> util = new ExcelUtil<>(LiabNonLifeScaleSummaryDTO.class);
        util.exportTemplateExcel(response, "非寿险负债规模汇总表");
    }

    /**
     * 导入非寿险负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:non:life:scale:summary:import')")
    @Log(title = "非寿险负债规模汇总表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<LiabNonLifeScaleSummaryDTO> util = new ExcelUtil<>(LiabNonLifeScaleSummaryDTO.class);
        List<LiabNonLifeScaleSummaryDTO> liabNonLifeScaleSummaryList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = liabNonLifeScaleSummaryService.importLiabNonLifeScaleSummaryDto(liabNonLifeScaleSummaryList, updateSupport, username);
        return Result.success(message);
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 非寿险负债规模汇总表数据列表
     */
    private void convertDictCodeToLabel(List<LiabNonLifeScaleSummaryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取险种主类字典数据
            List<SysDictData> mainTypeDict = dictTypeService.selectDictDataByType("cost_insurance_main_type");

            // 为每条记录转换字典编码为中文标签
            for (LiabNonLifeScaleSummaryDTO dto : list) {
                // 转换险种主类
                dto.setInsuranceMainType(convertCodeToLabel(dto.getInsuranceMainType(), mainTypeDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            logger.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将编码转换为标签
     *
     * @param code 编码
     * @param dictDataList 字典数据列表
     * @return 标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        return code;
    }
}
