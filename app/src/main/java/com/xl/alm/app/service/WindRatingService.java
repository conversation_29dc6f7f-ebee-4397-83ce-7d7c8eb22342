package com.xl.alm.app.service;

import com.xl.alm.app.dto.WindRatingDTO;
import com.xl.alm.app.query.WindRatingQuery;

import java.util.List;

/**
 * Wind评级表Service接口
 *
 * <AUTHOR> Assistant
 */
public interface WindRatingService {
    /**
     * 查询Wind评级表
     *
     * @param id Wind评级表主键
     * @return Wind评级表
     */
    public WindRatingDTO selectWindRatingDtoById(Long id);

    /**
     * 查询Wind评级表列表
     *
     * @param windRatingQuery Wind评级表查询条件
     * @return Wind评级表集合
     */
    public List<WindRatingDTO> selectWindRatingDtoList(WindRatingQuery windRatingQuery);

    /**
     * 新增Wind评级表
     *
     * @param windRatingDTO Wind评级表
     * @return 结果
     */
    public int insertWindRatingDto(WindRatingDTO windRatingDTO);

    /**
     * 修改Wind评级表
     *
     * @param windRatingDTO Wind评级表
     * @return 结果
     */
    public int updateWindRatingDto(WindRatingDTO windRatingDTO);

    /**
     * 批量删除Wind评级表
     *
     * @param ids 需要删除的Wind评级表主键集合
     * @return 结果
     */
    public int deleteWindRatingDtoByIds(Long[] ids);

    /**
     * 删除Wind评级表信息
     *
     * @param id Wind评级表主键
     * @return 结果
     */
    public int deleteWindRatingDtoById(Long id);

    /**
     * 批量新增Wind评级表
     *
     * @param windRatingDTOList Wind评级表列表
     * @return 结果
     */
    public int batchInsertWindRatingDto(List<WindRatingDTO> windRatingDTOList);

    /**
     * 根据账期删除Wind评级表
     *
     * @param accountingPeriod 账期
     * @return 结果
     */
    public int deleteWindRatingDtoByPeriod(String accountingPeriod);

    /**
     * 导入Wind评级表数据
     *
     * @param windRatingDTOList Wind评级表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importWindRatingDto(List<WindRatingDTO> windRatingDTOList, Boolean isUpdateSupport, String operName);
}
