package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.PaymentPeriodMappingDTO;
import com.xl.alm.app.query.PaymentPeriodMappingQuery;
import com.xl.alm.app.service.PaymentPeriodMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 缴费年期映射表 Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/liab/payment/period/mapping")
public class PaymentPeriodMappingController extends BaseController {

    @Autowired
    private PaymentPeriodMappingService paymentPeriodMappingService;

    /**
     * 查询缴费年期映射列表
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentPeriodMappingQuery paymentPeriodMappingQuery) {
        startPage();
        List<PaymentPeriodMappingDTO> list = paymentPeriodMappingService.selectPaymentPeriodMappingDtoList(paymentPeriodMappingQuery);
        return getDataTable(list);
    }

    /**
     * 获取缴费年期映射详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(paymentPeriodMappingService.selectPaymentPeriodMappingDtoById(id));
    }

    /**
     * 根据条件查询缴费年期映射
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("paymentPeriod") Integer paymentPeriod) {
        return Result.success(paymentPeriodMappingService.selectPaymentPeriodMappingDtoByCondition(accountingPeriod, paymentPeriod));
    }

    /**
     * 新增缴费年期映射
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:add')")
    @Log(title = "缴费年期映射", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody PaymentPeriodMappingDTO paymentPeriodMappingDto) {
        return toAjax(paymentPeriodMappingService.insertPaymentPeriodMappingDto(paymentPeriodMappingDto));
    }

    /**
     * 修改缴费年期映射
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:edit')")
    @Log(title = "缴费年期映射", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody PaymentPeriodMappingDTO paymentPeriodMappingDto) {
        return toAjax(paymentPeriodMappingService.updatePaymentPeriodMappingDto(paymentPeriodMappingDto));
    }

    /**
     * 删除缴费年期映射
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:remove')")
    @Log(title = "缴费年期映射", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(paymentPeriodMappingService.deletePaymentPeriodMappingDtoByIds(ids));
    }

    /**
     * 导出缴费年期映射列表
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:export')")
    @Log(title = "缴费年期映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentPeriodMappingQuery paymentPeriodMappingQuery) {
        List<PaymentPeriodMappingDTO> list = paymentPeriodMappingService.selectPaymentPeriodMappingDtoList(paymentPeriodMappingQuery);
        ExcelUtil<PaymentPeriodMappingDTO> util = new ExcelUtil<PaymentPeriodMappingDTO>(PaymentPeriodMappingDTO.class);
        util.exportExcel(response, list, "缴费年期映射数据");
    }

    /**
     * 获取缴费年期映射导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PaymentPeriodMappingDTO> util = new ExcelUtil<PaymentPeriodMappingDTO>(PaymentPeriodMappingDTO.class);
        util.importTemplateExcel(response, "缴费年期映射数据");
    }

    /**
     * 导入缴费年期映射数据
     */
    @PreAuthorize("@ss.hasPermi('liab:payment:period:mapping:import')")
    @Log(title = "缴费年期映射", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<PaymentPeriodMappingDTO> util = new ExcelUtil<PaymentPeriodMappingDTO>(PaymentPeriodMappingDTO.class);
        List<PaymentPeriodMappingDTO> paymentPeriodMappingList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = importPaymentPeriodMapping(paymentPeriodMappingList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 导入缴费年期映射数据
     */
    private String importPaymentPeriodMapping(List<PaymentPeriodMappingDTO> paymentPeriodMappingList, Boolean isUpdateSupport, String operName) {
        if (paymentPeriodMappingList == null || paymentPeriodMappingList.isEmpty()) {
            throw new RuntimeException("导入缴费年期映射数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (PaymentPeriodMappingDTO paymentPeriodMappingDto : paymentPeriodMappingList) {
            try {
                // 验证是否存在这个缴费年期映射
                PaymentPeriodMappingDTO existPaymentPeriodMapping = paymentPeriodMappingService.selectPaymentPeriodMappingDtoByCondition(
                        paymentPeriodMappingDto.getAccountingPeriod(), paymentPeriodMappingDto.getPaymentPeriod());
                if (existPaymentPeriodMapping == null) {
                    paymentPeriodMappingDto.setCreateBy(operName);
                    paymentPeriodMappingDto.setUpdateBy(operName);
                    this.paymentPeriodMappingService.insertPaymentPeriodMappingDto(paymentPeriodMappingDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + paymentPeriodMappingDto.getAccountingPeriod() + " 缴费年期 " + paymentPeriodMappingDto.getPaymentPeriod() + " 导入成功");
                } else if (isUpdateSupport) {
                    paymentPeriodMappingDto.setId(existPaymentPeriodMapping.getId());
                    paymentPeriodMappingDto.setUpdateBy(operName);
                    this.paymentPeriodMappingService.updatePaymentPeriodMappingDto(paymentPeriodMappingDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + paymentPeriodMappingDto.getAccountingPeriod() + " 缴费年期 " + paymentPeriodMappingDto.getPaymentPeriod() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + paymentPeriodMappingDto.getAccountingPeriod() + " 缴费年期 " + paymentPeriodMappingDto.getPaymentPeriod() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + paymentPeriodMappingDto.getAccountingPeriod() + " 缴费年期 " + paymentPeriodMappingDto.getPaymentPeriod() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
