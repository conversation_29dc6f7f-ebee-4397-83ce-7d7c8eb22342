package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.ThreeYearBusinessPlanDTO;
import com.xl.alm.app.query.ThreeYearBusinessPlanQuery;
import com.xl.alm.app.service.ThreeYearBusinessPlanService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 三年新业务规划Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/three/year/business/plan")
public class ThreeYearBusinessPlanController extends BaseController {

    @Autowired
    private ThreeYearBusinessPlanService threeYearBusinessPlanService;

    /**
     * 查询三年新业务规划列表
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(ThreeYearBusinessPlanQuery threeYearBusinessPlanQuery) {
        startPage();
        List<ThreeYearBusinessPlanDTO> list = threeYearBusinessPlanService.selectThreeYearBusinessPlanList(threeYearBusinessPlanQuery);
        return getDataTable(list);
    }

    /**
     * 导出三年新业务规划列表
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:export')")
    @Log(title = "三年新业务规划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThreeYearBusinessPlanQuery threeYearBusinessPlanQuery) {
        threeYearBusinessPlanService.exportThreeYearBusinessPlan(response, threeYearBusinessPlanQuery);
    }

    /**
     * 获取三年新业务规划详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(threeYearBusinessPlanService.selectThreeYearBusinessPlanById(id));
    }

    /**
     * 新增三年新业务规划
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:add')")
    @Log(title = "三年新业务规划", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody ThreeYearBusinessPlanDTO threeYearBusinessPlanDTO) {
        return toAjax(threeYearBusinessPlanService.insertThreeYearBusinessPlan(threeYearBusinessPlanDTO));
    }

    /**
     * 修改三年新业务规划
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:edit')")
    @Log(title = "三年新业务规划", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody ThreeYearBusinessPlanDTO threeYearBusinessPlanDTO) {
        return toAjax(threeYearBusinessPlanService.updateThreeYearBusinessPlan(threeYearBusinessPlanDTO));
    }

    /**
     * 删除三年新业务规划
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:remove')")
    @Log(title = "三年新业务规划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(threeYearBusinessPlanService.deleteThreeYearBusinessPlanByIds(ids));
    }

    /**
     * 导入三年新业务规划数据
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:import')")
    @Log(title = "三年新业务规划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<ThreeYearBusinessPlanDTO> util = new ExcelUtil<>(ThreeYearBusinessPlanDTO.class);
        List<ThreeYearBusinessPlanDTO> threeYearBusinessPlanList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = threeYearBusinessPlanService.importThreeYearBusinessPlan(threeYearBusinessPlanList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载三年新业务规划导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        threeYearBusinessPlanService.importTemplate(response);
    }

    /**
     * 根据账期删除三年新业务规划
     */
    @PreAuthorize("@ss.hasPermi('liab:threeYearBusinessPlan:remove')")
    @Log(title = "三年新业务规划", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(threeYearBusinessPlanService.deleteThreeYearBusinessPlanByPeriod(accountingPeriod));
    }
}
