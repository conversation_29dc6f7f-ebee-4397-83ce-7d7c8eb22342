package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.DurationAssetDetailDTO;
import com.xl.alm.app.query.DurationAssetDetailQuery;
import com.xl.alm.app.service.DurationAssetDetailService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 久期资产明细表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/duration/asset/detail")
public class DurationAssetDetailController extends BaseController {

    @Autowired
    private DurationAssetDetailService durationAssetDetailService;

    /**
     * 查询久期资产明细列表
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(DurationAssetDetailQuery durationAssetDetailQuery) {
        startPage();
        List<DurationAssetDetailDTO> list = durationAssetDetailService.selectDurationAssetDetailDtoList(durationAssetDetailQuery);
        return getDataTable(list);
    }

    /**
     * 获取久期资产明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(durationAssetDetailService.selectDurationAssetDetailDtoById(id));
    }

    /**
     * 新增久期资产明细
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:add')")
    @Log(title = "久期资产明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DurationAssetDetailDTO durationAssetDetailDto) {
        return toAjax(durationAssetDetailService.insertDurationAssetDetailDto(durationAssetDetailDto));
    }

    /**
     * 修改久期资产明细
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:edit')")
    @Log(title = "久期资产明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DurationAssetDetailDTO durationAssetDetailDto) {
        return toAjax(durationAssetDetailService.updateDurationAssetDetailDto(durationAssetDetailDto));
    }

    /**
     * 删除久期资产明细
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:remove')")
    @Log(title = "久期资产明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(durationAssetDetailService.deleteDurationAssetDetailDtoByIds(ids));
    }

    /**
     * 导出久期资产明细
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:export')")
    @Log(title = "久期资产明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DurationAssetDetailQuery durationAssetDetailQuery) {
        List<DurationAssetDetailDTO> list = durationAssetDetailService.selectDurationAssetDetailDtoList(durationAssetDetailQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        // 使用ValueSetExcelExporter导出，处理现金流值集字段
        // 将发行时点现金流值集和评估时点现金流值集JSON字段展开为多列，key和date作为表头，value作为值
        // 生成带时间戳的中文文件名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "久期资产明细表_" + timestamp;
        ValueSetExcelExporter.exportExcel(list, fileName, response, "issueCashflowSet", "evalCashflowSet");
    }

    /**
     * 获取久期资产明细导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DurationAssetDetailDTO> util = new ExcelUtil<>(DurationAssetDetailDTO.class);
        util.exportTemplateExcel(response, "久期资产明细数据");
    }

    /**
     * 导入久期资产明细
     */
    @PreAuthorize("@ss.hasPermi('adur:duration:asset:detail:import')")
    @Log(title = "久期资产明细", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DurationAssetDetailDTO> util = new ExcelUtil<>(DurationAssetDetailDTO.class);
        List<DurationAssetDetailDTO> durationAssetDetailList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = durationAssetDetailService.importDurationAssetDetailDto(durationAssetDetailList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 久期资产明细数据列表
     */
    private void convertDictValueToLabel(List<DurationAssetDetailDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 为每条记录转换字典值为中文标签
        for (DurationAssetDetailDTO dto : list) {
            // 转换账户名称：字典值转为中文标签
            if (dto.getAccountName() != null) {
                dto.setAccountName(DictConvertUtil.convertValueToLabel(dto.getAccountName(), "adur_account_name"));
            }

            // 转换付息方式：字典值转为中文标签
            if (dto.getPaymentMethod() != null) {
                dto.setPaymentMethod(DictConvertUtil.convertValueToLabel(dto.getPaymentMethod(), "adur_payment_method"));
            }
        }
    }
}
