package com.xl.alm.app.service;

import com.xl.alm.app.dto.ProductPremiumIncomeDetailDTO;
import com.xl.alm.app.query.ProductPremiumIncomeDetailQuery;

import java.util.List;

/**
 * 分产品保费收入表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface ProductPremiumIncomeDetailService {

    /**
     * 查询分产品保费收入列表
     *
     * @param productPremiumIncomeDetailQuery 分产品保费收入查询条件
     * @return 分产品保费收入列表
     */
    List<ProductPremiumIncomeDetailDTO> selectProductPremiumIncomeDetailDtoList(ProductPremiumIncomeDetailQuery productPremiumIncomeDetailQuery);

    /**
     * 用id查询分产品保费收入
     *
     * @param id id
     * @return 分产品保费收入
     */
    ProductPremiumIncomeDetailDTO selectProductPremiumIncomeDetailDtoById(Long id);

    /**
     * 根据统计期间和产品精算代码查询分产品保费收入
     *
     * @param accountingPeriod 统计期间
     * @param actuarialCode 产品精算代码
     * @return 分产品保费收入
     */
    ProductPremiumIncomeDetailDTO selectProductPremiumIncomeDetailDtoByCondition(
            String accountingPeriod,
            String actuarialCode);

    /**
     * 新增分产品保费收入
     *
     * @param dto 分产品保费收入
     * @return 影响行数
     */
    int insertProductPremiumIncomeDetailDto(ProductPremiumIncomeDetailDTO dto);

    /**
     * 批量插入分产品保费收入数据
     *
     * @param productPremiumIncomeDetailDtoList 分产品保费收入列表
     * @return 影响行数
     */
    int batchInsertProductPremiumIncomeDetailDto(List<ProductPremiumIncomeDetailDTO> productPremiumIncomeDetailDtoList);

    /**
     * 更新分产品保费收入数据
     *
     * @param dto 分产品保费收入
     * @return 结果
     */
    int updateProductPremiumIncomeDetailDto(ProductPremiumIncomeDetailDTO dto);

    /**
     * 删除指定统计期间的分产品保费收入数据
     *
     * @param accountingPeriod 统计期间
     * @return 影响行数
     */
    int deleteProductPremiumIncomeDetailDtoByPeriod(String accountingPeriod);

    /**
     * 删除指定id的分产品保费收入数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteProductPremiumIncomeDetailDtoById(Long id);

    /**
     * 批量删除分产品保费收入数据
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteProductPremiumIncomeDetailDtoByIds(Long[] ids);

    /**
     * 导入分产品保费收入
     *
     * @param dtoList       分产品保费收入数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importProductPremiumIncomeDetailDto(List<ProductPremiumIncomeDetailDTO> dtoList, Boolean updateSupport, String username);
}
