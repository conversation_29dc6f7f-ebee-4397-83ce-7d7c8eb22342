package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.ActuarialExpenseSummaryDTO;
import com.xl.alm.app.query.ActuarialExpenseSummaryQuery;
import com.xl.alm.app.service.ActuarialExpenseSummaryService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 精算业管费汇总表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/actuarial/expense/summary")
public class ActuarialExpenseSummaryController extends BaseController {

    @Autowired
    private ActuarialExpenseSummaryService actuarialExpenseSummaryService;

    /**
     * 查询精算业管费汇总表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActuarialExpenseSummaryQuery actuarialExpenseSummaryQuery) {
        startPage();
        List<ActuarialExpenseSummaryDTO> list = actuarialExpenseSummaryService.selectActuarialExpenseSummaryDtoList(actuarialExpenseSummaryQuery);
        return getDataTable(list);
    }

    /**
     * 获取精算业管费汇总表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(actuarialExpenseSummaryService.selectActuarialExpenseSummaryDtoById(id));
    }

    /**
     * 根据条件查询精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("scenarioName") String scenarioName,
            @RequestParam("financialExpenseType") String financialExpenseType,
            @RequestParam("businessType") String businessType,
            @RequestParam("designType") String designType) {
        return Result.success(actuarialExpenseSummaryService.selectActuarialExpenseSummaryDtoByCondition(
                accountingPeriod, scenarioName, financialExpenseType, businessType, designType));
    }

    /**
     * 新增精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:add')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody ActuarialExpenseSummaryDTO dto) {
        // 验证唯一性
        ActuarialExpenseSummaryDTO existDto = actuarialExpenseSummaryService.selectActuarialExpenseSummaryDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getActuarialExpenseType(),
                dto.getBusinessType(), dto.getDesignType());
        if (existDto != null) {
            return Result.error("该账期、情景名称、精算费用类型、业务类型、设计类型组合已存在");
        }
        
        dto.setCreateBy(SecurityUtils.getUsername());
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(actuarialExpenseSummaryService.insertActuarialExpenseSummaryDto(dto));
    }

    /**
     * 修改精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:edit')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody ActuarialExpenseSummaryDTO dto) {
        // 验证唯一性（排除自身）
        ActuarialExpenseSummaryDTO existDto = actuarialExpenseSummaryService.selectActuarialExpenseSummaryDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getActuarialExpenseType(),
                dto.getBusinessType(), dto.getDesignType());
        if (existDto != null && !existDto.getId().equals(dto.getId())) {
            return Result.error("该账期、情景名称、精算费用类型、业务类型、设计类型组合已存在");
        }
        
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(actuarialExpenseSummaryService.updateActuarialExpenseSummaryDto(dto));
    }

    /**
     * 删除精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:remove')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(actuarialExpenseSummaryService.deleteActuarialExpenseSummaryDtoByIds(ids));
    }

    /**
     * 批量新增精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:add')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<ActuarialExpenseSummaryDTO> actuarialExpenseSummaryDtoList) {
        return toAjax(actuarialExpenseSummaryService.batchInsertActuarialExpenseSummaryDto(actuarialExpenseSummaryDtoList));
    }

    /**
     * 根据账期删除精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:remove')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(actuarialExpenseSummaryService.deleteActuarialExpenseSummaryDtoByPeriod(accountingPeriod));
    }



    /**
     * 导出精算业管费汇总表
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:export')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ActuarialExpenseSummaryQuery query) throws Exception {
        actuarialExpenseSummaryService.exportActuarialExpenseSummaryHorizontal(response, query);
    }

    /**
     * 获取精算业管费汇总表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:import')")
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws Exception {
        actuarialExpenseSummaryService.exportTemplate(response);
    }

    /**
     * 导入精算业管费汇总表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:actuarial:expense:summary:import')")
    @Log(title = "精算业管费汇总表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        String operName = SecurityUtils.getUsername();
        String message = actuarialExpenseSummaryService.importActuarialExpenseSummaryDataFromExcel(file.getInputStream(), updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 精算业管费汇总数据列表
     */
    private void convertDictValueToLabel(List<ActuarialExpenseSummaryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (ActuarialExpenseSummaryDTO dto : list) {
            // 转换情景名称：字典值转为中文标签
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(
                        dto.getScenarioName(), 
                        "cft_scenario_name"
                );
                dto.setScenarioName(scenarioLabel);
            }

            // 精算费用类型不需要字典转换，直接使用变量名称

            // 转换业务类型：字典值转为中文标签
            if (dto.getBusinessType() != null) {
                String businessTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getBusinessType(), 
                        "cost_business_type"
                );
                dto.setBusinessType(businessTypeLabel);
            }

            // 转换设计类型：字典值转为中文标签
            if (dto.getDesignType() != null) {
                String designTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDesignType(), 
                        "cost_design_type"
                );
                dto.setDesignType(designTypeLabel);
            }
        }
    }
}
