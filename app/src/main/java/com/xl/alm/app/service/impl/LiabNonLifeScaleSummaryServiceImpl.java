package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.LiabNonLifeScaleSummaryDTO;
import com.xl.alm.app.entity.LiabNonLifeScaleSummaryEntity;
import com.xl.alm.app.mapper.LiabNonLifeScaleSummaryMapper;
import com.xl.alm.app.query.LiabNonLifeScaleSummaryQuery;
import com.xl.alm.app.service.LiabNonLifeScaleSummaryService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 非寿险负债规模汇总表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class LiabNonLifeScaleSummaryServiceImpl implements LiabNonLifeScaleSummaryService {

    @Autowired
    private LiabNonLifeScaleSummaryMapper liabNonLifeScaleSummaryMapper;

    /**
     * 查询非寿险负债规模汇总表列表
     *
     * @param liabNonLifeScaleSummaryQuery 非寿险负债规模汇总表查询条件
     * @return 非寿险负债规模汇总表列表
     */
    @Override
    public List<LiabNonLifeScaleSummaryDTO> selectLiabNonLifeScaleSummaryDtoList(LiabNonLifeScaleSummaryQuery liabNonLifeScaleSummaryQuery) {
        List<LiabNonLifeScaleSummaryEntity> entityList = liabNonLifeScaleSummaryMapper.selectLiabNonLifeScaleSummaryEntityList(liabNonLifeScaleSummaryQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, LiabNonLifeScaleSummaryDTO.class);
    }

    /**
     * 用id查询非寿险负债规模汇总表
     *
     * @param id id
     * @return 非寿险负债规模汇总表
     */
    @Override
    public LiabNonLifeScaleSummaryDTO selectLiabNonLifeScaleSummaryDtoById(Long id) {
        LiabNonLifeScaleSummaryEntity entity = liabNonLifeScaleSummaryMapper.selectLiabNonLifeScaleSummaryEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, LiabNonLifeScaleSummaryDTO.class);
    }

    /**
     * 根据账期和险种主类查询非寿险负债规模汇总表
     *
     * @param accountingPeriod  账期
     * @param insuranceMainType 险种主类
     * @return 非寿险负债规模汇总表
     */
    @Override
    public LiabNonLifeScaleSummaryDTO selectLiabNonLifeScaleSummaryDtoByCondition(
            String accountingPeriod,
            String insuranceMainType) {
        LiabNonLifeScaleSummaryEntity entity = liabNonLifeScaleSummaryMapper.selectLiabNonLifeScaleSummaryEntityByCondition(
                accountingPeriod,
                insuranceMainType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, LiabNonLifeScaleSummaryDTO.class);
    }

    /**
     * 新增非寿险负债规模汇总表
     *
     * @param dto 非寿险负债规模汇总表
     * @return 结果
     */
    @Override
    public int addLiabNonLifeScaleSummaryDto(LiabNonLifeScaleSummaryDTO dto) {
        LiabNonLifeScaleSummaryEntity entity = EntityDtoConvertUtil.convertToEntity(dto, LiabNonLifeScaleSummaryEntity.class);
        return liabNonLifeScaleSummaryMapper.insertLiabNonLifeScaleSummaryEntity(entity);
    }

    /**
     * 修改非寿险负债规模汇总表
     *
     * @param dto 非寿险负债规模汇总表
     * @return 结果
     */
    @Override
    public int updateLiabNonLifeScaleSummaryDto(LiabNonLifeScaleSummaryDTO dto) {
        LiabNonLifeScaleSummaryEntity entity = EntityDtoConvertUtil.convertToEntity(dto, LiabNonLifeScaleSummaryEntity.class);
        return liabNonLifeScaleSummaryMapper.updateLiabNonLifeScaleSummaryEntity(entity);
    }

    /**
     * 批量删除非寿险负债规模汇总表
     *
     * @param ids 需要删除的非寿险负债规模汇总表主键集合
     * @return 结果
     */
    @Override
    public int deleteLiabNonLifeScaleSummaryDtoByIds(Long[] ids) {
        return liabNonLifeScaleSummaryMapper.deleteLiabNonLifeScaleSummaryEntityByIds(ids);
    }

    /**
     * 删除非寿险负债规模汇总表信息
     *
     * @param id 非寿险负债规模汇总表主键
     * @return 结果
     */
    @Override
    public int deleteLiabNonLifeScaleSummaryDtoById(Long id) {
        return liabNonLifeScaleSummaryMapper.deleteLiabNonLifeScaleSummaryEntityById(id);
    }

    /**
     * 批量插入非寿险负债规模汇总表数据
     *
     * @param liabNonLifeScaleSummaryDtoList 非寿险负债规模汇总表列表
     * @return 影响行数
     */
    @Override
    public int batchInsertLiabNonLifeScaleSummaryDto(List<LiabNonLifeScaleSummaryDTO> liabNonLifeScaleSummaryDtoList) {
        if (liabNonLifeScaleSummaryDtoList == null || liabNonLifeScaleSummaryDtoList.isEmpty()) {
            return 0;
        }
        List<LiabNonLifeScaleSummaryEntity> entityList = EntityDtoConvertUtil.convertToEntityList(liabNonLifeScaleSummaryDtoList, LiabNonLifeScaleSummaryEntity.class);
        return liabNonLifeScaleSummaryMapper.batchInsertLiabNonLifeScaleSummaryEntity(entityList);
    }

    /**
     * 删除指定账期的非寿险负债规模汇总表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteLiabNonLifeScaleSummaryDtoByPeriod(String accountingPeriod) {
        return liabNonLifeScaleSummaryMapper.deleteLiabNonLifeScaleSummaryEntityByPeriod(accountingPeriod);
    }

    /**
     * 导入非寿险负债规模汇总表
     *
     * @param dtoList       非寿险负债规模汇总表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importLiabNonLifeScaleSummaryDto(List<LiabNonLifeScaleSummaryDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入非寿险负债规模汇总表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (LiabNonLifeScaleSummaryDTO dto : dtoList) {
            try {
                // 转换字典字段：如果是中文标签，转换为编码
                convertDictLabelsToCode(dto);

                // 验证是否存在这个非寿险负债规模汇总表
                LiabNonLifeScaleSummaryDTO existDto = selectLiabNonLifeScaleSummaryDtoByCondition(
                        dto.getAccountingPeriod(),
                        dto.getInsuranceMainType());
                if (StringUtils.isNull(existDto)) {
                    addLiabNonLifeScaleSummaryDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 险种主类 ").append(dto.getInsuranceMainType()).append(" 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    updateLiabNonLifeScaleSummaryDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 险种主类 ").append(dto.getInsuranceMainType()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 险种主类 ").append(dto.getInsuranceMainType()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 险种主类 " + dto.getInsuranceMainType() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 转换字典标签为编码
     *
     * @param dto 非寿险负债规模汇总表DTO
     */
    private void convertDictLabelsToCode(LiabNonLifeScaleSummaryDTO dto) {
        try {
            // 转换险种主类
            dto.setInsuranceMainType(convertLabelToCode(dto.getInsuranceMainType(), "cost_insurance_main_type"));

        } catch (Exception e) {
            log.error("转换字典标签时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 将字典标签转换为编码
     *
     * @param labelOrCode 标签或编码
     * @param dictType 字典类型
     * @return 编码值
     */
    private String convertLabelToCode(String labelOrCode, String dictType) {
        if (StringUtils.isEmpty(labelOrCode)) {
            return labelOrCode;
        }

        // 如果已经是编码格式，直接返回
        if (isCodeFormat(labelOrCode, dictType)) {
            return labelOrCode;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);
            List<SysDictData> dictDataList = dictTypeService.selectDictDataByType(dictType);

            // 查找对应的编码
            for (SysDictData dictData : dictDataList) {
                if (labelOrCode.equals(dictData.getDictLabel())) {
                    return dictData.getDictValue();
                }
            }

            // 如果没找到对应的编码，返回原值
            log.warn("未找到字典类型 '{}' 中标签 '{}' 对应的编码，将使用原值", dictType, labelOrCode);
            return labelOrCode;

        } catch (Exception e) {
            log.error("转换字典标签时发生异常: {}", e.getMessage(), e);
            return labelOrCode;
        }
    }

    /**
     * 判断是否为编码格式
     *
     * @param value 值
     * @param dictType 字典类型
     * @return 是否为编码格式
     */
    private boolean isCodeFormat(String value, String dictType) {
        // 险种主类：2位数字编码
        return value.matches("^\\d{2}$");
    }
}
