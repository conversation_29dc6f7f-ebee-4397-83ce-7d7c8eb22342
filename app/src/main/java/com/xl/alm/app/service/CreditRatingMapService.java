package com.xl.alm.app.service;

import com.xl.alm.app.dto.CreditRatingMapDTO;
import com.xl.alm.app.query.CreditRatingMapQuery;

import java.util.List;

/**
 * 信用评级映射表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface CreditRatingMapService {

    /**
     * 查询信用评级映射表列表
     *
     * @param creditRatingMapQuery 信用评级映射表查询条件
     * @return 信用评级映射表列表
     */
    List<CreditRatingMapDTO> selectCreditRatingMapDtoList(CreditRatingMapQuery creditRatingMapQuery);

    /**
     * 根据主键查询信用评级映射表
     *
     * @param id 主键
     * @return 信用评级映射表
     */
    CreditRatingMapDTO selectCreditRatingMapDtoById(Long id);

    /**
     * 新增信用评级映射表
     *
     * @param creditRatingMapDTO 信用评级映射表
     * @return 影响行数
     */
    int insertCreditRatingMapDto(CreditRatingMapDTO creditRatingMapDTO);

    /**
     * 修改信用评级映射表
     *
     * @param creditRatingMapDTO 信用评级映射表
     * @return 影响行数
     */
    int updateCreditRatingMapDto(CreditRatingMapDTO creditRatingMapDTO);

    /**
     * 批量删除信用评级映射表
     *
     * @param ids 需要删除的信用评级映射表主键集合
     * @return 影响行数
     */
    int deleteCreditRatingMapDtoByIds(Long[] ids);

    /**
     * 删除信用评级映射表信息
     *
     * @param id 信用评级映射表主键
     * @return 影响行数
     */
    int deleteCreditRatingMapDtoById(Long id);

    /**
     * 批量新增信用评级映射表
     *
     * @param creditRatingMapDtoList 信用评级映射表列表
     * @return 影响行数
     */
    int batchInsertCreditRatingMapDto(List<CreditRatingMapDTO> creditRatingMapDtoList);

    /**
     * 根据账期删除信用评级映射表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteCreditRatingMapDtoByPeriod(String accountingPeriod);

    /**
     * 导入信用评级映射表数据
     *
     * @param creditRatingMapDtoList 信用评级映射表数据列表
     * @param updateSupport 是否支持更新
     * @param operName 操作人员
     * @return 导入结果信息
     */
    String importCreditRatingMapDto(List<CreditRatingMapDTO> creditRatingMapDtoList, Boolean updateSupport, String operName);
}
