package com.xl.alm.app.service;

import com.xl.alm.app.dto.AlmNewPremiumStatisticsDTO;
import com.xl.alm.app.query.AlmNewPremiumStatisticsQuery;

import java.util.List;

/**
 * ALM新单保费统计表Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IAlmNewPremiumStatisticsService 
{
    /**
     * 查询ALM新单保费统计表
     * 
     * @param id ALM新单保费统计表主键
     * @return ALM新单保费统计表
     */
    public AlmNewPremiumStatisticsDTO selectAlmNewPremiumStatisticsById(Long id);

    /**
     * 查询ALM新单保费统计表列表
     * 
     * @param query ALM新单保费统计表查询参数
     * @return ALM新单保费统计表集合
     */
    public List<AlmNewPremiumStatisticsDTO> selectAlmNewPremiumStatisticsList(AlmNewPremiumStatisticsQuery query);

    /**
     * 新增ALM新单保费统计表
     * 
     * @param almNewPremiumStatisticsDTO ALM新单保费统计表
     * @return 结果
     */
    public int insertAlmNewPremiumStatistics(AlmNewPremiumStatisticsDTO almNewPremiumStatisticsDTO);

    /**
     * 修改ALM新单保费统计表
     * 
     * @param almNewPremiumStatisticsDTO ALM新单保费统计表
     * @return 结果
     */
    public int updateAlmNewPremiumStatistics(AlmNewPremiumStatisticsDTO almNewPremiumStatisticsDTO);

    /**
     * 批量删除ALM新单保费统计表
     * 
     * @param ids 需要删除的ALM新单保费统计表主键集合
     * @return 结果
     */
    public int deleteAlmNewPremiumStatisticsByIds(Long[] ids);

    /**
     * 删除ALM新单保费统计表信息
     * 
     * @param id ALM新单保费统计表主键
     * @return 结果
     */
    public int deleteAlmNewPremiumStatisticsById(Long id);

    /**
     * 检查记录唯一性
     * 
     * @param almNewPremiumStatisticsDTO ALM新单保费统计表
     * @return 结果
     */
    public boolean checkUniqueRecord(AlmNewPremiumStatisticsDTO almNewPremiumStatisticsDTO);

    /**
     * 导入ALM新单保费统计表数据
     * 
     * @param almNewPremiumStatisticsList ALM新单保费统计表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importAlmNewPremiumStatistics(List<AlmNewPremiumStatisticsDTO> almNewPremiumStatisticsList, Boolean isUpdateSupport, String operName);
}
