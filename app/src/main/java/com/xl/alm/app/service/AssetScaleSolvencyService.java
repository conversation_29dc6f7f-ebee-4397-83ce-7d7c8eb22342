package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetScaleSolvencyDTO;
import com.xl.alm.app.query.AssetScaleSolvencyQuery;

import java.util.List;

/**
 * 资产规模与偿付能力表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetScaleSolvencyService {

    /**
     * 查询资产规模与偿付能力表列表
     *
     * @param assetScaleSolvencyQuery 资产规模与偿付能力表查询条件
     * @return 资产规模与偿付能力表列表
     */
    List<AssetScaleSolvencyDTO> selectAssetScaleSolvencyDtoList(AssetScaleSolvencyQuery assetScaleSolvencyQuery);

    /**
     * 用id查询资产规模与偿付能力表
     *
     * @param id id
     * @return 资产规模与偿付能力表
     */
    AssetScaleSolvencyDTO selectAssetScaleSolvencyDtoById(Long id);

    /**
     * 根据账期和项目名称查询资产规模与偿付能力表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return 资产规模与偿付能力表
     */
    AssetScaleSolvencyDTO selectAssetScaleSolvencyDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增资产规模与偿付能力表
     *
     * @param assetScaleSolvencyDTO 资产规模与偿付能力表
     * @return 结果
     */
    int insertAssetScaleSolvencyDto(AssetScaleSolvencyDTO assetScaleSolvencyDTO);

    /**
     * 批量新增资产规模与偿付能力表
     *
     * @param assetScaleSolvencyDtoList 资产规模与偿付能力表列表
     * @return 影响行数
     */
    int batchInsertAssetScaleSolvencyDto(List<AssetScaleSolvencyDTO> assetScaleSolvencyDtoList);

    /**
     * 更新资产规模与偿付能力表数据
     *
     * @param assetScaleSolvencyDTO 资产规模与偿付能力表
     * @return 结果
     */
    int updateAssetScaleSolvencyDto(AssetScaleSolvencyDTO assetScaleSolvencyDTO);

    /**
     * 删除指定id的资产规模与偿付能力表数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAssetScaleSolvencyDtoById(Long id);

    /**
     * 批量删除资产规模与偿付能力表
     *
     * @param ids 需要删除的资产规模与偿付能力表主键集合
     * @return 结果
     */
    int deleteAssetScaleSolvencyDtoByIds(Long[] ids);

    /**
     * 删除指定账期的资产规模与偿付能力表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAssetScaleSolvencyDtoByPeriod(String accountingPeriod);

    /**
     * 导入资产规模与偿付能力表
     *
     * @param dtoList       资产规模与偿付能力表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAssetScaleSolvencyDto(List<AssetScaleSolvencyDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 计算资产规模与偿付能力表
     *
     * @param accountingPeriod 账期
     * @param lastYearPeriod 上年末账期
     * @param lastQuarterPeriod 上季末账期
     * @param username 操作用户
     * @return 结果
     */
    String calculateAssetScaleSolvency(String accountingPeriod, String lastYearPeriod, String lastQuarterPeriod, String username);
}
