package com.xl.alm.app.service;

import com.xl.alm.app.dto.FundUtilizationRatioDTO;
import com.xl.alm.app.query.FundUtilizationRatioQuery;

import java.util.List;

/**
 * 资金运用比例监管表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FundUtilizationRatioService {

    /**
     * 查询资金运用比例监管表列表
     *
     * @param fundUtilizationRatioQuery 资金运用比例监管表查询条件
     * @return 资金运用比例监管表列表
     */
    List<FundUtilizationRatioDTO> selectFundUtilizationRatioDtoList(FundUtilizationRatioQuery fundUtilizationRatioQuery);

    /**
     * 用id查询资金运用比例监管表
     *
     * @param id id
     * @return 资金运用比例监管表
     */
    FundUtilizationRatioDTO selectFundUtilizationRatioDtoById(Long id);

    /**
     * 根据账期、资产小小类和账户名称查询资金运用比例监管表
     *
     * @param accountingPeriod 账期
     * @param assetSubSubCategory 资产小小类
     * @param accountName 账户名称
     * @return 资金运用比例监管表
     */
    FundUtilizationRatioDTO selectFundUtilizationRatioDtoByCondition(String accountingPeriod, String assetSubSubCategory, String accountName);

    /**
     * 新增资金运用比例监管表
     *
     * @param fundUtilizationRatioDTO 资金运用比例监管表
     * @return 结果
     */
    int insertFundUtilizationRatioDto(FundUtilizationRatioDTO fundUtilizationRatioDTO);

    /**
     * 批量新增资金运用比例监管表
     *
     * @param fundUtilizationRatioDtoList 资金运用比例监管表列表
     * @return 影响行数
     */
    int batchInsertFundUtilizationRatioDto(List<FundUtilizationRatioDTO> fundUtilizationRatioDtoList);

    /**
     * 修改资金运用比例监管表
     *
     * @param fundUtilizationRatioDTO 资金运用比例监管表
     * @return 结果
     */
    int updateFundUtilizationRatioDto(FundUtilizationRatioDTO fundUtilizationRatioDTO);

    /**
     * 批量删除资金运用比例监管表
     *
     * @param ids 需要删除的资金运用比例监管表主键集合
     * @return 结果
     */
    int deleteFundUtilizationRatioDtoByIds(Long[] ids);

    /**
     * 删除资金运用比例监管表信息
     *
     * @param id 资金运用比例监管表主键
     * @return 结果
     */
    int deleteFundUtilizationRatioDtoById(Long id);

    /**
     * 导入资金运用比例监管表数据
     *
     * @param fundUtilizationRatioDtoList 资金运用比例监管表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFundUtilizationRatioDto(List<FundUtilizationRatioDTO> fundUtilizationRatioDtoList, Boolean isUpdateSupport, String operName);
}
