package com.xl.alm.app.service;

import com.xl.alm.app.dto.MonthlyDiscountFactorDTO;
import com.xl.alm.app.query.MonthlyDiscountFactorQuery;

import java.util.List;

/**
 * 月度折现因子表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface MonthlyDiscountFactorService {

    /**
     * 查询月度折现因子含价差列表
     *
     * @param monthlyDiscountFactorQuery 月度折现因子含价差查询条件
     * @return 月度折现因子含价差列表
     */
    List<MonthlyDiscountFactorDTO> selectMonthlyDiscountFactorDtoList(MonthlyDiscountFactorQuery monthlyDiscountFactorQuery);

    /**
     * 根据ID查询月度折现因子含价差
     *
     * @param id 主键ID
     * @return 月度折现因子含价差
     */
    MonthlyDiscountFactorDTO selectMonthlyDiscountFactorDtoById(Long id);

    /**
     * 新增月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    int insertMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto);

    /**
     * 修改月度折现因子含价差
     *
     * @param monthlyDiscountFactorDto 月度折现因子含价差
     * @return 结果
     */
    int updateMonthlyDiscountFactorDto(MonthlyDiscountFactorDTO monthlyDiscountFactorDto);

    /**
     * 批量删除月度折现因子含价差
     *
     * @param ids 需要删除的月度折现因子含价差主键集合
     * @return 结果
     */
    int deleteMonthlyDiscountFactorDtoByIds(Long[] ids);

    /**
     * 删除月度折现因子含价差信息
     *
     * @param id 月度折现因子含价差主键
     * @return 结果
     */
    int deleteMonthlyDiscountFactorDtoById(Long id);



    /**
     * 根据账期删除月度折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteMonthlyDiscountFactorDtoByAccountPeriod(String accountPeriod);

    /**
     * 批量插入月度折现因子含价差数据
     *
     * @param monthlyDiscountFactorList 月度折现因子含价差列表
     * @return 影响行数
     */
    int batchInsertMonthlyDiscountFactorDto(List<MonthlyDiscountFactorDTO> monthlyDiscountFactorList);
}
