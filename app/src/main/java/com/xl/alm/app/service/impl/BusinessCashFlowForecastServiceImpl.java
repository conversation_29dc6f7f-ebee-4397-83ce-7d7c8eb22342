package com.xl.alm.app.service.impl;

import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.BusinessCashFlowForecastDTO;
import com.xl.alm.app.entity.BusinessCashFlowForecastEntity;
import com.xl.alm.app.mapper.BusinessCashFlowForecastMapper;
import com.xl.alm.app.query.BusinessCashFlowForecastQuery;
import com.xl.alm.app.service.BusinessCashFlowForecastService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务现金流预测表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class BusinessCashFlowForecastServiceImpl implements BusinessCashFlowForecastService {

    @Autowired
    private BusinessCashFlowForecastMapper businessCashFlowForecastMapper;

    /**
     * 查询业务现金流预测表列表
     *
     * @param businessCashFlowForecastQuery 业务现金流预测表查询条件
     * @return 业务现金流预测表列表
     */
    @Override
    public List<BusinessCashFlowForecastDTO> selectBusinessCashFlowForecastDtoList(BusinessCashFlowForecastQuery businessCashFlowForecastQuery) {
        List<BusinessCashFlowForecastEntity> entityList = businessCashFlowForecastMapper.selectBusinessCashFlowForecastEntityList(businessCashFlowForecastQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, BusinessCashFlowForecastDTO.class);
    }

    /**
     * 根据ID查询业务现金流预测表
     *
     * @param id 主键ID
     * @return 业务现金流预测表
     */
    @Override
    public BusinessCashFlowForecastDTO selectBusinessCashFlowForecastDtoById(Long id) {
        BusinessCashFlowForecastEntity entity = businessCashFlowForecastMapper.selectBusinessCashFlowForecastEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, BusinessCashFlowForecastDTO.class);
    }

    /**
     * 新增业务现金流预测表
     *
     * @param businessCashFlowForecastDto 业务现金流预测表
     * @return 影响行数
     */
    @Override
    public int insertBusinessCashFlowForecastDto(BusinessCashFlowForecastDTO businessCashFlowForecastDto) {
        BusinessCashFlowForecastEntity entity = EntityDtoConvertUtil.convertToEntity(businessCashFlowForecastDto, BusinessCashFlowForecastEntity.class);
        return businessCashFlowForecastMapper.insertBusinessCashFlowForecastEntity(entity);
    }

    /**
     * 修改业务现金流预测表
     *
     * @param businessCashFlowForecastDto 业务现金流预测表
     * @return 影响行数
     */
    @Override
    public int updateBusinessCashFlowForecastDto(BusinessCashFlowForecastDTO businessCashFlowForecastDto) {
        BusinessCashFlowForecastEntity entity = EntityDtoConvertUtil.convertToEntity(businessCashFlowForecastDto, BusinessCashFlowForecastEntity.class);
        return businessCashFlowForecastMapper.updateBusinessCashFlowForecastEntity(entity);
    }

    /**
     * 删除业务现金流预测表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Override
    public int deleteBusinessCashFlowForecastDtoById(Long id) {
        return businessCashFlowForecastMapper.deleteBusinessCashFlowForecastEntityById(id);
    }

    /**
     * 批量删除业务现金流预测表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    @Override
    public int deleteBusinessCashFlowForecastDtoByIds(Long[] ids) {
        return businessCashFlowForecastMapper.deleteBusinessCashFlowForecastEntityByIds(ids);
    }

    /**
     * 批量新增业务现金流预测表
     *
     * @param businessCashFlowForecastDtoList 业务现金流预测表列表
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertBusinessCashFlowForecastDto(List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList) {
        if (businessCashFlowForecastDtoList == null || businessCashFlowForecastDtoList.isEmpty()) {
            return 0;
        }
        // 转换字典标签为字典值
        for (BusinessCashFlowForecastDTO dto : businessCashFlowForecastDtoList) {
            convertDictLabelToValue(dto);
        }
        List<BusinessCashFlowForecastEntity> entityList = EntityDtoConvertUtil.convertToEntityList(businessCashFlowForecastDtoList, BusinessCashFlowForecastEntity.class);
        return businessCashFlowForecastMapper.batchInsertBusinessCashFlowForecastEntity(entityList);
    }

    /**
     * 根据账期删除业务现金流预测表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteBusinessCashFlowForecastDtoByPeriod(String accountingPeriod) {
        return businessCashFlowForecastMapper.deleteBusinessCashFlowForecastEntityByPeriod(accountingPeriod);
    }

    /**
     * 根据条件查询业务现金流预测表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param designType 设计类型
     * @param businessType 业务类型
     * @param item 项目
     * @return 业务现金流预测表
     */
    @Override
    public BusinessCashFlowForecastDTO selectBusinessCashFlowForecastDtoByCondition(String accountingPeriod, String scenarioName, String designType, String businessType, String item) {
        BusinessCashFlowForecastEntity entity = businessCashFlowForecastMapper.selectBusinessCashFlowForecastEntityByCondition(accountingPeriod, scenarioName, designType, businessType, item);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, BusinessCashFlowForecastDTO.class);
    }

    /**
     * 导入业务现金流预测表数据
     *
     * @param businessCashFlowForecastDtoList 业务现金流预测表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importBusinessCashFlowForecastData(List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(businessCashFlowForecastDtoList) || businessCashFlowForecastDtoList.size() == 0) {
            throw new ServiceException("导入业务现金流预测数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (BusinessCashFlowForecastDTO businessCashFlowForecastDto : businessCashFlowForecastDtoList) {
            try {
                // 转换字典标签为字典值
                convertDictLabelToValue(businessCashFlowForecastDto);
                
                // 验证是否存在这个业务现金流预测
                BusinessCashFlowForecastDTO existDto = this.selectBusinessCashFlowForecastDtoByCondition(
                        businessCashFlowForecastDto.getAccountingPeriod(),
                        businessCashFlowForecastDto.getScenarioName(),
                        businessCashFlowForecastDto.getDesignType(),
                        businessCashFlowForecastDto.getBusinessType(),
                        businessCashFlowForecastDto.getItem());
                
                if (StringUtils.isNull(existDto)) {
                    businessCashFlowForecastDto.setCreateBy(operName);
                    this.insertBusinessCashFlowForecastDto(businessCashFlowForecastDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + businessCashFlowForecastDto.getAccountingPeriod() + " 的数据导入成功");
                } else if (isUpdateSupport) {
                    businessCashFlowForecastDto.setUpdateBy(operName);
                    businessCashFlowForecastDto.setId(existDto.getId());
                    this.updateBusinessCashFlowForecastDto(businessCashFlowForecastDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + businessCashFlowForecastDto.getAccountingPeriod() + " 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + businessCashFlowForecastDto.getAccountingPeriod() + " 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + businessCashFlowForecastDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 转换字典标签为字典值
     *
     * @param businessCashFlowForecastDto 业务现金流预测DTO
     */
    private void convertDictLabelToValue(BusinessCashFlowForecastDTO businessCashFlowForecastDto) {
        // 转换情景名称
        if (StringUtils.isNotEmpty(businessCashFlowForecastDto.getScenarioName())) {
            String convertedScenarioName = DictConvertUtil.convertLabelToValue(
                    businessCashFlowForecastDto.getScenarioName(), 
                    "cft_scenario_name"
            );
            businessCashFlowForecastDto.setScenarioName(convertedScenarioName);
        }

        // 转换设计类型
        if (StringUtils.isNotEmpty(businessCashFlowForecastDto.getDesignType())) {
            String convertedDesignType = DictConvertUtil.convertLabelToValue(
                    businessCashFlowForecastDto.getDesignType(), 
                    "cost_design_type"
            );
            businessCashFlowForecastDto.setDesignType(convertedDesignType);
        }

        // 转换业务类型
        if (StringUtils.isNotEmpty(businessCashFlowForecastDto.getBusinessType())) {
            String convertedBusinessType = DictConvertUtil.convertLabelToValue(
                    businessCashFlowForecastDto.getBusinessType(), 
                    "cost_business_type"
            );
            businessCashFlowForecastDto.setBusinessType(convertedBusinessType);
        }
    }

    /**
     * 导出业务现金流预测表模板
     */
    @Override
    public void exportTemplate(HttpServletResponse response) throws Exception {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("业务现金流预测表");

        // 创建表头
        Row headerRow = sheet.createRow(0);

        // 基础列
        String[] baseColumns = {"账期", "情景名称", "设计类型", "业务类型", "项目",
                               "未来第一季度", "未来第二季度", "未来第三季度", "未来第四季度",
                               "未来第二年剩余季度", "未来第三年"};
        for (int i = 0; i < baseColumns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(baseColumns[i]);
        }

        // 设置列宽
        for (int i = 0; i < baseColumns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("业务现金流预测表导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    /**
     * 导出业务现金流预测表
     */
    @Override
    public void exportBusinessCashFlowForecast(HttpServletResponse response, BusinessCashFlowForecastQuery query) throws Exception {
        // 查询数据
        List<BusinessCashFlowForecastDTO> dataList = selectBusinessCashFlowForecastDtoList(query);

        if (dataList.isEmpty()) {
            throw new RuntimeException("没有数据可导出");
        }

        // 转换字典值为中文标签
        convertDictValueToLabelForExport(dataList);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("业务现金流预测表");

        // 创建表头
        Row headerRow = sheet.createRow(0);

        // 完整的表头（基础列 + 季度列）
        String[] headers = {"账期", "情景名称", "业务类型", "设计类型", "项目",
                           "未来第一季度", "未来第二季度", "未来第三季度", "未来第四季度",
                           "未来第二年剩余季度", "未来第三年"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据行
        for (int rowIndex = 0; rowIndex < dataList.size(); rowIndex++) {
            BusinessCashFlowForecastDTO dto = dataList.get(rowIndex);
            Row dataRow = sheet.createRow(rowIndex + 1);

            // 基础字段
            dataRow.createCell(0).setCellValue(dto.getAccountingPeriod() != null ? dto.getAccountingPeriod() : "");
            dataRow.createCell(1).setCellValue(dto.getScenarioName() != null ? dto.getScenarioName() : "");
            dataRow.createCell(2).setCellValue(dto.getBusinessType() != null ? dto.getBusinessType() : "");
            dataRow.createCell(3).setCellValue(dto.getDesignType() != null ? dto.getDesignType() : "");
            dataRow.createCell(4).setCellValue(dto.getItem() != null ? dto.getItem() : "");

            // 季度现金流数据
            setCellBigDecimalValue(dataRow.createCell(5), dto.getFutureFirstQuarter());
            setCellBigDecimalValue(dataRow.createCell(6), dto.getFutureSecondQuarter());
            setCellBigDecimalValue(dataRow.createCell(7), dto.getFutureThirdQuarter());
            setCellBigDecimalValue(dataRow.createCell(8), dto.getFutureFourthQuarter());
            setCellBigDecimalValue(dataRow.createCell(9), dto.getFutureSecondYearRemainingQuarters());
            setCellBigDecimalValue(dataRow.createCell(10), dto.getFutureThirdYear());
        }

        // 设置列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("业务现金流预测表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    /**
     * 设置单元格BigDecimal值
     */
    private void setCellBigDecimalValue(Cell cell, BigDecimal value) {
        if (value == null) {
            cell.setCellValue("-");
        } else {
            try {
                cell.setCellValue(String.format("%,.2f", value.doubleValue()));
            } catch (Exception e) {
                cell.setCellValue(value.toString());
            }
        }
    }

    /**
     * 转换字典值为中文标签用于导出
     */
    private void convertDictValueToLabelForExport(List<BusinessCashFlowForecastDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (BusinessCashFlowForecastDTO dto : list) {
            // 转换情景名称
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(dto.getScenarioName(), "cft_scenario_name");
                dto.setScenarioName(scenarioLabel);
            }

            // 转换设计类型
            if (dto.getDesignType() != null) {
                String designTypeLabel = DictConvertUtil.convertValueToLabel(dto.getDesignType(), "cost_design_type");
                dto.setDesignType(designTypeLabel);
            }

            // 转换业务类型
            if (dto.getBusinessType() != null) {
                String businessTypeLabel = DictConvertUtil.convertValueToLabel(dto.getBusinessType(), "cost_business_type");
                dto.setBusinessType(businessTypeLabel);
            }
        }
    }
}
