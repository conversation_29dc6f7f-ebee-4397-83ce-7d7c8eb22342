package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.WindYieldCurveDTO;
import com.xl.alm.app.query.WindYieldCurveQuery;
import com.xl.alm.app.service.WindYieldCurveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 万得收益率曲线表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/wind/yield/curve")
public class WindYieldCurveController extends BaseController {

    @Autowired
    private WindYieldCurveService windYieldCurveService;

    /**
     * 查询万得收益率曲线列表
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:list')")
    @GetMapping("/list")
    public TableDataInfo list(WindYieldCurveQuery windYieldCurveQuery) {
        startPage();
        List<WindYieldCurveDTO> list = windYieldCurveService.selectWindYieldCurveDtoList(windYieldCurveQuery);
        return getDataTable(list);
    }

    /**
     * 导出万得收益率曲线列表
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:export')")
    @Log(title = "万得收益率曲线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WindYieldCurveQuery windYieldCurveQuery) {
        List<WindYieldCurveDTO> list = windYieldCurveService.selectWindYieldCurveDtoList(windYieldCurveQuery);
        ExcelUtil<WindYieldCurveDTO> util = new ExcelUtil<WindYieldCurveDTO>(WindYieldCurveDTO.class);
        util.exportExcel(list, "万得收益率曲线数据", response);
    }

    /**
     * 获取万得收益率曲线详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(windYieldCurveService.selectWindYieldCurveDtoById(id));
    }

    /**
     * 新增万得收益率曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:add')")
    @Log(title = "万得收益率曲线", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody WindYieldCurveDTO windYieldCurveDTO) {
        windYieldCurveDTO.setCreateBy(getUsername());
        return toAjax(windYieldCurveService.insertWindYieldCurveDto(windYieldCurveDTO));
    }

    /**
     * 修改万得收益率曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:edit')")
    @Log(title = "万得收益率曲线", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody WindYieldCurveDTO windYieldCurveDTO) {
        windYieldCurveDTO.setUpdateBy(getUsername());
        return toAjax(windYieldCurveService.updateWindYieldCurveDto(windYieldCurveDTO));
    }

    /**
     * 删除万得收益率曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:remove')")
    @Log(title = "万得收益率曲线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(windYieldCurveService.deleteWindYieldCurveDtoByIds(ids));
    }

    /**
     * 获取万得收益率曲线导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WindYieldCurveDTO> util = new ExcelUtil<WindYieldCurveDTO>(WindYieldCurveDTO.class);
        util.exportTemplateExcel(response, "万得收益率曲线数据");
    }

    /**
     * 导入万得收益率曲线数据
     */
    @PreAuthorize("@ss.hasPermi('adur:wind:yield:curve:import')")
    @Log(title = "万得收益率曲线", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<WindYieldCurveDTO> util = new ExcelUtil<WindYieldCurveDTO>(WindYieldCurveDTO.class);
        List<WindYieldCurveDTO> windYieldCurveList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = windYieldCurveService.importWindYieldCurveDto(windYieldCurveList, updateSupport, operName);
        return Result.success(message);
    }
}
