package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurMonthlyDiscountCurveWithSpreadDTO;
import com.xl.alm.app.query.AdurMonthlyDiscountCurveWithSpreadQuery;

import java.util.List;

/**
 * ADUR月度折现曲线表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurMonthlyDiscountCurveWithSpreadService {

    /**
     * 查询ADUR月度折现曲线含价差列表
     *
     * @param adurMonthlyDiscountCurveWithSpreadQuery ADUR月度折现曲线含价差查询条件
     * @return ADUR月度折现曲线含价差列表
     */
    List<AdurMonthlyDiscountCurveWithSpreadDTO> selectAdurMonthlyDiscountCurveWithSpreadDtoList(AdurMonthlyDiscountCurveWithSpreadQuery adurMonthlyDiscountCurveWithSpreadQuery);

    /**
     * 根据ID查询ADUR月度折现曲线含价差
     *
     * @param id 主键ID
     * @return ADUR月度折现曲线含价差
     */
    AdurMonthlyDiscountCurveWithSpreadDTO selectAdurMonthlyDiscountCurveWithSpreadDtoById(Long id);

    /**
     * 新增ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadDto ADUR月度折现曲线含价差
     * @return 结果
     */
    int insertAdurMonthlyDiscountCurveWithSpreadDto(AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDto);

    /**
     * 修改ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadDto ADUR月度折现曲线含价差
     * @return 结果
     */
    int updateAdurMonthlyDiscountCurveWithSpreadDto(AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDto);

    /**
     * 批量删除ADUR月度折现曲线含价差
     *
     * @param ids 需要删除的ADUR月度折现曲线含价差主键集合
     * @return 结果
     */
    int deleteAdurMonthlyDiscountCurveWithSpreadDtoByIds(Long[] ids);

    /**
     * 删除ADUR月度折现曲线含价差信息
     *
     * @param id ADUR月度折现曲线含价差主键
     * @return 结果
     */
    int deleteAdurMonthlyDiscountCurveWithSpreadDtoById(Long id);

    /**
     * 导入ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadList ADUR月度折现曲线含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    String importAdurMonthlyDiscountCurveWithSpreadDto(List<AdurMonthlyDiscountCurveWithSpreadDTO> adurMonthlyDiscountCurveWithSpreadList, Boolean updateSupport, String operName);

    /**
     * 根据账期删除ADUR月度折现曲线含价差数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteAdurMonthlyDiscountCurveWithSpreadDtoByAccountPeriod(String accountPeriod);

    /**
     * 批量插入ADUR月度折现曲线含价差数据
     *
     * @param adurMonthlyDiscountCurveWithSpreadList ADUR月度折现曲线含价差列表
     * @return 影响行数
     */
    int batchInsertAdurMonthlyDiscountCurveWithSpreadDto(List<AdurMonthlyDiscountCurveWithSpreadDTO> adurMonthlyDiscountCurveWithSpreadList);
}
