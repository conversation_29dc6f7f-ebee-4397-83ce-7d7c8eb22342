package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.WindIndustryDTO;
import com.xl.alm.app.query.WindIndustryQuery;
import com.xl.alm.app.service.WindIndustryService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * Wind行业表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/wind/industry")
public class WindIndustryController extends BaseController {

    @Autowired
    private WindIndustryService windIndustryService;

    /**
     * 查询Wind行业表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:list')")
    @GetMapping("/list")
    public TableDataInfo list(WindIndustryQuery windIndustryQuery) {
        startPage();
        List<WindIndustryDTO> list = windIndustryService.selectWindIndustryDtoList(windIndustryQuery);
        return getDataTable(list);
    }

    /**
     * 获取Wind行业表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:query')")
    @GetMapping(value = "/info/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(windIndustryService.selectWindIndustryDtoById(id));
    }

    /**
     * 新增Wind行业表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:add')")
    @Log(title = "Wind行业表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody WindIndustryDTO windIndustryDTO) {
        return toAjax(windIndustryService.insertWindIndustryDto(windIndustryDTO));
    }

    /**
     * 修改Wind行业表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:edit')")
    @Log(title = "Wind行业表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody WindIndustryDTO windIndustryDTO) {
        return toAjax(windIndustryService.updateWindIndustryDto(windIndustryDTO));
    }

    /**
     * 删除Wind行业表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:remove')")
    @Log(title = "Wind行业表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(windIndustryService.deleteWindIndustryDtoByIds(ids));
    }

    /**
     * 批量新增Wind行业表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:add')")
    @Log(title = "Wind行业表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<WindIndustryDTO> windIndustryDTOList) {
        return toAjax(windIndustryService.batchInsertWindIndustryDto(windIndustryDTOList));
    }

    /**
     * 根据账期删除Wind行业表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:remove')")
    @Log(title = "Wind行业表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(windIndustryService.deleteWindIndustryDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入Wind行业表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:import')")
    @Log(title = "Wind行业表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<WindIndustryDTO> util = new ExcelUtil(WindIndustryDTO.class);
        List<WindIndustryDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = windIndustryService.importWindIndustryDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出Wind行业表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:export')")
    @Log(title = "Wind行业表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WindIndustryQuery query) {
        ExcelUtil<WindIndustryDTO> util = new ExcelUtil<>(WindIndustryDTO.class);
        List<WindIndustryDTO> list = windIndustryService.selectWindIndustryDtoList(query);
        util.exportExcel(list, "Wind行业表数据", response);
    }

    /**
     * 获取Wind行业表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:industry:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WindIndustryDTO> util = new ExcelUtil<>(WindIndustryDTO.class);
        util.exportTemplateExcel(response, "Wind行业表");
    }
}
