package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.PolicyDetailDTO;
import com.xl.alm.app.entity.PolicyDetailEntity;
import com.xl.alm.app.mapper.PolicyDetailMapper;
import com.xl.alm.app.query.PolicyDetailQuery;
import com.xl.alm.app.service.PolicyDetailService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 保单数据明细表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class PolicyDetailServiceImpl implements PolicyDetailService {

    @Autowired
    private PolicyDetailMapper policyDetailMapper;

    /**
     * 查询保单数据明细列表
     *
     * @param query 保单数据明细查询条件
     * @return 保单数据明细列表
     */
    @Override
    public List<PolicyDetailDTO> selectPolicyDetailDtoList(PolicyDetailQuery query) {
        List<PolicyDetailEntity> entityList = policyDetailMapper.selectPolicyDetailEntityList(query);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, PolicyDetailDTO.class);
    }

    /**
     * 用id查询保单数据明细
     *
     * @param id id
     * @return 保单数据明细
     */
    @Override
    public PolicyDetailDTO selectPolicyDetailDtoById(Long id) {
        PolicyDetailEntity entity = policyDetailMapper.selectPolicyDetailEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, PolicyDetailDTO.class);
    }

    /**
     * 根据险种号查询保单数据明细
     *
     * @param polNo 险种号
     * @return 保单数据明细
     */
    @Override
    public PolicyDetailDTO selectPolicyDetailDtoByPolNo(String polNo) {
        PolicyDetailEntity entity = policyDetailMapper.selectPolicyDetailEntityByPolNo(polNo);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, PolicyDetailDTO.class);
    }

    /**
     * 新增保单数据明细
     *
     * @param dto 保单数据明细
     * @return 结果
     */
    @Override
    public int insertPolicyDetailDto(PolicyDetailDTO dto) {
        PolicyDetailEntity entity = EntityDtoConvertUtil.convertToEntity(dto, PolicyDetailEntity.class);
        return policyDetailMapper.insertPolicyDetailEntity(entity);
    }

    /**
     * 批量新增保单数据明细
     *
     * @param dtoList 保单数据明细列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertPolicyDetailDto(List<PolicyDetailDTO> dtoList) {
        List<PolicyDetailEntity> entityList = EntityDtoConvertUtil.convertToEntityList(dtoList, PolicyDetailEntity.class);
        return policyDetailMapper.batchInsertPolicyDetailEntity(entityList);
    }

    /**
     * 修改保单数据明细
     *
     * @param dto 保单数据明细
     * @return 结果
     */
    @Override
    public int updatePolicyDetailDto(PolicyDetailDTO dto) {
        PolicyDetailEntity entity = EntityDtoConvertUtil.convertToEntity(dto, PolicyDetailEntity.class);
        return policyDetailMapper.updatePolicyDetailEntity(entity);
    }

    /**
     * 批量删除保单数据明细
     *
     * @param ids 需要删除的保单数据明细主键集合
     * @return 结果
     */
    @Override
    public int deletePolicyDetailDtoByIds(Long[] ids) {
        return policyDetailMapper.deletePolicyDetailEntityByIds(ids);
    }

    /**
     * 删除保单数据明细信息
     *
     * @param id 保单数据明细主键
     * @return 结果
     */
    @Override
    public int deletePolicyDetailDtoById(Long id) {
        return policyDetailMapper.deletePolicyDetailEntityById(id);
    }

    /**
     * 导入保单数据明细
     *
     * @param dtoList 保单数据明细数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importPolicyDetailDto(List<PolicyDetailDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.isEmpty()) {
            throw new RuntimeException("导入保单数据明细数据不能为空！");
        }

        long startTime = System.currentTimeMillis();
        log.info("开始导入保单数据明细，共{}条记录", dtoList.size());

        // 设置创建者和更新者，并自动计算月份字段
        for (PolicyDetailDTO dto : dtoList) {
            dto.setCreateBy(username);
            dto.setUpdateBy(username);

            // 自动计算险种生效月份
            if (dto.getEffectiveDate() != null) {
                dto.setEffectiveMonth(formatDateToMonth(dto.getEffectiveDate()));
            }

            // 自动计算险种状态变化月份
            if (dto.getPolStatusChangedTime() != null) {
                dto.setPolStatusChangedMonth(formatDateToMonth(dto.getPolStatusChangedTime()));
            }
        }

        try {
            // 使用批量插入优化性能
            int batchSize = 2000; // 每批处理2000条记录，参考系统中其他模块的最佳实践
            int totalSize = dtoList.size();
            int batchCount = (totalSize + batchSize - 1) / batchSize; // 计算批次数
            int successNum = 0;

            log.info("开始批量导入数据，共{}条记录，分{}批处理，每批{}条", totalSize, batchCount, batchSize);

            for (int i = 0; i < totalSize; i += batchSize) {
                long batchStartTime = System.currentTimeMillis();
                int endIndex = Math.min(i + batchSize, totalSize);
                List<PolicyDetailDTO> batchList = dtoList.subList(i, endIndex);

                try {
                    // 执行批量插入
                    int insertCount = batchInsertPolicyDetailDto(batchList);
                    successNum += insertCount;

                    log.info("批量导入第{}批数据完成，{}条记录，耗时：{}ms",
                            (i / batchSize) + 1, batchList.size(), System.currentTimeMillis() - batchStartTime);
                } catch (Exception e) {
                    log.error("批量导入第{}批数据失败，尝试单条插入", (i / batchSize) + 1, e);

                    // 如果批量插入失败，尝试单条插入
                    for (PolicyDetailDTO dto : batchList) {
                        try {
                            insertPolicyDetailDto(dto);
                            successNum++;
                        } catch (Exception ex) {
                            log.error("单条插入失败，险种号：{}", dto.getPolNo(), ex);
                            throw new RuntimeException("导入失败，险种号 " + dto.getPolNo() + " 数据有误：" + ex.getMessage());
                        }
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            String successMsg = "恭喜您，数据已全部导入成功！共 " + successNum + " 条，耗时：" + (endTime - startTime) + "ms";
            log.info("保单数据明细导入完成，{}", successMsg);
            return successMsg;

        } catch (Exception e) {
            log.error("保单数据明细导入失败", e);
            throw new RuntimeException("导入失败：" + e.getMessage());
        }
    }

    /**
     * 将日期格式化为YYYYMM格式的月份字符串
     *
     * @param date 日期
     * @return YYYYMM格式的字符串
     */
    private String formatDateToMonth(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return sdf.format(date);
    }
}
