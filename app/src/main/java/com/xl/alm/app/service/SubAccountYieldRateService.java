package com.xl.alm.app.service;

import com.xl.alm.app.dto.SubAccountYieldRateDTO;
import com.xl.alm.app.query.SubAccountYieldRateQuery;

import java.util.List;

/**
 * 子账户收益率表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface SubAccountYieldRateService {

    /**
     * 查询子账户收益率列表
     *
     * @param subAccountYieldRateQuery 子账户收益率查询条件
     * @return 子账户收益率列表
     */
    List<SubAccountYieldRateDTO> selectSubAccountYieldRateDtoList(SubAccountYieldRateQuery subAccountYieldRateQuery);

    /**
     * 用id查询子账户收益率
     *
     * @param id id
     * @return 子账户收益率
     */
    SubAccountYieldRateDTO selectSubAccountYieldRateDtoById(Long id);

    /**
     * 根据统计期间和子账户名称查询子账户收益率
     *
     * @param accountingPeriod 统计期间
     * @param accountName 子账户名称
     * @return 子账户收益率
     */
    SubAccountYieldRateDTO selectSubAccountYieldRateDtoByCondition(
            String accountingPeriod,
            String accountName);

    /**
     * 新增子账户收益率
     *
     * @param dto 子账户收益率
     * @return 影响行数
     */
    int insertSubAccountYieldRateDto(SubAccountYieldRateDTO dto);

    /**
     * 批量插入子账户收益率数据
     *
     * @param subAccountYieldRateDtoList 子账户收益率列表
     * @return 影响行数
     */
    int batchInsertSubAccountYieldRateDto(List<SubAccountYieldRateDTO> subAccountYieldRateDtoList);

    /**
     * 更新子账户收益率数据
     *
     * @param dto 子账户收益率
     * @return 结果
     */
    int updateSubAccountYieldRateDto(SubAccountYieldRateDTO dto);

    /**
     * 删除指定统计期间的子账户收益率数据
     *
     * @param accountingPeriod 统计期间
     * @return 影响行数
     */
    int deleteSubAccountYieldRateDtoByPeriod(String accountingPeriod);

    /**
     * 删除指定id的子账户收益率数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteSubAccountYieldRateDtoById(Long id);

    /**
     * 批量删除子账户收益率数据
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteSubAccountYieldRateDtoByIds(Long[] ids);

    /**
     * 导入子账户收益率
     *
     * @param dtoList       子账户收益率数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importSubAccountYieldRateDto(List<SubAccountYieldRateDTO> dtoList, Boolean updateSupport, String username);
}
