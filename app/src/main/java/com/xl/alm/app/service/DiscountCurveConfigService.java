package com.xl.alm.app.service;

import com.xl.alm.app.dto.DiscountCurveConfigDTO;
import com.xl.alm.app.query.DiscountCurveConfigQuery;

import java.util.List;

/**
 * 折现曲线配置表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface DiscountCurveConfigService {

    /**
     * 查询折现曲线配置表列表
     *
     * @param discountCurveConfigQuery 折现曲线配置表查询条件
     * @return 折现曲线配置表列表
     */
    List<DiscountCurveConfigDTO> selectDiscountCurveConfigDtoList(DiscountCurveConfigQuery discountCurveConfigQuery);

    /**
     * 根据主键查询折现曲线配置表
     *
     * @param id 主键
     * @return 折现曲线配置表
     */
    DiscountCurveConfigDTO selectDiscountCurveConfigDtoById(Long id);

    /**
     * 新增折现曲线配置表
     *
     * @param discountCurveConfigDTO 折现曲线配置表
     * @return 影响行数
     */
    int insertDiscountCurveConfigDto(DiscountCurveConfigDTO discountCurveConfigDTO);

    /**
     * 修改折现曲线配置表
     *
     * @param discountCurveConfigDTO 折现曲线配置表
     * @return 影响行数
     */
    int updateDiscountCurveConfigDto(DiscountCurveConfigDTO discountCurveConfigDTO);

    /**
     * 批量删除折现曲线配置表
     *
     * @param ids 需要删除的折现曲线配置表主键集合
     * @return 影响行数
     */
    int deleteDiscountCurveConfigDtoByIds(Long[] ids);

    /**
     * 删除折现曲线配置表信息
     *
     * @param id 折现曲线配置表主键
     * @return 影响行数
     */
    int deleteDiscountCurveConfigDtoById(Long id);

    /**
     * 批量新增折现曲线配置表
     *
     * @param discountCurveConfigDtoList 折现曲线配置表列表
     * @return 影响行数
     */
    int batchInsertDiscountCurveConfigDto(List<DiscountCurveConfigDTO> discountCurveConfigDtoList);

    /**
     * 根据账期删除折现曲线配置表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteDiscountCurveConfigDtoByPeriod(String accountingPeriod);

    /**
     * 导入折现曲线配置表数据
     *
     * @param discountCurveConfigDtoList 折现曲线配置表数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importDiscountCurveConfigDto(List<DiscountCurveConfigDTO> discountCurveConfigDtoList, Boolean updateSupport, String operName);
}
