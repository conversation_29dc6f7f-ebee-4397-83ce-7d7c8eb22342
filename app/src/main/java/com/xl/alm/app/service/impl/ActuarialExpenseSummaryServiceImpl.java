package com.xl.alm.app.service.impl;

import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.ActuarialExpenseSummaryDTO;
import com.xl.alm.app.dto.ActuarialExpenseSummaryImportDTO;
import com.xl.alm.app.entity.ActuarialExpenseSummaryEntity;
import com.xl.alm.app.mapper.ActuarialExpenseSummaryMapper;
import com.xl.alm.app.query.ActuarialExpenseSummaryQuery;
import com.xl.alm.app.service.ActuarialExpenseSummaryService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import java.util.ArrayList;
import java.util.List;

/**
 * 精算业管费汇总表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class ActuarialExpenseSummaryServiceImpl implements ActuarialExpenseSummaryService {

    @Autowired
    private ActuarialExpenseSummaryMapper actuarialExpenseSummaryMapper;

    /**
     * 查询精算业管费汇总表列表
     *
     * @param actuarialExpenseSummaryQuery 精算业管费汇总表查询条件
     * @return 精算业管费汇总表列表
     */
    @Override
    public List<ActuarialExpenseSummaryDTO> selectActuarialExpenseSummaryDtoList(ActuarialExpenseSummaryQuery actuarialExpenseSummaryQuery) {
        List<ActuarialExpenseSummaryEntity> entityList = actuarialExpenseSummaryMapper.selectActuarialExpenseSummaryEntityList(actuarialExpenseSummaryQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, ActuarialExpenseSummaryDTO.class);
    }

    /**
     * 根据ID查询精算业管费汇总表
     *
     * @param id 主键ID
     * @return 精算业管费汇总表
     */
    @Override
    public ActuarialExpenseSummaryDTO selectActuarialExpenseSummaryDtoById(Long id) {
        ActuarialExpenseSummaryEntity entity = actuarialExpenseSummaryMapper.selectActuarialExpenseSummaryEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, ActuarialExpenseSummaryDTO.class);
    }

    /**
     * 新增精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDto 精算业管费汇总表
     * @return 影响行数
     */
    @Override
    public int insertActuarialExpenseSummaryDto(ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto) {
        ActuarialExpenseSummaryEntity entity = EntityDtoConvertUtil.convertToEntity(actuarialExpenseSummaryDto, ActuarialExpenseSummaryEntity.class);
        return actuarialExpenseSummaryMapper.insertActuarialExpenseSummaryEntity(entity);
    }

    /**
     * 修改精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDto 精算业管费汇总表
     * @return 影响行数
     */
    @Override
    public int updateActuarialExpenseSummaryDto(ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto) {
        ActuarialExpenseSummaryEntity entity = EntityDtoConvertUtil.convertToEntity(actuarialExpenseSummaryDto, ActuarialExpenseSummaryEntity.class);
        return actuarialExpenseSummaryMapper.updateActuarialExpenseSummaryEntity(entity);
    }

    /**
     * 删除精算业管费汇总表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Override
    public int deleteActuarialExpenseSummaryDtoById(Long id) {
        return actuarialExpenseSummaryMapper.deleteActuarialExpenseSummaryEntityById(id);
    }

    /**
     * 批量删除精算业管费汇总表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    @Override
    public int deleteActuarialExpenseSummaryDtoByIds(Long[] ids) {
        return actuarialExpenseSummaryMapper.deleteActuarialExpenseSummaryEntityByIds(ids);
    }

    /**
     * 批量新增精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDtoList 精算业管费汇总表列表
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertActuarialExpenseSummaryDto(List<ActuarialExpenseSummaryDTO> actuarialExpenseSummaryDtoList) {
        if (actuarialExpenseSummaryDtoList == null || actuarialExpenseSummaryDtoList.isEmpty()) {
            return 0;
        }
        // 转换字典标签为字典值
        for (ActuarialExpenseSummaryDTO dto : actuarialExpenseSummaryDtoList) {
            convertDictLabelToValue(dto);
        }
        List<ActuarialExpenseSummaryEntity> entityList = EntityDtoConvertUtil.convertToEntityList(actuarialExpenseSummaryDtoList, ActuarialExpenseSummaryEntity.class);
        return actuarialExpenseSummaryMapper.batchInsertActuarialExpenseSummaryEntity(entityList);
    }

    /**
     * 根据账期删除精算业管费汇总表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteActuarialExpenseSummaryDtoByPeriod(String accountingPeriod) {
        return actuarialExpenseSummaryMapper.deleteActuarialExpenseSummaryEntityByPeriod(accountingPeriod);
    }

    /**
     * 根据条件查询精算业管费汇总表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param actuarialExpenseType 精算费用类型
     * @param businessType 业务类型
     * @param designType 设计类型
     * @return 精算业管费汇总表
     */
    @Override
    public ActuarialExpenseSummaryDTO selectActuarialExpenseSummaryDtoByCondition(
            String accountingPeriod, String scenarioName, String actuarialExpenseType,
            String businessType, String designType) {
        ActuarialExpenseSummaryEntity entity = actuarialExpenseSummaryMapper.selectActuarialExpenseSummaryEntityByCondition(
                accountingPeriod, scenarioName, actuarialExpenseType, businessType, designType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, ActuarialExpenseSummaryDTO.class);
    }

    /**
     * 导入精算业管费汇总表数据
     *
     * @param actuarialExpenseSummaryDtoList 精算业管费汇总表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importActuarialExpenseSummaryData(List<ActuarialExpenseSummaryDTO> actuarialExpenseSummaryDtoList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(actuarialExpenseSummaryDtoList) || actuarialExpenseSummaryDtoList.size() == 0) {
            throw new ServiceException("导入精算业管费汇总数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto : actuarialExpenseSummaryDtoList) {
            try {
                // 转换字典标签为字典值
                convertDictLabelToValue(actuarialExpenseSummaryDto);
                
                // 验证是否存在这个精算业管费汇总
                ActuarialExpenseSummaryDTO existDto = this.selectActuarialExpenseSummaryDtoByCondition(
                        actuarialExpenseSummaryDto.getAccountingPeriod(),
                        actuarialExpenseSummaryDto.getScenarioName(),
                        actuarialExpenseSummaryDto.getActuarialExpenseType(),
                        actuarialExpenseSummaryDto.getBusinessType(),
                        actuarialExpenseSummaryDto.getDesignType());
                
                if (StringUtils.isNull(existDto)) {
                    actuarialExpenseSummaryDto.setCreateBy(operName);
                    this.insertActuarialExpenseSummaryDto(actuarialExpenseSummaryDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + actuarialExpenseSummaryDto.getAccountingPeriod() + " 的数据导入成功");
                } else if (isUpdateSupport) {
                    actuarialExpenseSummaryDto.setUpdateBy(operName);
                    actuarialExpenseSummaryDto.setId(existDto.getId());
                    this.updateActuarialExpenseSummaryDto(actuarialExpenseSummaryDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + actuarialExpenseSummaryDto.getAccountingPeriod() + " 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + actuarialExpenseSummaryDto.getAccountingPeriod() + " 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + actuarialExpenseSummaryDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }



    /**
     * 转换字典标签为字典值
     *
     * @param actuarialExpenseSummaryDto 精算业管费汇总DTO
     */
    private void convertDictLabelToValue(ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto) {
        // 转换情景名称
        if (StringUtils.isNotEmpty(actuarialExpenseSummaryDto.getScenarioName())) {
            String convertedScenarioName = DictConvertUtil.convertLabelToValue(
                    actuarialExpenseSummaryDto.getScenarioName(), 
                    "cft_scenario_name"
            );
            actuarialExpenseSummaryDto.setScenarioName(convertedScenarioName);
        }

        // 精算费用类型不需要字典转换，直接使用变量名称

        // 转换业务类型
        if (StringUtils.isNotEmpty(actuarialExpenseSummaryDto.getBusinessType())) {
            String convertedBusinessType = DictConvertUtil.convertLabelToValue(
                    actuarialExpenseSummaryDto.getBusinessType(), 
                    "cost_business_type"
            );
            actuarialExpenseSummaryDto.setBusinessType(convertedBusinessType);
        }

        // 转换设计类型
        if (StringUtils.isNotEmpty(actuarialExpenseSummaryDto.getDesignType())) {
            String convertedDesignType = DictConvertUtil.convertLabelToValue(
                    actuarialExpenseSummaryDto.getDesignType(), 
                    "cost_design_type"
            );
            actuarialExpenseSummaryDto.setDesignType(convertedDesignType);
        }
    }

    /**
     * 导入精算业管费汇总数据（横向Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importActuarialExpenseSummaryDataFromExcel(InputStream inputStream, Boolean isUpdateSupport, String operName) {
        try {
            // 解析Excel文件
            List<ActuarialExpenseSummaryImportDTO> importDtoList = parseExcelFile(inputStream);

            if (importDtoList == null || importDtoList.isEmpty()) {
                throw new RuntimeException("导入数据不能为空");
            }

            log.info("开始批量导入精算业管费汇总数据，总数量: {}", importDtoList.size());

            // 转换为标准DTO并导入
            return importActuarialExpenseSummaryDataBatch(importDtoList, isUpdateSupport, operName);

        } catch (Exception e) {
            log.error("导入精算业管费汇总数据失败", e);
            throw new RuntimeException("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出精算业管费汇总表模板
     */
    @Override
    public void exportTemplate(HttpServletResponse response) throws Exception {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("精算业管费汇总表");

        // 创建表头
        Row headerRow = sheet.createRow(0);

        // 基础列
        String[] baseColumns = {"账期", "情景名称", "精算费用类型", "业务类型", "设计类型"};
        for (int i = 0; i < baseColumns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(baseColumns[i]);
        }

        // 生成36个月的日期列（从当前月开始）
        List<String> dateColumns = generateDateSequence("");
        for (int i = 0; i < dateColumns.size(); i++) {
            Cell cell = headerRow.createCell(baseColumns.length + i);
            cell.setCellValue(dateColumns.get(i));
        }

        // 设置列宽
        for (int i = 0; i < baseColumns.length + dateColumns.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("精算业管费汇总表导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    /**
     * 导出精算业管费汇总表（水平格式）
     */
    @Override
    public void exportActuarialExpenseSummaryHorizontal(HttpServletResponse response, ActuarialExpenseSummaryQuery query) throws Exception {
        // 查询数据
        List<ActuarialExpenseSummaryDTO> dataList = selectActuarialExpenseSummaryDtoList(query);

        if (dataList.isEmpty()) {
            throw new RuntimeException("没有数据可导出");
        }

        // 转换字典值为中文标签
        convertDictValueToLabelForExport(dataList);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("精算业管费汇总表");

        // 生成36个月的日期列表
        List<String> dateColumns = generateDateSequence(dataList.get(0).getAccountingPeriod());

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] baseHeaders = {"账期", "情景名称", "精算费用类型", "业务类型", "设计类型"};

        // 设置基础列头
        for (int i = 0; i < baseHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(baseHeaders[i]);
        }

        // 设置日期列头
        for (int i = 0; i < dateColumns.size(); i++) {
            Cell cell = headerRow.createCell(baseHeaders.length + i);
            cell.setCellValue(dateColumns.get(i));
        }

        // 填充数据行
        for (int rowIndex = 0; rowIndex < dataList.size(); rowIndex++) {
            ActuarialExpenseSummaryDTO dto = dataList.get(rowIndex);
            Row dataRow = sheet.createRow(rowIndex + 1);

            // 基础字段
            dataRow.createCell(0).setCellValue(dto.getAccountingPeriod() != null ? dto.getAccountingPeriod() : "");
            dataRow.createCell(1).setCellValue(dto.getScenarioName() != null ? dto.getScenarioName() : "");
            dataRow.createCell(2).setCellValue(dto.getActuarialExpenseType() != null ? dto.getActuarialExpenseType() : "");
            dataRow.createCell(3).setCellValue(dto.getBusinessType() != null ? dto.getBusinessType() : "");
            dataRow.createCell(4).setCellValue(dto.getDesignType() != null ? dto.getDesignType() : "");

            // 现金流数据
            Map<String, Object> cashFlowMap = parseCashFlowValueSet(dto.getCashFlowValueSet());
            for (int i = 0; i < dateColumns.size(); i++) {
                String dateKey = dateColumns.get(i);
                String value = getCashFlowValueForDate(cashFlowMap, dateKey);

                Cell cell = dataRow.createCell(baseHeaders.length + i);
                if ("-".equals(value) || "0".equals(value)) {
                    cell.setCellValue("-");
                } else {
                    try {
                        // 尝试转换为数字并格式化
                        double numValue = Double.parseDouble(value.replace(",", ""));
                        cell.setCellValue(String.format("%,.2f", numValue));
                    } catch (NumberFormatException e) {
                        cell.setCellValue(value);
                    }
                }
            }
        }

        // 设置列宽
        for (int i = 0; i < baseHeaders.length + dateColumns.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("精算业管费汇总表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    /**
     * 解析Excel文件
     */
    private List<ActuarialExpenseSummaryImportDTO> parseExcelFile(InputStream inputStream) throws Exception {
        List<ActuarialExpenseSummaryImportDTO> result = new ArrayList<>();

        Workbook workbook = WorkbookFactory.create(inputStream);
        Sheet sheet = workbook.getSheetAt(0);

        if (sheet.getPhysicalNumberOfRows() <= 1) {
            throw new RuntimeException("Excel文件没有数据行");
        }

        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new RuntimeException("Excel文件缺少表头行");
        }

        // 解析表头，找到各列的位置
        Map<String, Integer> columnIndexMap = new HashMap<>();
        List<String> dateColumns = new ArrayList<>();

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String columnName = getCellValueAsString(cell);
                columnIndexMap.put(columnName, i);

                // 识别日期列
                if (isDateColumn(columnName)) {
                    dateColumns.add(columnName);
                }
            }
        }

        // 解析数据行
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;

            ActuarialExpenseSummaryImportDTO dto = new ActuarialExpenseSummaryImportDTO();
            Map<String, String> cashFlowDataMap = new HashMap<>();

            // 解析基础字段
            dto.setAccountingPeriod(getCellValue(row, columnIndexMap.get("账期")));
            dto.setScenarioName(getCellValue(row, columnIndexMap.get("情景名称")));
            dto.setActuarialExpenseType(getCellValue(row, columnIndexMap.get("精算费用类型")));
            dto.setBusinessType(getCellValue(row, columnIndexMap.get("业务类型")));
            dto.setDesignType(getCellValue(row, columnIndexMap.get("设计类型")));

            // 解析日期数据
            for (String dateColumn : dateColumns) {
                Integer columnIndex = columnIndexMap.get(dateColumn);
                if (columnIndex != null) {
                    String value = getCellValue(row, columnIndex);
                    if (StringUtils.isNotEmpty(value) && !"-".equals(value.trim())) {
                        cashFlowDataMap.put(dateColumn, value);
                    }
                }
            }

            dto.setCashFlowDataMap(cashFlowDataMap);
            result.add(dto);
        }

        workbook.close();
        return result;
    }

    /**
     * 批量导入精算业管费汇总数据
     */
    private String importActuarialExpenseSummaryDataBatch(List<ActuarialExpenseSummaryImportDTO> importDtoList, Boolean isUpdateSupport, String operName) {
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ActuarialExpenseSummaryImportDTO importDto : importDtoList) {
            try {
                // 转换为标准DTO
                ActuarialExpenseSummaryDTO dto = convertImportDtoToDto(importDto);
                dto.setCreateBy(operName);
                dto.setUpdateBy(operName);

                // 转换字典标签为字典值
                convertDictLabelToValue(dto);

                // 验证是否存在
                ActuarialExpenseSummaryDTO existDto = this.selectActuarialExpenseSummaryDtoByCondition(
                        dto.getAccountingPeriod(), dto.getScenarioName(), dto.getActuarialExpenseType(),
                        dto.getBusinessType(), dto.getDesignType());

                if (existDto == null) {
                    this.insertActuarialExpenseSummaryDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据导入成功");
                } else if (isUpdateSupport) {
                    dto.setId(existDto.getId());
                    this.updateActuarialExpenseSummaryDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + importDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 转换导入DTO为标准DTO
     */
    private ActuarialExpenseSummaryDTO convertImportDtoToDto(ActuarialExpenseSummaryImportDTO importDto) throws Exception {
        ActuarialExpenseSummaryDTO dto = new ActuarialExpenseSummaryDTO();
        dto.setAccountingPeriod(importDto.getAccountingPeriod());
        dto.setScenarioName(importDto.getScenarioName());
        dto.setActuarialExpenseType(importDto.getActuarialExpenseType());
        dto.setBusinessType(importDto.getBusinessType());
        dto.setDesignType(importDto.getDesignType());

        // 转换现金流数据为JSON
        String cashFlowValueSet = convertCashFlowDataMapToJson(importDto.getCashFlowDataMap(), importDto.getAccountingPeriod());
        dto.setCashFlowValueSet(cashFlowValueSet);

        return dto;
    }

    /**
     * 转换现金流数据Map为JSON格式
     */
    private String convertCashFlowDataMapToJson(Map<String, String> cashFlowDataMap, String accountingPeriod) throws Exception {
        if (cashFlowDataMap == null || cashFlowDataMap.isEmpty()) {
            return "{}";
        }

        Map<String, Map<String, Object>> cashFlowMap = new HashMap<>();
        int index = 0;

        // 生成正确的日期序列
        List<String> correctDates = generateDateSequence(accountingPeriod);

        for (String date : correctDates) {
            String value = cashFlowDataMap.get(date);
            if (StringUtils.isNotEmpty(value) && !"-".equals(value.trim())) {
                // 清理数值（去除逗号、空格等）
                String cleanValue = cleanNumericValue(value);

                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("日期", date);
                valueMap.put("值", cleanValue);

                cashFlowMap.put(String.valueOf(index), valueMap);
                index++;
            }
        }

        // 转换为JSON字符串
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(cashFlowMap);
    }

    /**
     * 生成日期序列（36个月）
     */
    private List<String> generateDateSequence(String accountingPeriod) {
        List<String> dates = new ArrayList<>();

        LocalDate startDate;
        if (StringUtils.isNotEmpty(accountingPeriod) && accountingPeriod.length() == 6) {
            // 如果账期有值，从账期当月开始
            try {
                int year = Integer.parseInt(accountingPeriod.substring(0, 4));
                int month = Integer.parseInt(accountingPeriod.substring(4, 6));
                startDate = LocalDate.of(year, month, 1);
            } catch (Exception e) {
                log.warn("账期格式错误，使用当前时间: " + accountingPeriod, e);
                startDate = LocalDate.now().withDayOfMonth(1);
            }
        } else {
            // 如果账期为空，从当前时间开始
            startDate = LocalDate.now().withDayOfMonth(1);
        }

        // 生成36个月的日期序列
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
        for (int i = 0; i < 36; i++) {
            LocalDate currentDate = startDate.plusMonths(i);
            // 获取该月的最后一天
            LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
            dates.add(lastDayOfMonth.format(formatter));
        }

        return dates;
    }

    /**
     * 生成季度序列
     */
    private List<String> generateQuarterSequence(String accountingPeriod) {
        List<String> quarters = new ArrayList<>();

        if (StringUtils.isEmpty(accountingPeriod)) {
            // 如果没有账期，从当前季度开始
            LocalDate now = LocalDate.now();
            int year = now.getYear();
            int quarter = (now.getMonthValue() - 1) / 3 + 1;

            for (int i = 0; i < 12; i++) {
                quarters.add(year + "Q" + quarter);
                quarter++;
                if (quarter > 4) {
                    quarter = 1;
                    year++;
                }
            }
        } else {
            // 根据账期生成季度序列
            try {
                int year = Integer.parseInt(accountingPeriod.substring(0, 4));
                int month = Integer.parseInt(accountingPeriod.substring(4, 6));
                int startQuarter = (month - 1) / 3 + 1;

                // 从下一个季度开始
                startQuarter++;
                if (startQuarter > 4) {
                    startQuarter = 1;
                    year++;
                }

                for (int i = 0; i < 12; i++) {
                    quarters.add(year + "Q" + startQuarter);
                    startQuarter++;
                    if (startQuarter > 4) {
                        startQuarter = 1;
                        year++;
                    }
                }
            } catch (Exception e) {
                log.warn("解析账期失败，使用默认季度序列: {}", accountingPeriod);
                return generateQuarterSequence("");
            }
        }

        return quarters;
    }

    /**
     * 判断是否为日期列
     */
    private boolean isDateColumn(String columnName) {
        if (StringUtils.isEmpty(columnName)) {
            return false;
        }
        // 匹配格式：yyyy/M/d 或 yyyy/MM/dd
        return columnName.matches("\\d{4}/\\d{1,2}/\\d{1,2}");
    }

    /**
     * 判断是否为季度列
     */
    private boolean isQuarterColumn(String columnName) {
        if (StringUtils.isEmpty(columnName)) {
            return false;
        }
        // 匹配格式：YYYYQX
        return columnName.matches("\\d{4}Q[1-4]");
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(cell.getLocalDateTimeCellValue().toLocalDate());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Row row, Integer columnIndex) {
        if (row == null || columnIndex == null) {
            return "";
        }

        Cell cell = row.getCell(columnIndex);
        return getCellValueAsString(cell);
    }

    /**
     * 清理数值字符串
     */
    private String cleanNumericValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return "0";
        }

        // 去除逗号、空格、货币符号等
        String cleaned = value.trim()
                .replace(",", "")
                .replace("，", "")
                .replace(" ", "")
                .replace("￥", "")
                .replace("$", "");

        // 验证是否为有效数字
        try {
            Double.parseDouble(cleaned);
            return cleaned;
        } catch (NumberFormatException e) {
            log.warn("无效的数值格式: {}", value);
            return "0";
        }
    }

    /**
     * 转换字典值为中文标签用于导出
     */
    private void convertDictValueToLabelForExport(List<ActuarialExpenseSummaryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (ActuarialExpenseSummaryDTO dto : list) {
            // 转换情景名称
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(dto.getScenarioName(), "cft_scenario_name");
                dto.setScenarioName(scenarioLabel);
            }

            // 精算费用类型不需要字典转换，直接使用变量名称

            // 转换业务类型
            if (dto.getBusinessType() != null) {
                String businessTypeLabel = DictConvertUtil.convertValueToLabel(dto.getBusinessType(), "cost_business_type");
                dto.setBusinessType(businessTypeLabel);
            }

            // 转换设计类型
            if (dto.getDesignType() != null) {
                String designTypeLabel = DictConvertUtil.convertValueToLabel(dto.getDesignType(), "cost_design_type");
                dto.setDesignType(designTypeLabel);
            }
        }
    }

    /**
     * 解析现金流值集
     */
    private Map<String, Object> parseCashFlowValueSet(String cashFlowValueSet) {
        if (StringUtils.isEmpty(cashFlowValueSet)) {
            return new HashMap<>();
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(cashFlowValueSet, Map.class);
        } catch (Exception e) {
            log.warn("解析现金流值集失败: {}", cashFlowValueSet);
            return new HashMap<>();
        }
    }

    /**
     * 获取指定日期的现金流值
     */
    private String getCashFlowValueForDate(Map<String, Object> cashFlowMap, String targetDate) {
        if (cashFlowMap == null || cashFlowMap.isEmpty()) {
            return "-";
        }

        // 遍历现金流数据，查找匹配的日期
        for (Map.Entry<String, Object> entry : cashFlowMap.entrySet()) {
            Object valueObj = entry.getValue();
            if (valueObj instanceof Map) {
                Map<String, Object> valueMap = (Map<String, Object>) valueObj;
                String date = String.valueOf(valueMap.get("日期"));

                // 支持多种日期格式匹配
                if (targetDate.equals(date) || isDateMatch(date, targetDate)) {
                    Object value = valueMap.get("值");
                    if (value != null) {
                        return String.valueOf(value);
                    }
                }
            }
        }

        return "-";
    }

    /**
     * 判断日期是否匹配
     */
    private boolean isDateMatch(String dateInData, String targetDate) {
        if (StringUtils.isEmpty(dateInData) || StringUtils.isEmpty(targetDate)) {
            return false;
        }

        // 直接比较日期字符串
        if (dateInData.equals(targetDate)) {
            return true;
        }

        // 尝试标准化日期格式进行比较
        try {
            String normalizedDataDate = normalizeDateString(dateInData);
            String normalizedTargetDate = normalizeDateString(targetDate);
            return normalizedDataDate.equals(normalizedTargetDate);
        } catch (Exception e) {
            log.warn("日期格式比较失败: dateInData={}, targetDate={}", dateInData, targetDate);
            return false;
        }
    }

    /**
     * 标准化日期字符串格式
     */
    private String normalizeDateString(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return "";
        }

        // 处理格式：yyyy/M/d 或 yyyy/MM/dd
        if (dateStr.matches("\\d{4}/\\d{1,2}/\\d{1,2}")) {
            try {
                String[] parts = dateStr.split("/");
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                return String.format("%d/%d/%d", year, month, day);
            } catch (Exception e) {
                log.warn("日期格式标准化失败: {}", dateStr);
                return dateStr;
            }
        }

        return dateStr;
    }
}
