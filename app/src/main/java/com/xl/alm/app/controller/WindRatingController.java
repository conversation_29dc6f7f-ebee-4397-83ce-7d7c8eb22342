package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.WindRatingDTO;
import com.xl.alm.app.query.WindRatingQuery;
import com.xl.alm.app.service.WindRatingService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * Wind评级表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/wind/rating")
public class WindRatingController extends BaseController {

    @Autowired
    private WindRatingService windRatingService;

    /**
     * 查询Wind评级表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:list')")
    @GetMapping("/list")
    public TableDataInfo list(WindRatingQuery windRatingQuery) {
        startPage();
        List<WindRatingDTO> list = windRatingService.selectWindRatingDtoList(windRatingQuery);
        return getDataTable(list);
    }

    /**
     * 获取Wind评级表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:query')")
    @GetMapping(value = "/info/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(windRatingService.selectWindRatingDtoById(id));
    }

    /**
     * 新增Wind评级表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:add')")
    @Log(title = "Wind评级表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody WindRatingDTO windRatingDTO) {
        return toAjax(windRatingService.insertWindRatingDto(windRatingDTO));
    }

    /**
     * 修改Wind评级表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:edit')")
    @Log(title = "Wind评级表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody WindRatingDTO windRatingDTO) {
        return toAjax(windRatingService.updateWindRatingDto(windRatingDTO));
    }

    /**
     * 删除Wind评级表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:remove')")
    @Log(title = "Wind评级表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(windRatingService.deleteWindRatingDtoByIds(ids));
    }

    /**
     * 批量新增Wind评级表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:add')")
    @Log(title = "Wind评级表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<WindRatingDTO> windRatingDTOList) {
        return toAjax(windRatingService.batchInsertWindRatingDto(windRatingDTOList));
    }

    /**
     * 根据账期删除Wind评级表
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:remove')")
    @Log(title = "Wind评级表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(windRatingService.deleteWindRatingDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入Wind评级表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:import')")
    @Log(title = "Wind评级表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<WindRatingDTO> util = new ExcelUtil(WindRatingDTO.class);
        List<WindRatingDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = windRatingService.importWindRatingDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出Wind评级表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:export')")
    @Log(title = "Wind评级表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WindRatingQuery query) {
        ExcelUtil<WindRatingDTO> util = new ExcelUtil<>(WindRatingDTO.class);
        List<WindRatingDTO> list = windRatingService.selectWindRatingDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "Wind评级表数据", response);
    }

    /**
     * 获取Wind评级表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:wind:rating:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WindRatingDTO> util = new ExcelUtil<>(WindRatingDTO.class);
        util.exportTemplateExcel(response, "Wind评级表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list Wind评级表数据列表
     */
    private void convertDictValueToLabel(List<WindRatingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (WindRatingDTO dto : list) {
            // 转换主体评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getEntityRating())) {
                String entityRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getEntityRating(),
                        "ast_credit_rating"
                );
                dto.setEntityRating(entityRatingLabel);
            }

            // 转换债项评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getBondRating())) {
                String bondRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getBondRating(),
                        "ast_credit_rating"
                );
                dto.setBondRating(bondRatingLabel);
            }
        }
    }
}
