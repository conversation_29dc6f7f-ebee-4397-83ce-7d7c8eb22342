package com.xl.alm.app.service;

import com.xl.alm.app.dto.FinancialBudgetExpenseSplitDTO;
import com.xl.alm.app.query.FinancialBudgetExpenseSplitQuery;

import java.util.List;

/**
 * 财务预算费用拆分表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FinancialBudgetExpenseSplitService {

    /**
     * 查询财务预算费用拆分表列表
     *
     * @param financialBudgetExpenseSplitQuery 财务预算费用拆分表查询条件
     * @return 财务预算费用拆分表列表
     */
    List<FinancialBudgetExpenseSplitDTO> selectFinancialBudgetExpenseSplitDtoList(FinancialBudgetExpenseSplitQuery financialBudgetExpenseSplitQuery);

    /**
     * 根据ID查询财务预算费用拆分表
     *
     * @param id 主键ID
     * @return 财务预算费用拆分表
     */
    FinancialBudgetExpenseSplitDTO selectFinancialBudgetExpenseSplitDtoById(Long id);

    /**
     * 新增财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDto 财务预算费用拆分表
     * @return 影响行数
     */
    int insertFinancialBudgetExpenseSplitDto(FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto);

    /**
     * 修改财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDto 财务预算费用拆分表
     * @return 影响行数
     */
    int updateFinancialBudgetExpenseSplitDto(FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto);

    /**
     * 删除财务预算费用拆分表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseSplitDtoById(Long id);

    /**
     * 批量删除财务预算费用拆分表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseSplitDtoByIds(Long[] ids);

    /**
     * 批量新增财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDtoList 财务预算费用拆分表列表
     * @return 影响行数
     */
    int batchInsertFinancialBudgetExpenseSplitDto(List<FinancialBudgetExpenseSplitDTO> financialBudgetExpenseSplitDtoList);

    /**
     * 根据账期删除财务预算费用拆分表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseSplitDtoByPeriod(String accountingPeriod);

    /**
     * 根据条件查询财务预算费用拆分表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param financialExpenseType 财务费用类型
     * @param businessType 业务类型
     * @param designType 设计类型
     * @return 财务预算费用拆分表
     */
    FinancialBudgetExpenseSplitDTO selectFinancialBudgetExpenseSplitDtoByCondition(String accountingPeriod, String scenarioName, String financialExpenseType, String businessType, String designType);

    /**
     * 导入财务预算费用拆分表数据
     *
     * @param financialBudgetExpenseSplitDtoList 财务预算费用拆分表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFinancialBudgetExpenseSplitData(List<FinancialBudgetExpenseSplitDTO> financialBudgetExpenseSplitDtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导入财务预算费用拆分表数据（Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFinancialBudgetExpenseSplitDataFromExcel(java.io.InputStream inputStream, Boolean isUpdateSupport, String operName);

    /**
     * 导出财务预算费用拆分表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 导出财务预算费用拆分表
     *
     * @param response HTTP响应
     * @param query 查询条件
     */
    void exportFinancialBudgetExpenseSplitHorizontal(javax.servlet.http.HttpServletResponse response, FinancialBudgetExpenseSplitQuery query) throws Exception;
}
