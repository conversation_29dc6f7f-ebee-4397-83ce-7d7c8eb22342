package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.BusinessCashFlowForecastDTO;
import com.xl.alm.app.query.BusinessCashFlowForecastQuery;
import com.xl.alm.app.service.BusinessCashFlowForecastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 业务现金流预测表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/business/cash/flow/forecast")
public class BusinessCashFlowForecastController extends BaseController {

    @Autowired
    private BusinessCashFlowForecastService businessCashFlowForecastService;

    /**
     * 查询业务现金流预测表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessCashFlowForecastQuery query) {
        startPage();
        List<BusinessCashFlowForecastDTO> list = businessCashFlowForecastService.selectBusinessCashFlowForecastDtoList(query);
        return getDataTable(list);
    }

    /**
     * 根据ID获取业务现金流预测表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(businessCashFlowForecastService.selectBusinessCashFlowForecastDtoById(id));
    }

    /**
     * 新增业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:add')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody BusinessCashFlowForecastDTO businessCashFlowForecastDto) {
        // 验证是否存在相同的业务现金流预测
        BusinessCashFlowForecastDTO existDto = businessCashFlowForecastService.selectBusinessCashFlowForecastDtoByCondition(
                businessCashFlowForecastDto.getAccountingPeriod(),
                businessCashFlowForecastDto.getScenarioName(),
                businessCashFlowForecastDto.getDesignType(),
                businessCashFlowForecastDto.getBusinessType(),
                businessCashFlowForecastDto.getItem());

        if (existDto != null) {
            return Result.error("该业务现金流预测已存在");
        }

        businessCashFlowForecastDto.setCreateBy(SecurityUtils.getUsername());
        return toAjax(businessCashFlowForecastService.insertBusinessCashFlowForecastDto(businessCashFlowForecastDto));
    }

    /**
     * 修改业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:edit')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody BusinessCashFlowForecastDTO businessCashFlowForecastDto) {
        // 验证是否存在相同的业务现金流预测（排除自己）
        BusinessCashFlowForecastDTO existDto = businessCashFlowForecastService.selectBusinessCashFlowForecastDtoByCondition(
                businessCashFlowForecastDto.getAccountingPeriod(),
                businessCashFlowForecastDto.getScenarioName(),
                businessCashFlowForecastDto.getDesignType(),
                businessCashFlowForecastDto.getBusinessType(),
                businessCashFlowForecastDto.getItem());

        if (existDto != null && !existDto.getId().equals(businessCashFlowForecastDto.getId())) {
            return Result.error("该业务现金流预测已存在");
        }

        businessCashFlowForecastDto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(businessCashFlowForecastService.updateBusinessCashFlowForecastDto(businessCashFlowForecastDto));
    }

    /**
     * 删除业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:remove')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(businessCashFlowForecastService.deleteBusinessCashFlowForecastDtoByIds(ids));
    }

    /**
     * 根据条件查询业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod,
                                 @RequestParam String scenarioName,
                                 @RequestParam String designType,
                                 @RequestParam String businessType,
                                 @RequestParam String item) {
        BusinessCashFlowForecastDTO dto = businessCashFlowForecastService.selectBusinessCashFlowForecastDtoByCondition(
                accountingPeriod, scenarioName, designType, businessType, item);
        return Result.success(dto);
    }

    /**
     * 批量新增业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:add')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList) {
        String operName = SecurityUtils.getUsername();
        for (BusinessCashFlowForecastDTO dto : businessCashFlowForecastDtoList) {
            dto.setCreateBy(operName);
        }
        return toAjax(businessCashFlowForecastService.batchInsertBusinessCashFlowForecastDto(businessCashFlowForecastDtoList));
    }

    /**
     * 根据账期删除业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:remove')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(businessCashFlowForecastService.deleteBusinessCashFlowForecastDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出业务现金流预测表
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:export')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessCashFlowForecastQuery query) throws Exception {
        businessCashFlowForecastService.exportBusinessCashFlowForecast(response, query);
    }

    /**
     * 获取业务现金流预测表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:import')")
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws Exception {
        businessCashFlowForecastService.exportTemplate(response);
    }

    /**
     * 导入业务现金流预测表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:business:cash:flow:forecast:import')")
    @Log(title = "业务现金流预测表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BusinessCashFlowForecastDTO> util = new ExcelUtil<>(BusinessCashFlowForecastDTO.class);
        List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = businessCashFlowForecastService.importBusinessCashFlowForecastData(businessCashFlowForecastDtoList, updateSupport, operName);
        return Result.success(message);
    }
}
