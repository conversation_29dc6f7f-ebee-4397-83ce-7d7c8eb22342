package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationFactorWithSpreadDTO;
import com.xl.alm.app.query.AdurKeyDurationFactorWithSpreadQuery;

import java.util.List;

/**
 * ADUR关键久期折现因子表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurKeyDurationFactorWithSpreadService {

    /**
     * 查询ADUR关键久期折现因子表含价差列表
     *
     * @param adurKeyDurationFactorWithSpreadQuery ADUR关键久期折现因子表含价差查询条件
     * @return ADUR关键久期折现因子表含价差列表
     */
    List<AdurKeyDurationFactorWithSpreadDTO> selectAdurKeyDurationFactorWithSpreadDtoList(AdurKeyDurationFactorWithSpreadQuery adurKeyDurationFactorWithSpreadQuery);

    /**
     * 用id查询ADUR关键久期折现因子表含价差
     *
     * @param id id
     * @return ADUR关键久期折现因子表含价差
     */
    AdurKeyDurationFactorWithSpreadDTO selectAdurKeyDurationFactorWithSpreadDtoById(Long id);

    /**
     * 根据账期、资产编号、关键期限和压力方向查询ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现因子表含价差
     */
    AdurKeyDurationFactorWithSpreadDTO selectAdurKeyDurationFactorWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection);

    /**
     * 新增ADUR关键久期折现因子表含价差
     *
     * @param adurKeyDurationFactorWithSpreadDTO ADUR关键久期折现因子表含价差
     * @return 结果
     */
    int insertAdurKeyDurationFactorWithSpreadDto(AdurKeyDurationFactorWithSpreadDTO adurKeyDurationFactorWithSpreadDTO);

    /**
     * 批量插入ADUR关键久期折现因子表含价差数据
     *
     * @param adurKeyDurationFactorWithSpreadDtoList ADUR关键久期折现因子表含价差列表
     * @return 影响行数
     */
    int batchInsertAdurKeyDurationFactorWithSpreadDto(List<AdurKeyDurationFactorWithSpreadDTO> adurKeyDurationFactorWithSpreadDtoList);

    /**
     * 更新ADUR关键久期折现因子表含价差数据
     *
     * @param dto ADUR关键久期折现因子表含价差
     * @return 结果
     */
    int updateAdurKeyDurationFactorWithSpreadDto(AdurKeyDurationFactorWithSpreadDTO dto);

    /**
     * 删除指定id的ADUR关键久期折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurKeyDurationFactorWithSpreadDtoById(Long id);

    /**
     * 批量删除ADUR关键久期折现因子表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现因子表含价差主键
     * @return 结果
     */
    int deleteAdurKeyDurationFactorWithSpreadDtoByIds(Long[] ids);

    /**
     * 根据账期删除ADUR关键久期折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurKeyDurationFactorWithSpreadDtoByAccountPeriod(String accountPeriod);

    /**
     * 导入ADUR关键久期折现因子表含价差
     *
     * @param dtoList       ADUR关键久期折现因子表含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAdurKeyDurationFactorWithSpreadDto(List<AdurKeyDurationFactorWithSpreadDTO> dtoList, Boolean updateSupport, String username);
}
