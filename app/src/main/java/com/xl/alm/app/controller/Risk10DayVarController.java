package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.Risk10DayVarDTO;
import com.xl.alm.app.query.Risk10DayVarQuery;
import com.xl.alm.app.service.Risk10DayVarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 风险10日VaR值表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/risk/10day/var")
public class Risk10DayVarController extends BaseController {

    @Autowired
    private Risk10DayVarService risk10DayVarService;

    /**
     * 查询风险10日VaR值表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:list')")
    @GetMapping("/list")
    public TableDataInfo list(Risk10DayVarQuery risk10DayVarQuery) {
        startPage();
        List<Risk10DayVarDTO> list = risk10DayVarService.selectRisk10DayVarDtoList(risk10DayVarQuery);
        return getDataTable(list);
    }

    /**
     * 导出风险10日VaR值表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:export')")
    @Log(title = "风险10日VaR值表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Risk10DayVarQuery risk10DayVarQuery) {
        List<Risk10DayVarDTO> list = risk10DayVarService.selectRisk10DayVarDtoList(risk10DayVarQuery);
        convertDictValueToLabel(list);
        ExcelUtil<Risk10DayVarDTO> util = new ExcelUtil<>(Risk10DayVarDTO.class);
        util.exportExcel(list, "风险10日VaR值表", response);
    }

    /**
     * 获取风险10日VaR值表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(risk10DayVarService.selectRisk10DayVarDtoById(id));
    }

    /**
     * 根据条件查询风险10日VaR值表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod, 
                                 @RequestParam String domesticForeign, 
                                 @RequestParam String itemCategory, 
                                 @RequestParam String samplePeriod) {
        return Result.success(risk10DayVarService.selectRisk10DayVarDtoByCondition(accountingPeriod, domesticForeign, itemCategory, samplePeriod));
    }

    /**
     * 新增风险10日VaR值表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:add')")
    @Log(title = "风险10日VaR值表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody Risk10DayVarDTO risk10DayVarDTO) {
        return toAjax(risk10DayVarService.insertRisk10DayVarDto(risk10DayVarDTO));
    }

    /**
     * 修改风险10日VaR值表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:edit')")
    @Log(title = "风险10日VaR值表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody Risk10DayVarDTO risk10DayVarDTO) {
        return toAjax(risk10DayVarService.updateRisk10DayVarDto(risk10DayVarDTO));
    }

    /**
     * 删除风险10日VaR值表
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:remove')")
    @Log(title = "风险10日VaR值表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(risk10DayVarService.deleteRisk10DayVarDtoByIds(ids));
    }

    /**
     * 导入风险10日VaR值表数据
     */
    @PreAuthorize("@ss.hasPermi('asm:risk:10day:var:import')")
    @Log(title = "风险10日VaR值表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<Risk10DayVarDTO> util = new ExcelUtil<>(Risk10DayVarDTO.class);
        List<Risk10DayVarDTO> risk10DayVarList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(risk10DayVarList);
        String operName = getUsername();
        try {
            String message = risk10DayVarService.importRisk10DayVarDto(risk10DayVarList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载风险10日VaR值表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<Risk10DayVarDTO> util = new ExcelUtil<>(Risk10DayVarDTO.class);
        util.exportExcel(new ArrayList<>(), "风险10日VaR值表数据", response);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 风险10日VaR值表数据列表
     */
    private void convertDictValueToLabel(List<Risk10DayVarDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (Risk10DayVarDTO dto : list) {
            // 转换境内外标识字典值
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertValueToLabel(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换项目分类字典值
            if (dto.getItemCategory() != null) {
                dto.setItemCategory(DictConvertUtil.convertValueToLabel(dto.getItemCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换样本期限字典值
            if (dto.getSamplePeriod() != null) {
                dto.setSamplePeriod(DictConvertUtil.convertValueToLabel(dto.getSamplePeriod(), "acm_sample_period"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 风险10日VaR值表数据列表
     */
    private void convertDictLabelToValue(List<Risk10DayVarDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (Risk10DayVarDTO dto : list) {
            // 转换境内外标识字典标签
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertLabelToValue(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换项目分类字典标签
            if (dto.getItemCategory() != null) {
                dto.setItemCategory(DictConvertUtil.convertLabelToValue(dto.getItemCategory(), "ast_asset_sub_sub_category"));
            }
            // 转换样本期限字典标签
            if (dto.getSamplePeriod() != null) {
                dto.setSamplePeriod(DictConvertUtil.convertLabelToValue(dto.getSamplePeriod(), "acm_sample_period"));
            }
        }
    }
}
