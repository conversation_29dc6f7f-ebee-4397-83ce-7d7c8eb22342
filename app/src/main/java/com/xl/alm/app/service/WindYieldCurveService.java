package com.xl.alm.app.service;

import com.xl.alm.app.dto.WindYieldCurveDTO;
import com.xl.alm.app.query.WindYieldCurveQuery;

import java.util.List;

/**
 * 万得收益率曲线表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface WindYieldCurveService {

    /**
     * 查询万得收益率曲线列表
     *
     * @param windYieldCurveQuery 万得收益率曲线查询条件
     * @return 万得收益率曲线列表
     */
    List<WindYieldCurveDTO> selectWindYieldCurveDtoList(WindYieldCurveQuery windYieldCurveQuery);

    /**
     * 用id查询万得收益率曲线
     *
     * @param id id
     * @return 万得收益率曲线
     */
    WindYieldCurveDTO selectWindYieldCurveDtoById(Long id);

    /**
     * 根据曲线名称、曲线标识和日期查询万得收益率曲线
     *
     * @param curveName 曲线名称
     * @param curveId 曲线标识
     * @param date 日期
     * @return 万得收益率曲线
     */
    WindYieldCurveDTO selectWindYieldCurveDtoByCondition(String curveName, String curveId, java.util.Date date);

    /**
     * 新增万得收益率曲线
     *
     * @param windYieldCurveDTO 万得收益率曲线
     * @return 结果
     */
    int insertWindYieldCurveDto(WindYieldCurveDTO windYieldCurveDTO);

    /**
     * 批量插入万得收益率曲线数据
     *
     * @param windYieldCurveDtoList 万得收益率曲线列表
     * @return 影响行数
     */
    int batchInsertWindYieldCurveDto(List<WindYieldCurveDTO> windYieldCurveDtoList);

    /**
     * 更新万得收益率曲线数据
     *
     * @param dto 万得收益率曲线
     * @return 结果
     */
    int updateWindYieldCurveDto(WindYieldCurveDTO dto);

    /**
     * 删除指定id的万得收益率曲线数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteWindYieldCurveDtoById(Long id);

    /**
     * 批量删除万得收益率曲线
     *
     * @param ids 需要删除的万得收益率曲线主键
     * @return 结果
     */
    int deleteWindYieldCurveDtoByIds(Long[] ids);

    /**
     * 导入万得收益率曲线
     *
     * @param dtoList       万得收益率曲线数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importWindYieldCurveDto(List<WindYieldCurveDTO> dtoList, Boolean updateSupport, String username);
}
