package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.PremiumIncomeDetailDTO;
import com.xl.alm.app.entity.PremiumIncomeDetailEntity;
import com.xl.alm.app.mapper.PremiumIncomeDetailMapper;
import com.xl.alm.app.query.PremiumIncomeDetailQuery;
import com.xl.alm.app.service.PremiumIncomeDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 保费收入明细Service业务层处理
 *
 * <AUTHOR> Assistant
 */
@Service
public class PremiumIncomeDetailServiceImpl implements PremiumIncomeDetailService {

    @Autowired
    private PremiumIncomeDetailMapper premiumIncomeDetailMapper;

    /**
     * 查询保费收入明细列表
     *
     * @param premiumIncomeDetailQuery 保费收入明细查询条件
     * @return 保费收入明细列表
     */
    @Override
    public List<PremiumIncomeDetailDTO> selectPremiumIncomeDetailDtoList(PremiumIncomeDetailQuery premiumIncomeDetailQuery) {
        List<PremiumIncomeDetailEntity> entityList = premiumIncomeDetailMapper.selectPremiumIncomeDetailEntityList(premiumIncomeDetailQuery);
        List<PremiumIncomeDetailDTO> dtoList = new ArrayList<>();
        for (PremiumIncomeDetailEntity entity : entityList) {
            PremiumIncomeDetailDTO dto = new PremiumIncomeDetailDTO();
            BeanUtils.copyProperties(entity, dto);
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 查询保费收入明细
     *
     * @param id 保费收入明细主键
     * @return 保费收入明细
     */
    @Override
    public PremiumIncomeDetailDTO selectPremiumIncomeDetailDtoById(Long id) {
        PremiumIncomeDetailEntity entity = premiumIncomeDetailMapper.selectPremiumIncomeDetailEntityById(id);
        if (entity != null) {
            PremiumIncomeDetailDTO dto = new PremiumIncomeDetailDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }
        return null;
    }

    /**
     * 新增保费收入明细
     *
     * @param premiumIncomeDetailDTO 保费收入明细
     * @return 结果
     */
    @Override
    public int insertPremiumIncomeDetailDto(PremiumIncomeDetailDTO premiumIncomeDetailDTO) {
        PremiumIncomeDetailEntity entity = new PremiumIncomeDetailEntity();
        BeanUtils.copyProperties(premiumIncomeDetailDTO, entity);
        entity.setCreateTime(DateUtils.getNowDate());
        return premiumIncomeDetailMapper.insertPremiumIncomeDetailEntity(entity);
    }

    /**
     * 批量新增保费收入明细
     *
     * @param premiumIncomeDetailDtoList 保费收入明细列表
     * @return 结果
     */
    @Override
    public int batchInsertPremiumIncomeDetailDto(List<PremiumIncomeDetailDTO> premiumIncomeDetailDtoList) {
        if (premiumIncomeDetailDtoList == null || premiumIncomeDetailDtoList.isEmpty()) {
            return 0;
        }
        
        List<PremiumIncomeDetailEntity> entityList = new ArrayList<>();
        for (PremiumIncomeDetailDTO dto : premiumIncomeDetailDtoList) {
            PremiumIncomeDetailEntity entity = new PremiumIncomeDetailEntity();
            BeanUtils.copyProperties(dto, entity);
            entity.setCreateTime(DateUtils.getNowDate());
            entityList.add(entity);
        }
        return premiumIncomeDetailMapper.batchInsertPremiumIncomeDetailEntity(entityList);
    }

    /**
     * 修改保费收入明细
     *
     * @param premiumIncomeDetailDTO 保费收入明细
     * @return 结果
     */
    @Override
    public int updatePremiumIncomeDetailDto(PremiumIncomeDetailDTO premiumIncomeDetailDTO) {
        PremiumIncomeDetailEntity entity = new PremiumIncomeDetailEntity();
        BeanUtils.copyProperties(premiumIncomeDetailDTO, entity);
        entity.setUpdateTime(DateUtils.getNowDate());
        return premiumIncomeDetailMapper.updatePremiumIncomeDetailEntity(entity);
    }

    /**
     * 批量删除保费收入明细
     *
     * @param ids 需要删除的保费收入明细主键
     * @return 结果
     */
    @Override
    public int deletePremiumIncomeDetailDtoByIds(Long[] ids) {
        return premiumIncomeDetailMapper.deletePremiumIncomeDetailEntityByIds(ids);
    }

    /**
     * 删除保费收入明细信息
     *
     * @param id 保费收入明细主键
     * @return 结果
     */
    @Override
    public int deletePremiumIncomeDetailDtoById(Long id) {
        return premiumIncomeDetailMapper.deletePremiumIncomeDetailEntityById(id);
    }

    /**
     * 删除指定账期的保费收入明细数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deletePremiumIncomeDetailDtoByPeriod(String accountingPeriod) {
        return premiumIncomeDetailMapper.deletePremiumIncomeDetailEntityByPeriod(accountingPeriod);
    }
}
