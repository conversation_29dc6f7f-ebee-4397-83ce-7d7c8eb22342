package com.xl.alm.app.service;

import com.xl.alm.app.dto.NewPremiumSummaryDTO;
import com.xl.alm.app.query.NewPremiumSummaryQuery;

import java.util.List;

/**
 * 新单保费汇总表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface NewPremiumSummaryService {

    /**
     * 查询新单保费汇总列表
     *
     * @param newPremiumSummaryQuery 新单保费汇总查询条件
     * @return 新单保费汇总列表
     */
    List<NewPremiumSummaryDTO> selectNewPremiumSummaryDtoList(NewPremiumSummaryQuery newPremiumSummaryQuery);

    /**
     * 用id查询新单保费汇总
     *
     * @param id id
     * @return 新单保费汇总
     */
    NewPremiumSummaryDTO selectNewPremiumSummaryDtoById(Long id);

    /**
     * 根据账期、业务代码、缴费频率和缴费年期查询新单保费汇总
     *
     * @param accountingPeriod 账期
     * @param businessCode 业务代码
     * @param paymentFrequency 缴费频率
     * @param paymentPeriod 缴费年期
     * @return 新单保费汇总
     */
    NewPremiumSummaryDTO selectNewPremiumSummaryDtoByCondition(String accountingPeriod, String businessCode, String paymentFrequency, Integer paymentPeriod);

    /**
     * 新增新单保费汇总
     *
     * @param newPremiumSummaryDto 新单保费汇总
     * @return 结果
     */
    int insertNewPremiumSummaryDto(NewPremiumSummaryDTO newPremiumSummaryDto);

    /**
     * 批量新增新单保费汇总
     *
     * @param newPremiumSummaryDtoList 新单保费汇总列表
     * @return 结果
     */
    int batchInsertNewPremiumSummaryDto(List<NewPremiumSummaryDTO> newPremiumSummaryDtoList);

    /**
     * 修改新单保费汇总
     *
     * @param newPremiumSummaryDto 新单保费汇总
     * @return 结果
     */
    int updateNewPremiumSummaryDto(NewPremiumSummaryDTO newPremiumSummaryDto);

    /**
     * 删除新单保费汇总
     *
     * @param id 新单保费汇总主键
     * @return 结果
     */
    int deleteNewPremiumSummaryDtoById(Long id);

    /**
     * 批量删除新单保费汇总
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteNewPremiumSummaryDtoByIds(Long[] ids);

    /**
     * 导入新单保费汇总数据
     *
     * @param newPremiumSummaryDtoList 新单保费汇总列表
     * @param updateSupport 是否更新已存在数据
     * @param operName 操作人
     * @return 导入结果信息
     */
    String importNewPremiumSummaryDto(List<NewPremiumSummaryDTO> newPremiumSummaryDtoList, Boolean updateSupport, String operName);
}
