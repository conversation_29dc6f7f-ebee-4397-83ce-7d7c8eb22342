package com.xl.alm.app.query;

import com.jd.lightning.common.core.domain.BaseQuery;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 保费收入明细Query
 *
 * <AUTHOR> Assistant
 */
@Data
public class PremiumIncomeDetailQuery extends BaseQuery {
    private Long id;
    
    /**
     * 统计期间，格式：YYYYMM
     */
    private String accountingPeriod;
    
    /**
     * 公司唯一标识
     */
    private String companyCode;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 产品唯一标识
     */
    private String productCode;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 渠道唯一标识
     */
    private String channelCode;
    
    /**
     * 渠道名称
     */
    private String channelName;
    
    /**
     * 账户唯一标识
     */
    private String accountCode;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 原保费-趸交(本月)
     */
    private BigDecimal currentSinglePremium;
    
    /**
     * 原保费-期交(本月)
     */
    private BigDecimal currentRegularPremium;
    
    /**
     * 原保费-续期(本月)
     */
    private BigDecimal currentRenewalPremium;
    
    /**
     * 原保费-合计(本月)
     */
    private BigDecimal currentTotalPremium;
    
    /**
     * 万能投连-趸交(本月)
     */
    private BigDecimal currentUlSingle;
    
    /**
     * 万能投连-期交(本月)
     */
    private BigDecimal currentUlRegular;
    
    /**
     * 万能投连-续期(本月)
     */
    private BigDecimal currentUlRenewal;
    
    /**
     * 万能投连-初始费用(本月)
     */
    private BigDecimal currentUlInitialFee;
    
    /**
     * 万能投连-合计(本月)
     */
    private BigDecimal currentUlTotal;
    
    /**
     * 规模保费合计(本月)
     */
    private BigDecimal currentScalePremium;
    
    /**
     * 保户储金及投资款余额(本月)
     */
    private BigDecimal currentInvestmentBalance;
    
    /**
     * 退保金(本月)
     */
    private BigDecimal currentSurrender;
    
    /**
     * 万能投连领取(本月)
     */
    private BigDecimal currentUlWithdraw;
    
    /**
     * 赔款支出(本月)
     */
    private BigDecimal currentClaim;
    
    /**
     * 死伤医疗给付(本月)
     */
    private BigDecimal currentMedical;
    
    /**
     * 满期给付(本月)
     */
    private BigDecimal currentMaturity;
    
    /**
     * 年金给付(本月)
     */
    private BigDecimal currentAnnuity;
    
    /**
     * 万能投连-赔款支出(本月)
     */
    private BigDecimal currentUlClaim;
    
    /**
     * 万能投连-死伤医疗给付(本月)
     */
    private BigDecimal currentUlMedical;
    
    /**
     * 万能投连-满期给付(本月)
     */
    private BigDecimal currentUlMaturity;
    
    /**
     * 万能投连-年金给付(本月)
     */
    private BigDecimal currentUlAnnuity;
    
    /**
     * 赔付支出合计(本月)
     */
    private BigDecimal currentTotalClaim;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
