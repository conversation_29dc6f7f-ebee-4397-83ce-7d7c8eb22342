package com.xl.alm.app.service;

import com.xl.alm.app.dto.ThreeAccountHoldingDTO;
import com.xl.alm.app.dto.ThreeAccountHoldingImportDTO;
import com.xl.alm.app.query.ThreeAccountHoldingQuery;

import java.util.List;

/**
 * TB0002-三账户持仓表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface ThreeAccountHoldingService {

    /**
     * 查询三账户持仓列表
     *
     * @param threeAccountHoldingQuery 三账户持仓查询条件
     * @return 三账户持仓列表
     */
    List<ThreeAccountHoldingDTO> selectThreeAccountHoldingDtoList(ThreeAccountHoldingQuery threeAccountHoldingQuery);

    /**
     * 根据ID查询三账户持仓
     *
     * @param id 主键ID
     * @return 三账户持仓
     */
    ThreeAccountHoldingDTO selectThreeAccountHoldingDtoById(Long id);

    /**
     * 新增三账户持仓
     *
     * @param threeAccountHoldingDTO 三账户持仓
     * @return 结果
     */
    int insertThreeAccountHoldingDto(ThreeAccountHoldingDTO threeAccountHoldingDTO);

    /**
     * 修改三账户持仓
     *
     * @param threeAccountHoldingDTO 三账户持仓
     * @return 结果
     */
    int updateThreeAccountHoldingDto(ThreeAccountHoldingDTO threeAccountHoldingDTO);

    /**
     * 批量删除三账户持仓
     *
     * @param ids 需要删除的三账户持仓主键集合
     * @return 结果
     */
    int deleteThreeAccountHoldingDtoByIds(Long[] ids);

    /**
     * 删除三账户持仓信息
     *
     * @param id 三账户持仓主键
     * @return 结果
     */
    int deleteThreeAccountHoldingDtoById(Long id);

    /**
     * 批量插入三账户持仓数据
     *
     * @param threeAccountHoldingDtoList 三账户持仓列表
     * @return 影响行数
     */
    int batchInsertThreeAccountHoldingDto(List<ThreeAccountHoldingDTO> threeAccountHoldingDtoList);

    /**
     * 根据账期删除三账户持仓数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteThreeAccountHoldingDtoByPeriod(String accountingPeriod);

    /**
     * 导入三账户持仓数据
     *
     * @param importList       导入数据列表
     * @param updateSupport    是否更新支持，如果已存在，是否更新
     * @param username         操作用户
     * @param accountingPeriod 账期
     * @return 结果
     */
    String importThreeAccountHoldingDto(List<ThreeAccountHoldingImportDTO> importList, Boolean updateSupport, String username, String accountingPeriod);
}
