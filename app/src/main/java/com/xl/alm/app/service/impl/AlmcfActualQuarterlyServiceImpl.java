package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AlmcfActualQuarterlyDTO;
import com.xl.alm.app.dto.AlmcfActualYtdDTO;
import com.xl.alm.app.entity.AlmcfActualQuarterlyEntity;
import com.xl.alm.app.mapper.AlmcfActualQuarterlyMapper;
import com.xl.alm.app.query.AlmcfActualQuarterlyQuery;
import com.xl.alm.app.service.AlmcfActualQuarterlyService;
import com.xl.alm.app.service.AlmcfActualYtdService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * ALMCF实际发生数本季度累计表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AlmcfActualQuarterlyServiceImpl implements AlmcfActualQuarterlyService {

    @Autowired
    private AlmcfActualQuarterlyMapper almcfActualQuarterlyMapper;

    @Autowired
    private AlmcfActualYtdService almcfActualYtdService;

    /**
     * 查询ALMCF实际发生数本季度累计表列表
     *
     * @param almcfActualQuarterlyQuery ALMCF实际发生数本季度累计表查询条件
     * @return ALMCF实际发生数本季度累计表列表
     */
    @Override
    public List<AlmcfActualQuarterlyDTO> selectAlmcfActualQuarterlyDtoList(AlmcfActualQuarterlyQuery almcfActualQuarterlyQuery) {
        List<AlmcfActualQuarterlyEntity> entityList = almcfActualQuarterlyMapper.selectAlmcfActualQuarterlyEntityList(almcfActualQuarterlyQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AlmcfActualQuarterlyDTO.class);
    }

    /**
     * 用id查询ALMCF实际发生数本季度累计表
     *
     * @param id id
     * @return ALMCF实际发生数本季度累计表
     */
    @Override
    public AlmcfActualQuarterlyDTO selectAlmcfActualQuarterlyDtoById(Long id) {
        AlmcfActualQuarterlyEntity entity = almcfActualQuarterlyMapper.selectAlmcfActualQuarterlyEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AlmcfActualQuarterlyDTO.class);
    }

    /**
     * 根据账期查询ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @return ALMCF实际发生数本季度累计表列表
     */
    @Override
    public List<AlmcfActualQuarterlyDTO> selectAlmcfActualQuarterlyDtoByPeriod(String accountingPeriod) {
        List<AlmcfActualQuarterlyEntity> entityList = almcfActualQuarterlyMapper.selectAlmcfActualQuarterlyEntityByPeriod(accountingPeriod);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AlmcfActualQuarterlyDTO.class);
    }

    /**
     * 根据账期和项目名称查询ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return ALMCF实际发生数本季度累计表
     */
    @Override
    public AlmcfActualQuarterlyDTO selectAlmcfActualQuarterlyDtoByCondition(String accountingPeriod, String itemName) {
        AlmcfActualQuarterlyEntity entity = almcfActualQuarterlyMapper.selectAlmcfActualQuarterlyEntityByCondition(accountingPeriod, itemName);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AlmcfActualQuarterlyDTO.class);
    }

    /**
     * 新增ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDto ALMCF实际发生数本季度累计表
     * @return 影响行数
     */
    @Override
    public int insertAlmcfActualQuarterlyDto(AlmcfActualQuarterlyDTO almcfActualQuarterlyDto) {
        AlmcfActualQuarterlyEntity entity = EntityDtoConvertUtil.convertToEntity(almcfActualQuarterlyDto, AlmcfActualQuarterlyEntity.class);
        return almcfActualQuarterlyMapper.insertAlmcfActualQuarterlyEntity(entity);
    }

    /**
     * 批量新增ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDtoList ALMCF实际发生数本季度累计表列表
     * @return 影响行数
     */
    @Override
    public int batchInsertAlmcfActualQuarterlyDto(List<AlmcfActualQuarterlyDTO> almcfActualQuarterlyDtoList) {
        if (almcfActualQuarterlyDtoList == null || almcfActualQuarterlyDtoList.isEmpty()) {
            return 0;
        }
        List<AlmcfActualQuarterlyEntity> entityList = EntityDtoConvertUtil.convertToEntityList(almcfActualQuarterlyDtoList, AlmcfActualQuarterlyEntity.class);
        return almcfActualQuarterlyMapper.batchInsertAlmcfActualQuarterlyEntity(entityList);
    }

    /**
     * 更新ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDto ALMCF实际发生数本季度累计表
     * @return 影响行数
     */
    @Override
    public int updateAlmcfActualQuarterlyDto(AlmcfActualQuarterlyDTO almcfActualQuarterlyDto) {
        AlmcfActualQuarterlyEntity entity = EntityDtoConvertUtil.convertToEntity(almcfActualQuarterlyDto, AlmcfActualQuarterlyEntity.class);
        return almcfActualQuarterlyMapper.updateAlmcfActualQuarterlyEntity(entity);
    }

    /**
     * 删除ALMCF实际发生数本季度累计表
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    public int deleteAlmcfActualQuarterlyDtoById(Long id) {
        return almcfActualQuarterlyMapper.deleteAlmcfActualQuarterlyEntityById(id);
    }

    /**
     * 批量删除ALMCF实际发生数本季度累计表
     *
     * @param ids id数组
     * @return 影响行数
     */
    @Override
    public int deleteAlmcfActualQuarterlyDtoByIds(Long[] ids) {
        return almcfActualQuarterlyMapper.deleteAlmcfActualQuarterlyEntityByIds(ids);
    }

    /**
     * 根据账期删除ALMCF实际发生数本季度累计表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteAlmcfActualQuarterlyDtoByPeriod(String accountingPeriod) {
        return almcfActualQuarterlyMapper.deleteAlmcfActualQuarterlyEntityByPeriod(accountingPeriod);
    }

    /**
     * 导入ALMCF实际发生数本季度累计表
     *
     * @param dtoList ALMCF实际发生数本季度累计表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    @Override
    public String importAlmcfActualQuarterlyDto(List<AlmcfActualQuarterlyDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入ALMCF实际发生数本季度累计表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AlmcfActualQuarterlyDTO dto : dtoList) {
            try {
                // 验证是否存在这个ALMCF实际发生数本季度累计表
                AlmcfActualQuarterlyDTO existDto = this.selectAlmcfActualQuarterlyDtoByCondition(dto.getAccountingPeriod(), dto.getItemName());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAlmcfActualQuarterlyDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 项目 " + dto.getItemName() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    dto.setUpdateBy(username);
                    this.updateAlmcfActualQuarterlyDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 项目 " + dto.getItemName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 项目 " + dto.getItemName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 项目 " + dto.getItemName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 计算ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @param username 操作用户
     * @return 结果
     */
    @Override
    public String calculateAlmcfActualQuarterlyDto(String accountingPeriod, String username) {
        try {
            log.info("开始计算ALMCF实际发生数本季度累计表，账期：{}", accountingPeriod);

            // 获取账期的月份
            int month = Integer.parseInt(accountingPeriod.substring(4));
            String year = accountingPeriod.substring(0, 4);

            List<AlmcfActualQuarterlyDTO> resultList = new ArrayList<>();

            if (month >= 1 && month <= 3) {
                // 第一季度：直接取本年累计表数据
                resultList = calculateFirstQuarter(accountingPeriod, username);
            } else {
                // 其他季度：本账期数据 - 上一季度末账期数据
                resultList = calculateOtherQuarter(accountingPeriod, year, month, username);
            }

            // 删除该账期的旧数据
            this.deleteAlmcfActualQuarterlyDtoByPeriod(accountingPeriod);

            // 批量插入新数据
            if (!resultList.isEmpty()) {
                this.batchInsertAlmcfActualQuarterlyDto(resultList);
                log.info("成功计算并保存{}条ALMCF实际发生数本季度累计表数据", resultList.size());
            }

            log.info("ALMCF实际发生数本季度累计表计算完成，账期：{}", accountingPeriod);
            return "ALMCF实际发生数本季度累计表计算成功！账期：" + accountingPeriod;
        } catch (Exception e) {
            log.error("计算ALMCF实际发生数本季度累计表失败，账期：{}", accountingPeriod, e);
            throw new RuntimeException("计算ALMCF实际发生数本季度累计表失败：" + e.getMessage());
        }
    }

    /**
     * 计算第一季度数据
     */
    private List<AlmcfActualQuarterlyDTO> calculateFirstQuarter(String accountingPeriod, String username) {
        List<AlmcfActualYtdDTO> ytdList = almcfActualYtdService.selectAlmcfActualYtdDtoByPeriod(accountingPeriod);
        List<AlmcfActualQuarterlyDTO> resultList = new ArrayList<>();

        for (AlmcfActualYtdDTO ytdDto : ytdList) {
            AlmcfActualQuarterlyDTO quarterlyDto = new AlmcfActualQuarterlyDTO();
            quarterlyDto.setAccountingPeriod(accountingPeriod);
            quarterlyDto.setItemName(ytdDto.getItemName());
            quarterlyDto.setTotalAccount(ytdDto.getTotalAccount());
            quarterlyDto.setGeneralAccount(ytdDto.getOrdinaryAccount());
            quarterlyDto.setTraditionalAccount(ytdDto.getTraditionalAccount());
            quarterlyDto.setDividendAccount(ytdDto.getBonusAccount());
            quarterlyDto.setUniversalAccount(ytdDto.getUniversalAccount());
            quarterlyDto.setInvestmentAccount(ytdDto.getInvestmentAccount());
            quarterlyDto.setCreateBy(username);

            resultList.add(quarterlyDto);
        }

        return resultList;
    }

    /**
     * 计算其他季度数据
     */
    private List<AlmcfActualQuarterlyDTO> calculateOtherQuarter(String accountingPeriod, String year, int month, String username) {
        // 计算上一季度末月份
        String previousQuarterEndPeriod = getPreviousQuarterEndPeriod(year, month);

        // 获取本账期和上一季度末的本年累计数据
        List<AlmcfActualYtdDTO> currentYtdList = almcfActualYtdService.selectAlmcfActualYtdDtoByPeriod(accountingPeriod);
        List<AlmcfActualYtdDTO> previousYtdList = almcfActualYtdService.selectAlmcfActualYtdDtoByPeriod(previousQuarterEndPeriod);

        List<AlmcfActualQuarterlyDTO> resultList = new ArrayList<>();

        for (AlmcfActualYtdDTO currentYtd : currentYtdList) {
            // 查找对应的上一季度末数据
            AlmcfActualYtdDTO previousYtd = findMatchingYtdData(previousYtdList, currentYtd.getItemName());

            AlmcfActualQuarterlyDTO quarterlyDto = new AlmcfActualQuarterlyDTO();
            quarterlyDto.setAccountingPeriod(accountingPeriod);
            quarterlyDto.setItemName(currentYtd.getItemName());
            quarterlyDto.setTotalAccount(subtract(currentYtd.getTotalAccount(), previousYtd != null ? previousYtd.getTotalAccount() : BigDecimal.ZERO));
            quarterlyDto.setGeneralAccount(subtract(currentYtd.getOrdinaryAccount(), previousYtd != null ? previousYtd.getOrdinaryAccount() : BigDecimal.ZERO));
            quarterlyDto.setTraditionalAccount(subtract(currentYtd.getTraditionalAccount(), previousYtd != null ? previousYtd.getTraditionalAccount() : BigDecimal.ZERO));
            quarterlyDto.setDividendAccount(subtract(currentYtd.getBonusAccount(), previousYtd != null ? previousYtd.getBonusAccount() : BigDecimal.ZERO));
            quarterlyDto.setUniversalAccount(subtract(currentYtd.getUniversalAccount(), previousYtd != null ? previousYtd.getUniversalAccount() : BigDecimal.ZERO));
            quarterlyDto.setInvestmentAccount(subtract(currentYtd.getInvestmentAccount(), previousYtd != null ? previousYtd.getInvestmentAccount() : BigDecimal.ZERO));
            quarterlyDto.setCreateBy(username);

            resultList.add(quarterlyDto);
        }

        return resultList;
    }

    /**
     * 获取上一季度末账期
     */
    private String getPreviousQuarterEndPeriod(String year, int month) {
        if (month >= 4 && month <= 6) {
            // 第二季度，上一季度末为3月
            return year + "03";
        } else if (month >= 7 && month <= 9) {
            // 第三季度，上一季度末为6月
            return year + "06";
        } else if (month >= 10 && month <= 12) {
            // 第四季度，上一季度末为9月
            return year + "09";
        } else {
            throw new RuntimeException("无效的月份：" + month);
        }
    }

    /**
     * 查找匹配的本年累计数据
     */
    private AlmcfActualYtdDTO findMatchingYtdData(List<AlmcfActualYtdDTO> ytdList, String itemName) {
        return ytdList.stream()
                .filter(ytd -> itemName.equals(ytd.getItemName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安全的BigDecimal减法
     */
    private BigDecimal subtract(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.subtract(b);
    }
}
