package com.xl.alm.app.service;

import com.xl.alm.app.dto.WindIndustryDTO;
import com.xl.alm.app.query.WindIndustryQuery;

import java.util.List;

/**
 * Wind行业表Service接口
 *
 * <AUTHOR> Assistant
 */
public interface WindIndustryService {

    /**
     * 查询Wind行业表
     *
     * @param id Wind行业表主键
     * @return Wind行业表
     */
    public WindIndustryDTO selectWindIndustryDtoById(Long id);

    /**
     * 查询Wind行业表列表
     *
     * @param windIndustryQuery Wind行业表查询条件
     * @return Wind行业表集合
     */
    public List<WindIndustryDTO> selectWindIndustryDtoList(WindIndustryQuery windIndustryQuery);

    /**
     * 新增Wind行业表
     *
     * @param windIndustryDTO Wind行业表
     * @return 结果
     */
    public int insertWindIndustryDto(WindIndustryDTO windIndustryDTO);

    /**
     * 修改Wind行业表
     *
     * @param windIndustryDTO Wind行业表
     * @return 结果
     */
    public int updateWindIndustryDto(WindIndustryDTO windIndustryDTO);

    /**
     * 批量删除Wind行业表
     *
     * @param ids 需要删除的Wind行业表主键集合
     * @return 结果
     */
    public int deleteWindIndustryDtoByIds(Long[] ids);

    /**
     * 删除Wind行业表信息
     *
     * @param id Wind行业表主键
     * @return 结果
     */
    public int deleteWindIndustryDtoById(Long id);

    /**
     * 批量新增Wind行业表
     *
     * @param windIndustryDTOList Wind行业表集合
     * @return 结果
     */
    public int batchInsertWindIndustryDto(List<WindIndustryDTO> windIndustryDTOList);

    /**
     * 根据账期删除Wind行业表
     *
     * @param accountingPeriod 账期
     * @return 结果
     */
    public int deleteWindIndustryDtoByPeriod(String accountingPeriod);

    /**
     * 导入Wind行业表数据
     *
     * @param windIndustryDTOList Wind行业表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importWindIndustryDto(List<WindIndustryDTO> windIndustryDTOList, Boolean isUpdateSupport, String operName);
}
