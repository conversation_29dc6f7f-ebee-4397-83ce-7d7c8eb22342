package com.xl.alm.app.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR月度折现曲线表含价差DTO
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AdurMonthlyDiscountCurveWithSpreadDTO extends BaseDTO {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账期
     */
    @NotBlank(message = "账期不能为空")
    @Size(max = 6, message = "账期长度不能超过6个字符")
    @Excel(name = "账期")
    @ExcelProperty("账期")
    private String accountPeriod;

    /**
     * 久期类型
     */
    @NotBlank(message = "久期类型不能为空")
    @Size(max = 20, message = "久期类型长度不能超过20个字符")
    @Excel(name = "久期类型", dictType = "adur_duration_type")
    @ExcelProperty("久期类型")
    private String durationType;

    /**
     * 基点类型
     */
    @NotBlank(message = "基点类型不能为空")
    @Size(max = 20, message = "基点类型长度不能超过20个字符")
    @Excel(name = "基点类型", dictType = "adur_basis_point_type")
    @ExcelProperty("基点类型")
    private String basisPointType;

    /**
     * 日期类型
     */
    @NotBlank(message = "日期类型不能为空")
    @Size(max = 20, message = "日期类型长度不能超过20个字符")
    @Excel(name = "日期类型", dictType = "adur_date_type")
    @ExcelProperty("日期类型")
    private String dateType;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    @Excel(name = "日期", dateFormat = "yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date date;

    /**
     * 价差类型
     */
    @Size(max = 20, message = "价差类型长度不能超过20个字符")
    @Excel(name = "价差类型", dictType = "adur_spread_type")
    @ExcelProperty("价差类型")
    private String spreadType;

    /**
     * 价差
     */
    @DecimalMin(value = "0", message = "价差不能小于0")
    @Excel(name = "价差")
    @ExcelProperty("价差")
    private BigDecimal spread;

    /**
     * 曲线细分类
     */
    @Size(max = 10, message = "曲线细分类长度不能超过10个字符")
    @Excel(name = "曲线细分类")
    @ExcelProperty("曲线细分类")
    private String curveSubCategory;

    /**
     * 资产编号
     */
    @NotBlank(message = "资产编号不能为空")
    @Size(max = 20, message = "资产编号长度不能超过20个字符")
    @Excel(name = "资产编号")
    @ExcelProperty("资产编号")
    private String assetNumber;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    @Size(max = 50, message = "账户名称长度不能超过50个字符")
    @Excel(name = "账户名称", dictType = "adur_account_name")
    @ExcelProperty("账户名称")
    private String accountName;

    /**
     * 资产名称
     */
    @Size(max = 100, message = "资产名称长度不能超过100个字符")
    @Excel(name = "资产名称")
    @ExcelProperty("资产名称")
    private String assetName;

    /**
     * 证券代码
     */
    @Size(max = 20, message = "证券代码长度不能超过20个字符")
    @Excel(name = "证券代码")
    @ExcelProperty("证券代码")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @Size(max = 10, message = "折现曲线标识长度不能超过10个字符")
    @Excel(name = "折现曲线标识")
    @ExcelProperty("折现曲线标识")
    private String curveId;

    /**
     * 月度折现曲线利率含价差值集（JSON格式存储term_0到term_600的数据）
     */
    @Excel(name = "月度折现曲线利率含价差值集", width = 50)
    @ExcelProperty("月度折现曲线利率含价差值集")
    private String monthlyDiscountRateWithSpreadSet;

    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
