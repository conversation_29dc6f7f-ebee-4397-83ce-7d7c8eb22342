package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.FixedIncomeCreditRatingDTO;
import com.xl.alm.app.query.FixedIncomeCreditRatingQuery;
import com.xl.alm.app.service.FixedIncomeCreditRatingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 固定收益类投资资产信用评级表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/fixed/income/credit/rating")
public class FixedIncomeCreditRatingController extends BaseController {

    @Autowired
    private FixedIncomeCreditRatingService fixedIncomeCreditRatingService;

    /**
     * 查询固定收益类投资资产信用评级表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:list')")
    @GetMapping("/list")
    public TableDataInfo list(FixedIncomeCreditRatingQuery fixedIncomeCreditRatingQuery) {
        startPage();
        List<FixedIncomeCreditRatingDTO> list = fixedIncomeCreditRatingService.selectFixedIncomeCreditRatingDtoList(fixedIncomeCreditRatingQuery);
        return getDataTable(list);
    }

    /**
     * 导出固定收益类投资资产信用评级表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:export')")
    @Log(title = "固定收益类投资资产信用评级表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FixedIncomeCreditRatingQuery fixedIncomeCreditRatingQuery) {
        List<FixedIncomeCreditRatingDTO> list = fixedIncomeCreditRatingService.selectFixedIncomeCreditRatingDtoList(fixedIncomeCreditRatingQuery);
        convertDictValueToLabel(list);
        ExcelUtil<FixedIncomeCreditRatingDTO> util = new ExcelUtil<>(FixedIncomeCreditRatingDTO.class);
        util.exportExcel(list, "固定收益类投资资产信用评级表", response);
    }

    /**
     * 获取固定收益类投资资产信用评级表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fixedIncomeCreditRatingService.selectFixedIncomeCreditRatingDtoById(id));
    }

    /**
     * 根据条件查询固定收益类投资资产信用评级表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod,
                                 @RequestParam String domesticForeign,
                                 @RequestParam String fixedIncomeTermCategory,
                                 @RequestParam String creditRatingCategory) {
        return Result.success(fixedIncomeCreditRatingService.selectFixedIncomeCreditRatingDtoByCondition(accountingPeriod, domesticForeign, fixedIncomeTermCategory, creditRatingCategory));
    }

    /**
     * 新增固定收益类投资资产信用评级表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:add')")
    @Log(title = "固定收益类投资资产信用评级表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FixedIncomeCreditRatingDTO fixedIncomeCreditRatingDTO) {
        return toAjax(fixedIncomeCreditRatingService.insertFixedIncomeCreditRatingDto(fixedIncomeCreditRatingDTO));
    }

    /**
     * 修改固定收益类投资资产信用评级表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:edit')")
    @Log(title = "固定收益类投资资产信用评级表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FixedIncomeCreditRatingDTO fixedIncomeCreditRatingDTO) {
        return toAjax(fixedIncomeCreditRatingService.updateFixedIncomeCreditRatingDto(fixedIncomeCreditRatingDTO));
    }

    /**
     * 删除固定收益类投资资产信用评级表
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:remove')")
    @Log(title = "固定收益类投资资产信用评级表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fixedIncomeCreditRatingService.deleteFixedIncomeCreditRatingDtoByIds(ids));
    }

    /**
     * 导入固定收益类投资资产信用评级表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:fixed:income:credit:rating:import')")
    @Log(title = "固定收益类投资资产信用评级表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FixedIncomeCreditRatingDTO> util = new ExcelUtil<>(FixedIncomeCreditRatingDTO.class);
        List<FixedIncomeCreditRatingDTO> fixedIncomeCreditRatingList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(fixedIncomeCreditRatingList);
        String operName = getUsername();
        try {
            String message = fixedIncomeCreditRatingService.importFixedIncomeCreditRatingDto(fixedIncomeCreditRatingList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载固定收益类投资资产信用评级表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FixedIncomeCreditRatingDTO> util = new ExcelUtil<>(FixedIncomeCreditRatingDTO.class);
        util.exportExcel(new ArrayList<>(), "固定收益类投资资产信用评级表数据", response);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 固定收益类投资资产信用评级表数据列表
     */
    private void convertDictValueToLabel(List<FixedIncomeCreditRatingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeCreditRatingDTO dto : list) {
            // 转换境内外标识字典值
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertValueToLabel(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换固收资产剩余期限资产分类字典值
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertValueToLabel(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
            // 转换信用评级分类字典值
            if (dto.getCreditRatingCategory() != null) {
                dto.setCreditRatingCategory(DictConvertUtil.convertValueToLabel(dto.getCreditRatingCategory(), "ast_credit_rating"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 固定收益类投资资产信用评级表数据列表
     */
    private void convertDictLabelToValue(List<FixedIncomeCreditRatingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeCreditRatingDTO dto : list) {
            // 转换境内外标识字典标签
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertLabelToValue(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换固收资产剩余期限资产分类字典标签
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertLabelToValue(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
            // 转换信用评级分类字典标签
            if (dto.getCreditRatingCategory() != null) {
                dto.setCreditRatingCategory(DictConvertUtil.convertLabelToValue(dto.getCreditRatingCategory(), "ast_credit_rating"));
            }
        }
    }
}
