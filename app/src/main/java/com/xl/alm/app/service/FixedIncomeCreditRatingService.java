package com.xl.alm.app.service;

import com.xl.alm.app.dto.FixedIncomeCreditRatingDTO;
import com.xl.alm.app.query.FixedIncomeCreditRatingQuery;

import java.util.List;

/**
 * 固定收益类投资资产信用评级表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FixedIncomeCreditRatingService {

    /**
     * 查询固定收益类投资资产信用评级表列表
     *
     * @param fixedIncomeCreditRatingQuery 固定收益类投资资产信用评级表查询条件
     * @return 固定收益类投资资产信用评级表列表
     */
    List<FixedIncomeCreditRatingDTO> selectFixedIncomeCreditRatingDtoList(FixedIncomeCreditRatingQuery fixedIncomeCreditRatingQuery);

    /**
     * 用id查询固定收益类投资资产信用评级表
     *
     * @param id id
     * @return 固定收益类投资资产信用评级表
     */
    FixedIncomeCreditRatingDTO selectFixedIncomeCreditRatingDtoById(Long id);

    /**
     * 根据账期、境内外标识、固收资产剩余期限资产分类和信用评级分类查询固定收益类投资资产信用评级表
     *
     * @param accountingPeriod 账期
     * @param domesticForeign 境内外标识
     * @param fixedIncomeTermCategory 固收资产剩余期限资产分类
     * @param creditRatingCategory 信用评级分类
     * @return 固定收益类投资资产信用评级表
     */
    FixedIncomeCreditRatingDTO selectFixedIncomeCreditRatingDtoByCondition(String accountingPeriod, String domesticForeign, String fixedIncomeTermCategory, String creditRatingCategory);

    /**
     * 新增固定收益类投资资产信用评级表
     *
     * @param fixedIncomeCreditRatingDTO 固定收益类投资资产信用评级表
     * @return 结果
     */
    int insertFixedIncomeCreditRatingDto(FixedIncomeCreditRatingDTO fixedIncomeCreditRatingDTO);

    /**
     * 批量新增固定收益类投资资产信用评级表
     *
     * @param fixedIncomeCreditRatingDtoList 固定收益类投资资产信用评级表列表
     * @return 影响行数
     */
    int batchInsertFixedIncomeCreditRatingDto(List<FixedIncomeCreditRatingDTO> fixedIncomeCreditRatingDtoList);

    /**
     * 修改固定收益类投资资产信用评级表
     *
     * @param fixedIncomeCreditRatingDTO 固定收益类投资资产信用评级表
     * @return 结果
     */
    int updateFixedIncomeCreditRatingDto(FixedIncomeCreditRatingDTO fixedIncomeCreditRatingDTO);

    /**
     * 批量删除固定收益类投资资产信用评级表
     *
     * @param ids 需要删除的固定收益类投资资产信用评级表主键集合
     * @return 结果
     */
    int deleteFixedIncomeCreditRatingDtoByIds(Long[] ids);

    /**
     * 删除固定收益类投资资产信用评级表信息
     *
     * @param id 固定收益类投资资产信用评级表主键
     * @return 结果
     */
    int deleteFixedIncomeCreditRatingDtoById(Long id);

    /**
     * 导入固定收益类投资资产信用评级表数据
     *
     * @param fixedIncomeCreditRatingDtoList 固定收益类投资资产信用评级表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFixedIncomeCreditRatingDto(List<FixedIncomeCreditRatingDTO> fixedIncomeCreditRatingDtoList, Boolean isUpdateSupport, String operName);
}
