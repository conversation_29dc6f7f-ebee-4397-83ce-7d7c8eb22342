package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.SubAccountYieldRateDTO;
import com.xl.alm.app.query.SubAccountYieldRateQuery;
import com.xl.alm.app.service.SubAccountYieldRateService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 子账户收益率表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/base/sub/account/yield/rate")
public class SubAccountYieldRateController extends BaseController {

    @Autowired
    private SubAccountYieldRateService subAccountYieldRateService;

    /**
     * 查询子账户收益率列表
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:list')")
    @GetMapping("/list")
    public TableDataInfo list(SubAccountYieldRateQuery subAccountYieldRateQuery) {
        startPage();
        List<SubAccountYieldRateDTO> list = subAccountYieldRateService.selectSubAccountYieldRateDtoList(subAccountYieldRateQuery);
        return getDataTable(list);
    }

    /**
     * 获取子账户收益率详细信息
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(subAccountYieldRateService.selectSubAccountYieldRateDtoById(id));
    }

    /**
     * 根据条件查询子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("accountName") String accountName) {
        return Result.success(subAccountYieldRateService.selectSubAccountYieldRateDtoByCondition(
                accountingPeriod, accountName));
    }

    /**
     * 新增子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:add')")
    @Log(title = "子账户收益率", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody SubAccountYieldRateDTO subAccountYieldRateDto) {
        return toAjax(subAccountYieldRateService.insertSubAccountYieldRateDto(subAccountYieldRateDto));
    }

    /**
     * 修改子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:edit')")
    @Log(title = "子账户收益率", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody SubAccountYieldRateDTO subAccountYieldRateDto) {
        return toAjax(subAccountYieldRateService.updateSubAccountYieldRateDto(subAccountYieldRateDto));
    }

    /**
     * 批量新增子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:add')")
    @Log(title = "子账户收益率", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<SubAccountYieldRateDTO> subAccountYieldRateEntityList) {
        return toAjax(subAccountYieldRateService.batchInsertSubAccountYieldRateDto(subAccountYieldRateEntityList));
    }

    /**
     * 删除子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:remove')")
    @Log(title = "子账户收益率", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(subAccountYieldRateService.deleteSubAccountYieldRateDtoByIds(ids));
    }

    /**
     * 删除指定统计期间的子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:remove')")
    @Log(title = "子账户收益率", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(subAccountYieldRateService.deleteSubAccountYieldRateDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出子账户收益率
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:export')")
    @Log(title = "子账户收益率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SubAccountYieldRateQuery subAccountYieldRateQuery) {
        List<SubAccountYieldRateDTO> list = subAccountYieldRateService.selectSubAccountYieldRateDtoList(subAccountYieldRateQuery);
        ExcelUtil<SubAccountYieldRateDTO> util = new ExcelUtil<>(SubAccountYieldRateDTO.class);
        util.exportExcel(list, "子账户收益率数据", response);
    }

    /**
     * 获取子账户收益率导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SubAccountYieldRateDTO> util = new ExcelUtil<>(SubAccountYieldRateDTO.class);
        util.exportTemplateExcel(response, "子账户收益率数据");
    }

    /**
     * 导入子账户收益率数据
     */
    @PreAuthorize("@ss.hasPermi('base:sub:account:yield:rate:import')")
    @Log(title = "子账户收益率", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SubAccountYieldRateDTO> util = new ExcelUtil<>(SubAccountYieldRateDTO.class);
        List<SubAccountYieldRateDTO> subAccountYieldRateList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = subAccountYieldRateService.importSubAccountYieldRateDto(subAccountYieldRateList, updateSupport, username);
        return Result.success(message);
    }
}
