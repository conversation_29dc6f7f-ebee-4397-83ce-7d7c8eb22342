package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurKeyDurationDiscountCurveDTO;
import com.xl.alm.app.entity.AdurKeyDurationDiscountCurveEntity;
import com.xl.alm.app.mapper.AdurKeyDurationDiscountCurveMapper;
import com.xl.alm.app.query.AdurKeyDurationDiscountCurveQuery;
import com.xl.alm.app.service.AdurKeyDurationDiscountCurveService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurKeyDurationDiscountCurveServiceImpl implements AdurKeyDurationDiscountCurveService {

    @Autowired
    private AdurKeyDurationDiscountCurveMapper adurKeyDurationDiscountCurveMapper;

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationDiscountCurveQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    @Override
    public List<AdurKeyDurationDiscountCurveDTO> selectAdurKeyDurationDiscountCurveDtoList(AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery) {
        List<AdurKeyDurationDiscountCurveEntity> entityList = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityList(adurKeyDurationDiscountCurveQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoById(Long id) {
        AdurKeyDurationDiscountCurveEntity entity = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 根据条件查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    @Override
    public AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection) {
        AdurKeyDurationDiscountCurveEntity entity = adurKeyDurationDiscountCurveMapper.selectAdurKeyDurationDiscountCurveEntityByCondition(accountPeriod, assetNumber, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurKeyDurationDiscountCurveDTO.class);
    }

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationDiscountCurveDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO) {
        AdurKeyDurationDiscountCurveEntity entity = EntityDtoConvertUtil.convertToEntity(adurKeyDurationDiscountCurveDTO, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.insertAdurKeyDurationDiscountCurveEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationDiscountCurveDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurKeyDurationDiscountCurveDto(List<AdurKeyDurationDiscountCurveDTO> adurKeyDurationDiscountCurveDtoList) {
        if (adurKeyDurationDiscountCurveDtoList == null || adurKeyDurationDiscountCurveDtoList.isEmpty()) {
            return 0;
        }
        List<AdurKeyDurationDiscountCurveEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurKeyDurationDiscountCurveDtoList, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.batchInsertAdurKeyDurationDiscountCurveEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO dto) {
        AdurKeyDurationDiscountCurveEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurKeyDurationDiscountCurveEntity.class);
        return adurKeyDurationDiscountCurveMapper.updateAdurKeyDurationDiscountCurveEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoById(Long id) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoByIds(Long[] ids) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityByIds(ids);
    }



    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurKeyDurationDiscountCurveDtoByAccountPeriod(String accountPeriod) {
        return adurKeyDurationDiscountCurveMapper.deleteAdurKeyDurationDiscountCurveEntityByAccountPeriod(accountPeriod);
    }
}
