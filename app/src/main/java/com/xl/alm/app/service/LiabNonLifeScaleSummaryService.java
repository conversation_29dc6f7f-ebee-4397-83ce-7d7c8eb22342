package com.xl.alm.app.service;

import com.xl.alm.app.dto.LiabNonLifeScaleSummaryDTO;
import com.xl.alm.app.query.LiabNonLifeScaleSummaryQuery;

import java.util.List;

/**
 * 非寿险负债规模汇总表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface LiabNonLifeScaleSummaryService {

    /**
     * 查询非寿险负债规模汇总表列表
     *
     * @param liabNonLifeScaleSummaryQuery 非寿险负债规模汇总表查询条件
     * @return 非寿险负债规模汇总表列表
     */
    List<LiabNonLifeScaleSummaryDTO> selectLiabNonLifeScaleSummaryDtoList(LiabNonLifeScaleSummaryQuery liabNonLifeScaleSummaryQuery);

    /**
     * 用id查询非寿险负债规模汇总表
     *
     * @param id id
     * @return 非寿险负债规模汇总表
     */
    LiabNonLifeScaleSummaryDTO selectLiabNonLifeScaleSummaryDtoById(Long id);

    /**
     * 根据账期和险种主类查询非寿险负债规模汇总表
     *
     * @param accountingPeriod  账期
     * @param insuranceMainType 险种主类
     * @return 非寿险负债规模汇总表
     */
    LiabNonLifeScaleSummaryDTO selectLiabNonLifeScaleSummaryDtoByCondition(
            String accountingPeriod,
            String insuranceMainType);

    /**
     * 新增非寿险负债规模汇总表
     *
     * @param dto 非寿险负债规模汇总表
     * @return 结果
     */
    int addLiabNonLifeScaleSummaryDto(LiabNonLifeScaleSummaryDTO dto);

    /**
     * 修改非寿险负债规模汇总表
     *
     * @param dto 非寿险负债规模汇总表
     * @return 结果
     */
    int updateLiabNonLifeScaleSummaryDto(LiabNonLifeScaleSummaryDTO dto);

    /**
     * 批量删除非寿险负债规模汇总表
     *
     * @param ids 需要删除的非寿险负债规模汇总表主键集合
     * @return 结果
     */
    int deleteLiabNonLifeScaleSummaryDtoByIds(Long[] ids);

    /**
     * 删除非寿险负债规模汇总表信息
     *
     * @param id 非寿险负债规模汇总表主键
     * @return 结果
     */
    int deleteLiabNonLifeScaleSummaryDtoById(Long id);

    /**
     * 批量插入非寿险负债规模汇总表数据
     *
     * @param liabNonLifeScaleSummaryDtoList 非寿险负债规模汇总表列表
     * @return 影响行数
     */
    int batchInsertLiabNonLifeScaleSummaryDto(List<LiabNonLifeScaleSummaryDTO> liabNonLifeScaleSummaryDtoList);

    /**
     * 删除指定账期的非寿险负债规模汇总表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteLiabNonLifeScaleSummaryDtoByPeriod(String accountingPeriod);

    /**
     * 导入非寿险负债规模汇总表
     *
     * @param dtoList       非寿险负债规模汇总表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importLiabNonLifeScaleSummaryDto(List<LiabNonLifeScaleSummaryDTO> dtoList, Boolean updateSupport, String username);
}
