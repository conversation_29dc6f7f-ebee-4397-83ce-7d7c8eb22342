package com.xl.alm.app.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.VariableMappingDTO;
import com.xl.alm.app.query.VariableMappingQuery;
import com.xl.alm.app.service.VariableMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 变量映射表Controller
 *
 * <AUTHOR> Assistant
 * @date 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/cft/variable/mapping")
public class VariableMappingController extends BaseController {

    @Autowired
    private VariableMappingService variableMappingService;

    /**
     * 查询变量映射表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(VariableMappingQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<VariableMappingDTO> list = variableMappingService.selectVariableMappingList(query);
        return getDataTable(list);
    }

    /**
     * 导出变量映射表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:export')")
    @Log(title = "变量映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VariableMappingQuery query) {
        List<VariableMappingDTO> list = variableMappingService.selectVariableMappingList(query);
        ExcelUtil<VariableMappingDTO> util = new ExcelUtil<>(VariableMappingDTO.class);
        util.exportExcel(response, list, "变量映射表数据");
    }

    /**
     * 获取变量映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(variableMappingService.selectVariableMappingById(id));
    }

    /**
     * 新增变量映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:add')")
    @Log(title = "变量映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody VariableMappingDTO dto) {
        dto.setCreateBy(SecurityUtils.getUsername());
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(variableMappingService.insertVariableMapping(dto));
    }

    /**
     * 修改变量映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:edit')")
    @Log(title = "变量映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody VariableMappingDTO dto) {
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(variableMappingService.updateVariableMapping(dto));
    }

    /**
     * 删除变量映射表
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:remove')")
    @Log(title = "变量映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(variableMappingService.deleteVariableMappingByIds(ids));
    }

    /**
     * 获取变量映射表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        try {
            variableMappingService.exportTemplate(response);
        } catch (Exception e) {
            throw new RuntimeException("模板下载失败：" + e.getMessage());
        }
    }

    /**
     * 导入变量映射表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:variable:mapping:import')")
    @Log(title = "变量映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("导入失败：请选择要导入的文件");
            }

            ExcelUtil<VariableMappingDTO> util = new ExcelUtil<>(VariableMappingDTO.class);
            List<VariableMappingDTO> dtoList = util.importExcel(file.getInputStream());

            if (dtoList == null || dtoList.isEmpty()) {
                return Result.error("导入失败：Excel文件中没有数据");
            }

            String operName = SecurityUtils.getUsername();
            String message = variableMappingService.importVariableMappingData(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            log.error("导入变量映射表数据失败", e);
            String errorMsg = e.getMessage();
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                errorMsg = "导入过程中发生未知错误";
            }
            return Result.error("导入失败：" + errorMsg);
        }
    }
}
