package com.xl.alm.app.service;

import com.xl.alm.app.dto.LiabScaleDetailDTO;
import com.xl.alm.app.query.LiabScaleDetailQuery;

import java.util.List;

/**
 * 负债规模明细表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface LiabScaleDetailService {

    /**
     * 查询负债规模明细表列表
     *
     * @param liabScaleDetailQuery 负债规模明细表查询条件
     * @return 负债规模明细表列表
     */
    List<LiabScaleDetailDTO> selectLiabScaleDetailDtoList(LiabScaleDetailQuery liabScaleDetailQuery);

    /**
     * 用id查询负债规模明细表
     *
     * @param id id
     * @return 负债规模明细表
     */
    LiabScaleDetailDTO selectLiabScaleDetailDtoById(Long id);

    /**
     * 根据账期和精算代码查询负债规模明细表
     *
     * @param accountingPeriod 账期
     * @param actuarialCode    精算代码
     * @return 负债规模明细表
     */
    LiabScaleDetailDTO selectLiabScaleDetailDtoByCondition(
            String accountingPeriod,
            String actuarialCode);

    /**
     * 新增负债规模明细表
     *
     * @param dto 负债规模明细表
     * @return 结果
     */
    int addLiabScaleDetailDto(LiabScaleDetailDTO dto);

    /**
     * 修改负债规模明细表
     *
     * @param dto 负债规模明细表
     * @return 结果
     */
    int updateLiabScaleDetailDto(LiabScaleDetailDTO dto);

    /**
     * 批量删除负债规模明细表
     *
     * @param ids 需要删除的负债规模明细表主键集合
     * @return 结果
     */
    int deleteLiabScaleDetailDtoByIds(Long[] ids);

    /**
     * 删除负债规模明细表信息
     *
     * @param id 负债规模明细表主键
     * @return 结果
     */
    int deleteLiabScaleDetailDtoById(Long id);

    /**
     * 批量插入负债规模明细表数据
     *
     * @param liabScaleDetailDtoList 负债规模明细表列表
     * @return 影响行数
     */
    int batchInsertLiabScaleDetailDto(List<LiabScaleDetailDTO> liabScaleDetailDtoList);

    /**
     * 删除指定账期的负债规模明细表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteLiabScaleDetailDtoByPeriod(String accountingPeriod);

    /**
     * 导入负债规模明细表
     *
     * @param dtoList       负债规模明细表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importLiabScaleDetailDto(List<LiabScaleDetailDTO> dtoList, Boolean updateSupport, String username);
}
