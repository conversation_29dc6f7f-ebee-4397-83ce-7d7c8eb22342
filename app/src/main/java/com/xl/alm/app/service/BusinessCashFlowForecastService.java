package com.xl.alm.app.service;

import com.xl.alm.app.dto.BusinessCashFlowForecastDTO;
import com.xl.alm.app.query.BusinessCashFlowForecastQuery;

import java.util.List;

/**
 * 业务现金流预测表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface BusinessCashFlowForecastService {

    /**
     * 查询业务现金流预测表列表
     *
     * @param businessCashFlowForecastQuery 业务现金流预测表查询条件
     * @return 业务现金流预测表列表
     */
    List<BusinessCashFlowForecastDTO> selectBusinessCashFlowForecastDtoList(BusinessCashFlowForecastQuery businessCashFlowForecastQuery);

    /**
     * 根据ID查询业务现金流预测表
     *
     * @param id 主键ID
     * @return 业务现金流预测表
     */
    BusinessCashFlowForecastDTO selectBusinessCashFlowForecastDtoById(Long id);

    /**
     * 新增业务现金流预测表
     *
     * @param businessCashFlowForecastDto 业务现金流预测表
     * @return 影响行数
     */
    int insertBusinessCashFlowForecastDto(BusinessCashFlowForecastDTO businessCashFlowForecastDto);

    /**
     * 修改业务现金流预测表
     *
     * @param businessCashFlowForecastDto 业务现金流预测表
     * @return 影响行数
     */
    int updateBusinessCashFlowForecastDto(BusinessCashFlowForecastDTO businessCashFlowForecastDto);

    /**
     * 删除业务现金流预测表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteBusinessCashFlowForecastDtoById(Long id);

    /**
     * 批量删除业务现金流预测表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteBusinessCashFlowForecastDtoByIds(Long[] ids);

    /**
     * 批量新增业务现金流预测表
     *
     * @param businessCashFlowForecastDtoList 业务现金流预测表列表
     * @return 影响行数
     */
    int batchInsertBusinessCashFlowForecastDto(List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList);

    /**
     * 根据账期删除业务现金流预测表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteBusinessCashFlowForecastDtoByPeriod(String accountingPeriod);

    /**
     * 根据条件查询业务现金流预测表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param designType 设计类型
     * @param businessType 业务类型
     * @param item 项目
     * @return 业务现金流预测表
     */
    BusinessCashFlowForecastDTO selectBusinessCashFlowForecastDtoByCondition(String accountingPeriod, String scenarioName, String designType, String businessType, String item);

    /**
     * 导入业务现金流预测表数据
     *
     * @param businessCashFlowForecastDtoList 业务现金流预测表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importBusinessCashFlowForecastData(List<BusinessCashFlowForecastDTO> businessCashFlowForecastDtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导出业务现金流预测表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 导出业务现金流预测表
     *
     * @param response HTTP响应
     * @param query 查询条件
     */
    void exportBusinessCashFlowForecast(javax.servlet.http.HttpServletResponse response, BusinessCashFlowForecastQuery query) throws Exception;
}
