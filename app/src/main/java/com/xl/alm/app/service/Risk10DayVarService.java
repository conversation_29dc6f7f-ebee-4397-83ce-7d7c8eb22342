package com.xl.alm.app.service;

import com.xl.alm.app.dto.Risk10DayVarDTO;
import com.xl.alm.app.query.Risk10DayVarQuery;

import java.util.List;

/**
 * 风险10日VaR值表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface Risk10DayVarService {

    /**
     * 查询风险10日VaR值表列表
     *
     * @param risk10DayVarQuery 风险10日VaR值表查询条件
     * @return 风险10日VaR值表列表
     */
    List<Risk10DayVarDTO> selectRisk10DayVarDtoList(Risk10DayVarQuery risk10DayVarQuery);

    /**
     * 用id查询风险10日VaR值表
     *
     * @param id id
     * @return 风险10日VaR值表
     */
    Risk10DayVarDTO selectRisk10DayVarDtoById(Long id);

    /**
     * 根据账期、境内外标识、项目分类和样本期限查询风险10日VaR值表
     *
     * @param accountingPeriod 账期
     * @param domesticForeign 境内外标识
     * @param itemCategory 项目分类
     * @param samplePeriod 样本期限
     * @return 风险10日VaR值表
     */
    Risk10DayVarDTO selectRisk10DayVarDtoByCondition(String accountingPeriod, String domesticForeign, String itemCategory, String samplePeriod);

    /**
     * 新增风险10日VaR值表
     *
     * @param risk10DayVarDTO 风险10日VaR值表
     * @return 结果
     */
    int insertRisk10DayVarDto(Risk10DayVarDTO risk10DayVarDTO);

    /**
     * 批量新增风险10日VaR值表
     *
     * @param risk10DayVarDtoList 风险10日VaR值表列表
     * @return 影响行数
     */
    int batchInsertRisk10DayVarDto(List<Risk10DayVarDTO> risk10DayVarDtoList);

    /**
     * 修改风险10日VaR值表
     *
     * @param risk10DayVarDTO 风险10日VaR值表
     * @return 结果
     */
    int updateRisk10DayVarDto(Risk10DayVarDTO risk10DayVarDTO);

    /**
     * 批量删除风险10日VaR值表
     *
     * @param ids 需要删除的风险10日VaR值表主键集合
     * @return 结果
     */
    int deleteRisk10DayVarDtoByIds(Long[] ids);

    /**
     * 删除风险10日VaR值表信息
     *
     * @param id 风险10日VaR值表主键
     * @return 结果
     */
    int deleteRisk10DayVarDtoById(Long id);

    /**
     * 导入风险10日VaR值表数据
     *
     * @param risk10DayVarDtoList 风险10日VaR值表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importRisk10DayVarDto(List<Risk10DayVarDTO> risk10DayVarDtoList, Boolean isUpdateSupport, String operName);
}
