package com.xl.alm.app.service;

import com.xl.alm.app.dto.FixedIncomeRatingTermDistDTO;
import com.xl.alm.app.query.FixedIncomeRatingTermDistQuery;

import java.util.List;

/**
 * 固定收益类投资资产外部评级剩余期限分布表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FixedIncomeRatingTermDistService {

    /**
     * 查询固定收益类投资资产外部评级剩余期限分布表列表
     *
     * @param fixedIncomeRatingTermDistQuery 固定收益类投资资产外部评级剩余期限分布表查询条件
     * @return 固定收益类投资资产外部评级剩余期限分布表列表
     */
    List<FixedIncomeRatingTermDistDTO> selectFixedIncomeRatingTermDistDtoList(FixedIncomeRatingTermDistQuery fixedIncomeRatingTermDistQuery);

    /**
     * 用id查询固定收益类投资资产外部评级剩余期限分布表
     *
     * @param id id
     * @return 固定收益类投资资产外部评级剩余期限分布表
     */
    FixedIncomeRatingTermDistDTO selectFixedIncomeRatingTermDistDtoById(Long id);

    /**
     * 根据账期、境内外标识、信用评级分类和固收资产剩余期限资产分类查询固定收益类投资资产外部评级剩余期限分布表
     *
     * @param accountingPeriod 账期
     * @param domesticForeign 境内外标识
     * @param creditRatingCategory 信用评级分类
     * @param fixedIncomeTermCategory 固收资产剩余期限资产分类
     * @return 固定收益类投资资产外部评级剩余期限分布表
     */
    FixedIncomeRatingTermDistDTO selectFixedIncomeRatingTermDistDtoByCondition(
            String accountingPeriod,
            String domesticForeign,
            String creditRatingCategory,
            String fixedIncomeTermCategory);

    /**
     * 新增固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dto 固定收益类投资资产外部评级剩余期限分布表
     * @return 结果
     */
    int addFixedIncomeRatingTermDistDto(FixedIncomeRatingTermDistDTO dto);

    /**
     * 修改固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dto 固定收益类投资资产外部评级剩余期限分布表
     * @return 结果
     */
    int updateFixedIncomeRatingTermDistDto(FixedIncomeRatingTermDistDTO dto);

    /**
     * 批量删除固定收益类投资资产外部评级剩余期限分布表
     *
     * @param ids 需要删除的固定收益类投资资产外部评级剩余期限分布表主键集合
     * @return 结果
     */
    int deleteFixedIncomeRatingTermDistDtoByIds(Long[] ids);

    /**
     * 删除固定收益类投资资产外部评级剩余期限分布表信息
     *
     * @param id 固定收益类投资资产外部评级剩余期限分布表主键
     * @return 结果
     */
    int deleteFixedIncomeRatingTermDistDtoById(Long id);

    /**
     * 批量插入固定收益类投资资产外部评级剩余期限分布表数据
     *
     * @param fixedIncomeRatingTermDistDtoList 固定收益类投资资产外部评级剩余期限分布表列表
     * @return 影响行数
     */
    int batchInsertFixedIncomeRatingTermDistDto(List<FixedIncomeRatingTermDistDTO> fixedIncomeRatingTermDistDtoList);

    /**
     * 删除指定账期的固定收益类投资资产外部评级剩余期限分布表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteFixedIncomeRatingTermDistDtoByPeriod(String accountingPeriod);

    /**
     * 导入固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dtoList       固定收益类投资资产外部评级剩余期限分布表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importFixedIncomeRatingTermDistDto(List<FixedIncomeRatingTermDistDTO> dtoList, Boolean updateSupport, String username);
}
