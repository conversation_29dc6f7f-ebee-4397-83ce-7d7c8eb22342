package com.xl.alm.app.service;

import com.xl.alm.app.dto.ActuarialExpenseSummaryDTO;
import com.xl.alm.app.query.ActuarialExpenseSummaryQuery;

import java.util.List;

/**
 * 精算业管费汇总表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface ActuarialExpenseSummaryService {

    /**
     * 查询精算业管费汇总表列表
     *
     * @param actuarialExpenseSummaryQuery 精算业管费汇总表查询条件
     * @return 精算业管费汇总表列表
     */
    List<ActuarialExpenseSummaryDTO> selectActuarialExpenseSummaryDtoList(ActuarialExpenseSummaryQuery actuarialExpenseSummaryQuery);

    /**
     * 根据ID查询精算业管费汇总表
     *
     * @param id 主键ID
     * @return 精算业管费汇总表
     */
    ActuarialExpenseSummaryDTO selectActuarialExpenseSummaryDtoById(Long id);

    /**
     * 新增精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDto 精算业管费汇总表
     * @return 影响行数
     */
    int insertActuarialExpenseSummaryDto(ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto);

    /**
     * 修改精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDto 精算业管费汇总表
     * @return 影响行数
     */
    int updateActuarialExpenseSummaryDto(ActuarialExpenseSummaryDTO actuarialExpenseSummaryDto);

    /**
     * 删除精算业管费汇总表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteActuarialExpenseSummaryDtoById(Long id);

    /**
     * 批量删除精算业管费汇总表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteActuarialExpenseSummaryDtoByIds(Long[] ids);

    /**
     * 批量新增精算业管费汇总表
     *
     * @param actuarialExpenseSummaryDtoList 精算业管费汇总表列表
     * @return 影响行数
     */
    int batchInsertActuarialExpenseSummaryDto(List<ActuarialExpenseSummaryDTO> actuarialExpenseSummaryDtoList);

    /**
     * 根据账期删除精算业管费汇总表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteActuarialExpenseSummaryDtoByPeriod(String accountingPeriod);

    /**
     * 根据条件查询精算业管费汇总表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param actuarialExpenseType 精算费用类型
     * @param businessType 业务类型
     * @param designType 设计类型
     * @return 精算业管费汇总表
     */
    ActuarialExpenseSummaryDTO selectActuarialExpenseSummaryDtoByCondition(
            String accountingPeriod, String scenarioName, String actuarialExpenseType,
            String businessType, String designType);

    /**
     * 导入精算业管费汇总表数据
     *
     * @param actuarialExpenseSummaryDtoList 精算业管费汇总表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importActuarialExpenseSummaryData(List<ActuarialExpenseSummaryDTO> actuarialExpenseSummaryDtoList, Boolean isUpdateSupport, String operName);



    /**
     * 导入精算业管费汇总数据（Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importActuarialExpenseSummaryDataFromExcel(java.io.InputStream inputStream, Boolean isUpdateSupport, String operName);

    /**
     * 导出精算业管费汇总表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 导出精算业管费汇总表
     *
     * @param response HTTP响应
     * @param query 查询条件
     */
    void exportActuarialExpenseSummaryHorizontal(javax.servlet.http.HttpServletResponse response, ActuarialExpenseSummaryQuery query) throws Exception;
}
