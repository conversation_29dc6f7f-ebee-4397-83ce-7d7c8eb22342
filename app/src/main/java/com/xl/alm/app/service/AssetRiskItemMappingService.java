package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetRiskItemMappingDTO;
import com.xl.alm.app.query.AssetRiskItemMappingQuery;

import java.util.List;

/**
 * 保险资产风险项目映射表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetRiskItemMappingService {

    /**
     * 查询保险资产风险项目映射表列表
     *
     * @param assetRiskItemMappingQuery 保险资产风险项目映射表查询条件
     * @return 保险资产风险项目映射表列表
     */
    List<AssetRiskItemMappingDTO> selectAssetRiskItemMappingDtoList(AssetRiskItemMappingQuery assetRiskItemMappingQuery);

    /**
     * 用id查询保险资产风险项目映射表
     *
     * @param id id
     * @return 保险资产风险项目映射表
     */
    AssetRiskItemMappingDTO selectAssetRiskItemMappingDtoById(Long id);

    /**
     * 根据账期、项目名称和五级分类资产统计标识查询保险资产风险项目映射表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @param fiveLevelStatisticsFlag 五级分类资产统计标识
     * @return 保险资产风险项目映射表
     */
    AssetRiskItemMappingDTO selectAssetRiskItemMappingDtoByCondition(
            String accountingPeriod,
            String itemName,
            String fiveLevelStatisticsFlag);

    /**
     * 新增保险资产风险项目映射表
     *
     * @param dto 保险资产风险项目映射表
     * @return 结果
     */
    int addAssetRiskItemMappingDto(AssetRiskItemMappingDTO dto);

    /**
     * 修改保险资产风险项目映射表
     *
     * @param dto 保险资产风险项目映射表
     * @return 结果
     */
    int updateAssetRiskItemMappingDto(AssetRiskItemMappingDTO dto);

    /**
     * 批量删除保险资产风险项目映射表
     *
     * @param ids 需要删除的保险资产风险项目映射表主键集合
     * @return 结果
     */
    int deleteAssetRiskItemMappingDtoByIds(Long[] ids);

    /**
     * 删除保险资产风险项目映射表信息
     *
     * @param id 保险资产风险项目映射表主键
     * @return 结果
     */
    int deleteAssetRiskItemMappingDtoById(Long id);

    /**
     * 批量插入保险资产风险项目映射表数据
     *
     * @param assetRiskItemMappingDtoList 保险资产风险项目映射表列表
     * @return 影响行数
     */
    int batchInsertAssetRiskItemMappingDto(List<AssetRiskItemMappingDTO> assetRiskItemMappingDtoList);

    /**
     * 删除指定账期的保险资产风险项目映射表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAssetRiskItemMappingDtoByPeriod(String accountingPeriod);

    /**
     * 导入保险资产风险项目映射表
     *
     * @param dtoList       保险资产风险项目映射表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importAssetRiskItemMappingDto(List<AssetRiskItemMappingDTO> dtoList, Boolean updateSupport, String username);
}
