package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.FinancialBudgetExpenseSplitDTO;
import com.xl.alm.app.query.FinancialBudgetExpenseSplitQuery;
import com.xl.alm.app.service.FinancialBudgetExpenseSplitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 财务预算费用拆分表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/financial/budget/expense/split")
public class FinancialBudgetExpenseSplitController extends BaseController {

    @Autowired
    private FinancialBudgetExpenseSplitService financialBudgetExpenseSplitService;

    /**
     * 查询财务预算费用拆分表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancialBudgetExpenseSplitQuery query) {
        startPage();
        List<FinancialBudgetExpenseSplitDTO> list = financialBudgetExpenseSplitService.selectFinancialBudgetExpenseSplitDtoList(query);
        return getDataTable(list);
    }

    /**
     * 根据ID获取财务预算费用拆分表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(financialBudgetExpenseSplitService.selectFinancialBudgetExpenseSplitDtoById(id));
    }

    /**
     * 新增财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:add')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto) {
        // 验证是否存在相同的财务预算费用拆分
        FinancialBudgetExpenseSplitDTO existDto = financialBudgetExpenseSplitService.selectFinancialBudgetExpenseSplitDtoByCondition(
                financialBudgetExpenseSplitDto.getAccountingPeriod(),
                financialBudgetExpenseSplitDto.getScenarioName(),
                financialBudgetExpenseSplitDto.getFinancialExpenseType(),
                financialBudgetExpenseSplitDto.getBusinessType(),
                financialBudgetExpenseSplitDto.getDesignType());

        if (existDto != null) {
            return Result.error("该财务预算费用拆分已存在");
        }

        financialBudgetExpenseSplitDto.setCreateBy(SecurityUtils.getUsername());
        return toAjax(financialBudgetExpenseSplitService.insertFinancialBudgetExpenseSplitDto(financialBudgetExpenseSplitDto));
    }

    /**
     * 修改财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:edit')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto) {
        // 验证是否存在相同的财务预算费用拆分（排除自己）
        FinancialBudgetExpenseSplitDTO existDto = financialBudgetExpenseSplitService.selectFinancialBudgetExpenseSplitDtoByCondition(
                financialBudgetExpenseSplitDto.getAccountingPeriod(),
                financialBudgetExpenseSplitDto.getScenarioName(),
                financialBudgetExpenseSplitDto.getFinancialExpenseType(),
                financialBudgetExpenseSplitDto.getBusinessType(),
                financialBudgetExpenseSplitDto.getDesignType());

        if (existDto != null && !existDto.getId().equals(financialBudgetExpenseSplitDto.getId())) {
            return Result.error("该财务预算费用拆分已存在");
        }

        financialBudgetExpenseSplitDto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(financialBudgetExpenseSplitService.updateFinancialBudgetExpenseSplitDto(financialBudgetExpenseSplitDto));
    }

    /**
     * 删除财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:remove')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(financialBudgetExpenseSplitService.deleteFinancialBudgetExpenseSplitDtoByIds(ids));
    }

    /**
     * 根据条件查询财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod,
                                 @RequestParam String scenarioName,
                                 @RequestParam String financialExpenseType,
                                 @RequestParam String businessType,
                                 @RequestParam String designType) {
        FinancialBudgetExpenseSplitDTO dto = financialBudgetExpenseSplitService.selectFinancialBudgetExpenseSplitDtoByCondition(
                accountingPeriod, scenarioName, financialExpenseType, businessType, designType);
        return Result.success(dto);
    }

    /**
     * 批量新增财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:add')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<FinancialBudgetExpenseSplitDTO> financialBudgetExpenseSplitDtoList) {
        String operName = SecurityUtils.getUsername();
        for (FinancialBudgetExpenseSplitDTO dto : financialBudgetExpenseSplitDtoList) {
            dto.setCreateBy(operName);
        }
        return toAjax(financialBudgetExpenseSplitService.batchInsertFinancialBudgetExpenseSplitDto(financialBudgetExpenseSplitDtoList));
    }

    /**
     * 根据账期删除财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:remove')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(financialBudgetExpenseSplitService.deleteFinancialBudgetExpenseSplitDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出财务预算费用拆分表
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:export')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancialBudgetExpenseSplitQuery query) throws Exception {
        financialBudgetExpenseSplitService.exportFinancialBudgetExpenseSplitHorizontal(response, query);
    }

    /**
     * 获取财务预算费用拆分表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:import')")
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws Exception {
        financialBudgetExpenseSplitService.exportTemplate(response);
    }

    /**
     * 导入财务预算费用拆分表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:financial:budget:expense:split:import')")
    @Log(title = "财务预算费用拆分表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        String operName = SecurityUtils.getUsername();
        String message = financialBudgetExpenseSplitService.importFinancialBudgetExpenseSplitDataFromExcel(file.getInputStream(), updateSupport, operName);
        return Result.success(message);
    }
}
