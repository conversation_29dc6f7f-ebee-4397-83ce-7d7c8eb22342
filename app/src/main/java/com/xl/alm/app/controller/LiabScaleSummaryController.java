package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.LiabScaleSummaryDTO;
import com.xl.alm.app.query.LiabScaleSummaryQuery;
import com.xl.alm.app.service.LiabScaleSummaryService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 负债规模汇总表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/scale/summary")
public class LiabScaleSummaryController extends BaseController {

    @Autowired
    private LiabScaleSummaryService liabScaleSummaryService;

    /**
     * 查询负债规模汇总表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(LiabScaleSummaryQuery liabScaleSummaryQuery) {
        startPage();
        List<LiabScaleSummaryDTO> list = liabScaleSummaryService.selectLiabScaleSummaryDtoList(liabScaleSummaryQuery);
        return getDataTable(list);
    }

    /**
     * 获取负债规模汇总表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(liabScaleSummaryService.selectLiabScaleSummaryDtoById(id));
    }

    /**
     * 新增负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:add')")
    @Log(title = "负债规模汇总表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody LiabScaleSummaryDTO liabScaleSummaryDTO) {
        return toAjax(liabScaleSummaryService.addLiabScaleSummaryDto(liabScaleSummaryDTO));
    }

    /**
     * 修改负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:edit')")
    @Log(title = "负债规模汇总表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody LiabScaleSummaryDTO liabScaleSummaryDTO) {
        return toAjax(liabScaleSummaryService.updateLiabScaleSummaryDto(liabScaleSummaryDTO));
    }

    /**
     * 删除负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:remove')")
    @Log(title = "负债规模汇总表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(liabScaleSummaryService.deleteLiabScaleSummaryDtoByIds(ids));
    }

    /**
     * 导出负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:export')")
    @Log(title = "负债规模汇总表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiabScaleSummaryQuery liabScaleSummaryQuery) {
        List<LiabScaleSummaryDTO> list = liabScaleSummaryService.selectLiabScaleSummaryDtoList(liabScaleSummaryQuery);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<LiabScaleSummaryDTO> util = new ExcelUtil<>(LiabScaleSummaryDTO.class);
        util.exportExcel(list, "负债规模汇总表数据", response);
    }

    /**
     * 获取负债规模汇总表导入模板
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<LiabScaleSummaryDTO> util = new ExcelUtil<>(LiabScaleSummaryDTO.class);
        util.exportTemplateExcel(response, "负债规模汇总表");
    }

    /**
     * 导入负债规模汇总表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:summary:import')")
    @Log(title = "负债规模汇总表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<LiabScaleSummaryDTO> util = new ExcelUtil<>(LiabScaleSummaryDTO.class);
        List<LiabScaleSummaryDTO> liabScaleSummaryList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = liabScaleSummaryService.importLiabScaleSummaryDto(liabScaleSummaryList, updateSupport, username);
        return Result.success(message);
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 负债规模汇总表数据列表
     */
    private void convertDictCodeToLabel(List<LiabScaleSummaryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取设计类型字典数据
            List<SysDictData> designTypeDict = dictTypeService.selectDictDataByType("cost_design_type");

            // 为每条记录转换字典编码为中文标签
            for (LiabScaleSummaryDTO dto : list) {
                // 转换设计类型
                dto.setDesignType(convertCodeToLabel(dto.getDesignType(), designTypeDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            logger.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将编码转换为标签
     *
     * @param code 编码
     * @param dictDataList 字典数据列表
     * @return 标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        return code;
    }
}
