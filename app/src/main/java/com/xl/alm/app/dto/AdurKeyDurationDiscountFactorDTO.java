package com.xl.alm.app.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ADUR关键久期折现因子表含价差DTO
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AdurKeyDurationDiscountFactorDTO extends BaseDTO {
    private Long id;

    /**
     * 账期,格式YYYYMM
     */
    @NotBlank(message = "账期不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "账期格式必须为YYYYMM")
    @Excel(name = "账期")
    @ExcelProperty("账期")
    private String accountPeriod;

    /**
     * 久期类型
     */
    @NotBlank(message = "久期类型不能为空")
    @Size(max = 20, message = "久期类型长度不能超过20个字符")
    @Excel(name = "久期类型", dictType = "adur_duration_type")
    @ExcelProperty("久期类型")
    private String durationType;

    /**
     * 基点类型
     */
    @Size(max = 20, message = "基点类型长度不能超过20个字符")
    @Excel(name = "基点类型", dictType = "adur_basis_point_type")
    @ExcelProperty("基点类型")
    private String basisPointType;

    /**
     * 关键期限
     */
    @NotBlank(message = "关键期限不能为空")
    @Size(max = 20, message = "关键期限长度不能超过20个字符")
    @Excel(name = "关键期限", dictType = "adur_key_term")
    @ExcelProperty("关键期限")
    private String keyTerm;

    /**
     * 压力方向
     */
    @NotBlank(message = "压力方向不能为空")
    @Size(max = 10, message = "压力方向长度不能超过10个字符")
    @Excel(name = "压力方向", dictType = "adur_stress_direction")
    @ExcelProperty("压力方向")
    private String stressDirection;

    /**
     * 日期类型
     */
    @NotBlank(message = "日期类型不能为空")
    @Size(max = 20, message = "日期类型长度不能超过20个字符")
    @Excel(name = "日期类型", dictType = "adur_date_type")
    @ExcelProperty("日期类型")
    private String dateType;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    @Excel(name = "日期", dateFormat = "yyyy-MM-dd")
    @ExcelProperty("日期")
    private Date date;

    /**
     * 价差类型
     */
    @Size(max = 20, message = "价差类型长度不能超过20个字符")
    @Excel(name = "价差类型", dictType = "adur_spread_type")
    @ExcelProperty("价差类型")
    private String spreadType;

    /**
     * 价差
     */
    @DecimalMin(value = "0", message = "价差不能小于0")
    @Digits(integer = 4, fraction = 6, message = "价差格式不正确，整数部分不能超过4位，小数部分不能超过6位")
    @Excel(name = "价差")
    @ExcelProperty("价差")
    private BigDecimal spread;

    /**
     * 曲线细分类
     */
    @Size(max = 10, message = "曲线细分类长度不能超过10个字符")
    @Excel(name = "曲线细分类", dictType = "adur_curve_sub_category")
    @ExcelProperty("曲线细分类")
    private String curveSubCategory;

    /**
     * 资产编号
     */
    @NotBlank(message = "资产编号不能为空")
    @Size(max = 20, message = "资产编号长度不能超过20个字符")
    @Excel(name = "资产编号")
    @ExcelProperty("资产编号")
    private String assetNumber;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    @Size(max = 50, message = "账户名称长度不能超过50个字符")
    @Excel(name = "账户名称", dictType = "adur_account_name")
    @ExcelProperty("账户名称")
    private String accountName;

    /**
     * 资产名称
     */
    @NotBlank(message = "资产名称不能为空")
    @Size(max = 100, message = "资产名称长度不能超过100个字符")
    @Excel(name = "资产名称")
    @ExcelProperty("资产名称")
    private String assetName;

    /**
     * 证券代码
     */
    @NotBlank(message = "证券代码不能为空")
    @Size(max = 20, message = "证券代码长度不能超过20个字符")
    @Excel(name = "证券代码")
    @ExcelProperty("证券代码")
    private String securityCode;

    /**
     * 折现曲线标识
     */
    @NotBlank(message = "折现曲线标识不能为空")
    @Size(max = 10, message = "折现曲线标识长度不能超过10个字符")
    @Excel(name = "折现曲线标识")
    @ExcelProperty("折现曲线标识")
    private String curveId;

    /**
     * 发行时点价差
     */
    @DecimalMin(value = "0", message = "发行时点价差不能小于0")
    @Digits(integer = 4, fraction = 6, message = "发行时点价差格式不正确，整数部分不能超过4位，小数部分不能超过6位")
    @Excel(name = "发行时点价差")
    @ExcelProperty("发行时点价差")
    private BigDecimal issueSpread;

    // 期限字段 term_0 到 term_600，由于字段过多，这里只展示前几个和最后一个
    /**
     * 关键久期折现因子表含价差值集
     */
    @Excel(name = "关键久期折现因子表含价差值集", prompt = "JSON格式数据，包含term_0到term_600的期限数据")
    @ExcelProperty("关键久期折现因子表含价差值集")
    private String keyDurationDiscountFactorSet;
}
