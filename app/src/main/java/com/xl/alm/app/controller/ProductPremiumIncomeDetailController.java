package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.ProductPremiumIncomeDetailDTO;
import com.xl.alm.app.query.ProductPremiumIncomeDetailQuery;
import com.xl.alm.app.service.ProductPremiumIncomeDetailService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分产品保费收入表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cost/product/premium/income/detail")
public class ProductPremiumIncomeDetailController extends BaseController {

    @Autowired
    private ProductPremiumIncomeDetailService productPremiumIncomeDetailService;

    /**
     * 查询分产品保费收入列表
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductPremiumIncomeDetailQuery productPremiumIncomeDetailQuery) {
        startPage();
        List<ProductPremiumIncomeDetailDTO> list = productPremiumIncomeDetailService.selectProductPremiumIncomeDetailDtoList(productPremiumIncomeDetailQuery);
        return getDataTable(list);
    }

    /**
     * 获取分产品保费收入详细信息
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(productPremiumIncomeDetailService.selectProductPremiumIncomeDetailDtoById(id));
    }

    /**
     * 根据条件查询分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("actuarialCode") String actuarialCode) {
        return Result.success(productPremiumIncomeDetailService.selectProductPremiumIncomeDetailDtoByCondition(
                accountingPeriod, actuarialCode));
    }

    /**
     * 新增分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:add')")
    @Log(title = "分产品保费收入", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody ProductPremiumIncomeDetailDTO productPremiumIncomeDetailDto) {
        return toAjax(productPremiumIncomeDetailService.insertProductPremiumIncomeDetailDto(productPremiumIncomeDetailDto));
    }

    /**
     * 修改分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:edit')")
    @Log(title = "分产品保费收入", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody ProductPremiumIncomeDetailDTO productPremiumIncomeDetailDto) {
        return toAjax(productPremiumIncomeDetailService.updateProductPremiumIncomeDetailDto(productPremiumIncomeDetailDto));
    }

    /**
     * 批量新增分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:add')")
    @Log(title = "分产品保费收入", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<ProductPremiumIncomeDetailDTO> productPremiumIncomeDetailEntityList) {
        return toAjax(productPremiumIncomeDetailService.batchInsertProductPremiumIncomeDetailDto(productPremiumIncomeDetailEntityList));
    }

    /**
     * 删除分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:remove')")
    @Log(title = "分产品保费收入", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(productPremiumIncomeDetailService.deleteProductPremiumIncomeDetailDtoByIds(ids));
    }

    /**
     * 删除指定统计期间的分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:remove')")
    @Log(title = "分产品保费收入", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(productPremiumIncomeDetailService.deleteProductPremiumIncomeDetailDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出分产品保费收入
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:export')")
    @Log(title = "分产品保费收入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductPremiumIncomeDetailQuery productPremiumIncomeDetailQuery) {
        List<ProductPremiumIncomeDetailDTO> list = productPremiumIncomeDetailService.selectProductPremiumIncomeDetailDtoList(productPremiumIncomeDetailQuery);
        ExcelUtil<ProductPremiumIncomeDetailDTO> util = new ExcelUtil<>(ProductPremiumIncomeDetailDTO.class);
        util.exportExcel(list, "分产品保费收入数据", response);
    }

    /**
     * 获取分产品保费收入导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProductPremiumIncomeDetailDTO> util = new ExcelUtil<>(ProductPremiumIncomeDetailDTO.class);
        util.exportTemplateExcel(response, "分产品保费收入数据");
    }

    /**
     * 导入分产品保费收入数据
     */
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:detail:import')")
    @Log(title = "分产品保费收入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<ProductPremiumIncomeDetailDTO> util = new ExcelUtil<>(ProductPremiumIncomeDetailDTO.class);
        List<ProductPremiumIncomeDetailDTO> productPremiumIncomeDetailList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = productPremiumIncomeDetailService.importProductPremiumIncomeDetailDto(productPremiumIncomeDetailList, updateSupport, username);
        return Result.success(message);
    }
}
