package com.xl.alm.app.service;

import com.jd.lightning.common.core.page.TableDataInfo;
import com.xl.alm.app.dto.PaymentMethodMapDTO;
import com.xl.alm.app.query.PaymentMethodMapQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 付息方式映射表 Service接口
 *
 * <AUTHOR> Assistant
 */
public interface PaymentMethodMapService {

    /**
     * 查询付息方式映射表列表
     *
     * @param query 查询条件
     * @return 付息方式映射表集合
     */
    List<PaymentMethodMapDTO> selectPaymentMethodMapDtoList(PaymentMethodMapQuery query);

    /**
     * 查询付息方式映射表列表（不分页）
     *
     * @param query 查询条件
     * @return 付息方式映射表集合
     */
    List<PaymentMethodMapDTO> selectPaymentMethodMapDtoListWithoutPage(PaymentMethodMapQuery query);

    /**
     * 根据主键查询付息方式映射表
     *
     * @param id 主键
     * @return 付息方式映射表
     */
    PaymentMethodMapDTO selectPaymentMethodMapDtoById(Long id);

    /**
     * 新增付息方式映射表
     *
     * @param dto 付息方式映射表
     * @return 结果
     */
    int insertPaymentMethodMapDto(PaymentMethodMapDTO dto);

    /**
     * 修改付息方式映射表
     *
     * @param dto 付息方式映射表
     * @return 结果
     */
    int updatePaymentMethodMapDto(PaymentMethodMapDTO dto);

    /**
     * 批量删除付息方式映射表
     *
     * @param ids 需要删除的付息方式映射表主键集合
     * @return 结果
     */
    int deletePaymentMethodMapDtoByIds(Long[] ids);

    /**
     * 删除付息方式映射表信息
     *
     * @param id 付息方式映射表主键
     * @return 结果
     */
    int deletePaymentMethodMapDtoById(Long id);

    /**
     * 导入付息方式映射表数据
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    String importPaymentMethodMap(MultipartFile file, boolean updateSupport);

    /**
     * 导出付息方式映射表模板
     *
     * @param response HTTP响应对象
     */
    void importTemplatePaymentMethodMap(HttpServletResponse response);
}
