package com.xl.alm.app.service;

import com.xl.alm.app.dto.BpCashFlowDTO;
import com.xl.alm.app.dto.BpCashFlowImportDTO;
import com.xl.alm.app.query.BpCashFlowQuery;

import java.util.List;

/**
 * BP现金流量表Service接口
 *
 * <AUTHOR> Assistant
 */
public interface BpCashFlowService {

    /**
     * 查询BP现金流量表列表
     *
     * @param query 查询条件
     * @return BP现金流量表列表
     */
    List<BpCashFlowDTO> selectBpCashFlowDtoList(BpCashFlowQuery query);

    /**
     * 根据主键查询BP现金流量表
     *
     * @param id 主键
     * @return BP现金流量表
     */
    BpCashFlowDTO selectBpCashFlowDtoById(Long id);

    /**
     * 新增BP现金流量表
     *
     * @param dto BP现金流量表
     * @return 结果
     */
    int insertBpCashFlowDto(BpCashFlowDTO dto);

    /**
     * 修改BP现金流量表
     *
     * @param dto BP现金流量表
     * @return 结果
     */
    int updateBpCashFlowDto(BpCashFlowDTO dto);

    /**
     * 批量删除BP现金流量表
     *
     * @param ids 需要删除的BP现金流量表主键集合
     * @return 结果
     */
    int deleteBpCashFlowDtoByIds(Long[] ids);

    /**
     * 删除BP现金流量表信息
     *
     * @param id BP现金流量表主键
     * @return 结果
     */
    int deleteBpCashFlowDtoById(Long id);

    /**
     * 根据条件查询BP现金流量表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param businessType 业务类型
     * @param actuarialCode 精算代码
     * @param variableList 变量列表
     * @return BP现金流量表
     */
    BpCashFlowDTO selectBpCashFlowDtoByCondition(String accountingPeriod, String scenarioName, 
                                                 String businessType, String actuarialCode, String variableList);

    /**
     * 导入BP现金流量表数据
     *
     * @param dtoList BP现金流量表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importBpCashFlowData(List<BpCashFlowDTO> dtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导入BP现金流量表数据（横向Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importBpCashFlowDataFromExcel(java.io.InputStream inputStream, Boolean isUpdateSupport, String operName);

    /**
     * 导出BP现金流量表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 导出BP现金流量表（水平格式）
     *
     * @param response HTTP响应
     * @param query 查询条件
     */
    void exportBpCashFlowHorizontal(javax.servlet.http.HttpServletResponse response, BpCashFlowQuery query) throws Exception;
}
