package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AssetRiskItemMappingDTO;
import com.xl.alm.app.query.AssetRiskItemMappingQuery;
import com.xl.alm.app.service.AssetRiskItemMappingService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 保险资产风险项目映射表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/acm/asset/risk/item/mapping")
public class AssetRiskItemMappingController extends BaseController {
    @Autowired
    private AssetRiskItemMappingService assetRiskItemMappingService;

    /**
     * 查询保险资产风险项目映射表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetRiskItemMappingQuery query) {
        startPage();
        List<AssetRiskItemMappingDTO> list = assetRiskItemMappingService.selectAssetRiskItemMappingDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出保险资产风险项目映射表列表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:export')")
    @Log(title = "保险资产风险项目映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetRiskItemMappingQuery query) {
        List<AssetRiskItemMappingDTO> list = assetRiskItemMappingService.selectAssetRiskItemMappingDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        ExcelUtil<AssetRiskItemMappingDTO> util = new ExcelUtil<AssetRiskItemMappingDTO>(AssetRiskItemMappingDTO.class);
        util.exportExcel(list, "保险资产风险项目映射表数据", response);
    }

    /**
     * 获取保险资产风险项目映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetRiskItemMappingService.selectAssetRiskItemMappingDtoById(id));
    }

    /**
     * 新增保险资产风险项目映射表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:add')")
    @Log(title = "保险资产风险项目映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AssetRiskItemMappingDTO assetRiskItemMappingDto) {
        return toAjax(assetRiskItemMappingService.addAssetRiskItemMappingDto(assetRiskItemMappingDto));
    }

    /**
     * 修改保险资产风险项目映射表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:edit')")
    @Log(title = "保险资产风险项目映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AssetRiskItemMappingDTO assetRiskItemMappingDto) {
        return toAjax(assetRiskItemMappingService.updateAssetRiskItemMappingDto(assetRiskItemMappingDto));
    }

    /**
     * 删除保险资产风险项目映射表
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:remove')")
    @Log(title = "保险资产风险项目映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetRiskItemMappingService.deleteAssetRiskItemMappingDtoByIds(ids));
    }

    /**
     * 获取保险资产风险项目映射表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetRiskItemMappingDTO> util = new ExcelUtil<AssetRiskItemMappingDTO>(AssetRiskItemMappingDTO.class);
        util.exportTemplateExcel(response, "保险资产风险项目映射表");
    }

    /**
     * 导入保险资产风险项目映射表数据
     */
    @PreAuthorize("@ss.hasPermi('acm:asset:risk:item:mapping:import')")
    @Log(title = "保险资产风险项目映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetRiskItemMappingDTO> util = new ExcelUtil<AssetRiskItemMappingDTO>(AssetRiskItemMappingDTO.class);
        List<AssetRiskItemMappingDTO> assetRiskItemMappingList = util.importExcel(file.getInputStream());
        // 转换字典标签为值用于导入
        convertDictLabelToValue(assetRiskItemMappingList);
        String operName = getUsername();
        String message = assetRiskItemMappingService.importAssetRiskItemMappingDto(assetRiskItemMappingList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 保险资产风险项目映射表数据列表
     */
    private void convertDictValueToLabel(List<AssetRiskItemMappingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetRiskItemMappingDTO dto : list) {
            // 转换项目名称字典值
            if (dto.getItemName() != null) {
                dto.setItemName(DictConvertUtil.convertValueToLabel(dto.getItemName(), "acm_risk_item_name"));
            }
            // 转换五级分类资产统计标识字典值
            if (dto.getFiveLevelStatisticsFlag() != null) {
                dto.setFiveLevelStatisticsFlag(DictConvertUtil.convertValueToLabel(dto.getFiveLevelStatisticsFlag(), "ast_five_level_statistics_flag"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 保险资产风险项目映射表数据列表
     */
    private void convertDictLabelToValue(List<AssetRiskItemMappingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetRiskItemMappingDTO dto : list) {
            // 转换项目名称字典标签
            if (dto.getItemName() != null) {
                dto.setItemName(DictConvertUtil.convertLabelToValue(dto.getItemName(), "acm_risk_item_name"));
            }
            // 转换五级分类资产统计标识字典标签
            if (dto.getFiveLevelStatisticsFlag() != null) {
                dto.setFiveLevelStatisticsFlag(DictConvertUtil.convertLabelToValue(dto.getFiveLevelStatisticsFlag(), "ast_five_level_statistics_flag"));
            }
        }
    }
}
