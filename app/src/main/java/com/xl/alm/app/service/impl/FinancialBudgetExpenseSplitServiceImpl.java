package com.xl.alm.app.service.impl;

import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.FinancialBudgetExpenseSplitDTO;
import com.xl.alm.app.dto.FinancialBudgetExpenseSplitImportDTO;
import com.xl.alm.app.entity.FinancialBudgetExpenseSplitEntity;
import com.xl.alm.app.mapper.FinancialBudgetExpenseSplitMapper;
import com.xl.alm.app.query.FinancialBudgetExpenseSplitQuery;
import com.xl.alm.app.service.FinancialBudgetExpenseSplitService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务预算费用拆分表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class FinancialBudgetExpenseSplitServiceImpl implements FinancialBudgetExpenseSplitService {

    @Autowired
    private FinancialBudgetExpenseSplitMapper financialBudgetExpenseSplitMapper;

    /**
     * 查询财务预算费用拆分表列表
     *
     * @param financialBudgetExpenseSplitQuery 财务预算费用拆分表查询条件
     * @return 财务预算费用拆分表列表
     */
    @Override
    public List<FinancialBudgetExpenseSplitDTO> selectFinancialBudgetExpenseSplitDtoList(FinancialBudgetExpenseSplitQuery financialBudgetExpenseSplitQuery) {
        List<FinancialBudgetExpenseSplitEntity> entityList = financialBudgetExpenseSplitMapper.selectFinancialBudgetExpenseSplitEntityList(financialBudgetExpenseSplitQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, FinancialBudgetExpenseSplitDTO.class);
    }

    /**
     * 根据ID查询财务预算费用拆分表
     *
     * @param id 主键ID
     * @return 财务预算费用拆分表
     */
    @Override
    public FinancialBudgetExpenseSplitDTO selectFinancialBudgetExpenseSplitDtoById(Long id) {
        FinancialBudgetExpenseSplitEntity entity = financialBudgetExpenseSplitMapper.selectFinancialBudgetExpenseSplitEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FinancialBudgetExpenseSplitDTO.class);
    }

    /**
     * 新增财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDto 财务预算费用拆分表
     * @return 影响行数
     */
    @Override
    public int insertFinancialBudgetExpenseSplitDto(FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto) {
        FinancialBudgetExpenseSplitEntity entity = EntityDtoConvertUtil.convertToEntity(financialBudgetExpenseSplitDto, FinancialBudgetExpenseSplitEntity.class);
        return financialBudgetExpenseSplitMapper.insertFinancialBudgetExpenseSplitEntity(entity);
    }

    /**
     * 修改财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDto 财务预算费用拆分表
     * @return 影响行数
     */
    @Override
    public int updateFinancialBudgetExpenseSplitDto(FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto) {
        FinancialBudgetExpenseSplitEntity entity = EntityDtoConvertUtil.convertToEntity(financialBudgetExpenseSplitDto, FinancialBudgetExpenseSplitEntity.class);
        return financialBudgetExpenseSplitMapper.updateFinancialBudgetExpenseSplitEntity(entity);
    }

    /**
     * 删除财务预算费用拆分表
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseSplitDtoById(Long id) {
        return financialBudgetExpenseSplitMapper.deleteFinancialBudgetExpenseSplitEntityById(id);
    }

    /**
     * 批量删除财务预算费用拆分表
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseSplitDtoByIds(Long[] ids) {
        return financialBudgetExpenseSplitMapper.deleteFinancialBudgetExpenseSplitEntityByIds(ids);
    }

    /**
     * 批量新增财务预算费用拆分表
     *
     * @param financialBudgetExpenseSplitDtoList 财务预算费用拆分表列表
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertFinancialBudgetExpenseSplitDto(List<FinancialBudgetExpenseSplitDTO> financialBudgetExpenseSplitDtoList) {
        if (financialBudgetExpenseSplitDtoList == null || financialBudgetExpenseSplitDtoList.isEmpty()) {
            return 0;
        }
        // 转换字典标签为字典值
        for (FinancialBudgetExpenseSplitDTO dto : financialBudgetExpenseSplitDtoList) {
            convertDictLabelToValue(dto);
        }
        List<FinancialBudgetExpenseSplitEntity> entityList = EntityDtoConvertUtil.convertToEntityList(financialBudgetExpenseSplitDtoList, FinancialBudgetExpenseSplitEntity.class);
        return financialBudgetExpenseSplitMapper.batchInsertFinancialBudgetExpenseSplitEntity(entityList);
    }

    /**
     * 根据账期删除财务预算费用拆分表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseSplitDtoByPeriod(String accountingPeriod) {
        return financialBudgetExpenseSplitMapper.deleteFinancialBudgetExpenseSplitEntityByPeriod(accountingPeriod);
    }

    /**
     * 根据条件查询财务预算费用拆分表（用于验证唯一性）
     *
     * @param accountingPeriod 账期
     * @param scenarioName 情景名称
     * @param financialExpenseType 财务费用类型
     * @param businessType 业务类型
     * @param designType 设计类型
     * @return 财务预算费用拆分表
     */
    @Override
    public FinancialBudgetExpenseSplitDTO selectFinancialBudgetExpenseSplitDtoByCondition(String accountingPeriod, String scenarioName, String financialExpenseType, String businessType, String designType) {
        FinancialBudgetExpenseSplitEntity entity = financialBudgetExpenseSplitMapper.selectFinancialBudgetExpenseSplitEntityByCondition(accountingPeriod, scenarioName, financialExpenseType, businessType, designType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FinancialBudgetExpenseSplitDTO.class);
    }

    /**
     * 导入财务预算费用拆分表数据
     *
     * @param financialBudgetExpenseSplitDtoList 财务预算费用拆分表列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFinancialBudgetExpenseSplitData(List<FinancialBudgetExpenseSplitDTO> financialBudgetExpenseSplitDtoList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(financialBudgetExpenseSplitDtoList) || financialBudgetExpenseSplitDtoList.size() == 0) {
            throw new ServiceException("导入财务预算费用拆分数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto : financialBudgetExpenseSplitDtoList) {
            try {
                // 转换字典标签为字典值
                convertDictLabelToValue(financialBudgetExpenseSplitDto);
                
                // 验证是否存在这个财务预算费用拆分
                FinancialBudgetExpenseSplitDTO existDto = this.selectFinancialBudgetExpenseSplitDtoByCondition(
                        financialBudgetExpenseSplitDto.getAccountingPeriod(),
                        financialBudgetExpenseSplitDto.getScenarioName(),
                        financialBudgetExpenseSplitDto.getFinancialExpenseType(),
                        financialBudgetExpenseSplitDto.getBusinessType(),
                        financialBudgetExpenseSplitDto.getDesignType());
                
                if (StringUtils.isNull(existDto)) {
                    financialBudgetExpenseSplitDto.setCreateBy(operName);
                    this.insertFinancialBudgetExpenseSplitDto(financialBudgetExpenseSplitDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + financialBudgetExpenseSplitDto.getAccountingPeriod() + " 的数据导入成功");
                } else if (isUpdateSupport) {
                    financialBudgetExpenseSplitDto.setUpdateBy(operName);
                    financialBudgetExpenseSplitDto.setId(existDto.getId());
                    this.updateFinancialBudgetExpenseSplitDto(financialBudgetExpenseSplitDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + financialBudgetExpenseSplitDto.getAccountingPeriod() + " 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + financialBudgetExpenseSplitDto.getAccountingPeriod() + " 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + financialBudgetExpenseSplitDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 转换字典标签为字典值
     *
     * @param financialBudgetExpenseSplitDto 财务预算费用拆分DTO
     */
    private void convertDictLabelToValue(FinancialBudgetExpenseSplitDTO financialBudgetExpenseSplitDto) {
        // 转换情景名称
        if (StringUtils.isNotEmpty(financialBudgetExpenseSplitDto.getScenarioName())) {
            String convertedScenarioName = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseSplitDto.getScenarioName(), 
                    "cft_scenario_name"
            );
            financialBudgetExpenseSplitDto.setScenarioName(convertedScenarioName);
        }

        // 转换财务费用类型
        if (StringUtils.isNotEmpty(financialBudgetExpenseSplitDto.getFinancialExpenseType())) {
            String convertedFinancialExpenseType = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseSplitDto.getFinancialExpenseType(), 
                    "cft_financial_expense_type"
            );
            financialBudgetExpenseSplitDto.setFinancialExpenseType(convertedFinancialExpenseType);
        }

        // 转换业务类型
        if (StringUtils.isNotEmpty(financialBudgetExpenseSplitDto.getBusinessType())) {
            String convertedBusinessType = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseSplitDto.getBusinessType(), 
                    "cost_business_type"
            );
            financialBudgetExpenseSplitDto.setBusinessType(convertedBusinessType);
        }

        // 转换设计类型
        if (StringUtils.isNotEmpty(financialBudgetExpenseSplitDto.getDesignType())) {
            String convertedDesignType = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseSplitDto.getDesignType(), 
                    "cost_design_type"
            );
            financialBudgetExpenseSplitDto.setDesignType(convertedDesignType);
        }
    }

    /**
     * 导入财务预算费用拆分表数据（Excel格式）
     *
     * @param inputStream Excel文件输入流
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importFinancialBudgetExpenseSplitDataFromExcel(InputStream inputStream, Boolean isUpdateSupport, String operName) {
        try {
            // 解析Excel文件
            List<FinancialBudgetExpenseSplitImportDTO> importDtoList = parseExcelFile(inputStream);

            if (importDtoList == null || importDtoList.isEmpty()) {
                throw new RuntimeException("导入数据不能为空");
            }

            log.info("开始批量导入财务预算费用拆分数据，总数量: {}", importDtoList.size());

            // 转换为标准DTO并导入
            return importFinancialBudgetExpenseSplitDataBatch(importDtoList, isUpdateSupport, operName);

        } catch (Exception e) {
            log.error("导入财务预算费用拆分数据失败", e);
            throw new RuntimeException("导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出财务预算费用拆分表模板
     */
    @Override
    public void exportTemplate(HttpServletResponse response) throws Exception {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("财务预算费用拆分表");

        // 创建表头
        Row headerRow = sheet.createRow(0);

        // 基础列
        String[] baseColumns = {"账期", "情景名称", "财务费用类型", "业务类型", "设计类型"};
        for (int i = 0; i < baseColumns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(baseColumns[i]);
        }

        // 生成36个月的日期列（与UC0004、UC0005保持一致）
        List<String> dateColumns = generateDateSequence("");
        for (int i = 0; i < dateColumns.size(); i++) {
            Cell cell = headerRow.createCell(baseColumns.length + i);
            cell.setCellValue(dateColumns.get(i));
        }

        // 设置列宽
        for (int i = 0; i < baseColumns.length + dateColumns.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("财务预算费用拆分表导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    /**
     * 导出财务预算费用拆分表
     */
    @Override
    public void exportFinancialBudgetExpenseSplitHorizontal(HttpServletResponse response, FinancialBudgetExpenseSplitQuery query) throws Exception {
        // 查询数据
        List<FinancialBudgetExpenseSplitDTO> dataList = selectFinancialBudgetExpenseSplitDtoList(query);

        if (dataList.isEmpty()) {
            throw new RuntimeException("没有数据可导出");
        }

        // 转换字典值为中文标签
        convertDictValueToLabelForExport(dataList);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("财务预算费用拆分表");

        // 生成36个月的日期列表（与UC0004、UC0005保持一致）
        List<String> dateColumns = generateDateSequence(dataList.get(0).getAccountingPeriod());

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] baseHeaders = {"账期", "情景名称", "财务费用类型", "业务类型", "设计类型"};

        // 设置基础列头
        for (int i = 0; i < baseHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(baseHeaders[i]);
        }

        // 设置日期列头
        for (int i = 0; i < dateColumns.size(); i++) {
            Cell cell = headerRow.createCell(baseHeaders.length + i);
            cell.setCellValue(dateColumns.get(i));
        }

        // 填充数据行
        for (int rowIndex = 0; rowIndex < dataList.size(); rowIndex++) {
            FinancialBudgetExpenseSplitDTO dto = dataList.get(rowIndex);
            Row dataRow = sheet.createRow(rowIndex + 1);

            // 基础字段
            dataRow.createCell(0).setCellValue(dto.getAccountingPeriod() != null ? dto.getAccountingPeriod() : "");
            dataRow.createCell(1).setCellValue(dto.getScenarioName() != null ? dto.getScenarioName() : "");
            dataRow.createCell(2).setCellValue(dto.getFinancialExpenseType() != null ? dto.getFinancialExpenseType() : "");
            dataRow.createCell(3).setCellValue(dto.getBusinessType() != null ? dto.getBusinessType() : "");
            dataRow.createCell(4).setCellValue(dto.getDesignType() != null ? dto.getDesignType() : "");

            // 现金流数据
            Map<String, Object> cashFlowMap = parseCashFlowValueSet(dto.getCashFlowValueSet());
            for (int i = 0; i < dateColumns.size(); i++) {
                String dateKey = dateColumns.get(i);
                String value = getCashFlowValueForDate(cashFlowMap, dateKey);

                Cell cell = dataRow.createCell(baseHeaders.length + i);
                if ("-".equals(value) || "0".equals(value)) {
                    cell.setCellValue("-");
                } else {
                    try {
                        // 尝试转换为数字并格式化
                        double numValue = Double.parseDouble(value.replace(",", ""));
                        cell.setCellValue(String.format("%,.2f", numValue));
                    } catch (NumberFormatException e) {
                        cell.setCellValue(value);
                    }
                }
            }
        }

        // 设置列宽
        for (int i = 0; i < baseHeaders.length + dateColumns.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("财务预算费用拆分表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 输出到响应流
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

    // 参考TB0004和TB0005的实现，添加相同的辅助方法
    private List<FinancialBudgetExpenseSplitImportDTO> parseExcelFile(InputStream inputStream) throws Exception {
        List<FinancialBudgetExpenseSplitImportDTO> result = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet.getPhysicalNumberOfRows() <= 1) {
            throw new RuntimeException("Excel文件没有数据行");
        }
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new RuntimeException("Excel文件缺少表头行");
        }
        Map<String, Integer> columnIndexMap = new HashMap<>();
        List<String> dateColumns = new ArrayList<>();
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String columnName = getCellValueAsString(cell);
                columnIndexMap.put(columnName, i);
                if (isDateColumn(columnName)) {
                    dateColumns.add(columnName);
                }
            }
        }
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            FinancialBudgetExpenseSplitImportDTO dto = new FinancialBudgetExpenseSplitImportDTO();
            Map<String, String> cashFlowDataMap = new HashMap<>();
            dto.setAccountingPeriod(getCellValue(row, columnIndexMap.get("账期")));
            dto.setScenarioName(getCellValue(row, columnIndexMap.get("情景名称")));
            dto.setFinancialExpenseType(getCellValue(row, columnIndexMap.get("财务费用类型")));
            dto.setBusinessType(getCellValue(row, columnIndexMap.get("业务类型")));
            dto.setDesignType(getCellValue(row, columnIndexMap.get("设计类型")));
            for (String dateColumn : dateColumns) {
                Integer columnIndex = columnIndexMap.get(dateColumn);
                if (columnIndex != null) {
                    String value = getCellValue(row, columnIndex);
                    if (StringUtils.isNotEmpty(value) && !"-".equals(value.trim())) {
                        cashFlowDataMap.put(dateColumn, value);
                    }
                }
            }
            dto.setCashFlowDataMap(cashFlowDataMap);
            result.add(dto);
        }
        workbook.close();
        return result;
    }

    private String importFinancialBudgetExpenseSplitDataBatch(List<FinancialBudgetExpenseSplitImportDTO> importDtoList, Boolean isUpdateSupport, String operName) {
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (FinancialBudgetExpenseSplitImportDTO importDto : importDtoList) {
            try {
                FinancialBudgetExpenseSplitDTO dto = convertImportDtoToDto(importDto);
                dto.setCreateBy(operName);
                dto.setUpdateBy(operName);
                convertDictLabelToValue(dto);
                FinancialBudgetExpenseSplitDTO existDto = this.selectFinancialBudgetExpenseSplitDtoByCondition(
                        dto.getAccountingPeriod(), dto.getScenarioName(), dto.getFinancialExpenseType(),
                        dto.getBusinessType(), dto.getDesignType());
                if (existDto == null) {
                    this.insertFinancialBudgetExpenseSplitDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据导入成功");
                } else if (isUpdateSupport) {
                    dto.setId(existDto.getId());
                    this.updateFinancialBudgetExpenseSplitDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期 ").append(dto.getAccountingPeriod()).append(" 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + importDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    private FinancialBudgetExpenseSplitDTO convertImportDtoToDto(FinancialBudgetExpenseSplitImportDTO importDto) throws Exception {
        FinancialBudgetExpenseSplitDTO dto = new FinancialBudgetExpenseSplitDTO();
        dto.setAccountingPeriod(importDto.getAccountingPeriod());
        dto.setScenarioName(importDto.getScenarioName());
        dto.setFinancialExpenseType(importDto.getFinancialExpenseType());
        dto.setBusinessType(importDto.getBusinessType());
        dto.setDesignType(importDto.getDesignType());
        String cashFlowValueSet = convertCashFlowDataMapToJson(importDto.getCashFlowDataMap(), importDto.getAccountingPeriod());
        dto.setCashFlowValueSet(cashFlowValueSet);
        return dto;
    }

    private String convertCashFlowDataMapToJson(Map<String, String> cashFlowDataMap, String accountingPeriod) throws Exception {
        if (cashFlowDataMap == null || cashFlowDataMap.isEmpty()) {
            return "{}";
        }
        Map<String, Map<String, Object>> cashFlowMap = new HashMap<>();
        int index = 0;
        // 修改为使用月度日期序列，与UC0004、UC0005保持一致
        List<String> correctDates = generateDateSequence(accountingPeriod);
        for (String date : correctDates) {
            String value = cashFlowDataMap.get(date);
            if (StringUtils.isNotEmpty(value) && !"-".equals(value.trim())) {
                String cleanValue = cleanNumericValue(value);
                Map<String, Object> valueMap = new HashMap<>();
                valueMap.put("日期", date);
                valueMap.put("值", cleanValue);
                cashFlowMap.put(String.valueOf(index), valueMap);
                index++;
            }
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(cashFlowMap);
    }

    /**
     * 生成日期序列（36个月）- 与UC0004、UC0005保持一致
     */
    private List<String> generateDateSequence(String accountingPeriod) {
        List<String> dates = new ArrayList<>();

        LocalDate startDate;
        if (StringUtils.isNotEmpty(accountingPeriod) && accountingPeriod.length() == 6) {
            // 如果账期有值，从账期的下一个月开始（与UC0005保持一致）
            try {
                int year = Integer.parseInt(accountingPeriod.substring(0, 4));
                int month = Integer.parseInt(accountingPeriod.substring(4, 6));
                startDate = LocalDate.of(year, month, 1).plusMonths(1);
            } catch (Exception e) {
                log.warn("账期格式错误，使用当前时间: " + accountingPeriod, e);
                startDate = LocalDate.now().withDayOfMonth(1);
            }
        } else {
            // 如果账期为空，从当前时间开始
            startDate = LocalDate.now().withDayOfMonth(1);
        }

        // 生成36个月的日期序列
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
        for (int i = 0; i < 36; i++) {
            LocalDate currentDate = startDate.plusMonths(i);
            // 获取该月的最后一天
            LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
            dates.add(lastDayOfMonth.format(formatter));
        }

        return dates;
    }

    private List<String> generateQuarterSequence(String accountingPeriod) {
        List<String> quarters = new ArrayList<>();
        if (StringUtils.isEmpty(accountingPeriod)) {
            LocalDate now = LocalDate.now();
            int year = now.getYear();
            int quarter = (now.getMonthValue() - 1) / 3 + 1;
            for (int i = 0; i < 12; i++) {
                quarters.add(year + "Q" + quarter);
                quarter++;
                if (quarter > 4) {
                    quarter = 1;
                    year++;
                }
            }
        } else {
            try {
                int year = Integer.parseInt(accountingPeriod.substring(0, 4));
                int month = Integer.parseInt(accountingPeriod.substring(4, 6));
                int startQuarter = (month - 1) / 3 + 1;
                startQuarter++;
                if (startQuarter > 4) {
                    startQuarter = 1;
                    year++;
                }
                for (int i = 0; i < 12; i++) {
                    quarters.add(year + "Q" + startQuarter);
                    startQuarter++;
                    if (startQuarter > 4) {
                        startQuarter = 1;
                        year++;
                    }
                }
            } catch (Exception e) {
                log.warn("解析账期失败，使用默认季度序列: {}", accountingPeriod);
                return generateQuarterSequence("");
            }
        }
        return quarters;
    }

    /**
     * 判断是否为日期列 - 与UC0004、UC0005保持一致
     */
    private boolean isDateColumn(String columnName) {
        if (StringUtils.isEmpty(columnName)) {
            return false;
        }
        // 匹配格式：yyyy/M/d 或 yyyy/MM/dd
        return columnName.matches("\\d{4}/\\d{1,2}/\\d{1,2}");
    }

    private boolean isQuarterColumn(String columnName) {
        if (StringUtils.isEmpty(columnName)) {
            return false;
        }
        return columnName.matches("\\d{4}Q[1-4]");
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(cell.getLocalDateTimeCellValue().toLocalDate());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private String getCellValue(Row row, Integer columnIndex) {
        if (row == null || columnIndex == null) {
            return "";
        }
        Cell cell = row.getCell(columnIndex);
        return getCellValueAsString(cell);
    }

    private String cleanNumericValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return "0";
        }
        String cleaned = value.trim()
                .replace(",", "")
                .replace("，", "")
                .replace(" ", "")
                .replace("￥", "")
                .replace("$", "");
        try {
            Double.parseDouble(cleaned);
            return cleaned;
        } catch (NumberFormatException e) {
            log.warn("无效的数值格式: {}", value);
            return "0";
        }
    }

    private void convertDictValueToLabelForExport(List<FinancialBudgetExpenseSplitDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        for (FinancialBudgetExpenseSplitDTO dto : list) {
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(dto.getScenarioName(), "cft_scenario_name");
                dto.setScenarioName(scenarioLabel);
            }
            if (dto.getFinancialExpenseType() != null) {
                String expenseTypeLabel = DictConvertUtil.convertValueToLabel(dto.getFinancialExpenseType(), "cft_financial_expense_type");
                dto.setFinancialExpenseType(expenseTypeLabel);
            }
            if (dto.getBusinessType() != null) {
                String businessTypeLabel = DictConvertUtil.convertValueToLabel(dto.getBusinessType(), "cost_business_type");
                dto.setBusinessType(businessTypeLabel);
            }
            if (dto.getDesignType() != null) {
                String designTypeLabel = DictConvertUtil.convertValueToLabel(dto.getDesignType(), "cost_design_type");
                dto.setDesignType(designTypeLabel);
            }
        }
    }

    private Map<String, Object> parseCashFlowValueSet(String cashFlowValueSet) {
        if (StringUtils.isEmpty(cashFlowValueSet)) {
            return new HashMap<>();
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(cashFlowValueSet, Map.class);
        } catch (Exception e) {
            log.warn("解析现金流值集失败: {}", cashFlowValueSet);
            return new HashMap<>();
        }
    }

    /**
     * 获取指定日期的现金流值 - 与UC0004、UC0005保持一致
     */
    private String getCashFlowValueForDate(Map<String, Object> cashFlowMap, String targetDate) {
        if (cashFlowMap == null || cashFlowMap.isEmpty()) {
            return "-";
        }

        // 遍历现金流数据，查找匹配的日期
        for (Map.Entry<String, Object> entry : cashFlowMap.entrySet()) {
            Object valueObj = entry.getValue();
            if (valueObj instanceof Map) {
                Map<String, Object> valueMap = (Map<String, Object>) valueObj;
                String date = String.valueOf(valueMap.get("日期"));

                // 支持多种日期格式匹配
                if (targetDate.equals(date) || isDateMatch(date, targetDate)) {
                    Object value = valueMap.get("值");
                    if (value != null) {
                        return String.valueOf(value);
                    }
                }
            }
        }
        return "-";
    }

    /**
     * 判断日期是否匹配 - 与UC0004、UC0005保持一致
     */
    private boolean isDateMatch(String dateInData, String targetDate) {
        if (StringUtils.isEmpty(dateInData) || StringUtils.isEmpty(targetDate)) {
            return false;
        }

        // 直接比较日期字符串
        if (dateInData.equals(targetDate)) {
            return true;
        }

        // 尝试标准化日期格式进行比较
        try {
            String normalizedDataDate = normalizeDateString(dateInData);
            String normalizedTargetDate = normalizeDateString(targetDate);
            return normalizedDataDate.equals(normalizedTargetDate);
        } catch (Exception e) {
            log.warn("日期格式比较失败: dateInData={}, targetDate={}", dateInData, targetDate);
            return false;
        }
    }

    /**
     * 标准化日期字符串格式 - 与UC0004、UC0005保持一致
     */
    private String normalizeDateString(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return "";
        }

        // 处理格式：yyyy/M/d 或 yyyy/MM/dd
        if (dateStr.matches("\\d{4}/\\d{1,2}/\\d{1,2}")) {
            try {
                String[] parts = dateStr.split("/");
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                return String.format("%d/%d/%d", year, month, day);
            } catch (Exception e) {
                log.warn("日期格式标准化失败: {}", dateStr);
                return dateStr;
            }
        }

        return dateStr;
    }

    private boolean isQuarterMatch(String dateInData, String targetQuarter) {
        if (StringUtils.isEmpty(dateInData) || StringUtils.isEmpty(targetQuarter)) {
            return false;
        }
        if (dateInData.matches("\\d{4}Q[1-4]")) {
            return dateInData.equals(targetQuarter);
        }
        if (dateInData.matches("\\d{4}/\\d{1,2}/\\d{1,2}")) {
            try {
                String[] parts = dateInData.split("/");
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int quarter = (month - 1) / 3 + 1;
                String convertedQuarter = year + "Q" + quarter;
                return convertedQuarter.equals(targetQuarter);
            } catch (Exception e) {
                log.warn("日期格式转换失败: {}", dateInData);
                return false;
            }
        }
        return false;
    }
}
