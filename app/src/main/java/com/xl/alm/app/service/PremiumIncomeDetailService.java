package com.xl.alm.app.service;

import com.xl.alm.app.dto.PremiumIncomeDetailDTO;
import com.xl.alm.app.query.PremiumIncomeDetailQuery;

import java.util.List;

/**
 * 保费收入明细Service接口
 *
 * <AUTHOR> Assistant
 */
public interface PremiumIncomeDetailService {

    /**
     * 查询保费收入明细列表
     *
     * @param premiumIncomeDetailQuery 保费收入明细查询条件
     * @return 保费收入明细列表
     */
    List<PremiumIncomeDetailDTO> selectPremiumIncomeDetailDtoList(PremiumIncomeDetailQuery premiumIncomeDetailQuery);

    /**
     * 查询保费收入明细
     *
     * @param id 保费收入明细主键
     * @return 保费收入明细
     */
    PremiumIncomeDetailDTO selectPremiumIncomeDetailDtoById(Long id);

    /**
     * 新增保费收入明细
     *
     * @param premiumIncomeDetailDTO 保费收入明细
     * @return 结果
     */
    int insertPremiumIncomeDetailDto(PremiumIncomeDetailDTO premiumIncomeDetailDTO);

    /**
     * 批量新增保费收入明细
     *
     * @param premiumIncomeDetailDtoList 保费收入明细列表
     * @return 结果
     */
    int batchInsertPremiumIncomeDetailDto(List<PremiumIncomeDetailDTO> premiumIncomeDetailDtoList);

    /**
     * 修改保费收入明细
     *
     * @param premiumIncomeDetailDTO 保费收入明细
     * @return 结果
     */
    int updatePremiumIncomeDetailDto(PremiumIncomeDetailDTO premiumIncomeDetailDTO);

    /**
     * 批量删除保费收入明细
     *
     * @param ids 需要删除的保费收入明细主键集合
     * @return 结果
     */
    int deletePremiumIncomeDetailDtoByIds(Long[] ids);

    /**
     * 删除保费收入明细信息
     *
     * @param id 保费收入明细主键
     * @return 结果
     */
    int deletePremiumIncomeDetailDtoById(Long id);

    /**
     * 删除指定账期的保费收入明细数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deletePremiumIncomeDetailDtoByPeriod(String accountingPeriod);
}
