package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationDiscountCurveDTO;
import com.xl.alm.app.query.AdurKeyDurationDiscountCurveQuery;

import java.util.List;

/**
 * ADUR关键久期折现曲线表含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AdurKeyDurationDiscountCurveService {

    /**
     * 查询ADUR关键久期折现曲线表含价差列表
     *
     * @param adurKeyDurationDiscountCurveQuery ADUR关键久期折现曲线表含价差查询条件
     * @return ADUR关键久期折现曲线表含价差列表
     */
    List<AdurKeyDurationDiscountCurveDTO> selectAdurKeyDurationDiscountCurveDtoList(AdurKeyDurationDiscountCurveQuery adurKeyDurationDiscountCurveQuery);

    /**
     * 用id查询ADUR关键久期折现曲线表含价差
     *
     * @param id id
     * @return ADUR关键久期折现曲线表含价差
     */
    AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoById(Long id);

    /**
     * 根据条件查询ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现曲线表含价差
     */
    AdurKeyDurationDiscountCurveDTO selectAdurKeyDurationDiscountCurveDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection);

    /**
     * 新增ADUR关键久期折现曲线表含价差
     *
     * @param adurKeyDurationDiscountCurveDTO ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    int insertAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO adurKeyDurationDiscountCurveDTO);

    /**
     * 批量插入ADUR关键久期折现曲线表含价差数据
     *
     * @param adurKeyDurationDiscountCurveDtoList ADUR关键久期折现曲线表含价差列表
     * @return 影响行数
     */
    int batchInsertAdurKeyDurationDiscountCurveDto(List<AdurKeyDurationDiscountCurveDTO> adurKeyDurationDiscountCurveDtoList);

    /**
     * 更新ADUR关键久期折现曲线表含价差数据
     *
     * @param dto ADUR关键久期折现曲线表含价差
     * @return 结果
     */
    int updateAdurKeyDurationDiscountCurveDto(AdurKeyDurationDiscountCurveDTO dto);

    /**
     * 删除指定id的ADUR关键久期折现曲线表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAdurKeyDurationDiscountCurveDtoById(Long id);

    /**
     * 批量删除ADUR关键久期折现曲线表含价差
     *
     * @param ids 需要删除的ADUR关键久期折现曲线表含价差主键
     * @return 结果
     */
    int deleteAdurKeyDurationDiscountCurveDtoByIds(Long[] ids);



    /**
     * 根据账期删除ADUR关键久期折现曲线表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    int deleteAdurKeyDurationDiscountCurveDtoByAccountPeriod(String accountPeriod);
}
