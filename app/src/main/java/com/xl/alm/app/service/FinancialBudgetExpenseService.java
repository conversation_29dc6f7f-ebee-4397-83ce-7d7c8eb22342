package com.xl.alm.app.service;

import com.xl.alm.app.dto.FinancialBudgetExpenseDTO;
import com.xl.alm.app.query.FinancialBudgetExpenseQuery;

import java.util.List;

/**
 * 财务预算费用表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FinancialBudgetExpenseService {

    /**
     * 查询财务预算费用列表
     *
     * @param financialBudgetExpenseQuery 财务预算费用查询条件
     * @return 财务预算费用列表
     */
    List<FinancialBudgetExpenseDTO> selectFinancialBudgetExpenseDtoList(FinancialBudgetExpenseQuery financialBudgetExpenseQuery);

    /**
     * 根据ID查询财务预算费用
     *
     * @param id 主键ID
     * @return 财务预算费用
     */
    FinancialBudgetExpenseDTO selectFinancialBudgetExpenseDtoById(Long id);

    /**
     * 新增财务预算费用
     *
     * @param financialBudgetExpenseDto 财务预算费用
     * @return 影响行数
     */
    int insertFinancialBudgetExpenseDto(FinancialBudgetExpenseDTO financialBudgetExpenseDto);

    /**
     * 修改财务预算费用
     *
     * @param financialBudgetExpenseDto 财务预算费用
     * @return 影响行数
     */
    int updateFinancialBudgetExpenseDto(FinancialBudgetExpenseDTO financialBudgetExpenseDto);

    /**
     * 删除财务预算费用
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseDtoById(Long id);

    /**
     * 批量删除财务预算费用
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseDtoByIds(Long[] ids);

    /**
     * 批量新增财务预算费用
     *
     * @param financialBudgetExpenseDtoList 财务预算费用列表
     * @return 影响行数
     */
    int batchInsertFinancialBudgetExpenseDto(List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList);

    /**
     * 根据账期删除财务预算费用
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteFinancialBudgetExpenseDtoByPeriod(String accountingPeriod);

    /**
     * 导入财务预算费用数据
     *
     * @param financialBudgetExpenseDtoList 财务预算费用列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFinancialBudgetExpenseData(List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList, Boolean isUpdateSupport, String operName);
}
