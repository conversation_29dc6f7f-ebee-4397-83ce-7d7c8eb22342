package com.xl.alm.app.service;

import com.xl.alm.app.dto.ThreeYearBusinessPlanDTO;
import com.xl.alm.app.query.ThreeYearBusinessPlanQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 三年新业务规划Service接口
 *
 * <AUTHOR> Assistant
 */
public interface ThreeYearBusinessPlanService {

    /**
     * 查询三年新业务规划列表
     *
     * @param threeYearBusinessPlanQuery 三年新业务规划查询条件
     * @return 三年新业务规划列表
     */
    List<ThreeYearBusinessPlanDTO> selectThreeYearBusinessPlanList(ThreeYearBusinessPlanQuery threeYearBusinessPlanQuery);

    /**
     * 根据id查询三年新业务规划
     *
     * @param id 三年新业务规划主键
     * @return 三年新业务规划
     */
    ThreeYearBusinessPlanDTO selectThreeYearBusinessPlanById(Long id);

    /**
     * 新增三年新业务规划
     *
     * @param threeYearBusinessPlanDTO 三年新业务规划
     * @return 结果
     */
    int insertThreeYearBusinessPlan(ThreeYearBusinessPlanDTO threeYearBusinessPlanDTO);

    /**
     * 修改三年新业务规划
     *
     * @param threeYearBusinessPlanDTO 三年新业务规划
     * @return 结果
     */
    int updateThreeYearBusinessPlan(ThreeYearBusinessPlanDTO threeYearBusinessPlanDTO);

    /**
     * 批量删除三年新业务规划
     *
     * @param ids 需要删除的三年新业务规划主键集合
     * @return 结果
     */
    int deleteThreeYearBusinessPlanByIds(Long[] ids);

    /**
     * 删除三年新业务规划信息
     *
     * @param id 三年新业务规划主键
     * @return 结果
     */
    int deleteThreeYearBusinessPlanById(Long id);

    /**
     * 导出三年新业务规划列表
     *
     * @param response 响应对象
     * @param threeYearBusinessPlanQuery 查询条件
     */
    void exportThreeYearBusinessPlan(HttpServletResponse response, ThreeYearBusinessPlanQuery threeYearBusinessPlanQuery);

    /**
     * 导入三年新业务规划数据
     *
     * @param dtoList 三年新业务规划数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param username 操作用户
     * @return 结果
     */
    String importThreeYearBusinessPlan(List<ThreeYearBusinessPlanDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 下载三年新业务规划导入模板
     *
     * @param response 响应对象
     */
    void importTemplate(HttpServletResponse response);

    /**
     * 根据账期删除三年新业务规划
     *
     * @param accountingPeriod 账期
     * @return 结果
     */
    int deleteThreeYearBusinessPlanByPeriod(String accountingPeriod);
}
