package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurMonthlyDiscountFactorWithSpreadDTO;
import com.xl.alm.app.entity.AdurMonthlyDiscountFactorWithSpreadEntity;
import com.xl.alm.app.mapper.AdurMonthlyDiscountFactorWithSpreadMapper;
import com.xl.alm.app.query.AdurMonthlyDiscountFactorWithSpreadQuery;
import com.xl.alm.app.service.AdurMonthlyDiscountFactorWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR月度折现因子表含价差 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountFactorWithSpreadServiceImpl implements AdurMonthlyDiscountFactorWithSpreadService {

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadMapper adurMonthlyDiscountFactorWithSpreadMapper;

    /**
     * 查询ADUR月度折现因子表含价差列表
     *
     * @param adurMonthlyDiscountFactorWithSpreadQuery ADUR月度折现因子表含价差查询条件
     * @return ADUR月度折现因子表含价差列表
     */
    @Override
    public List<AdurMonthlyDiscountFactorWithSpreadDTO> selectAdurMonthlyDiscountFactorWithSpreadDtoList(AdurMonthlyDiscountFactorWithSpreadQuery adurMonthlyDiscountFactorWithSpreadQuery) {
        List<AdurMonthlyDiscountFactorWithSpreadEntity> entityList = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityList(adurMonthlyDiscountFactorWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 用id查询ADUR月度折现因子表含价差
     *
     * @param id id
     * @return ADUR月度折现因子表含价差
     */
    @Override
    public AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoById(Long id) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 根据账期、资产编号、久期类型和基点类型查询ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param durationType 久期类型
     * @param basisPointType 基点类型
     * @return ADUR月度折现因子表含价差
     */
    @Override
    public AdurMonthlyDiscountFactorWithSpreadDTO selectAdurMonthlyDiscountFactorWithSpreadDtoByCondition(String accountPeriod, String assetNumber, String durationType, String basisPointType) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = adurMonthlyDiscountFactorWithSpreadMapper.selectAdurMonthlyDiscountFactorWithSpreadEntityByCondition(accountPeriod, assetNumber, durationType, basisPointType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurMonthlyDiscountFactorWithSpreadDTO.class);
    }

    /**
     * 新增ADUR月度折现因子表含价差
     *
     * @param adurMonthlyDiscountFactorWithSpreadDTO ADUR月度折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO adurMonthlyDiscountFactorWithSpreadDTO) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurMonthlyDiscountFactorWithSpreadDTO, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.insertAdurMonthlyDiscountFactorWithSpreadEntity(entity);
    }

    /**
     * 批量插入ADUR月度折现因子表含价差数据
     *
     * @param adurMonthlyDiscountFactorWithSpreadDtoList ADUR月度折现因子表含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurMonthlyDiscountFactorWithSpreadDto(List<AdurMonthlyDiscountFactorWithSpreadDTO> adurMonthlyDiscountFactorWithSpreadDtoList) {
        if (adurMonthlyDiscountFactorWithSpreadDtoList == null || adurMonthlyDiscountFactorWithSpreadDtoList.isEmpty()) {
            return 0;
        }
        List<AdurMonthlyDiscountFactorWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurMonthlyDiscountFactorWithSpreadDtoList, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.batchInsertAdurMonthlyDiscountFactorWithSpreadEntity(entityList);
    }

    /**
     * 更新ADUR月度折现因子表含价差数据
     *
     * @param dto ADUR月度折现因子表含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurMonthlyDiscountFactorWithSpreadDto(AdurMonthlyDiscountFactorWithSpreadDTO dto) {
        AdurMonthlyDiscountFactorWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurMonthlyDiscountFactorWithSpreadEntity.class);
        return adurMonthlyDiscountFactorWithSpreadMapper.updateAdurMonthlyDiscountFactorWithSpreadEntity(entity);
    }

    /**
     * 删除指定id的ADUR月度折现因子表含价差数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoById(Long id) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityById(id);
    }

    /**
     * 批量删除ADUR月度折现因子表含价差
     *
     * @param ids 需要删除的ADUR月度折现因子表含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoByIds(Long[] ids) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityByIds(ids);
    }

    /**
     * 根据账期删除ADUR月度折现因子表含价差
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountFactorWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurMonthlyDiscountFactorWithSpreadMapper.deleteAdurMonthlyDiscountFactorWithSpreadEntityByAccountPeriod(accountPeriod);
    }


}
