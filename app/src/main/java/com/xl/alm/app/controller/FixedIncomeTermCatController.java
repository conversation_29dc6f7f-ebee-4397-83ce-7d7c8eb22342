package com.xl.alm.app.controller;

import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.FixedIncomeTermCatDTO;
import com.xl.alm.app.query.FixedIncomeTermCatQuery;
import com.xl.alm.app.service.FixedIncomeTermCatService;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 固收资产剩余期限资产分类表 Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/ast/fixed/income/term/cat")
public class FixedIncomeTermCatController extends BaseController {

    @Autowired
    private FixedIncomeTermCatService fixedIncomeTermCatService;

    /**
     * 查询固收资产剩余期限资产分类表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:list')")
    @GetMapping("/list")
    public TableDataInfo list(FixedIncomeTermCatQuery fixedIncomeTermCatQuery) {
        startPage();
        List<FixedIncomeTermCatDTO> list = fixedIncomeTermCatService.selectFixedIncomeTermCatDtoList(fixedIncomeTermCatQuery);
        return getDataTable(list);
    }

    /**
     * 导出固收资产剩余期限资产分类表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, FixedIncomeTermCatQuery fixedIncomeTermCatQuery) {
        List<FixedIncomeTermCatDTO> list = fixedIncomeTermCatService.selectFixedIncomeTermCatDtoList(fixedIncomeTermCatQuery);
        convertDictValueToLabel(list);
        com.xl.alm.app.util.ExcelUtil<FixedIncomeTermCatDTO> util = new com.xl.alm.app.util.ExcelUtil<>(FixedIncomeTermCatDTO.class);
        util.exportExcel(list, "固收资产剩余期限资产分类表数据", response);
    }

    /**
     * 根据主键获取固收资产剩余期限资产分类表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fixedIncomeTermCatService.selectFixedIncomeTermCatDtoById(id));
    }

    /**
     * 新增固收资产剩余期限资产分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:add')")
    @PostMapping
    public Result add(@Validated @RequestBody FixedIncomeTermCatDTO fixedIncomeTermCatDTO) {
        return toAjax(fixedIncomeTermCatService.insertFixedIncomeTermCatDto(fixedIncomeTermCatDTO));
    }

    /**
     * 修改固收资产剩余期限资产分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:edit')")
    @PutMapping
    public Result edit(@Validated @RequestBody FixedIncomeTermCatDTO fixedIncomeTermCatDTO) {
        return toAjax(fixedIncomeTermCatService.updateFixedIncomeTermCatDto(fixedIncomeTermCatDTO));
    }

    /**
     * 删除固收资产剩余期限资产分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:remove')")
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fixedIncomeTermCatService.deleteFixedIncomeTermCatDtoByIds(ids));
    }

    /**
     * 导入固收资产剩余期限资产分类表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:import')")
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        com.xl.alm.app.util.ExcelUtil<FixedIncomeTermCatDTO> util = new com.xl.alm.app.util.ExcelUtil<>(FixedIncomeTermCatDTO.class);
        List<FixedIncomeTermCatDTO> fixedIncomeTermCatList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(fixedIncomeTermCatList);
        String operName = getUsername();
        String message = fixedIncomeTermCatService.importFixedIncomeTermCatDto(fixedIncomeTermCatList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取固收资产剩余期限资产分类表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:fixed:income:term:cat:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        com.xl.alm.app.util.ExcelUtil<FixedIncomeTermCatDTO> util = new com.xl.alm.app.util.ExcelUtil<>(FixedIncomeTermCatDTO.class);
        util.exportTemplateExcel(response, "固收资产剩余期限资产分类表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 固收资产剩余期限资产分类表数据列表
     */
    private void convertDictValueToLabel(List<FixedIncomeTermCatDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        for (FixedIncomeTermCatDTO dto : list) {
            // 转换资产小小类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                dto.setAssetSubSubCategory(assetSubSubCategoryLabel);
            }

            // 转换境内外标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getDomesticForeign())) {
                String domesticForeignLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDomesticForeign(),
                        "ast_domestic_foreign"
                );
                dto.setDomesticForeign(domesticForeignLabel);
            }

            // 转换债券类型：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getBondType())) {
                String bondTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getBondType(),
                        "ast_bond_type"
                );
                dto.setBondType(bondTypeLabel);
            }

            // 转换固收资产剩余期限分类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getFixedIncomeTermCategory())) {
                String fixedIncomeTermCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getFixedIncomeTermCategory(),
                        "ast_fixed_income_term_category"
                );
                dto.setFixedIncomeTermCategory(fixedIncomeTermCategoryLabel);
            }
        }
    }

    /**
     * 将中文标签转换为字典值用于导入
     *
     * @param list 固收资产剩余期限资产分类表数据列表
     */
    private void convertDictLabelToValue(List<FixedIncomeTermCatDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        for (FixedIncomeTermCatDTO dto : list) {
            // 转换资产小小类：中文标签转为字典值
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryValue = DictConvertUtil.convertLabelToValue(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                if (StringUtils.isNotEmpty(assetSubSubCategoryValue)) {
                    dto.setAssetSubSubCategory(assetSubSubCategoryValue);
                }
            }

            // 转换境内外标识：中文标签转为字典值
            if (StringUtils.isNotEmpty(dto.getDomesticForeign())) {
                String domesticForeignValue = DictConvertUtil.convertLabelToValue(
                        dto.getDomesticForeign(),
                        "ast_domestic_foreign"
                );
                if (StringUtils.isNotEmpty(domesticForeignValue)) {
                    dto.setDomesticForeign(domesticForeignValue);
                }
            }

            // 转换债券类型：中文标签转为字典值
            if (StringUtils.isNotEmpty(dto.getBondType())) {
                String bondTypeValue = DictConvertUtil.convertLabelToValue(
                        dto.getBondType(),
                        "ast_bond_type"
                );
                if (StringUtils.isNotEmpty(bondTypeValue)) {
                    dto.setBondType(bondTypeValue);
                }
            }

            // 转换固收资产剩余期限分类：中文标签转为字典值
            if (StringUtils.isNotEmpty(dto.getFixedIncomeTermCategory())) {
                String fixedIncomeTermCategoryValue = DictConvertUtil.convertLabelToValue(
                        dto.getFixedIncomeTermCategory(),
                        "ast_fixed_income_term_category"
                );
                if (StringUtils.isNotEmpty(fixedIncomeTermCategoryValue)) {
                    dto.setFixedIncomeTermCategory(fixedIncomeTermCategoryValue);
                }
            }
        }
    }
}
