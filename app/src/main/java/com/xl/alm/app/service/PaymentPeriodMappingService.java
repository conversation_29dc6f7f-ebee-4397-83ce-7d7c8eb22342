package com.xl.alm.app.service;

import com.xl.alm.app.dto.PaymentPeriodMappingDTO;
import com.xl.alm.app.query.PaymentPeriodMappingQuery;

import java.util.List;

/**
 * 缴费年期映射表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface PaymentPeriodMappingService {

    /**
     * 查询缴费年期映射列表
     *
     * @param paymentPeriodMappingQuery 缴费年期映射查询条件
     * @return 缴费年期映射列表
     */
    List<PaymentPeriodMappingDTO> selectPaymentPeriodMappingDtoList(PaymentPeriodMappingQuery paymentPeriodMappingQuery);

    /**
     * 用id查询缴费年期映射
     *
     * @param id id
     * @return 缴费年期映射
     */
    PaymentPeriodMappingDTO selectPaymentPeriodMappingDtoById(Long id);

    /**
     * 根据账期和缴费年期查询缴费年期映射
     *
     * @param accountingPeriod 账期
     * @param paymentPeriod 缴费年期
     * @return 缴费年期映射
     */
    PaymentPeriodMappingDTO selectPaymentPeriodMappingDtoByCondition(String accountingPeriod, Integer paymentPeriod);

    /**
     * 新增缴费年期映射
     *
     * @param paymentPeriodMappingDto 缴费年期映射
     * @return 结果
     */
    int insertPaymentPeriodMappingDto(PaymentPeriodMappingDTO paymentPeriodMappingDto);

    /**
     * 批量新增缴费年期映射
     *
     * @param paymentPeriodMappingDtoList 缴费年期映射列表
     * @return 结果
     */
    int batchInsertPaymentPeriodMappingDto(List<PaymentPeriodMappingDTO> paymentPeriodMappingDtoList);

    /**
     * 修改缴费年期映射
     *
     * @param paymentPeriodMappingDto 缴费年期映射
     * @return 结果
     */
    int updatePaymentPeriodMappingDto(PaymentPeriodMappingDTO paymentPeriodMappingDto);

    /**
     * 删除缴费年期映射
     *
     * @param id 缴费年期映射主键
     * @return 结果
     */
    int deletePaymentPeriodMappingDtoById(Long id);

    /**
     * 批量删除缴费年期映射
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePaymentPeriodMappingDtoByIds(Long[] ids);
}
