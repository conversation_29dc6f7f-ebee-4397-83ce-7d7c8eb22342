package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.AccountNameMapDTO;
import com.xl.alm.app.query.AccountNameMapQuery;
import com.xl.alm.app.service.AccountNameMapService;
import com.xl.alm.app.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 账户名称映射表Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/ast/account/name/map")
public class AccountNameMapController extends BaseController {

    @Autowired
    private AccountNameMapService accountNameMapService;

    /**
     * 查询账户名称映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:list')")
    @GetMapping("/list")
    public TableDataInfo list(AccountNameMapQuery accountNameMapQuery) {
        startPage();
        List<AccountNameMapDTO> list = accountNameMapService.selectAccountNameMapDtoList(accountNameMapQuery);
        return getDataTable(list);
    }

    /**
     * 导出账户名称映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:export')")
    @Log(title = "账户名称映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountNameMapQuery accountNameMapQuery) {
        List<AccountNameMapDTO> list = accountNameMapService.selectAccountNameMapDtoList(accountNameMapQuery);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<AccountNameMapDTO> util = new ExcelUtil<>(AccountNameMapDTO.class);
        util.exportExcel(list, "账户名称映射表数据", response);
    }

    /**
     * 获取账户名称映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(accountNameMapService.selectAccountNameMapDtoById(id));
    }

    /**
     * 新增账户名称映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:add')")
    @Log(title = "账户名称映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody AccountNameMapDTO accountNameMapDTO) {
        accountNameMapDTO.setCreateBy(SecurityUtils.getUsername());
        return toAjax(accountNameMapService.insertAccountNameMapDto(accountNameMapDTO));
    }

    /**
     * 修改账户名称映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:edit')")
    @Log(title = "账户名称映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody AccountNameMapDTO accountNameMapDTO) {
        accountNameMapDTO.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(accountNameMapService.updateAccountNameMapDto(accountNameMapDTO));
    }

    /**
     * 删除账户名称映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:remove')")
    @Log(title = "账户名称映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(accountNameMapService.deleteAccountNameMapDtoByIds(ids));
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AccountNameMapDTO> util = new ExcelUtil<>(AccountNameMapDTO.class);
        util.exportTemplateExcel(response, "账户名称映射表数据");
    }

    /**
     * 导入账户名称映射表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:account:name:map:import')")
    @Log(title = "账户名称映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AccountNameMapDTO> util = new ExcelUtil<>(AccountNameMapDTO.class);
        List<AccountNameMapDTO> accountNameMapList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = accountNameMapService.importAccountNameMapDto(accountNameMapList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 账户名称映射表数据列表
     */
    private void convertDictCodeToLabel(List<AccountNameMapDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取账户名称映射字典数据
            List<SysDictData> accountNameMappingDict = dictTypeService.selectDictDataByType("ast_account_name_mapping");

            // 为每条记录转换字典编码为中文标签
            for (AccountNameMapDTO dto : list) {
                // 转换映射后的标准账户名称
                dto.setAccountNameMapping(convertCodeToLabel(dto.getAccountNameMapping(), accountNameMappingDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            log.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将字典编码转换为中文标签
     *
     * @param code 字典编码
     * @param dictDataList 字典数据列表
     * @return 中文标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        // 如果没有找到匹配的标签，返回原编码
        return code;
    }
}
