package com.xl.alm.app.service.impl;

import com.jd.lightning.common.exception.ServiceException;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.FinancialBudgetExpenseDTO;
import com.xl.alm.app.entity.FinancialBudgetExpenseEntity;
import com.xl.alm.app.mapper.FinancialBudgetExpenseMapper;
import com.xl.alm.app.query.FinancialBudgetExpenseQuery;
import com.xl.alm.app.service.FinancialBudgetExpenseService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import com.xl.alm.app.util.DictConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 财务预算费用表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class FinancialBudgetExpenseServiceImpl implements FinancialBudgetExpenseService {

    @Autowired
    private FinancialBudgetExpenseMapper financialBudgetExpenseMapper;

    /**
     * 查询财务预算费用列表
     *
     * @param financialBudgetExpenseQuery 财务预算费用查询条件
     * @return 财务预算费用列表
     */
    @Override
    public List<FinancialBudgetExpenseDTO> selectFinancialBudgetExpenseDtoList(FinancialBudgetExpenseQuery financialBudgetExpenseQuery) {
        List<FinancialBudgetExpenseEntity> entityList = financialBudgetExpenseMapper.selectFinancialBudgetExpenseEntityList(financialBudgetExpenseQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, FinancialBudgetExpenseDTO.class);
    }

    /**
     * 根据ID查询财务预算费用
     *
     * @param id 主键ID
     * @return 财务预算费用
     */
    @Override
    public FinancialBudgetExpenseDTO selectFinancialBudgetExpenseDtoById(Long id) {
        FinancialBudgetExpenseEntity entity = financialBudgetExpenseMapper.selectFinancialBudgetExpenseEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FinancialBudgetExpenseDTO.class);
    }

    /**
     * 新增财务预算费用
     *
     * @param financialBudgetExpenseDto 财务预算费用
     * @return 影响行数
     */
    @Override
    public int insertFinancialBudgetExpenseDto(FinancialBudgetExpenseDTO financialBudgetExpenseDto) {
        FinancialBudgetExpenseEntity entity = EntityDtoConvertUtil.convertToEntity(financialBudgetExpenseDto, FinancialBudgetExpenseEntity.class);
        return financialBudgetExpenseMapper.insertFinancialBudgetExpenseEntity(entity);
    }

    /**
     * 修改财务预算费用
     *
     * @param financialBudgetExpenseDto 财务预算费用
     * @return 影响行数
     */
    @Override
    public int updateFinancialBudgetExpenseDto(FinancialBudgetExpenseDTO financialBudgetExpenseDto) {
        FinancialBudgetExpenseEntity entity = EntityDtoConvertUtil.convertToEntity(financialBudgetExpenseDto, FinancialBudgetExpenseEntity.class);
        return financialBudgetExpenseMapper.updateFinancialBudgetExpenseEntity(entity);
    }

    /**
     * 删除财务预算费用
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseDtoById(Long id) {
        return financialBudgetExpenseMapper.deleteFinancialBudgetExpenseEntityById(id);
    }

    /**
     * 批量删除财务预算费用
     *
     * @param ids 主键ID数组
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseDtoByIds(Long[] ids) {
        return financialBudgetExpenseMapper.deleteFinancialBudgetExpenseEntityByIds(ids);
    }

    /**
     * 批量新增财务预算费用
     *
     * @param financialBudgetExpenseDtoList 财务预算费用列表
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertFinancialBudgetExpenseDto(List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList) {
        if (financialBudgetExpenseDtoList == null || financialBudgetExpenseDtoList.isEmpty()) {
            return 0;
        }
        // 转换字典标签为字典值
        for (FinancialBudgetExpenseDTO dto : financialBudgetExpenseDtoList) {
            convertDictLabelToValue(dto);
        }
        List<FinancialBudgetExpenseEntity> entityList = EntityDtoConvertUtil.convertToEntityList(financialBudgetExpenseDtoList, FinancialBudgetExpenseEntity.class);
        return financialBudgetExpenseMapper.batchInsertFinancialBudgetExpenseEntity(entityList);
    }

    /**
     * 根据账期删除财务预算费用
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteFinancialBudgetExpenseDtoByPeriod(String accountingPeriod) {
        return financialBudgetExpenseMapper.deleteFinancialBudgetExpenseEntityByPeriod(accountingPeriod);
    }

    /**
     * 导入财务预算费用数据
     *
     * @param financialBudgetExpenseDtoList 财务预算费用列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFinancialBudgetExpenseData(List<FinancialBudgetExpenseDTO> financialBudgetExpenseDtoList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(financialBudgetExpenseDtoList) || financialBudgetExpenseDtoList.size() == 0) {
            throw new ServiceException("导入财务预算费用数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (FinancialBudgetExpenseDTO financialBudgetExpenseDto : financialBudgetExpenseDtoList) {
            try {
                // 转换字典标签为字典值
                convertDictLabelToValue(financialBudgetExpenseDto);

                // 验证是否存在这个财务预算费用
                FinancialBudgetExpenseQuery query = new FinancialBudgetExpenseQuery();
                query.setAccountingPeriod(financialBudgetExpenseDto.getAccountingPeriod());
                query.setScenarioName(financialBudgetExpenseDto.getScenarioName());
                query.setFinancialExpenseType(financialBudgetExpenseDto.getFinancialExpenseType());
                query.setDate(financialBudgetExpenseDto.getDate());
                List<FinancialBudgetExpenseDTO> existList = this.selectFinancialBudgetExpenseDtoList(query);
                
                if (StringUtils.isNull(existList) || existList.size() == 0) {
                    financialBudgetExpenseDto.setCreateBy(operName);
                    this.insertFinancialBudgetExpenseDto(financialBudgetExpenseDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + financialBudgetExpenseDto.getAccountingPeriod() + " 的数据导入成功");
                } else if (isUpdateSupport) {
                    financialBudgetExpenseDto.setUpdateBy(operName);
                    financialBudgetExpenseDto.setId(existList.get(0).getId());
                    this.updateFinancialBudgetExpenseDto(financialBudgetExpenseDto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + financialBudgetExpenseDto.getAccountingPeriod() + " 的数据更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + financialBudgetExpenseDto.getAccountingPeriod() + " 的数据已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + financialBudgetExpenseDto.getAccountingPeriod() + " 的数据导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 转换字典标签为字典值
     *
     * @param financialBudgetExpenseDto 财务预算费用DTO
     */
    private void convertDictLabelToValue(FinancialBudgetExpenseDTO financialBudgetExpenseDto) {
        // 转换情景名称
        if (StringUtils.isNotEmpty(financialBudgetExpenseDto.getScenarioName())) {
            String convertedScenarioName = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseDto.getScenarioName(),
                    "cft_scenario_name"
            );
            financialBudgetExpenseDto.setScenarioName(convertedScenarioName);
        }

        // 转换财务费用类型
        if (StringUtils.isNotEmpty(financialBudgetExpenseDto.getFinancialExpenseType())) {
            String convertedFinancialExpenseType = DictConvertUtil.convertLabelToValue(
                    financialBudgetExpenseDto.getFinancialExpenseType(),
                    "cft_financial_expense_type"
            );
            financialBudgetExpenseDto.setFinancialExpenseType(convertedFinancialExpenseType);
        }
    }
}
