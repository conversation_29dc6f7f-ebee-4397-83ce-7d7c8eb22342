package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.BankClassificationMapDTO;
import com.xl.alm.app.query.BankClassificationMapQuery;
import com.xl.alm.app.service.BankClassificationMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 银行分类映射表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/bank/classification/map")
public class BankClassificationMapController extends BaseController {

    @Autowired
    private BankClassificationMapService bankClassificationMapService;

    /**
     * 查询银行分类映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:list')")
    @GetMapping("/list")
    public TableDataInfo list(BankClassificationMapQuery query) {
        startPage();
        List<BankClassificationMapDTO> list = bankClassificationMapService.selectBankClassificationMapDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出银行分类映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:export')")
    @Log(title = "银行分类映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BankClassificationMapQuery query) {
        bankClassificationMapService.exportBankClassificationMapDto(response, query);
    }

    /**
     * 获取银行分类映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(bankClassificationMapService.selectBankClassificationMapDtoById(id));
    }

    /**
     * 新增银行分类映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:add')")
    @Log(title = "银行分类映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody BankClassificationMapDTO dto) {
        return toAjax(bankClassificationMapService.insertBankClassificationMapDto(dto));
    }

    /**
     * 修改银行分类映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:edit')")
    @Log(title = "银行分类映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody BankClassificationMapDTO dto) {
        return toAjax(bankClassificationMapService.updateBankClassificationMapDto(dto));
    }

    /**
     * 删除银行分类映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:remove')")
    @Log(title = "银行分类映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(bankClassificationMapService.deleteBankClassificationMapDtoByIds(ids));
    }

    /**
     * 导入银行分类映射表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:import')")
    @Log(title = "银行分类映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        String operName = getUsername();
        String message = bankClassificationMapService.importBankClassificationMapDto(file, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取银行分类映射表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        bankClassificationMapService.importTemplate(response);
    }

    /**
     * 根据账期删除银行分类映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:bank:classification:map:remove')")
    @Log(title = "银行分类映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(bankClassificationMapService.deleteBankClassificationMapDtoByPeriod(accountingPeriod));
    }
}
