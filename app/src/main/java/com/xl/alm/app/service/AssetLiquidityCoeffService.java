package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetLiquidityCoeffDTO;
import com.xl.alm.app.query.AssetLiquidityCoeffQuery;

import java.util.List;

/**
 * 资产流动性分类及变现系数表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetLiquidityCoeffService {

    /**
     * 查询资产流动性分类及变现系数表列表
     *
     * @param assetLiquidityCoeffQuery 资产流动性分类及变现系数表查询条件
     * @return 资产流动性分类及变现系数表列表
     */
    List<AssetLiquidityCoeffDTO> selectAssetLiquidityCoeffDtoList(AssetLiquidityCoeffQuery assetLiquidityCoeffQuery);

    /**
     * 根据主键查询资产流动性分类及变现系数表
     *
     * @param id 主键
     * @return 资产流动性分类及变现系数表
     */
    AssetLiquidityCoeffDTO selectAssetLiquidityCoeffDtoById(Long id);

    /**
     * 新增资产流动性分类及变现系数表
     *
     * @param assetLiquidityCoeffDTO 资产流动性分类及变现系数表
     * @return 影响行数
     */
    int insertAssetLiquidityCoeffDto(AssetLiquidityCoeffDTO assetLiquidityCoeffDTO);

    /**
     * 修改资产流动性分类及变现系数表
     *
     * @param assetLiquidityCoeffDTO 资产流动性分类及变现系数表
     * @return 影响行数
     */
    int updateAssetLiquidityCoeffDto(AssetLiquidityCoeffDTO assetLiquidityCoeffDTO);

    /**
     * 批量删除资产流动性分类及变现系数表
     *
     * @param ids 需要删除的资产流动性分类及变现系数表主键集合
     * @return 影响行数
     */
    int deleteAssetLiquidityCoeffDtoByIds(Long[] ids);

    /**
     * 删除资产流动性分类及变现系数表信息
     *
     * @param id 资产流动性分类及变现系数表主键
     * @return 影响行数
     */
    int deleteAssetLiquidityCoeffDtoById(Long id);

    /**
     * 批量新增资产流动性分类及变现系数表
     *
     * @param assetLiquidityCoeffDtoList 资产流动性分类及变现系数表列表
     * @return 影响行数
     */
    int batchInsertAssetLiquidityCoeffDto(List<AssetLiquidityCoeffDTO> assetLiquidityCoeffDtoList);

    /**
     * 根据账期删除资产流动性分类及变现系数表
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAssetLiquidityCoeffDtoByPeriod(String accountingPeriod);

    /**
     * 导入资产流动性分类及变现系数表数据
     *
     * @param assetLiquidityCoeffDtoList 资产流动性分类及变现系数表数据列表
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importAssetLiquidityCoeffDto(List<AssetLiquidityCoeffDTO> assetLiquidityCoeffDtoList, Boolean updateSupport, String operName);

}
