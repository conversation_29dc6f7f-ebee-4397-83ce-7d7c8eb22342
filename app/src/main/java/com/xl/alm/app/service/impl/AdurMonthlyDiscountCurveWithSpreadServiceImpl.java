package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurMonthlyDiscountCurveWithSpreadDTO;
import com.xl.alm.app.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.app.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.app.query.AdurMonthlyDiscountCurveWithSpreadQuery;
import com.xl.alm.app.service.AdurMonthlyDiscountCurveWithSpreadService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR月度折现曲线表含价差 Service业务层处理
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountCurveWithSpreadServiceImpl implements AdurMonthlyDiscountCurveWithSpreadService {

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper adurMonthlyDiscountCurveWithSpreadMapper;

    /**
     * 查询ADUR月度折现曲线含价差列表
     *
     * @param adurMonthlyDiscountCurveWithSpreadQuery ADUR月度折现曲线含价差查询条件
     * @return ADUR月度折现曲线含价差列表
     */
    @Override
    public List<AdurMonthlyDiscountCurveWithSpreadDTO> selectAdurMonthlyDiscountCurveWithSpreadDtoList(AdurMonthlyDiscountCurveWithSpreadQuery adurMonthlyDiscountCurveWithSpreadQuery) {
        List<AdurMonthlyDiscountCurveWithSpreadEntity> entityList = adurMonthlyDiscountCurveWithSpreadMapper.selectAdurMonthlyDiscountCurveWithSpreadEntityList(adurMonthlyDiscountCurveWithSpreadQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurMonthlyDiscountCurveWithSpreadDTO.class);
    }

    /**
     * 根据ID查询ADUR月度折现曲线含价差
     *
     * @param id 主键ID
     * @return ADUR月度折现曲线含价差
     */
    @Override
    public AdurMonthlyDiscountCurveWithSpreadDTO selectAdurMonthlyDiscountCurveWithSpreadDtoById(Long id) {
        AdurMonthlyDiscountCurveWithSpreadEntity entity = adurMonthlyDiscountCurveWithSpreadMapper.selectAdurMonthlyDiscountCurveWithSpreadEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurMonthlyDiscountCurveWithSpreadDTO.class);
    }

    /**
     * 新增ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadDto ADUR月度折现曲线含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurMonthlyDiscountCurveWithSpreadDto(AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDto) {
        AdurMonthlyDiscountCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurMonthlyDiscountCurveWithSpreadDto, AdurMonthlyDiscountCurveWithSpreadEntity.class);
        return adurMonthlyDiscountCurveWithSpreadMapper.insertAdurMonthlyDiscountCurveWithSpreadEntity(entity);
    }

    /**
     * 修改ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadDto ADUR月度折现曲线含价差
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurMonthlyDiscountCurveWithSpreadDto(AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDto) {
        AdurMonthlyDiscountCurveWithSpreadEntity entity = EntityDtoConvertUtil.convertToEntity(adurMonthlyDiscountCurveWithSpreadDto, AdurMonthlyDiscountCurveWithSpreadEntity.class);
        return adurMonthlyDiscountCurveWithSpreadMapper.updateAdurMonthlyDiscountCurveWithSpreadEntity(entity);
    }

    /**
     * 批量删除ADUR月度折现曲线含价差
     *
     * @param ids 需要删除的ADUR月度折现曲线含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountCurveWithSpreadDtoByIds(Long[] ids) {
        return adurMonthlyDiscountCurveWithSpreadMapper.deleteAdurMonthlyDiscountCurveWithSpreadEntityByIds(ids);
    }

    /**
     * 删除ADUR月度折现曲线含价差信息
     *
     * @param id ADUR月度折现曲线含价差主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountCurveWithSpreadDtoById(Long id) {
        return adurMonthlyDiscountCurveWithSpreadMapper.deleteAdurMonthlyDiscountCurveWithSpreadEntityById(id);
    }

    /**
     * 导入ADUR月度折现曲线含价差
     *
     * @param adurMonthlyDiscountCurveWithSpreadList ADUR月度折现曲线含价差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurMonthlyDiscountCurveWithSpreadDto(List<AdurMonthlyDiscountCurveWithSpreadDTO> adurMonthlyDiscountCurveWithSpreadList, Boolean updateSupport, String operName) {
        if (StringUtils.isNull(adurMonthlyDiscountCurveWithSpreadList) || adurMonthlyDiscountCurveWithSpreadList.size() == 0) {
            throw new RuntimeException("导入ADUR月度折现曲线含价差数据不能为空！");
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (AdurMonthlyDiscountCurveWithSpreadDTO dto : adurMonthlyDiscountCurveWithSpreadList) {
            try {
                // 验证是否存在这个数据
                AdurMonthlyDiscountCurveWithSpreadEntity existEntity = adurMonthlyDiscountCurveWithSpreadMapper.selectAdurMonthlyDiscountCurveWithSpreadEntityByAccountPeriodAndAssetNumberAndDateType(
                        dto.getAccountPeriod(), dto.getAssetNumber(), dto.getDateType());
                
                if (StringUtils.isNull(existEntity)) {
                    dto.setCreateBy(operName);
                    this.insertAdurMonthlyDiscountCurveWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existEntity.getId());
                    dto.setUpdateBy(operName);
                    this.updateAdurMonthlyDiscountCurveWithSpreadDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        
        return successMsg.toString();
    }

    /**
     * 根据账期删除ADUR月度折现曲线含价差数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurMonthlyDiscountCurveWithSpreadDtoByAccountPeriod(String accountPeriod) {
        return adurMonthlyDiscountCurveWithSpreadMapper.deleteAdurMonthlyDiscountCurveWithSpreadEntityByAccountPeriod(accountPeriod);
    }

    /**
     * 批量插入ADUR月度折现曲线含价差数据
     *
     * @param adurMonthlyDiscountCurveWithSpreadList ADUR月度折现曲线含价差列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurMonthlyDiscountCurveWithSpreadDto(List<AdurMonthlyDiscountCurveWithSpreadDTO> adurMonthlyDiscountCurveWithSpreadList) {
        if (adurMonthlyDiscountCurveWithSpreadList == null || adurMonthlyDiscountCurveWithSpreadList.isEmpty()) {
            return 0;
        }
        List<AdurMonthlyDiscountCurveWithSpreadEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurMonthlyDiscountCurveWithSpreadList, AdurMonthlyDiscountCurveWithSpreadEntity.class);
        return adurMonthlyDiscountCurveWithSpreadMapper.batchInsertAdurMonthlyDiscountCurveWithSpreadEntity(entityList);
    }
}
