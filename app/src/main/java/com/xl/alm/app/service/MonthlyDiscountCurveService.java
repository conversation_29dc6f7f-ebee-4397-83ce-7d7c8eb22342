package com.xl.alm.app.service;

import com.xl.alm.app.dto.MonthlyDiscountCurveDTO;
import com.xl.alm.app.query.MonthlyDiscountCurveQuery;

import java.util.List;

/**
 * 月度折现曲线表不含价差 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface MonthlyDiscountCurveService {

    /**
     * 查询月度折现曲线列表
     *
     * @param monthlyDiscountCurveQuery 月度折现曲线查询条件
     * @return 月度折现曲线列表
     */
    List<MonthlyDiscountCurveDTO> selectMonthlyDiscountCurveDtoList(MonthlyDiscountCurveQuery monthlyDiscountCurveQuery);

    /**
     * 根据ID查询月度折现曲线
     *
     * @param id 主键ID
     * @return 月度折现曲线
     */
    MonthlyDiscountCurveDTO selectMonthlyDiscountCurveDtoById(Long id);

    /**
     * 新增月度折现曲线
     *
     * @param monthlyDiscountCurveDto 月度折现曲线
     * @return 结果
     */
    int insertMonthlyDiscountCurveDto(MonthlyDiscountCurveDTO monthlyDiscountCurveDto);

    /**
     * 修改月度折现曲线
     *
     * @param monthlyDiscountCurveDto 月度折现曲线
     * @return 结果
     */
    int updateMonthlyDiscountCurveDto(MonthlyDiscountCurveDTO monthlyDiscountCurveDto);

    /**
     * 批量删除月度折现曲线
     *
     * @param ids 需要删除的月度折现曲线主键集合
     * @return 结果
     */
    int deleteMonthlyDiscountCurveDtoByIds(Long[] ids);

    /**
     * 删除月度折现曲线信息
     *
     * @param id 月度折现曲线主键
     * @return 结果
     */
    int deleteMonthlyDiscountCurveDtoById(Long id);

    /**
     * 导入月度折现曲线
     *
     * @param monthlyDiscountCurveList 月度折现曲线数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作用户
     * @return 结果
     */
    String importMonthlyDiscountCurveDto(List<MonthlyDiscountCurveDTO> monthlyDiscountCurveList, Boolean updateSupport, String operName);

    /**
     * 根据账期删除月度折现曲线数据
     *
     * @param accountPeriod 账期
     * @return 影响行数
     */
    int deleteMonthlyDiscountCurveDtoByAccountPeriod(String accountPeriod);

    /**
     * 批量插入月度折现曲线数据
     *
     * @param monthlyDiscountCurveList 月度折现曲线列表
     * @return 影响行数
     */
    int batchInsertMonthlyDiscountCurveDto(List<MonthlyDiscountCurveDTO> monthlyDiscountCurveList);
}
