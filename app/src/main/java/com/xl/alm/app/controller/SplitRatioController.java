package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.SplitRatioDTO;
import com.xl.alm.app.query.SplitRatioQuery;
import com.xl.alm.app.service.SplitRatioService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 拆分比例表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/split/ratio")
public class SplitRatioController extends BaseController {

    @Autowired
    private SplitRatioService splitRatioService;

    /**
     * 查询拆分比例表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:list')")
    @GetMapping("/list")
    public TableDataInfo list(SplitRatioQuery splitRatioQuery) {
        startPage();
        List<SplitRatioDTO> list = splitRatioService.selectSplitRatioDtoList(splitRatioQuery);
        return getDataTable(list);
    }

    /**
     * 获取拆分比例表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(splitRatioService.selectSplitRatioDtoById(id));
    }

    /**
     * 根据条件查询拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("scenarioName") String scenarioName,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "designType", required = false) String designType,
            @RequestParam("splitRatioType") String splitRatioType) {
        return Result.success(splitRatioService.selectSplitRatioDtoByCondition(
                accountingPeriod, scenarioName, businessType, designType, splitRatioType));
    }

    /**
     * 新增拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:add')")
    @Log(title = "拆分比例表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody SplitRatioDTO dto) {
        // 验证唯一性
        SplitRatioDTO existDto = splitRatioService.selectSplitRatioDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getBusinessType(),
                dto.getDesignType(), dto.getSplitRatioType());
        if (existDto != null) {
            return Result.error("该账期、情景名称、业务类型、设计类型、拆分比例类型组合已存在");
        }
        
        dto.setCreateBy(SecurityUtils.getUsername());
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(splitRatioService.insertSplitRatioDto(dto));
    }

    /**
     * 修改拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:edit')")
    @Log(title = "拆分比例表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody SplitRatioDTO dto) {
        // 验证唯一性（排除自身）
        SplitRatioDTO existDto = splitRatioService.selectSplitRatioDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getBusinessType(),
                dto.getDesignType(), dto.getSplitRatioType());
        if (existDto != null && !existDto.getId().equals(dto.getId())) {
            return Result.error("该账期、情景名称、业务类型、设计类型、拆分比例类型组合已存在");
        }
        
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(splitRatioService.updateSplitRatioDto(dto));
    }

    /**
     * 删除拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:remove')")
    @Log(title = "拆分比例表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(splitRatioService.deleteSplitRatioDtoByIds(ids));
    }

    /**
     * 批量新增拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:add')")
    @Log(title = "拆分比例表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@RequestBody List<SplitRatioDTO> splitRatioDtoList) {
        return toAjax(splitRatioService.batchInsertSplitRatioDto(splitRatioDtoList));
    }

    /**
     * 根据账期删除拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:remove')")
    @Log(title = "拆分比例表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(splitRatioService.deleteSplitRatioDtoByPeriod(accountingPeriod));
    }

    /**
     * 导出拆分比例表
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:export')")
    @Log(title = "拆分比例表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SplitRatioQuery query) throws Exception {
        splitRatioService.exportSplitRatioHorizontal(response, query);
    }

    /**
     * 获取拆分比例表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:import')")
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws Exception {
        splitRatioService.exportTemplate(response);
    }

    /**
     * 导入拆分比例表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:split:ratio:import')")
    @Log(title = "拆分比例表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        String operName = SecurityUtils.getUsername();
        String message = splitRatioService.importSplitRatioDataFromExcel(file.getInputStream(), updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 拆分比例数据列表
     */
    private void convertDictValueToLabel(List<SplitRatioDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (SplitRatioDTO dto : list) {
            // 转换情景名称：字典值转为中文标签
            if (dto.getScenarioName() != null) {
                String scenarioLabel = DictConvertUtil.convertValueToLabel(
                        dto.getScenarioName(), 
                        "cft_scenario_name"
                );
                dto.setScenarioName(scenarioLabel);
            }

            // 转换业务类型：字典值转为中文标签
            if (dto.getBusinessType() != null) {
                String businessTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getBusinessType(), 
                        "cost_business_type"
                );
                dto.setBusinessType(businessTypeLabel);
            }

            // 转换设计类型：字典值转为中文标签
            if (dto.getDesignType() != null) {
                String designTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDesignType(), 
                        "cost_design_type"
                );
                dto.setDesignType(designTypeLabel);
            }

            // 转换拆分比例类型：字典值转为中文标签
            if (dto.getSplitRatioType() != null) {
                String splitRatioTypeLabel = DictConvertUtil.convertValueToLabel(
                        dto.getSplitRatioType(), 
                        "cft_split_ratio_type"
                );
                dto.setSplitRatioType(splitRatioTypeLabel);
            }
        }
    }
}
