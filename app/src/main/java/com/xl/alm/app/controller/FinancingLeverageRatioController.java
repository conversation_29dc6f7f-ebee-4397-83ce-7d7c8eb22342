package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.FinancingLeverageRatioDTO;
import com.xl.alm.app.query.FinancingLeverageRatioQuery;
import com.xl.alm.app.service.FinancingLeverageRatioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 融资杠杆比例表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/financing/leverage/ratio")
public class FinancingLeverageRatioController extends BaseController {

    @Autowired
    private FinancingLeverageRatioService financingLeverageRatioService;

    /**
     * 查询融资杠杆比例表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:list')")
    @GetMapping("/list")
    public TableDataInfo list(FinancingLeverageRatioQuery financingLeverageRatioQuery) {
        startPage();
        List<FinancingLeverageRatioDTO> list = financingLeverageRatioService.selectFinancingLeverageRatioDtoList(financingLeverageRatioQuery);
        return getDataTable(list);
    }

    /**
     * 导出融资杠杆比例表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:export')")
    @Log(title = "融资杠杆比例表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinancingLeverageRatioQuery financingLeverageRatioQuery) {
        List<FinancingLeverageRatioDTO> list = financingLeverageRatioService.selectFinancingLeverageRatioDtoList(financingLeverageRatioQuery);
        ExcelUtil<FinancingLeverageRatioDTO> util = new ExcelUtil<>(FinancingLeverageRatioDTO.class);
        util.exportExcel(list, "融资杠杆比例表", response);
    }

    /**
     * 获取融资杠杆比例表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(financingLeverageRatioService.selectFinancingLeverageRatioDtoById(id));
    }

    /**
     * 根据条件查询融资杠杆比例表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod, 
                                 @RequestParam String itemName) {
        return Result.success(financingLeverageRatioService.selectFinancingLeverageRatioDtoByCondition(accountingPeriod, itemName));
    }

    /**
     * 新增融资杠杆比例表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:add')")
    @Log(title = "融资杠杆比例表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FinancingLeverageRatioDTO financingLeverageRatioDTO) {
        return toAjax(financingLeverageRatioService.insertFinancingLeverageRatioDto(financingLeverageRatioDTO));
    }

    /**
     * 修改融资杠杆比例表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:edit')")
    @Log(title = "融资杠杆比例表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FinancingLeverageRatioDTO financingLeverageRatioDTO) {
        return toAjax(financingLeverageRatioService.updateFinancingLeverageRatioDto(financingLeverageRatioDTO));
    }

    /**
     * 删除融资杠杆比例表
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:remove')")
    @Log(title = "融资杠杆比例表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(financingLeverageRatioService.deleteFinancingLeverageRatioDtoByIds(ids));
    }

    /**
     * 导入融资杠杆比例表数据
     */
    @PreAuthorize("@ss.hasPermi('asm:financing:leverage:ratio:import')")
    @Log(title = "融资杠杆比例表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FinancingLeverageRatioDTO> util = new ExcelUtil<>(FinancingLeverageRatioDTO.class);
        List<FinancingLeverageRatioDTO> financingLeverageRatioList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = financingLeverageRatioService.importFinancingLeverageRatioDto(financingLeverageRatioList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载融资杠杆比例表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FinancingLeverageRatioDTO> util = new ExcelUtil<>(FinancingLeverageRatioDTO.class);
        util.exportExcel(new ArrayList<>(), "融资杠杆比例表数据", response);
    }
}
