package com.xl.alm.app.service;

import com.xl.alm.app.service.AssetDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 证券代码提取服务
 * 用于从维度名称中提取证券代码和资产名称
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class SecurityCodeExtractionService {

    @Autowired
    private AssetDefinitionService assetDefinitionService;

    /**
     * 从维度名称中提取证券代码和资产名称
     *
     * @param dimensionName 维度名称
     * @param accountName 账户名称
     * @param accountingPeriod 账期
     * @return 证券代码提取结果
     */
    public SecurityCodeResult extractSecurityCode(String dimensionName, String accountName, String accountingPeriod) {
        SecurityCodeResult result = new SecurityCodeResult();
        result.setDimensionName(dimensionName);
        result.setAccountName(accountName);

        // 方法1：直接从维度名称解析
        if (dimensionName.contains("--")) {
            String[] parts = dimensionName.split("--", 2);
            if (parts.length == 2) {
                String potentialCode = parts[0].trim();
                String assetName = parts[1].trim();

                // 验证证券代码格式（通常6-10位字母数字组合）
                if (isValidSecurityCodeFormat(potentialCode)) {
                    result.setSecurityCode(potentialCode);
                    result.setAssetName(assetName);
                    return result;
                }
            }
        }

        // 方法2：通过资产名称在TB0006中查找
        if (StringUtils.hasText(result.getAssetName())) {
            String securityCode = findSecurityCodeByAssetName(
                result.getAssetName(), accountName, accountingPeriod);
            if (StringUtils.hasText(securityCode)) {
                result.setSecurityCode(securityCode);
                return result;
            }
        }

        // 方法3：无法提取证券代码（汇总数据）
        result.setSecurityCode(null);
        result.setAssetName(null);
        return result;
    }

    /**
     * 验证证券代码格式
     *
     * @param code 证券代码
     * @return 是否为有效格式
     */
    private boolean isValidSecurityCodeFormat(String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        // 证券代码通常是6-10位的字母数字组合
        return code.matches("^[A-Za-z0-9]{6,10}$");
    }

    /**
     * 通过资产名称在TB0006中查找证券代码
     *
     * @param assetName 资产名称
     * @param accountName 账户名称
     * @param accountingPeriod 账期
     * @return 证券代码
     */
    private String findSecurityCodeByAssetName(String assetName, String accountName, String accountingPeriod) {
        try {
            // 精确匹配
            String securityCode = assetDefinitionService.findSecurityCodeByExactMatch(
                accountingPeriod, accountName, assetName);
            if (StringUtils.hasText(securityCode)) {
                return securityCode;
            }

            // 模糊匹配（去除特殊字符后匹配）
            String cleanAssetName = assetName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
            return assetDefinitionService.findSecurityCodeByFuzzyMatch(
                accountingPeriod, accountName, cleanAssetName);

        } catch (Exception e) {
            log.warn("查找证券代码失败: assetName={}, accountName={}, error={}",
                assetName, accountName, e.getMessage());
            return null;
        }
    }

    /**
     * 证券代码提取结果
     */
    public static class SecurityCodeResult {
        private String dimensionName;      // 原始维度名称
        private String accountName;        // 账户名称
        private String securityCode;       // 提取的证券代码
        private String assetName;          // 提取的资产名称

        // Getters and Setters
        public String getDimensionName() {
            return dimensionName;
        }

        public void setDimensionName(String dimensionName) {
            this.dimensionName = dimensionName;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getSecurityCode() {
            return securityCode;
        }

        public void setSecurityCode(String securityCode) {
            this.securityCode = securityCode;
        }

        public String getAssetName() {
            return assetName;
        }

        public void setAssetName(String assetName) {
            this.assetName = assetName;
        }
    }
}
