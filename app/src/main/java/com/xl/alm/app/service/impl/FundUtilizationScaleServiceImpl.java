package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.FundUtilizationScaleDTO;
import com.xl.alm.app.entity.FundUtilizationScaleEntity;
import com.xl.alm.app.mapper.FundUtilizationScaleMapper;
import com.xl.alm.app.query.FundUtilizationScaleQuery;
import com.xl.alm.app.service.FundUtilizationScaleService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 资金运用规模表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class FundUtilizationScaleServiceImpl implements FundUtilizationScaleService {

    @Autowired
    private FundUtilizationScaleMapper fundUtilizationScaleMapper;

    /**
     * 查询资金运用规模表列表
     *
     * @param fundUtilizationScaleQuery 资金运用规模表查询条件
     * @return 资金运用规模表列表
     */
    @Override
    public List<FundUtilizationScaleDTO> selectFundUtilizationScaleDtoList(FundUtilizationScaleQuery fundUtilizationScaleQuery) {
        List<FundUtilizationScaleEntity> entityList = fundUtilizationScaleMapper.selectFundUtilizationScaleList(fundUtilizationScaleQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, FundUtilizationScaleDTO.class);
    }

    /**
     * 用id查询资金运用规模表
     *
     * @param id id
     * @return 资金运用规模表
     */
    @Override
    public FundUtilizationScaleDTO selectFundUtilizationScaleDtoById(Long id) {
        FundUtilizationScaleEntity entity = fundUtilizationScaleMapper.selectFundUtilizationScaleById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FundUtilizationScaleDTO.class);
    }

    /**
     * 根据账期、项目名称、项目分级标识和数据类型查询资金运用规模表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @param itemClassificationLevel 项目分级标识
     * @param dataType 数据类型
     * @return 资金运用规模表
     */
    @Override
    public FundUtilizationScaleDTO selectFundUtilizationScaleDtoByCondition(String accountingPeriod, String itemName, String itemClassificationLevel, String dataType) {
        FundUtilizationScaleEntity entity = fundUtilizationScaleMapper.selectFundUtilizationScaleByCondition(accountingPeriod, itemName, itemClassificationLevel, dataType);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FundUtilizationScaleDTO.class);
    }

    /**
     * 新增资金运用规模表
     *
     * @param fundUtilizationScaleDTO 资金运用规模表
     * @return 结果
     */
    @Override
    @Transactional
    public int insertFundUtilizationScaleDto(FundUtilizationScaleDTO fundUtilizationScaleDTO) {
        FundUtilizationScaleEntity entity = EntityDtoConvertUtil.convertToEntity(fundUtilizationScaleDTO, FundUtilizationScaleEntity.class);
        return fundUtilizationScaleMapper.insertFundUtilizationScale(entity);
    }

    /**
     * 批量新增资金运用规模表
     *
     * @param fundUtilizationScaleDtoList 资金运用规模表列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertFundUtilizationScaleDto(List<FundUtilizationScaleDTO> fundUtilizationScaleDtoList) {
        if (fundUtilizationScaleDtoList == null || fundUtilizationScaleDtoList.isEmpty()) {
            return 0;
        }
        List<FundUtilizationScaleEntity> entityList = EntityDtoConvertUtil.convertToEntityList(fundUtilizationScaleDtoList, FundUtilizationScaleEntity.class);
        return fundUtilizationScaleMapper.batchInsertFundUtilizationScale(entityList);
    }

    /**
     * 更新资金运用规模表数据
     *
     * @param fundUtilizationScaleDTO 资金运用规模表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateFundUtilizationScaleDto(FundUtilizationScaleDTO fundUtilizationScaleDTO) {
        FundUtilizationScaleEntity entity = EntityDtoConvertUtil.convertToEntity(fundUtilizationScaleDTO, FundUtilizationScaleEntity.class);
        return fundUtilizationScaleMapper.updateFundUtilizationScale(entity);
    }

    /**
     * 删除指定id的资金运用规模表数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteFundUtilizationScaleDtoById(Long id) {
        return fundUtilizationScaleMapper.deleteFundUtilizationScaleById(id);
    }

    /**
     * 批量删除资金运用规模表
     *
     * @param ids 需要删除的资金运用规模表主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteFundUtilizationScaleDtoByIds(Long[] ids) {
        return fundUtilizationScaleMapper.deleteFundUtilizationScaleByIds(ids);
    }

    /**
     * 删除指定账期的资金运用规模表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteFundUtilizationScaleDtoByPeriod(String accountingPeriod) {
        return fundUtilizationScaleMapper.deleteFundUtilizationScaleByPeriod(accountingPeriod);
    }

    /**
     * 导入资金运用规模表
     *
     * @param dtoList       资金运用规模表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importFundUtilizationScaleDto(List<FundUtilizationScaleDTO> dtoList, Boolean updateSupport, String username) {
        if (dtoList == null || dtoList.isEmpty()) {
            return "导入数据为空";
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (FundUtilizationScaleDTO dto : dtoList) {
            try {
                // 验证必填字段
                if (StringUtils.isEmpty(dto.getAccountingPeriod()) || StringUtils.isEmpty(dto.getItemName()) 
                    || StringUtils.isEmpty(dto.getItemClassificationLevel()) || StringUtils.isEmpty(dto.getDataType())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期、项目名称、项目分级标识、数据类型不能为空");
                    continue;
                }

                // 检查是否已存在
                FundUtilizationScaleDTO existingDto = selectFundUtilizationScaleDtoByCondition(
                    dto.getAccountingPeriod(), dto.getItemName(), dto.getItemClassificationLevel(), dto.getDataType());

                if (existingDto == null) {
                    // 新增
                    dto.setCreateBy(username);
                    insertFundUtilizationScaleDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 项目 ").append(dto.getItemName()).append(" 新增成功");
                } else if (updateSupport) {
                    // 更新
                    dto.setId(existingDto.getId());
                    dto.setUpdateBy(username);
                    updateFundUtilizationScaleDto(dto);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 项目 ").append(dto.getItemName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账期 ").append(dto.getAccountingPeriod())
                            .append(" 项目 ").append(dto.getItemName()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 项目 " + dto.getItemName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error("导入资金运用规模表失败", e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
