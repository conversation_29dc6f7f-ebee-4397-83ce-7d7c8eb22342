package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.CreditRatingMapDTO;
import com.xl.alm.app.query.CreditRatingMapQuery;
import com.xl.alm.app.service.CreditRatingMapService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 信用评级映射表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/credit/rating/map")
public class CreditRatingMapController extends BaseController {

    @Autowired
    private CreditRatingMapService creditRatingMapService;

    /**
     * 查询信用评级映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:list')")
    @GetMapping("/list")
    public TableDataInfo list(CreditRatingMapQuery creditRatingMapQuery) {
        startPage();
        List<CreditRatingMapDTO> list = creditRatingMapService.selectCreditRatingMapDtoList(creditRatingMapQuery);
        return getDataTable(list);
    }

    /**
     * 导出信用评级映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:export')")
    @Log(title = "信用评级映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CreditRatingMapQuery creditRatingMapQuery) {
        ExcelUtil<CreditRatingMapDTO> util = new ExcelUtil<>(CreditRatingMapDTO.class);
        List<CreditRatingMapDTO> list = creditRatingMapService.selectCreditRatingMapDtoList(creditRatingMapQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "信用评级映射表数据", response);
    }

    /**
     * 获取信用评级映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:query')")
    @GetMapping(value = "/info/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(creditRatingMapService.selectCreditRatingMapDtoById(id));
    }

    /**
     * 新增信用评级映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:add')")
    @Log(title = "信用评级映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody CreditRatingMapDTO creditRatingMapDTO) {
        return toAjax(creditRatingMapService.insertCreditRatingMapDto(creditRatingMapDTO));
    }

    /**
     * 修改信用评级映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:edit')")
    @Log(title = "信用评级映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody CreditRatingMapDTO creditRatingMapDTO) {
        return toAjax(creditRatingMapService.updateCreditRatingMapDto(creditRatingMapDTO));
    }

    /**
     * 删除信用评级映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:remove')")
    @Log(title = "信用评级映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(creditRatingMapService.deleteCreditRatingMapDtoByIds(ids));
    }

    /**
     * 批量新增信用评级映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:add')")
    @Log(title = "信用评级映射表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Validated @RequestBody List<CreditRatingMapDTO> creditRatingMapDTOList) {
        return toAjax(creditRatingMapService.batchInsertCreditRatingMapDto(creditRatingMapDTOList));
    }

    /**
     * 根据账期删除信用评级映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:remove')")
    @Log(title = "信用评级映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(creditRatingMapService.deleteCreditRatingMapDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入信用评级映射表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:import')")
    @Log(title = "信用评级映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CreditRatingMapDTO> util = new ExcelUtil(CreditRatingMapDTO.class);
        List<CreditRatingMapDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = creditRatingMapService.importCreditRatingMapDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取信用评级映射表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:credit:rating:map:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CreditRatingMapDTO> util = new ExcelUtil<>(CreditRatingMapDTO.class);
        util.exportTemplateExcel(response, "信用评级映射表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 信用评级映射表数据列表
     */
    private void convertDictValueToLabel(List<CreditRatingMapDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (CreditRatingMapDTO dto : list) {
            // 转换原始信用评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getCreditRating())) {
                String creditRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getCreditRating(),
                        "ast_credit_rating"
                );
                dto.setCreditRating(creditRatingLabel);
            }

            // 转换信用评级表使用的评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getCreditRatingTableUsed())) {
                String creditRatingTableUsedLabel = DictConvertUtil.convertValueToLabel(
                        dto.getCreditRatingTableUsed(),
                        "ast_credit_rating"
                );
                dto.setCreditRatingTableUsed(creditRatingTableUsedLabel);
            }

            // 转换折现曲线使用评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getDiscountCurveRating())) {
                String discountCurveRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDiscountCurveRating(),
                        "ast_credit_rating"
                );
                dto.setDiscountCurveRating(discountCurveRatingLabel);
            }
        }
    }
}
