package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.CashFlowStatementDTO;
import com.xl.alm.app.query.CashFlowStatementQuery;
import com.xl.alm.app.service.CashFlowStatementService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 现金流量表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/cash/flow/statement")
public class CashFlowStatementController extends BaseController {

    @Autowired
    private CashFlowStatementService cashFlowStatementService;

    /**
     * 查询现金流量表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:list')")
    @GetMapping("/list")
    public TableDataInfo list(CashFlowStatementQuery cashFlowStatementQuery) {
        startPage();
        List<CashFlowStatementDTO> list = cashFlowStatementService.selectCashFlowStatementDtoList(cashFlowStatementQuery);
        return getDataTable(list);
    }

    /**
     * 导出现金流量表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:export')")
    @Log(title = "现金流量表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CashFlowStatementQuery cashFlowStatementQuery) {
        List<CashFlowStatementDTO> list = cashFlowStatementService.selectCashFlowStatementDtoList(cashFlowStatementQuery);
        ExcelUtil<CashFlowStatementDTO> util = new ExcelUtil<>(CashFlowStatementDTO.class);
        util.exportExcel(list, "现金流量表数据", response);
    }

    /**
     * 获取现金流量表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(cashFlowStatementService.selectCashFlowStatementDtoById(id));
    }

    /**
     * 新增现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:add')")
    @Log(title = "现金流量表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody CashFlowStatementDTO cashFlowStatementDto) {
        return toAjax(cashFlowStatementService.insertCashFlowStatementDto(cashFlowStatementDto));
    }

    /**
     * 修改现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:edit')")
    @Log(title = "现金流量表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody CashFlowStatementDTO cashFlowStatementDto) {
        return toAjax(cashFlowStatementService.updateCashFlowStatementDto(cashFlowStatementDto));
    }

    /**
     * 删除现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:remove')")
    @Log(title = "现金流量表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(cashFlowStatementService.deleteCashFlowStatementDtoByIds(ids));
    }

    /**
     * 获取现金流量表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CashFlowStatementDTO> util = new ExcelUtil<>(CashFlowStatementDTO.class);
        util.exportTemplateExcel(response, "现金流量表");
    }

    /**
     * 导入现金流量表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:cash:flow:statement:import')")
    @Log(title = "现金流量表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<CashFlowStatementDTO> util = new ExcelUtil<>(CashFlowStatementDTO.class);
        List<CashFlowStatementDTO> cashFlowStatementList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = cashFlowStatementService.importCashFlowStatementDto(cashFlowStatementList, updateSupport, operName);
        return Result.success(message);
    }
}
