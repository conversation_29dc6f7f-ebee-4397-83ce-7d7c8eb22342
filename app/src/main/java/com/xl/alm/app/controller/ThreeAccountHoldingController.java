package com.xl.alm.app.controller;

import com.xl.alm.app.dto.ThreeAccountHoldingDTO;
import com.xl.alm.app.dto.ThreeAccountHoldingImportDTO;
import com.xl.alm.app.query.ThreeAccountHoldingQuery;
import com.xl.alm.app.service.ThreeAccountHoldingService;
import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * TB0002-三账户持仓表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/three/account/holding")
public class ThreeAccountHoldingController extends BaseController {

    @Autowired
    private ThreeAccountHoldingService threeAccountHoldingService;

    /**
     * 查询三账户持仓列表
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:list')")
    @GetMapping("/list")
    public TableDataInfo list(ThreeAccountHoldingQuery threeAccountHoldingQuery) {
        startPage();
        List<ThreeAccountHoldingDTO> list = threeAccountHoldingService.selectThreeAccountHoldingDtoList(threeAccountHoldingQuery);
        return getDataTable(list);
    }

    /**
     * 导出三账户持仓列表
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:export')")
    @Log(title = "三账户持仓", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThreeAccountHoldingQuery threeAccountHoldingQuery) {
        ExcelUtil<ThreeAccountHoldingDTO> util = new ExcelUtil<>(ThreeAccountHoldingDTO.class);
        List<ThreeAccountHoldingDTO> list = threeAccountHoldingService.selectThreeAccountHoldingDtoList(threeAccountHoldingQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "三账户持仓数据", response);
    }

    /**
     * 获取三账户持仓详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(threeAccountHoldingService.selectThreeAccountHoldingDtoById(id));
    }

    /**
     * 新增三账户持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:add')")
    @Log(title = "三账户持仓", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody ThreeAccountHoldingDTO threeAccountHoldingDTO) {
        return toAjax(threeAccountHoldingService.insertThreeAccountHoldingDto(threeAccountHoldingDTO));
    }

    /**
     * 修改三账户持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:edit')")
    @Log(title = "三账户持仓", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody ThreeAccountHoldingDTO threeAccountHoldingDTO) {
        return toAjax(threeAccountHoldingService.updateThreeAccountHoldingDto(threeAccountHoldingDTO));
    }

    /**
     * 删除三账户持仓
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:remove')")
    @Log(title = "三账户持仓", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(threeAccountHoldingService.deleteThreeAccountHoldingDtoByIds(ids));
    }

    /**
     * 获取三账户持仓导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ThreeAccountHoldingImportDTO> util = new ExcelUtil<>(ThreeAccountHoldingImportDTO.class);
        util.exportTemplateExcel(response, "三账户持仓表");
    }

    /**
     * 导入三账户持仓数据
     */
    @PreAuthorize("@ss.hasPermi('ast:three:account:holding:import')")
    @Log(title = "三账户持仓", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport, String accountingPeriod) throws Exception {
        ExcelUtil<ThreeAccountHoldingImportDTO> util = new ExcelUtil<ThreeAccountHoldingImportDTO>(ThreeAccountHoldingImportDTO.class);
        List<ThreeAccountHoldingImportDTO> threeAccountHoldingList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = threeAccountHoldingService.importThreeAccountHoldingDto(threeAccountHoldingList, updateSupport, username, accountingPeriod);
        return Result.success(message);
    }

    /**
     * 将字典值转换为中文标签用于导出
     * TB0002表中会计类型字段不使用字典，直接显示原始值
     *
     * @param list 三账户持仓数据列表
     */
    private void convertDictValueToLabel(List<ThreeAccountHoldingDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // TB0002表按照用户要求，会计类型字段不使用字典转换
        // 所有字段都直接显示原始值，无需字典转换
        // 这里保留方法结构，以备将来需要添加其他字典转换

        // 注意：如果将来需要添加字典转换，可以参考以下模式：
        // for (ThreeAccountHoldingDTO dto : list) {
        //     // 示例：转换某个字段的字典值
        //     // if (StringUtils.isNotEmpty(dto.getSomeField())) {
        //     //     String label = DictConvertUtil.convertValueToLabel(
        //     //         dto.getSomeField(), "dict_type_name");
        //     //     dto.setSomeField(label);
        //     // }
        // }
    }
}
