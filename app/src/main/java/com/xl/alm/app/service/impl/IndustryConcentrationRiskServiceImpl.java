package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.IndustryConcentrationRiskDTO;
import com.xl.alm.app.entity.IndustryConcentrationRiskEntity;
import com.xl.alm.app.mapper.IndustryConcentrationRiskMapper;
import com.xl.alm.app.query.IndustryConcentrationRiskQuery;
import com.xl.alm.app.service.IndustryConcentrationRiskService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 行业集中度风险表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class IndustryConcentrationRiskServiceImpl implements IndustryConcentrationRiskService {

    @Autowired
    private IndustryConcentrationRiskMapper industryConcentrationRiskMapper;

    /**
     * 查询行业集中度风险表列表
     *
     * @param industryConcentrationRiskQuery 行业集中度风险表查询条件
     * @return 行业集中度风险表列表
     */
    @Override
    public List<IndustryConcentrationRiskDTO> selectIndustryConcentrationRiskDtoList(IndustryConcentrationRiskQuery industryConcentrationRiskQuery) {
        List<IndustryConcentrationRiskEntity> entityList = industryConcentrationRiskMapper.selectIndustryConcentrationRiskList(industryConcentrationRiskQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, IndustryConcentrationRiskDTO.class);
    }

    /**
     * 用id查询行业集中度风险表
     *
     * @param id id
     * @return 行业集中度风险表
     */
    @Override
    public IndustryConcentrationRiskDTO selectIndustryConcentrationRiskDtoById(Long id) {
        IndustryConcentrationRiskEntity entity = industryConcentrationRiskMapper.selectIndustryConcentrationRiskById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, IndustryConcentrationRiskDTO.class);
    }

    /**
     * 根据账期和行业名称查询行业集中度风险表
     *
     * @param accountingPeriod 账期
     * @param industryName     行业名称
     * @return 行业集中度风险表
     */
    @Override
    public IndustryConcentrationRiskDTO selectIndustryConcentrationRiskDtoByCondition(
            String accountingPeriod,
            String industryName) {
        IndustryConcentrationRiskEntity entity = industryConcentrationRiskMapper.selectIndustryConcentrationRiskByCondition(
                accountingPeriod,
                industryName);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, IndustryConcentrationRiskDTO.class);
    }

    /**
     * 新增行业集中度风险表
     *
     * @param dto 行业集中度风险表
     * @return 结果
     */
    @Override
    public int addIndustryConcentrationRiskDto(IndustryConcentrationRiskDTO dto) {
        IndustryConcentrationRiskEntity entity = EntityDtoConvertUtil.convertToEntity(dto, IndustryConcentrationRiskEntity.class);
        return industryConcentrationRiskMapper.insertIndustryConcentrationRisk(entity);
    }

    /**
     * 修改行业集中度风险表
     *
     * @param dto 行业集中度风险表
     * @return 结果
     */
    @Override
    public int updateIndustryConcentrationRiskDto(IndustryConcentrationRiskDTO dto) {
        IndustryConcentrationRiskEntity entity = EntityDtoConvertUtil.convertToEntity(dto, IndustryConcentrationRiskEntity.class);
        return industryConcentrationRiskMapper.updateIndustryConcentrationRisk(entity);
    }

    /**
     * 批量删除行业集中度风险表
     *
     * @param ids 需要删除的行业集中度风险表主键集合
     * @return 结果
     */
    @Override
    public int deleteIndustryConcentrationRiskDtoByIds(Long[] ids) {
        return industryConcentrationRiskMapper.deleteIndustryConcentrationRiskByIds(ids);
    }

    /**
     * 删除行业集中度风险表信息
     *
     * @param id 行业集中度风险表主键
     * @return 结果
     */
    @Override
    public int deleteIndustryConcentrationRiskDtoById(Long id) {
        return industryConcentrationRiskMapper.deleteIndustryConcentrationRiskById(id);
    }

    /**
     * 批量插入行业集中度风险表数据
     *
     * @param industryConcentrationRiskDtoList 行业集中度风险表列表
     * @return 影响行数
     */
    @Override
    public int batchInsertIndustryConcentrationRiskDto(List<IndustryConcentrationRiskDTO> industryConcentrationRiskDtoList) {
        if (industryConcentrationRiskDtoList == null || industryConcentrationRiskDtoList.isEmpty()) {
            return 0;
        }
        List<IndustryConcentrationRiskEntity> entityList = EntityDtoConvertUtil.convertToEntityList(industryConcentrationRiskDtoList, IndustryConcentrationRiskEntity.class);
        return industryConcentrationRiskMapper.batchInsertIndustryConcentrationRisk(entityList);
    }

    /**
     * 删除指定账期的行业集中度风险表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteIndustryConcentrationRiskDtoByPeriod(String accountingPeriod) {
        return industryConcentrationRiskMapper.deleteIndustryConcentrationRiskByPeriod(accountingPeriod);
    }

    /**
     * 导入行业集中度风险表
     *
     * @param dtoList       行业集中度风险表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importIndustryConcentrationRiskDto(List<IndustryConcentrationRiskDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入行业集中度风险表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (IndustryConcentrationRiskDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                IndustryConcentrationRiskDTO existDto = this.selectIndustryConcentrationRiskDtoByCondition(
                        dto.getAccountingPeriod(),
                        dto.getIndustryName());
                if (StringUtils.isNull(existDto)) {
                    this.addIndustryConcentrationRiskDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    this.updateIndustryConcentrationRiskDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}