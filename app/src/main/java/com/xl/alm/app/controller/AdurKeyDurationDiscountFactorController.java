package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AdurKeyDurationDiscountFactorDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurKeyDurationDiscountFactorQuery;
import com.xl.alm.app.service.AdurKeyDurationDiscountFactorService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期折现因子表含价差Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/key/duration/discount/factor")
public class AdurKeyDurationDiscountFactorController extends BaseController {

    @Autowired
    private AdurKeyDurationDiscountFactorService adurKeyDurationDiscountFactorService;

    /**
     * 查询ADUR关键久期折现因子表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurKeyDurationDiscountFactorQuery adurKeyDurationDiscountFactorQuery) {
        startPage();
        List<AdurKeyDurationDiscountFactorDTO> list = adurKeyDurationDiscountFactorService.selectAdurKeyDurationDiscountFactorDtoList(adurKeyDurationDiscountFactorQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期折现因子表含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:export')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurKeyDurationDiscountFactorQuery adurKeyDurationDiscountFactorQuery) {
        List<AdurKeyDurationDiscountFactorDTO> list = adurKeyDurationDiscountFactorService.selectAdurKeyDurationDiscountFactorDtoList(adurKeyDurationDiscountFactorQuery);
        ExcelUtil<AdurKeyDurationDiscountFactorDTO> util = new ExcelUtil<AdurKeyDurationDiscountFactorDTO>(AdurKeyDurationDiscountFactorDTO.class);
        util.exportExcel(list, "ADUR关键久期折现因子表含价差数据", response);
    }

    /**
     * 获取ADUR关键久期折现因子表含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurKeyDurationDiscountFactorService.selectAdurKeyDurationDiscountFactorDtoById(id));
    }

    /**
     * 新增ADUR关键久期折现因子表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:add')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurKeyDurationDiscountFactorDTO adurKeyDurationDiscountFactorDTO) {
        adurKeyDurationDiscountFactorDTO.setCreateBy(getUsername());
        return toAjax(adurKeyDurationDiscountFactorService.insertAdurKeyDurationDiscountFactorDto(adurKeyDurationDiscountFactorDTO));
    }

    /**
     * 修改ADUR关键久期折现因子表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:edit')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurKeyDurationDiscountFactorDTO adurKeyDurationDiscountFactorDTO) {
        adurKeyDurationDiscountFactorDTO.setUpdateBy(getUsername());
        return toAjax(adurKeyDurationDiscountFactorService.updateAdurKeyDurationDiscountFactorDto(adurKeyDurationDiscountFactorDTO));
    }

    /**
     * 删除ADUR关键久期折现因子表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurKeyDurationDiscountFactorService.deleteAdurKeyDurationDiscountFactorDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期折现因子表含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:remove')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurKeyDurationDiscountFactorService.deleteAdurKeyDurationDiscountFactorDtoByAccountPeriod(accountPeriod));
    }

    /**
     * 获取ADUR关键久期折现因子表含价差导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AdurKeyDurationDiscountFactorDTO> util = new ExcelUtil<AdurKeyDurationDiscountFactorDTO>(AdurKeyDurationDiscountFactorDTO.class);
        util.exportTemplateExcel(response, "ADUR关键久期折现因子表含价差数据");
    }

    /**
     * 导入ADUR关键久期折现因子表含价差数据
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:import')")
    @Log(title = "ADUR关键久期折现因子表含价差", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AdurKeyDurationDiscountFactorDTO> util = new ExcelUtil<AdurKeyDurationDiscountFactorDTO>(AdurKeyDurationDiscountFactorDTO.class);
        List<AdurKeyDurationDiscountFactorDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = adurKeyDurationDiscountFactorService.importAdurKeyDurationDiscountFactorDto(dtoList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurKeyDurationDiscountFactorDTO dto = adurKeyDurationDiscountFactorService.selectAdurKeyDurationDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getKeyDurationDiscountFactorSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:discount:factor:edit')")
    @Log(title = "关键久期折现因子期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurKeyDurationDiscountFactorDTO dto = adurKeyDurationDiscountFactorService.selectAdurKeyDurationDiscountFactorDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setKeyDurationDiscountFactorSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurKeyDurationDiscountFactorService.updateAdurKeyDurationDiscountFactorDto(dto));
    }
}
