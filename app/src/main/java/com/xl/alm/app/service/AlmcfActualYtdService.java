package com.xl.alm.app.service;

import com.xl.alm.app.dto.AlmcfActualYtdDTO;
import com.xl.alm.app.query.AlmcfActualYtdQuery;

import java.util.List;

/**
 * ALMCF实际发生数本年累计表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AlmcfActualYtdService {

    /**
     * 查询ALMCF实际发生数本年累计表列表
     *
     * @param almcfActualYtdQuery ALMCF实际发生数本年累计表查询条件
     * @return ALMCF实际发生数本年累计表列表
     */
    List<AlmcfActualYtdDTO> selectAlmcfActualYtdDtoList(AlmcfActualYtdQuery almcfActualYtdQuery);

    /**
     * 用id查询ALMCF实际发生数本年累计表
     *
     * @param id id
     * @return ALMCF实际发生数本年累计表
     */
    AlmcfActualYtdDTO selectAlmcfActualYtdDtoById(Long id);

    /**
     * 根据账期查询ALMCF实际发生数本年累计表
     *
     * @param accountingPeriod 账期
     * @return ALMCF实际发生数本年累计表列表
     */
    List<AlmcfActualYtdDTO> selectAlmcfActualYtdDtoByPeriod(String accountingPeriod);

    /**
     * 根据账期和项目名称查询ALMCF实际发生数本年累计表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return ALMCF实际发生数本年累计表
     */
    AlmcfActualYtdDTO selectAlmcfActualYtdDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增ALMCF实际发生数本年累计表
     *
     * @param almcfActualYtdDto ALMCF实际发生数本年累计表
     * @return 影响行数
     */
    int insertAlmcfActualYtdDto(AlmcfActualYtdDTO almcfActualYtdDto);

    /**
     * 批量新增ALMCF实际发生数本年累计表
     *
     * @param almcfActualYtdDtoList ALMCF实际发生数本年累计表列表
     * @return 影响行数
     */
    int batchInsertAlmcfActualYtdDto(List<AlmcfActualYtdDTO> almcfActualYtdDtoList);

    /**
     * 更新ALMCF实际发生数本年累计表
     *
     * @param almcfActualYtdDto ALMCF实际发生数本年累计表
     * @return 影响行数
     */
    int updateAlmcfActualYtdDto(AlmcfActualYtdDTO almcfActualYtdDto);

    /**
     * 删除ALMCF实际发生数本年累计表
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAlmcfActualYtdDtoById(Long id);

    /**
     * 批量删除ALMCF实际发生数本年累计表
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteAlmcfActualYtdDtoByIds(Long[] ids);

    /**
     * 根据账期删除ALMCF实际发生数本年累计表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAlmcfActualYtdDtoByPeriod(String accountingPeriod);

    /**
     * 导入ALMCF实际发生数本年累计表
     *
     * @param dtoList ALMCF实际发生数本年累计表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    String importAlmcfActualYtdDto(List<AlmcfActualYtdDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 计算ALMCF实际发生数本年累计表
     *
     * @param accountingPeriod 账期
     * @param username 操作用户
     * @return 结果
     */
    String calculateAlmcfActualYtdDto(String accountingPeriod, String username);
}
