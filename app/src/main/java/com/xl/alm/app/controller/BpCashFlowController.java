package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.SecurityUtils;
import com.xl.alm.app.dto.BpCashFlowDTO;
import com.xl.alm.app.dto.BpCashFlowImportDTO;
import com.xl.alm.app.query.BpCashFlowQuery;
import com.xl.alm.app.service.BpCashFlowService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * BP现金流量表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/bp/cash/flow")
public class BpCashFlowController extends BaseController {

    @Autowired
    private BpCashFlowService bpCashFlowService;

    /**
     * 查询BP现金流量表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:list')")
    @GetMapping("/list")
    public TableDataInfo list(BpCashFlowQuery query) {
        startPage();
        List<BpCashFlowDTO> list = bpCashFlowService.selectBpCashFlowDtoList(query);
        return getDataTable(list);
    }

    /**
     * 获取BP现金流量表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(bpCashFlowService.selectBpCashFlowDtoById(id));
    }

    /**
     * 根据条件查询BP现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("scenarioName") String scenarioName,
            @RequestParam("businessType") String businessType,
            @RequestParam("actuarialCode") String actuarialCode,
            @RequestParam("variableList") String variableList) {
        return Result.success(bpCashFlowService.selectBpCashFlowDtoByCondition(
                accountingPeriod, scenarioName, businessType, actuarialCode, variableList));
    }

    /**
     * 新增BP现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:add')")
    @Log(title = "BP现金流量表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody BpCashFlowDTO dto) {
        // 验证唯一性
        BpCashFlowDTO existDto = bpCashFlowService.selectBpCashFlowDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getBusinessType(),
                dto.getActuarialCode(), dto.getVariableList());
        if (existDto != null) {
            return Result.error("该账期、情景名称、业务类型、精算代码、变量列表组合已存在");
        }
        
        dto.setCreateBy(SecurityUtils.getUsername());
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(bpCashFlowService.insertBpCashFlowDto(dto));
    }

    /**
     * 修改BP现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:edit')")
    @Log(title = "BP现金流量表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody BpCashFlowDTO dto) {
        // 验证唯一性（排除自身）
        BpCashFlowDTO existDto = bpCashFlowService.selectBpCashFlowDtoByCondition(
                dto.getAccountingPeriod(), dto.getScenarioName(), dto.getBusinessType(),
                dto.getActuarialCode(), dto.getVariableList());
        if (existDto != null && !existDto.getId().equals(dto.getId())) {
            return Result.error("该账期、情景名称、业务类型、精算代码、变量列表组合已存在");
        }
        
        dto.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(bpCashFlowService.updateBpCashFlowDto(dto));
    }

    /**
     * 删除BP现金流量表
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:remove')")
    @Log(title = "BP现金流量表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(bpCashFlowService.deleteBpCashFlowDtoByIds(ids));
    }

    /**
     * 导出BP现金流量表（水平格式）
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:export')")
    @Log(title = "BP现金流量表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BpCashFlowQuery query) {
        try {
            bpCashFlowService.exportBpCashFlowHorizontal(response, query);
        } catch (Exception e) {
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取BP现金流量表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        try {
            bpCashFlowService.exportTemplate(response);
        } catch (Exception e) {
            throw new RuntimeException("模板下载失败：" + e.getMessage());
        }
    }

    /**
     * 导入BP现金流量表数据（横向Excel格式）
     */
    @PreAuthorize("@ss.hasPermi('cft:bp:cash:flow:import')")
    @Log(title = "BP现金流量表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) {
        try {
            String operName = SecurityUtils.getUsername();
            String message = bpCashFlowService.importBpCashFlowDataFromExcel(file.getInputStream(), updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error("导入失败：" + e.getMessage());
        }
    }
}
