package com.xl.alm.app.service;

import com.xl.alm.app.dto.FinancingLeverageRatioDTO;
import com.xl.alm.app.query.FinancingLeverageRatioQuery;

import java.util.List;

/**
 * 融资杠杆比例表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FinancingLeverageRatioService {

    /**
     * 查询融资杠杆比例表列表
     *
     * @param financingLeverageRatioQuery 融资杠杆比例表查询条件
     * @return 融资杠杆比例表列表
     */
    List<FinancingLeverageRatioDTO> selectFinancingLeverageRatioDtoList(FinancingLeverageRatioQuery financingLeverageRatioQuery);

    /**
     * 用id查询融资杠杆比例表
     *
     * @param id id
     * @return 融资杠杆比例表
     */
    FinancingLeverageRatioDTO selectFinancingLeverageRatioDtoById(Long id);

    /**
     * 根据账期和项目名称查询融资杠杆比例表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return 融资杠杆比例表
     */
    FinancingLeverageRatioDTO selectFinancingLeverageRatioDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增融资杠杆比例表
     *
     * @param financingLeverageRatioDTO 融资杠杆比例表
     * @return 结果
     */
    int insertFinancingLeverageRatioDto(FinancingLeverageRatioDTO financingLeverageRatioDTO);

    /**
     * 批量新增融资杠杆比例表
     *
     * @param financingLeverageRatioDtoList 融资杠杆比例表列表
     * @return 影响行数
     */
    int batchInsertFinancingLeverageRatioDto(List<FinancingLeverageRatioDTO> financingLeverageRatioDtoList);

    /**
     * 修改融资杠杆比例表
     *
     * @param financingLeverageRatioDTO 融资杠杆比例表
     * @return 结果
     */
    int updateFinancingLeverageRatioDto(FinancingLeverageRatioDTO financingLeverageRatioDTO);

    /**
     * 批量删除融资杠杆比例表
     *
     * @param ids 需要删除的融资杠杆比例表主键集合
     * @return 结果
     */
    int deleteFinancingLeverageRatioDtoByIds(Long[] ids);

    /**
     * 删除融资杠杆比例表信息
     *
     * @param id 融资杠杆比例表主键
     * @return 结果
     */
    int deleteFinancingLeverageRatioDtoById(Long id);

    /**
     * 导入融资杠杆比例表数据
     *
     * @param financingLeverageRatioDtoList 融资杠杆比例表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFinancingLeverageRatioDto(List<FinancingLeverageRatioDTO> financingLeverageRatioDtoList, Boolean isUpdateSupport, String operName);
}
