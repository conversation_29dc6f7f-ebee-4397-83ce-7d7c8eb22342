package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;

import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.NewPremiumSummaryDTO;
import com.xl.alm.app.dto.NewPremiumSummaryImportDTO;
import com.xl.alm.app.query.NewPremiumSummaryQuery;
import com.xl.alm.app.service.NewPremiumSummaryService;
import com.xl.alm.app.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 新单保费汇总表 Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/liab/new/premium/summary")
public class NewPremiumSummaryController extends BaseController {

    @Autowired
    private NewPremiumSummaryService newPremiumSummaryService;

    /**
     * 查询新单保费汇总列表
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:list')")
    @GetMapping("/list")
    public TableDataInfo list(NewPremiumSummaryQuery newPremiumSummaryQuery) {
        startPage();
        List<NewPremiumSummaryDTO> list = newPremiumSummaryService.selectNewPremiumSummaryDtoList(newPremiumSummaryQuery);
        return getDataTable(list);
    }

    /**
     * 获取新单保费汇总详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(newPremiumSummaryService.selectNewPremiumSummaryDtoById(id));
    }

    /**
     * 根据条件查询新单保费汇总
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("businessCode") String businessCode,
            @RequestParam("paymentFrequency") String paymentFrequency,
            @RequestParam("paymentPeriod") Integer paymentPeriod) {
        return Result.success(newPremiumSummaryService.selectNewPremiumSummaryDtoByCondition(
                accountingPeriod, businessCode, paymentFrequency, paymentPeriod));
    }

    /**
     * 新增新单保费汇总
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:add')")
    @Log(title = "新单保费汇总", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody NewPremiumSummaryDTO newPremiumSummaryDto) {
        return toAjax(newPremiumSummaryService.insertNewPremiumSummaryDto(newPremiumSummaryDto));
    }

    /**
     * 修改新单保费汇总
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:edit')")
    @Log(title = "新单保费汇总", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody NewPremiumSummaryDTO newPremiumSummaryDto) {
        return toAjax(newPremiumSummaryService.updateNewPremiumSummaryDto(newPremiumSummaryDto));
    }

    /**
     * 删除新单保费汇总
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:remove')")
    @Log(title = "新单保费汇总", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(newPremiumSummaryService.deleteNewPremiumSummaryDtoByIds(ids));
    }

    /**
     * 导出新单保费汇总列表
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:export')")
    @Log(title = "新单保费汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NewPremiumSummaryQuery newPremiumSummaryQuery) {
        List<NewPremiumSummaryDTO> list = newPremiumSummaryService.selectNewPremiumSummaryDtoList(newPremiumSummaryQuery);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<NewPremiumSummaryDTO> util = new ExcelUtil<NewPremiumSummaryDTO>(NewPremiumSummaryDTO.class);
        util.exportExcel(list, "新单保费汇总数据", response);
    }

    /**
     * 获取新单保费汇总导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<NewPremiumSummaryImportDTO> util = new ExcelUtil<NewPremiumSummaryImportDTO>(NewPremiumSummaryImportDTO.class);
        util.exportTemplateExcel(response, "新单保费汇总数据");
    }

    /**
     * 导入新单保费汇总数据
     */
    @PreAuthorize("@ss.hasPermi('liab:new:premium:summary:import')")
    @Log(title = "新单保费汇总", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file) throws Exception {
        ExcelUtil<NewPremiumSummaryImportDTO> util = new ExcelUtil<NewPremiumSummaryImportDTO>(NewPremiumSummaryImportDTO.class);
        List<NewPremiumSummaryImportDTO> importList = util.importExcel(file.getInputStream());

        // 转换导入DTO为完整DTO
        List<NewPremiumSummaryDTO> newPremiumSummaryList = convertImportDtoToDto(importList);

        String operName = getUsername();
        try {
            String message = newPremiumSummaryService.importNewPremiumSummaryDto(newPremiumSummaryList, false, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 新单保费汇总数据列表
     */
    private void convertDictCodeToLabel(List<NewPremiumSummaryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取各种字典数据
            List<SysDictData> paymentFrequencyDict = dictTypeService.selectDictDataByType("liab_payment_frequency");
            List<SysDictData> designTypeDict = dictTypeService.selectDictDataByType("cost_design_type");
            List<SysDictData> shortTermFlagDict = dictTypeService.selectDictDataByType("dur_is_short_term");
            List<SysDictData> termTypeDict = dictTypeService.selectDictDataByType("cost_term_type");

            // 为每条记录转换字典编码为中文标签
            for (NewPremiumSummaryDTO dto : list) {
                // 转换缴费频率
                dto.setPaymentFrequency(convertCodeToLabel(dto.getPaymentFrequency(), paymentFrequencyDict));

                // 转换设计类型
                dto.setDesignType(convertCodeToLabel(dto.getDesignType(), designTypeDict));

                // 转换是否中短
                dto.setShortTermFlag(convertCodeToLabel(dto.getShortTermFlag(), shortTermFlagDict));

                // 转换长短期标识
                dto.setTermType(convertCodeToLabel(dto.getTermType(), termTypeDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            log.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将编码转换为标签
     *
     * @param code 编码
     * @param dictDataList 字典数据列表
     * @return 标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        return code;
    }

    /**
     * 将导入DTO转换为完整DTO
     *
     * @param importList 导入DTO列表
     * @return 完整DTO列表
     */
    private List<NewPremiumSummaryDTO> convertImportDtoToDto(List<NewPremiumSummaryImportDTO> importList) {
        List<NewPremiumSummaryDTO> result = new ArrayList<>();

        for (NewPremiumSummaryImportDTO importDto : importList) {
            NewPremiumSummaryDTO dto = new NewPremiumSummaryDTO();

            // 复制用户填写的字段
            dto.setAccountingPeriod(importDto.getAccountingPeriod());
            dto.setBusinessCode(importDto.getBusinessCode());
            dto.setPaymentFrequency(importDto.getPaymentFrequency());
            dto.setPaymentPeriod(importDto.getPaymentPeriod());
            dto.setOriginalPremium(importDto.getOriginalPremium());
            dto.setInitialFee(importDto.getInitialFee());
            dto.setRemark(importDto.getRemark());

            result.add(dto);
        }

        return result;
    }
}
