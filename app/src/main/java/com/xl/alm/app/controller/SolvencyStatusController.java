package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.SolvencyStatusDTO;
import com.xl.alm.app.query.SolvencyStatusQuery;
import com.xl.alm.app.service.SolvencyStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 偿付能力状况表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/solvency/status")
public class SolvencyStatusController extends BaseController {

    @Autowired
    private SolvencyStatusService solvencyStatusService;

    /**
     * 查询偿付能力状况表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:list')")
    @GetMapping("/list")
    public TableDataInfo list(SolvencyStatusQuery solvencyStatusQuery) {
        startPage();
        List<SolvencyStatusDTO> list = solvencyStatusService.selectSolvencyStatusDtoList(solvencyStatusQuery);
        return getDataTable(list);
    }

    /**
     * 获取偿付能力状况表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(solvencyStatusService.selectSolvencyStatusDtoById(id));
    }

    /**
     * 根据条件查询偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:query')")
    @GetMapping("/condition")
    public Result getByCondition(
            @RequestParam("accountingPeriod") String accountingPeriod,
            @RequestParam("itemName") String itemName) {
        return Result.success(solvencyStatusService.selectSolvencyStatusDtoByCondition(accountingPeriod, itemName));
    }

    /**
     * 新增偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:add')")
    @Log(title = "偿付能力状况表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody SolvencyStatusDTO solvencyStatusDTO) {
        return toAjax(solvencyStatusService.insertSolvencyStatusDto(solvencyStatusDTO));
    }

    /**
     * 修改偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:edit')")
    @Log(title = "偿付能力状况表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody SolvencyStatusDTO solvencyStatusDTO) {
        return toAjax(solvencyStatusService.updateSolvencyStatusDto(solvencyStatusDTO));
    }

    /**
     * 删除偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:remove')")
    @Log(title = "偿付能力状况表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(solvencyStatusService.deleteSolvencyStatusDtoByIds(ids));
    }

    /**
     * 导入偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:import')")
    @Log(title = "偿付能力状况表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SolvencyStatusDTO> util = new ExcelUtil<>(SolvencyStatusDTO.class);
        List<SolvencyStatusDTO> solvencyStatusList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = solvencyStatusService.importSolvencyStatusDto(solvencyStatusList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 导出偿付能力状况表
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:export')")
    @Log(title = "偿付能力状况表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SolvencyStatusQuery query) {
        List<SolvencyStatusDTO> list = solvencyStatusService.selectSolvencyStatusDtoList(query);
        ExcelUtil<SolvencyStatusDTO> util = new ExcelUtil<>(SolvencyStatusDTO.class);
        util.exportExcel(list, "偿付能力状况表数据", response);
    }

    /**
     * 获取偿付能力状况表模板Excel
     */
    @PreAuthorize("@ss.hasPermi('asm:solvency:status:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SolvencyStatusDTO> util = new ExcelUtil<>(SolvencyStatusDTO.class);
        util.exportTemplateExcel(response, "偿付能力状况表");
    }
}
