package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.LiabScaleDetailDTO;
import com.xl.alm.app.query.LiabScaleDetailQuery;
import com.xl.alm.app.service.LiabScaleDetailService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 负债规模明细表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/liab/scale/detail")
public class LiabScaleDetailController extends BaseController {

    @Autowired
    private LiabScaleDetailService liabScaleDetailService;

    /**
     * 查询负债规模明细表列表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(LiabScaleDetailQuery liabScaleDetailQuery) {
        startPage();
        List<LiabScaleDetailDTO> list = liabScaleDetailService.selectLiabScaleDetailDtoList(liabScaleDetailQuery);
        return getDataTable(list);
    }

    /**
     * 获取负债规模明细表详细信息
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(liabScaleDetailService.selectLiabScaleDetailDtoById(id));
    }

    /**
     * 新增负债规模明细表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:add')")
    @Log(title = "负债规模明细表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody LiabScaleDetailDTO liabScaleDetailDTO) {
        return toAjax(liabScaleDetailService.addLiabScaleDetailDto(liabScaleDetailDTO));
    }

    /**
     * 修改负债规模明细表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:edit')")
    @Log(title = "负债规模明细表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody LiabScaleDetailDTO liabScaleDetailDTO) {
        return toAjax(liabScaleDetailService.updateLiabScaleDetailDto(liabScaleDetailDTO));
    }

    /**
     * 删除负债规模明细表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:remove')")
    @Log(title = "负债规模明细表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(liabScaleDetailService.deleteLiabScaleDetailDtoByIds(ids));
    }

    /**
     * 导出负债规模明细表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:export')")
    @Log(title = "负债规模明细表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiabScaleDetailQuery liabScaleDetailQuery) {
        List<LiabScaleDetailDTO> list = liabScaleDetailService.selectLiabScaleDetailDtoList(liabScaleDetailQuery);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<LiabScaleDetailDTO> util = new ExcelUtil<>(LiabScaleDetailDTO.class);
        util.exportExcel(list, "负债规模明细表数据", response);
    }

    /**
     * 获取负债规模明细表导入模板
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<LiabScaleDetailDTO> util = new ExcelUtil<>(LiabScaleDetailDTO.class);
        util.exportTemplateExcel(response, "负债规模明细表");
    }

    /**
     * 导入负债规模明细表
     */
    @PreAuthorize("@ss.hasPermi('liab:scale:detail:import')")
    @Log(title = "负债规模明细表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<LiabScaleDetailDTO> util = new ExcelUtil<>(LiabScaleDetailDTO.class);
        List<LiabScaleDetailDTO> liabScaleDetailList = util.importExcel(file.getInputStream());
        String username = getUsername();
        String message = liabScaleDetailService.importLiabScaleDetailDto(liabScaleDetailList, updateSupport, username);
        return Result.success(message);
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 负债规模明细表数据列表
     */
    private void convertDictCodeToLabel(List<LiabScaleDetailDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取各种字典数据
            List<SysDictData> termTypeDict = dictTypeService.selectDictDataByType("cost_term_type");
            List<SysDictData> mainTypeDict = dictTypeService.selectDictDataByType("cost_insurance_main_type");
            List<SysDictData> subTypeDict = dictTypeService.selectDictDataByType("cost_insurance_sub_type");
            List<SysDictData> designTypeDict = dictTypeService.selectDictDataByType("cost_design_type");

            // 为每条记录转换字典编码为中文标签
            for (LiabScaleDetailDTO dto : list) {
                // 转换长短期标识
                dto.setTermFlag(convertCodeToLabel(dto.getTermFlag(), termTypeDict));

                // 转换险种主类
                dto.setInsuranceMainType(convertCodeToLabel(dto.getInsuranceMainType(), mainTypeDict));

                // 转换险种细类
                dto.setInsuranceSubType(convertCodeToLabel(dto.getInsuranceSubType(), subTypeDict));

                // 转换设计类型
                dto.setDesignType(convertCodeToLabel(dto.getDesignType(), designTypeDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            logger.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将编码转换为标签
     *
     * @param code 编码
     * @param dictDataList 字典数据列表
     * @return 标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        return code;
    }
}
