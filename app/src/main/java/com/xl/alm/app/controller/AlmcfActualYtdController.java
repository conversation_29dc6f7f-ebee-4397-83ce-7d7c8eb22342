package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AlmcfActualYtdDTO;
import com.xl.alm.app.query.AlmcfActualYtdQuery;
import com.xl.alm.app.service.AlmcfActualYtdService;
import com.xl.alm.app.util.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ALMCF实际发生数本年累计表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/cft/almcf/actual/ytd")
public class AlmcfActualYtdController extends BaseController {

    @Autowired
    private AlmcfActualYtdService almcfActualYtdService;

    /**
     * 查询ALMCF实际发生数本年累计表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:list')")
    @GetMapping("/list")
    public TableDataInfo list(AlmcfActualYtdQuery almcfActualYtdQuery) {
        startPage();
        List<AlmcfActualYtdDTO> list = almcfActualYtdService.selectAlmcfActualYtdDtoList(almcfActualYtdQuery);
        return getDataTable(list);
    }

    /**
     * 导出ALMCF实际发生数本年累计表列表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:export')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AlmcfActualYtdQuery almcfActualYtdQuery) {
        List<AlmcfActualYtdDTO> list = almcfActualYtdService.selectAlmcfActualYtdDtoList(almcfActualYtdQuery);
        ExcelUtil<AlmcfActualYtdDTO> util = new ExcelUtil<>(AlmcfActualYtdDTO.class);
        util.exportExcel(list, "ALMCF实际发生数本年累计表数据", response);
    }

    /**
     * 获取ALMCF实际发生数本年累计表详细信息
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(almcfActualYtdService.selectAlmcfActualYtdDtoById(id));
    }

    /**
     * 新增ALMCF实际发生数本年累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:add')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AlmcfActualYtdDTO almcfActualYtdDto) {
        return toAjax(almcfActualYtdService.insertAlmcfActualYtdDto(almcfActualYtdDto));
    }

    /**
     * 修改ALMCF实际发生数本年累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:edit')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AlmcfActualYtdDTO almcfActualYtdDto) {
        return toAjax(almcfActualYtdService.updateAlmcfActualYtdDto(almcfActualYtdDto));
    }

    /**
     * 删除ALMCF实际发生数本年累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:remove')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(almcfActualYtdService.deleteAlmcfActualYtdDtoByIds(ids));
    }

    /**
     * 获取ALMCF实际发生数本年累计表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AlmcfActualYtdDTO> util = new ExcelUtil<>(AlmcfActualYtdDTO.class);
        util.exportTemplateExcel(response, "ALMCF实际发生数本年累计表");
    }

    /**
     * 导入ALMCF实际发生数本年累计表数据
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:import')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AlmcfActualYtdDTO> util = new ExcelUtil<>(AlmcfActualYtdDTO.class);
        List<AlmcfActualYtdDTO> almcfActualYtdList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = almcfActualYtdService.importAlmcfActualYtdDto(almcfActualYtdList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 计算ALMCF实际发生数本年累计表
     */
    @PreAuthorize("@ss.hasPermi('cft:almcf:actual:ytd:calculate')")
    @Log(title = "ALMCF实际发生数本年累计表", businessType = BusinessType.OTHER)
    @PostMapping("/calculate")
    public Result calculate(@RequestParam String accountingPeriod) {
        String operName = getUsername();
        String message = almcfActualYtdService.calculateAlmcfActualYtdDto(accountingPeriod, operName);
        return Result.success(message);
    }
}
