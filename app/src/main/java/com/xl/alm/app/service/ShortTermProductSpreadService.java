package com.xl.alm.app.service;

import com.xl.alm.app.dto.ShortTermProductSpreadDTO;
import com.xl.alm.app.query.ShortTermProductSpreadQuery;

import java.util.List;

/**
 * 中短存续期产品利差表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface ShortTermProductSpreadService {

    /**
     * 查询中短存续期产品利差列表
     *
     * @param shortTermProductSpreadQuery 中短存续期产品利差查询条件
     * @return 中短存续期产品利差列表
     */
    List<ShortTermProductSpreadDTO> selectShortTermProductSpreadDtoList(ShortTermProductSpreadQuery shortTermProductSpreadQuery);

    /**
     * 用id查询中短存续期产品利差
     *
     * @param id id
     * @return 中短存续期产品利差
     */
    ShortTermProductSpreadDTO selectShortTermProductSpreadDtoById(Long id);

    /**
     * 根据账期和产品精算代码查询中短存续期产品利差
     *
     * @param accountingPeriod 账期
     * @param actuarialCode 产品精算代码
     * @return 中短存续期产品利差
     */
    ShortTermProductSpreadDTO selectShortTermProductSpreadDtoByCondition(
            String accountingPeriod,
            String actuarialCode);

    /**
     * 新增中短存续期产品利差
     *
     * @param dto 中短存续期产品利差
     * @return 影响行数
     */
    int insertShortTermProductSpreadDto(ShortTermProductSpreadDTO dto);

    /**
     * 批量插入中短存续期产品利差数据
     *
     * @param shortTermProductSpreadDtoList 中短存续期产品利差列表
     * @return 影响行数
     */
    int batchInsertShortTermProductSpreadDto(List<ShortTermProductSpreadDTO> shortTermProductSpreadDtoList);

    /**
     * 更新中短存续期产品利差数据
     *
     * @param dto 中短存续期产品利差
     * @return 结果
     */
    int updateShortTermProductSpreadDto(ShortTermProductSpreadDTO dto);

    /**
     * 删除指定账期的中短存续期产品利差数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteShortTermProductSpreadDtoByPeriod(String accountingPeriod);

    /**
     * 删除指定id的中短存续期产品利差数据
     *
     * @param id id
     * @return 影响行数
     */
    int deleteShortTermProductSpreadDtoById(Long id);

    /**
     * 批量删除中短存续期产品利差数据
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteShortTermProductSpreadDtoByIds(Long[] ids);

    /**
     * 导入中短存续期产品利差
     *
     * @param dtoList       中短存续期产品利差数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    String importShortTermProductSpreadDto(List<ShortTermProductSpreadDTO> dtoList, Boolean updateSupport, String username);
}
