package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.dto.FixedIncomeTermDistDTO;
import com.xl.alm.app.query.FixedIncomeTermDistQuery;
import com.xl.alm.app.service.FixedIncomeTermDistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 固定收益类投资资产剩余期限分布表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/asm/fixed/income/term/dist")
public class FixedIncomeTermDistController extends BaseController {

    @Autowired
    private FixedIncomeTermDistService fixedIncomeTermDistService;

    /**
     * 查询固定收益类投资资产剩余期限分布表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:list')")
    @GetMapping("/list")
    public TableDataInfo list(FixedIncomeTermDistQuery fixedIncomeTermDistQuery) {
        startPage();
        List<FixedIncomeTermDistDTO> list = fixedIncomeTermDistService.selectFixedIncomeTermDistDtoList(fixedIncomeTermDistQuery);
        return getDataTable(list);
    }

    /**
     * 导出固定收益类投资资产剩余期限分布表列表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:export')")
    @Log(title = "固定收益类投资资产剩余期限分布表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FixedIncomeTermDistQuery fixedIncomeTermDistQuery) {
        List<FixedIncomeTermDistDTO> list = fixedIncomeTermDistService.selectFixedIncomeTermDistDtoList(fixedIncomeTermDistQuery);
        convertDictValueToLabel(list);
        ExcelUtil<FixedIncomeTermDistDTO> util = new ExcelUtil<>(FixedIncomeTermDistDTO.class);
        util.exportExcel(list, "固定收益类投资资产剩余期限分布表", response);
    }

    /**
     * 获取固定收益类投资资产剩余期限分布表详细信息
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(fixedIncomeTermDistService.selectFixedIncomeTermDistDtoById(id));
    }

    /**
     * 根据条件查询固定收益类投资资产剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:query')")
    @GetMapping("/condition")
    public Result getByCondition(@RequestParam String accountingPeriod, 
                                 @RequestParam String domesticForeign, 
                                 @RequestParam String fixedIncomeTermCategory, 
                                 @RequestParam String remainingTermFlag) {
        return Result.success(fixedIncomeTermDistService.selectFixedIncomeTermDistDtoByCondition(accountingPeriod, domesticForeign, fixedIncomeTermCategory, remainingTermFlag));
    }

    /**
     * 新增固定收益类投资资产剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:add')")
    @Log(title = "固定收益类投资资产剩余期限分布表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody FixedIncomeTermDistDTO fixedIncomeTermDistDTO) {
        return toAjax(fixedIncomeTermDistService.insertFixedIncomeTermDistDto(fixedIncomeTermDistDTO));
    }

    /**
     * 修改固定收益类投资资产剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:edit')")
    @Log(title = "固定收益类投资资产剩余期限分布表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody FixedIncomeTermDistDTO fixedIncomeTermDistDTO) {
        return toAjax(fixedIncomeTermDistService.updateFixedIncomeTermDistDto(fixedIncomeTermDistDTO));
    }

    /**
     * 删除固定收益类投资资产剩余期限分布表
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:remove')")
    @Log(title = "固定收益类投资资产剩余期限分布表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(fixedIncomeTermDistService.deleteFixedIncomeTermDistDtoByIds(ids));
    }

    /**
     * 导入固定收益类投资资产剩余期限分布表数据
     */
    @PreAuthorize("@ss.hasPermi('asm:fixed:income:term:dist:import')")
    @Log(title = "固定收益类投资资产剩余期限分布表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<FixedIncomeTermDistDTO> util = new ExcelUtil<>(FixedIncomeTermDistDTO.class);
        List<FixedIncomeTermDistDTO> fixedIncomeTermDistList = util.importExcel(file.getInputStream());
        convertDictLabelToValue(fixedIncomeTermDistList);
        String operName = getUsername();
        try {
            String message = fixedIncomeTermDistService.importFixedIncomeTermDistDto(fixedIncomeTermDistList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 下载固定收益类投资资产剩余期限分布表导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<FixedIncomeTermDistDTO> util = new ExcelUtil<>(FixedIncomeTermDistDTO.class);
        util.exportExcel(new ArrayList<>(), "固定收益类投资资产剩余期限分布表数据", response);
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 固定收益类投资资产剩余期限分布表数据列表
     */
    private void convertDictValueToLabel(List<FixedIncomeTermDistDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeTermDistDTO dto : list) {
            // 转换境内外标识字典值
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertValueToLabel(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换固收资产剩余期限资产分类字典值
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertValueToLabel(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
            // 转换剩余期限标识字典值
            if (dto.getRemainingTermFlag() != null) {
                dto.setRemainingTermFlag(DictConvertUtil.convertValueToLabel(dto.getRemainingTermFlag(), "acm_remaining_term_flag"));
            }
        }
    }

    /**
     * 将字典标签转换为值用于导入
     *
     * @param list 固定收益类投资资产剩余期限分布表数据列表
     */
    private void convertDictLabelToValue(List<FixedIncomeTermDistDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (FixedIncomeTermDistDTO dto : list) {
            // 转换境内外标识字典标签
            if (dto.getDomesticForeign() != null) {
                dto.setDomesticForeign(DictConvertUtil.convertLabelToValue(dto.getDomesticForeign(), "ast_domestic_foreign"));
            }
            // 转换固收资产剩余期限资产分类字典标签
            if (dto.getFixedIncomeTermCategory() != null) {
                dto.setFixedIncomeTermCategory(DictConvertUtil.convertLabelToValue(dto.getFixedIncomeTermCategory(), "ast_fixed_income_term_category"));
            }
            // 转换剩余期限标识字典标签
            if (dto.getRemainingTermFlag() != null) {
                dto.setRemainingTermFlag(DictConvertUtil.convertLabelToValue(dto.getRemainingTermFlag(), "acm_remaining_term_flag"));
            }
        }
    }
}
