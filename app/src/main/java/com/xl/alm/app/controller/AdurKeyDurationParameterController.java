package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.AdurKeyDurationParameterDTO;
import com.xl.alm.app.query.AdurKeyDurationParameterQuery;
import com.xl.alm.app.service.AdurKeyDurationParameterService;
import com.xl.alm.app.util.AdurDataMigrationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR关键久期参数表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/key/duration/parameter")
public class AdurKeyDurationParameterController extends BaseController {

    @Autowired
    private AdurKeyDurationParameterService adurKeyDurationParameterService;

    /**
     * 查询ADUR关键久期参数列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurKeyDurationParameterQuery adurKeyDurationParameterQuery) {
        startPage();
        List<AdurKeyDurationParameterDTO> list = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(adurKeyDurationParameterQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR关键久期参数列表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:export')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurKeyDurationParameterQuery adurKeyDurationParameterQuery) {
        List<AdurKeyDurationParameterDTO> list = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(adurKeyDurationParameterQuery);
        ExcelUtil<AdurKeyDurationParameterDTO> util = new ExcelUtil<AdurKeyDurationParameterDTO>(AdurKeyDurationParameterDTO.class);
        util.exportExcel(response, list, "ADUR关键久期参数数据");
    }

    /**
     * 获取ADUR关键久期参数详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoById(id));
    }

    /**
     * 新增ADUR关键久期参数
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:add')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurKeyDurationParameterDTO adurKeyDurationParameterDTO) {
        adurKeyDurationParameterDTO.setCreateBy(getUsername());
        return toAjax(adurKeyDurationParameterService.insertAdurKeyDurationParameterDto(adurKeyDurationParameterDTO));
    }

    /**
     * 修改ADUR关键久期参数
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:edit')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurKeyDurationParameterDTO adurKeyDurationParameterDTO) {
        adurKeyDurationParameterDTO.setUpdateBy(getUsername());
        return toAjax(adurKeyDurationParameterService.updateAdurKeyDurationParameterDto(adurKeyDurationParameterDTO));
    }

    /**
     * 删除ADUR关键久期参数
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:remove')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurKeyDurationParameterService.deleteAdurKeyDurationParameterDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR关键久期参数
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:remove')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurKeyDurationParameterService.deleteAdurKeyDurationParameterDtoByAccountPeriod(accountPeriod));
    }

    /**
     * 获取ADUR关键久期参数导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AdurKeyDurationParameterDTO> util = new ExcelUtil<AdurKeyDurationParameterDTO>(AdurKeyDurationParameterDTO.class);
        util.importTemplateExcel(response, "ADUR关键久期参数数据");
    }

    /**
     * 导入ADUR关键久期参数数据
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:import')")
    @Log(title = "ADUR关键久期参数", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AdurKeyDurationParameterDTO> util = new ExcelUtil<AdurKeyDurationParameterDTO>(AdurKeyDurationParameterDTO.class);
        List<AdurKeyDurationParameterDTO> adurKeyDurationParameterList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = adurKeyDurationParameterService.importAdurKeyDurationParameterDto(adurKeyDurationParameterList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 从老表迁移数据到新表
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:migrate')")
    @Log(title = "ADUR关键久期参数数据迁移", businessType = BusinessType.UPDATE)
    @PostMapping("/migrateData")
    public Result migrateData() {
        String operName = getUsername();
        String message = adurKeyDurationParameterService.migrateDataFromOldTable(operName);
        return Result.success(message);
    }

    /**
     * 测试JSON处理逻辑（调试用）
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:migrate')")
    @PostMapping("/testJsonProcessing")
    public Result testJsonProcessing(@RequestBody String jsonData) {
        String report = AdurDataMigrationUtil.testJsonProcessing(jsonData);
        return Result.success(report);
    }

    /**
     * 查看老表原始数据（调试用）
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:migrate')")
    @GetMapping("/viewOldTableData")
    public Result viewOldTableData() {
        String report = adurKeyDurationParameterService.analyzeOldTableData();
        return Result.success(report);
    }

    /**
     * 验证JSON顺序（调试用）
     */
    @PreAuthorize("@ss.hasPermi('adur:key:duration:parameter:migrate')")
    @PostMapping("/validateJsonOrder")
    public Result validateJsonOrder(@RequestBody String jsonData) {
        String report = AdurDataMigrationUtil.validateJsonOrder(jsonData);
        return Result.success(report);
    }
}
