package com.xl.alm.app.service;

import com.xl.alm.app.dto.VarAnalysisImportDTO;
import com.xl.alm.app.entity.VarAnalysisEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Stack;

/**
 * VaR值分析表层级数据解析服务
 * 参考TB0001的实现，处理层级结构的VaR数据
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class VarAnalysisHierarchicalParserService {

    @Autowired
    private AssetDefinitionService assetDefinitionService;

    /**
     * 解析层级数据，提取资产明细
     * 
     * 处理格式：
     * 传统                                    <- 顶层分类
     *   境内资产                              <- 子分类
     *     传统固定收益类投资资产                <- 子分类
     *       债券型基金                        <- 子分类
     *         001045OF--华夏可转债增强债券A    <- 资产明细（有数值）
     *
     * @param importList 导入数据列表
     * @param dataType 数据类型
     * @param accountingPeriod 账期
     * @return 解析后的资产明细列表
     */
    public List<VarAnalysisEntity> parseHierarchicalData(List<VarAnalysisImportDTO> importList,
                                                        String dataType, String accountingPeriod) {
        List<VarAnalysisEntity> result = new ArrayList<>();
        Stack<String> categoryStack = new Stack<>();
        Set<String> processedKeys = new HashSet<>(); // 用于检查重复数据

        log.info("开始解析VaR层级数据，共{}行，数据类型：{}", importList.size(), dataType);

        for (int i = 0; i < importList.size(); i++) {
            VarAnalysisImportDTO row = importList.get(i);

            if (isAssetDetail(row)) {
                // 资产明细行：创建数据记录
                String topLevelCategory = getTopLevelCategory(categoryStack);
                if (StringUtils.isEmpty(topLevelCategory)) {
                    log.warn("第{}行资产明细缺少顶层分类，使用默认分类'传统'：{}", i + 1, row.getDimensionName());
                    topLevelCategory = "传统"; // 使用默认分类
                }

                VarAnalysisEntity assetData = createAssetRecord(row, topLevelCategory, dataType, accountingPeriod);

                // 检查重复数据
                String uniqueKey = generateUniqueKey(assetData);
                if (processedKeys.contains(uniqueKey)) {
                    log.warn("第{}行数据重复，跳过：{}", i + 1, uniqueKey);
                    continue;
                }

                processedKeys.add(uniqueKey);
                result.add(assetData);
                log.debug("解析VaR资产明细：账户={}, 维度名称={}, 证券代码={}, 市值={}",
                         topLevelCategory, row.getDimensionName(), assetData.getSecurityCode(), row.getMarketValue());

            } else if (isCategoryRow(row)) {
                // 分类行：更新层级栈
                updateCategoryStack(categoryStack, row.getDimensionName());
                log.info("识别VaR分类行：{}, 当前栈内容={}", row.getDimensionName(), categoryStack);
            } else {
                log.warn("第{}行数据格式不符合要求，跳过：维度名称={}, 市值={}, 是否资产明细={}, 是否分类行={}",
                        i + 1, row.getDimensionName(), row.getMarketValue(),
                        isAssetDetail(row), isCategoryRow(row));
            }
        }

        log.info("VaR层级数据解析完成，提取到{}条资产明细", result.size());
        return result;
    }

    /**
     * 判断是否为资产明细行
     * 标准：能从维度名称中提取到证券代码，参考TB0001的逻辑
     */
    private boolean isAssetDetail(VarAnalysisImportDTO row) {
        if (!StringUtils.hasText(row.getDimensionName())) {
            return false;
        }

        String dimensionName = row.getDimensionName().trim();

        // 如果包含--分隔符，尝试提取证券代码
        if (dimensionName.contains("--")) {
            String[] parts = dimensionName.split("--", 2);
            if (parts.length == 2) {
                String potentialCode = parts[0].trim();
                // 验证证券代码格式
                if (isValidSecurityCodeFormat(potentialCode)) {
                    return true;
                }
            }
        }

        // 如果维度名称本身就是证券代码格式
        if (isValidSecurityCodeFormat(dimensionName)) {
            return true;
        }

        // 既没有证券代码，也无法从维度名称提取，则不是资产明细
        return false;
    }

    /**
     * 判断是否为分类行
     * 标准：维度名称是已知的分类名称，且不包含证券代码格式
     * TB0003的特点：分类行也有数值数据，主要通过维度名称判断
     */
    private boolean isCategoryRow(VarAnalysisImportDTO row) {
        if (!StringUtils.hasText(row.getDimensionName())) {
            return false;
        }

        String dimensionName = row.getDimensionName().trim();

        // 如果已经是资产明细行，则不是分类行
        if (isAssetDetail(row)) {
            return false;
        }

        // 检查是否为已知的分类名称
        return isKnownCategoryName(dimensionName);
    }

    /**
     * 判断是否为已知的分类名称
     * 使用精确匹配，参考TB0001的逻辑
     */
    private boolean isKnownCategoryName(String categoryName) {
        // 顶层分类
        if (isTopLevelCategory(categoryName)) {
            return true;
        }

        // 二级分类
        if (isSecondLevelCategory(categoryName)) {
            return true;
        }

        // 精确匹配常见分类名称
        return "境内资产".equals(categoryName) ||
               "境外资产".equals(categoryName) ||
               "传统固定收益类投资资产".equals(categoryName) ||
               "非标准固定收益类投资资产".equals(categoryName) ||
               "权益类投资资产".equals(categoryName) ||
               "债券型基金".equals(categoryName) ||
               "股票型基金".equals(categoryName) ||
               "混合型基金".equals(categoryName) ||
               "货币市场基金".equals(categoryName) ||
               "政府债券".equals(categoryName) ||
               "企业债券".equals(categoryName) ||
               "公司债企业债".equals(categoryName) ||
               "金融债券".equals(categoryName) ||
               "中期票据".equals(categoryName) ||
               "短期融资券".equals(categoryName) ||
               "存款".equals(categoryName) ||
               "股票".equals(categoryName) ||
               "证券投资基金".equals(categoryName) ||
               "固定收益类保险资产管理产品".equals(categoryName) ||
               "不动产债权投资计划".equals(categoryName) ||
               "基础设施债权投资计划".equals(categoryName) ||
               "其他固定收益类金融产品".equals(categoryName) ||
               "信托计划".equals(categoryName) ||
               "未定义".equals(categoryName);
    }



    /**
     * 更新分类层级栈
     * 根据分类名称的特征判断层级
     */
    private void updateCategoryStack(Stack<String> categoryStack, String dimensionName) {
        String categoryName = dimensionName.trim();

        // 判断是否为顶层分类（传统、分红、万能、投连等）
        if (isTopLevelCategory(categoryName)) {
            categoryStack.clear();
            categoryStack.push(categoryName);
            log.debug("设置VaR顶层分类：{}", categoryName);
        }
        // 判断是否为二级分类
        else if (isSecondLevelCategory(categoryName)) {
            // 保留顶层分类，替换二级及以下
            while (categoryStack.size() > 1) {
                categoryStack.pop();
            }
            if (!categoryStack.isEmpty()) {
                categoryStack.push(categoryName);
                log.debug("设置VaR二级分类：{}，当前栈：{}", categoryName, categoryStack);
            } else {
                // 如果没有顶层分类，将其作为顶层分类
                categoryStack.push(categoryName);
                log.warn("没有VaR顶层分类，将{}作为顶层分类", categoryName);
            }
        }
        // 其他情况作为子分类处理
        else {
            // 如果栈为空，将其作为顶层分类
            if (categoryStack.isEmpty()) {
                categoryStack.push(categoryName);
                log.warn("VaR栈为空，将{}作为顶层分类", categoryName);
            } else {
                // 添加为子分类，但不超过4层
                if (categoryStack.size() < 4) {
                    categoryStack.push(categoryName);
                    log.debug("添加VaR子分类：{}，当前栈：{}", categoryName, categoryStack);
                } else {
                    log.debug("VaR栈已满4层，忽略分类：{}", categoryName);
                }
            }
        }
    }

    /**
     * 判断是否为顶层分类
     */
    private boolean isTopLevelCategory(String categoryName) {
        return "传统".equals(categoryName) ||
               "分红险".equals(categoryName) ||
               "分红".equals(categoryName) ||
               "万能险".equals(categoryName) ||
               "万能".equals(categoryName) ||
               "投连险".equals(categoryName) ||
               "投连".equals(categoryName);
    }

    /**
     * 判断是否为二级分类
     */
    private boolean isSecondLevelCategory(String categoryName) {
        return categoryName.contains("投资资产") ||
               categoryName.contains("固定收益") ||
               categoryName.contains("权益类") ||
               categoryName.contains("境内资产") ||
               categoryName.contains("境外资产");
    }

    /**
     * 获取顶层分类
     */
    private String getTopLevelCategory(Stack<String> categoryStack) {
        return categoryStack.isEmpty() ? "未分类" : categoryStack.firstElement();
    }

    /**
     * 创建VaR资产记录
     * 从维度名称中提取证券代码和资产名称
     */
    private VarAnalysisEntity createAssetRecord(VarAnalysisImportDTO row, String accountName, 
                                              String dataType, String accountingPeriod) {
        VarAnalysisEntity entity = new VarAnalysisEntity();
        entity.setAccountingPeriod(accountingPeriod);
        entity.setDataType(dataType);
        entity.setAccountName(accountName); // 使用顶层分类作为账户名称
        entity.setDimensionName(row.getDimensionName()); // 保留原始维度名称

        // 从维度名称中提取证券代码和资产名称
        extractSecurityCodeAndAssetName(row.getDimensionName(), entity);

        // 设置业务数据
        entity.setPeriodDate(row.getPeriodDate());
        entity.setMarketValue(row.getMarketValue());
        entity.setVarAmount(row.getVarAmount());
        entity.setIncrementalVarAmount(row.getIncrementalVarAmount());
        entity.setCtePercentage(row.getCtePercentage());
        entity.setCteAmount(row.getCteAmount());
        entity.setComponentVarPercentage(row.getComponentVarPercentage());
        entity.setComponentVarContributionPercentage(row.getComponentVarContributionPercentage());
        entity.setComponentVarAmount(row.getComponentVarAmount());

        return entity;
    }

    /**
     * 从维度名称中提取证券代码和资产名称
     * 格式：001045OF--华夏可转债增强债券A
     * 关键修复：通过资产名称在TB0006中查找正确的证券代码
     */
    private void extractSecurityCodeAndAssetName(String dimensionName, VarAnalysisEntity entity) {
        if (!StringUtils.hasText(dimensionName)) {
            return;
        }

        String extractedAssetName = null;
        String originalSecurityCode = null;

        // 查找--分隔符
        int separatorIndex = dimensionName.indexOf("--");
        if (separatorIndex > 0) {
            originalSecurityCode = dimensionName.substring(0, separatorIndex).trim();
            extractedAssetName = dimensionName.substring(separatorIndex + 2).trim();
        } else {
            // 没有分隔符，整个维度名称作为资产名称
            extractedAssetName = dimensionName;
        }

        // 设置资产名称
        entity.setAssetName(extractedAssetName);

        // 通过资产名称在TB0006中查找正确的证券代码
        if (StringUtils.hasText(extractedAssetName)) {
            String correctSecurityCode = findSecurityCodeByAssetName(
                extractedAssetName, entity.getAccountName(), entity.getAccountingPeriod());

            if (StringUtils.hasText(correctSecurityCode)) {
                entity.setSecurityCode(correctSecurityCode);
                log.debug("通过资产名称找到正确证券代码：资产名称={}, 原始代码={}, 正确代码={}",
                         extractedAssetName, originalSecurityCode, correctSecurityCode);
            } else {
                // 如果在TB0006中找不到，使用原始提取的证券代码（如果有效）
                if (StringUtils.hasText(originalSecurityCode) && isValidSecurityCodeFormat(originalSecurityCode)) {
                    entity.setSecurityCode(originalSecurityCode);
                    log.warn("在TB0006中未找到资产名称对应的证券代码，使用原始代码：资产名称={}, 原始代码={}",
                            extractedAssetName, originalSecurityCode);
                } else {
                    log.warn("无法确定证券代码：资产名称={}, 原始代码={}", extractedAssetName, originalSecurityCode);
                }
            }
        }
    }

    /**
     * 通过资产名称在TB0006中查找证券代码
     */
    private String findSecurityCodeByAssetName(String assetName, String accountName, String accountingPeriod) {
        try {
            // 精确匹配
            String securityCode = assetDefinitionService.findSecurityCodeByExactMatch(
                accountingPeriod, accountName, assetName);
            if (StringUtils.hasText(securityCode)) {
                return securityCode;
            }

            // 模糊匹配（去除特殊字符后匹配）
            String cleanAssetName = assetName.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
            return assetDefinitionService.findSecurityCodeByFuzzyMatch(
                accountingPeriod, accountName, cleanAssetName);

        } catch (Exception e) {
            log.warn("查找证券代码失败: assetName={}, accountName={}, error={}",
                assetName, accountName, e.getMessage());
            return null;
        }
    }

    /**
     * 验证证券代码格式
     * 支持多种证券代码格式：
     * - 基金：6-8位字母数字 + OF (如001045OF)
     * - 中期票据：9位数字 + YH (如101658055YH)
     * - 存款：7位数字 + CK (如0003956CK)
     * - 债权投资：7位数字 + ZQ (如0000554ZQ)
     * - 资管产品：7位数字 + LC (如0011689LC)
     * - 其他：6-15位字母数字组合
     */
    private boolean isValidSecurityCodeFormat(String code) {
        if (!StringUtils.hasText(code)) {
            return false;
        }

        // 扩展证券代码格式验证，支持更多类型
        // 1. 基金代码：6-8位数字 + OF
        if (code.matches("^[0-9]{6,8}OF$")) {
            return true;
        }

        // 2. 中期票据：9位数字 + YH
        if (code.matches("^[0-9]{9}YH$")) {
            return true;
        }

        // 3. 存款代码：7位数字 + CK
        if (code.matches("^[0-9]{7}CK$")) {
            return true;
        }

        // 4. 债权投资：7位数字 + ZQ
        if (code.matches("^[0-9]{7}ZQ$")) {
            return true;
        }

        // 5. 资管产品：7位数字 + LC
        if (code.matches("^[0-9]{7}LC$")) {
            return true;
        }

        // 6. 交易所代码：6位数字 + SH/SZ
        if (code.matches("^[0-9]{6}(SH|SZ)$")) {
            return true;
        }

        // 7. 通用格式：6-15位字母数字组合（兼容其他格式）
        if (code.matches("^[A-Za-z0-9]{6,15}$")) {
            return true;
        }

        return false;
    }

    /**
     * 生成唯一键，用于检查重复数据
     * 格式：账期-数据类型-账户名称-证券代码
     */
    private String generateUniqueKey(VarAnalysisEntity entity) {
        String securityCode = StringUtils.hasText(entity.getSecurityCode()) ? entity.getSecurityCode() : "NULL";
        return entity.getAccountingPeriod() + "-" +
               entity.getDataType() + "-" +
               entity.getAccountName() + "-" +
               securityCode;
    }
}
