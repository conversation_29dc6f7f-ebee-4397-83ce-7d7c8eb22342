package com.xl.alm.app.service;

import com.xl.alm.app.dto.FixedIncomeTermDistDTO;
import com.xl.alm.app.query.FixedIncomeTermDistQuery;

import java.util.List;

/**
 * 固定收益类投资资产剩余期限分布表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface FixedIncomeTermDistService {

    /**
     * 查询固定收益类投资资产剩余期限分布表列表
     *
     * @param fixedIncomeTermDistQuery 固定收益类投资资产剩余期限分布表查询条件
     * @return 固定收益类投资资产剩余期限分布表列表
     */
    List<FixedIncomeTermDistDTO> selectFixedIncomeTermDistDtoList(FixedIncomeTermDistQuery fixedIncomeTermDistQuery);

    /**
     * 用id查询固定收益类投资资产剩余期限分布表
     *
     * @param id id
     * @return 固定收益类投资资产剩余期限分布表
     */
    FixedIncomeTermDistDTO selectFixedIncomeTermDistDtoById(Long id);

    /**
     * 根据账期、境内外标识、固收资产剩余期限资产分类和剩余期限标识查询固定收益类投资资产剩余期限分布表
     *
     * @param accountingPeriod 账期
     * @param domesticForeign 境内外标识
     * @param fixedIncomeTermCategory 固收资产剩余期限资产分类
     * @param remainingTermFlag 剩余期限标识
     * @return 固定收益类投资资产剩余期限分布表
     */
    FixedIncomeTermDistDTO selectFixedIncomeTermDistDtoByCondition(String accountingPeriod, String domesticForeign, String fixedIncomeTermCategory, String remainingTermFlag);

    /**
     * 新增固定收益类投资资产剩余期限分布表
     *
     * @param fixedIncomeTermDistDTO 固定收益类投资资产剩余期限分布表
     * @return 结果
     */
    int insertFixedIncomeTermDistDto(FixedIncomeTermDistDTO fixedIncomeTermDistDTO);

    /**
     * 批量新增固定收益类投资资产剩余期限分布表
     *
     * @param fixedIncomeTermDistDtoList 固定收益类投资资产剩余期限分布表列表
     * @return 影响行数
     */
    int batchInsertFixedIncomeTermDistDto(List<FixedIncomeTermDistDTO> fixedIncomeTermDistDtoList);

    /**
     * 修改固定收益类投资资产剩余期限分布表
     *
     * @param fixedIncomeTermDistDTO 固定收益类投资资产剩余期限分布表
     * @return 结果
     */
    int updateFixedIncomeTermDistDto(FixedIncomeTermDistDTO fixedIncomeTermDistDTO);

    /**
     * 批量删除固定收益类投资资产剩余期限分布表
     *
     * @param ids 需要删除的固定收益类投资资产剩余期限分布表主键集合
     * @return 结果
     */
    int deleteFixedIncomeTermDistDtoByIds(Long[] ids);

    /**
     * 删除固定收益类投资资产剩余期限分布表信息
     *
     * @param id 固定收益类投资资产剩余期限分布表主键
     * @return 结果
     */
    int deleteFixedIncomeTermDistDtoById(Long id);

    /**
     * 导入固定收益类投资资产剩余期限分布表数据
     *
     * @param fixedIncomeTermDistDtoList 固定收益类投资资产剩余期限分布表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importFixedIncomeTermDistDto(List<FixedIncomeTermDistDTO> fixedIncomeTermDistDtoList, Boolean isUpdateSupport, String operName);
}
