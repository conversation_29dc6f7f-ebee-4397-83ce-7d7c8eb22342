package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AssetDefinitionDTO;
import com.xl.alm.app.query.AssetDefinitionQuery;
import com.xl.alm.app.service.AssetDefinitionService;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 资产定义表Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/asset/definition")
public class AssetDefinitionController extends BaseController {

    @Autowired
    private AssetDefinitionService assetDefinitionService;

    /**
     * 查询资产定义表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetDefinitionQuery assetDefinitionQuery) {
        startPage();
        List<AssetDefinitionDTO> list = assetDefinitionService.selectAssetDefinitionDtoList(assetDefinitionQuery);
        return getDataTable(list);
    }

    /**
     * 获取资产定义表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:query')")
    @GetMapping(value = "/info/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetDefinitionService.selectAssetDefinitionDtoById(id));
    }

    /**
     * 新增资产定义表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:add')")
    @Log(title = "资产定义表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AssetDefinitionDTO assetDefinitionDTO) {
        return toAjax(assetDefinitionService.insertAssetDefinitionDto(assetDefinitionDTO));
    }

    /**
     * 修改资产定义表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:edit')")
    @Log(title = "资产定义表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AssetDefinitionDTO assetDefinitionDTO) {
        return toAjax(assetDefinitionService.updateAssetDefinitionDto(assetDefinitionDTO));
    }

    /**
     * 删除资产定义表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:remove')")
    @Log(title = "资产定义表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetDefinitionService.deleteAssetDefinitionDtoByIds(ids));
    }

    /**
     * 批量新增资产定义表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:add')")
    @Log(title = "资产定义表", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public Result batchAdd(@Valid @RequestBody List<AssetDefinitionDTO> assetDefinitionDTOList) {
        return toAjax(assetDefinitionService.batchInsertAssetDefinitionDto(assetDefinitionDTOList));
    }

    /**
     * 根据账期删除资产定义表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:remove')")
    @Log(title = "资产定义表", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountingPeriod}")
    public Result removeByPeriod(@PathVariable String accountingPeriod) {
        return toAjax(assetDefinitionService.deleteAssetDefinitionDtoByPeriod(accountingPeriod));
    }

    /**
     * 导入资产定义表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:import')")
    @Log(title = "资产定义表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetDefinitionDTO> util = new ExcelUtil(AssetDefinitionDTO.class);
        List<AssetDefinitionDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = assetDefinitionService.importAssetDefinitionDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出资产定义表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:export')")
    @Log(title = "资产定义表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetDefinitionQuery query) {
        ExcelUtil<AssetDefinitionDTO> util = new ExcelUtil<>(AssetDefinitionDTO.class);
        List<AssetDefinitionDTO> list = assetDefinitionService.selectAssetDefinitionDtoList(query);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "资产定义表数据", response);
    }

    /**
     * 获取资产定义表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:definition:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetDefinitionDTO> util = new ExcelUtil<>(AssetDefinitionDTO.class);
        util.exportTemplateExcel(response, "资产定义表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资产定义表数据列表
     */
    private void convertDictValueToLabel(List<AssetDefinitionDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetDefinitionDTO dto : list) {
            // 转换资产小小类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                dto.setAssetSubSubCategory(assetSubSubCategoryLabel);
            }

            // 转换境内外标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getDomesticForeign())) {
                String domesticForeignLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDomesticForeign(),
                        "ast_domestic_foreign"
                );
                dto.setDomesticForeign(domesticForeignLabel);
            }

            // 转换信用评级：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getCreditRating())) {
                String creditRatingLabel = DictConvertUtil.convertValueToLabel(
                        dto.getCreditRating(),
                        "ast_credit_rating"
                );
                dto.setCreditRating(creditRatingLabel);
            }

            // 转换五级分类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getFiveLevelClassification())) {
                String fiveLevelClassificationLabel = DictConvertUtil.convertValueToLabel(
                        dto.getFiveLevelClassification(),
                        "ast_five_level_classification"
                );
                dto.setFiveLevelClassification(fiveLevelClassificationLabel);
            }
        }
    }
}
