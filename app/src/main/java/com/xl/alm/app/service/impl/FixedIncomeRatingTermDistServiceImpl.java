package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.FixedIncomeRatingTermDistDTO;
import com.xl.alm.app.entity.FixedIncomeRatingTermDistEntity;
import com.xl.alm.app.mapper.FixedIncomeRatingTermDistMapper;
import com.xl.alm.app.query.FixedIncomeRatingTermDistQuery;
import com.xl.alm.app.service.FixedIncomeRatingTermDistService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 固定收益类投资资产外部评级剩余期限分布表 Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class FixedIncomeRatingTermDistServiceImpl implements FixedIncomeRatingTermDistService {

    @Autowired
    private FixedIncomeRatingTermDistMapper fixedIncomeRatingTermDistMapper;

    /**
     * 查询固定收益类投资资产外部评级剩余期限分布表列表
     *
     * @param fixedIncomeRatingTermDistQuery 固定收益类投资资产外部评级剩余期限分布表查询条件
     * @return 固定收益类投资资产外部评级剩余期限分布表列表
     */
    @Override
    public List<FixedIncomeRatingTermDistDTO> selectFixedIncomeRatingTermDistDtoList(FixedIncomeRatingTermDistQuery fixedIncomeRatingTermDistQuery) {
        List<FixedIncomeRatingTermDistEntity> entityList = fixedIncomeRatingTermDistMapper.selectFixedIncomeRatingTermDistList(fixedIncomeRatingTermDistQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, FixedIncomeRatingTermDistDTO.class);
    }

    /**
     * 用id查询固定收益类投资资产外部评级剩余期限分布表
     *
     * @param id id
     * @return 固定收益类投资资产外部评级剩余期限分布表
     */
    @Override
    public FixedIncomeRatingTermDistDTO selectFixedIncomeRatingTermDistDtoById(Long id) {
        FixedIncomeRatingTermDistEntity entity = fixedIncomeRatingTermDistMapper.selectFixedIncomeRatingTermDistById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FixedIncomeRatingTermDistDTO.class);
    }

    /**
     * 根据账期、境内外标识、信用评级分类和固收资产剩余期限资产分类查询固定收益类投资资产外部评级剩余期限分布表
     *
     * @param accountingPeriod 账期
     * @param domesticForeign 境内外标识
     * @param creditRatingCategory 信用评级分类
     * @param fixedIncomeTermCategory 固收资产剩余期限资产分类
     * @return 固定收益类投资资产外部评级剩余期限分布表
     */
    @Override
    public FixedIncomeRatingTermDistDTO selectFixedIncomeRatingTermDistDtoByCondition(
            String accountingPeriod,
            String domesticForeign,
            String creditRatingCategory,
            String fixedIncomeTermCategory) {
        FixedIncomeRatingTermDistEntity entity = fixedIncomeRatingTermDistMapper.selectFixedIncomeRatingTermDistByCondition(
                accountingPeriod,
                domesticForeign,
                creditRatingCategory,
                fixedIncomeTermCategory);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, FixedIncomeRatingTermDistDTO.class);
    }

    /**
     * 新增固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dto 固定收益类投资资产外部评级剩余期限分布表
     * @return 结果
     */
    @Override
    public int addFixedIncomeRatingTermDistDto(FixedIncomeRatingTermDistDTO dto) {
        FixedIncomeRatingTermDistEntity entity = EntityDtoConvertUtil.convertToEntity(dto, FixedIncomeRatingTermDistEntity.class);
        return fixedIncomeRatingTermDistMapper.insertFixedIncomeRatingTermDist(entity);
    }

    /**
     * 修改固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dto 固定收益类投资资产外部评级剩余期限分布表
     * @return 结果
     */
    @Override
    public int updateFixedIncomeRatingTermDistDto(FixedIncomeRatingTermDistDTO dto) {
        FixedIncomeRatingTermDistEntity entity = EntityDtoConvertUtil.convertToEntity(dto, FixedIncomeRatingTermDistEntity.class);
        return fixedIncomeRatingTermDistMapper.updateFixedIncomeRatingTermDist(entity);
    }

    /**
     * 批量删除固定收益类投资资产外部评级剩余期限分布表
     *
     * @param ids 需要删除的固定收益类投资资产外部评级剩余期限分布表主键集合
     * @return 结果
     */
    @Override
    public int deleteFixedIncomeRatingTermDistDtoByIds(Long[] ids) {
        return fixedIncomeRatingTermDistMapper.deleteFixedIncomeRatingTermDistByIds(ids);
    }

    /**
     * 删除固定收益类投资资产外部评级剩余期限分布表信息
     *
     * @param id 固定收益类投资资产外部评级剩余期限分布表主键
     * @return 结果
     */
    @Override
    public int deleteFixedIncomeRatingTermDistDtoById(Long id) {
        return fixedIncomeRatingTermDistMapper.deleteFixedIncomeRatingTermDistById(id);
    }

    /**
     * 批量插入固定收益类投资资产外部评级剩余期限分布表数据
     *
     * @param fixedIncomeRatingTermDistDtoList 固定收益类投资资产外部评级剩余期限分布表列表
     * @return 影响行数
     */
    @Override
    public int batchInsertFixedIncomeRatingTermDistDto(List<FixedIncomeRatingTermDistDTO> fixedIncomeRatingTermDistDtoList) {
        if (fixedIncomeRatingTermDistDtoList == null || fixedIncomeRatingTermDistDtoList.isEmpty()) {
            return 0;
        }
        List<FixedIncomeRatingTermDistEntity> entityList = EntityDtoConvertUtil.convertToEntityList(fixedIncomeRatingTermDistDtoList, FixedIncomeRatingTermDistEntity.class);
        return fixedIncomeRatingTermDistMapper.batchInsertFixedIncomeRatingTermDist(entityList);
    }

    /**
     * 删除指定账期的固定收益类投资资产外部评级剩余期限分布表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    @Override
    public int deleteFixedIncomeRatingTermDistDtoByPeriod(String accountingPeriod) {
        return fixedIncomeRatingTermDistMapper.deleteFixedIncomeRatingTermDistByPeriod(accountingPeriod);
    }

    /**
     * 导入固定收益类投资资产外部评级剩余期限分布表
     *
     * @param dtoList       固定收益类投资资产外部评级剩余期限分布表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFixedIncomeRatingTermDistDto(List<FixedIncomeRatingTermDistDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入固定收益类投资资产外部评级剩余期限分布表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (FixedIncomeRatingTermDistDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                FixedIncomeRatingTermDistDTO existDto = this.selectFixedIncomeRatingTermDistDtoByCondition(
                        dto.getAccountingPeriod(),
                        dto.getDomesticForeign(),
                        dto.getCreditRatingCategory(),
                        dto.getFixedIncomeTermCategory());
                if (StringUtils.isNull(existDto)) {
                    this.addFixedIncomeRatingTermDistDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    this.updateFixedIncomeRatingTermDistDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountingPeriod() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
