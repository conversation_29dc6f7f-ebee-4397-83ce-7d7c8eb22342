package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;

import com.jd.lightning.system.service.ISysDictTypeService;
import com.xl.alm.app.dto.PaymentMethodMapDTO;
import com.xl.alm.app.query.PaymentMethodMapQuery;
import com.xl.alm.app.service.PaymentMethodMapService;
import com.xl.alm.app.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 付息方式映射表Controller
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@RestController
@RequestMapping("/ast/payment/method/map")
public class PaymentMethodMapController extends BaseController {

    @Autowired
    private PaymentMethodMapService paymentMethodMapService;

    /**
     * 查询付息方式映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentMethodMapQuery query) {
        startPage();
        List<PaymentMethodMapDTO> list = paymentMethodMapService.selectPaymentMethodMapDtoList(query);
        return getDataTable(list);
    }

    /**
     * 导出付息方式映射表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:export')")
    @Log(title = "付息方式映射表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentMethodMapQuery query) {
        List<PaymentMethodMapDTO> list = paymentMethodMapService.selectPaymentMethodMapDtoListWithoutPage(query);

        // 转换字典编码为中文标签用于导出
        convertDictCodeToLabel(list);

        ExcelUtil<PaymentMethodMapDTO> util = new ExcelUtil<>(PaymentMethodMapDTO.class);
        util.exportExcel(list, "付息方式映射表数据", response);
    }

    /**
     * 获取付息方式映射表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(paymentMethodMapService.selectPaymentMethodMapDtoById(id));
    }

    /**
     * 新增付息方式映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:add')")
    @Log(title = "付息方式映射表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody PaymentMethodMapDTO dto) {
        return toAjax(paymentMethodMapService.insertPaymentMethodMapDto(dto));
    }

    /**
     * 修改付息方式映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:edit')")
    @Log(title = "付息方式映射表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody PaymentMethodMapDTO dto) {
        return toAjax(paymentMethodMapService.updatePaymentMethodMapDto(dto));
    }

    /**
     * 删除付息方式映射表
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:remove')")
    @Log(title = "付息方式映射表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(paymentMethodMapService.deletePaymentMethodMapDtoByIds(ids));
    }

    /**
     * 导入付息方式映射表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:import')")
    @Log(title = "付息方式映射表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) {
        try {
            String message = paymentMethodMapService.importPaymentMethodMap(file, updateSupport);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取付息方式映射表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:payment:method:map:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        paymentMethodMapService.importTemplatePaymentMethodMap(response);
    }

    /**
     * 将字典编码转换为中文标签用于导出
     *
     * @param list 付息方式映射表数据列表
     */
    private void convertDictCodeToLabel(List<PaymentMethodMapDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 获取字典服务
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);

            // 获取付息方式字典数据
            List<SysDictData> paymentMethodDict = dictTypeService.selectDictDataByType("ast_payment_method");

            // 为每条记录转换字典编码为中文标签
            for (PaymentMethodMapDTO dto : list) {
                // 转换付息方式
                dto.setPaymentMethod(convertCodeToLabel(dto.getPaymentMethod(), paymentMethodDict));
            }
        } catch (Exception e) {
            // 转换失败时记录日志，但不影响导出
            log.warn("转换字典编码为中文标签时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 将字典编码转换为中文标签
     *
     * @param code 字典编码
     * @param dictDataList 字典数据列表
     * @return 中文标签
     */
    private String convertCodeToLabel(String code, List<SysDictData> dictDataList) {
        if (StringUtils.isEmpty(code) || dictDataList == null) {
            return code;
        }

        // 查找对应的中文标签
        for (SysDictData dictData : dictDataList) {
            if (code.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        // 如果没有找到匹配的标签，返回原值
        return code;
    }
}
