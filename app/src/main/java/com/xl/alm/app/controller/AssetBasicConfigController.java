package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.AssetBasicConfigDTO;
import com.xl.alm.app.query.AssetBasicConfigQuery;
import com.xl.alm.app.service.AssetBasicConfigService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产基础配置表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/asset/basic/config")
public class AssetBasicConfigController extends BaseController {

    @Autowired
    private AssetBasicConfigService assetBasicConfigService;

    /**
     * 查询资产基础配置表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetBasicConfigQuery assetBasicConfigQuery) {
        startPage();
        List<AssetBasicConfigDTO> list = assetBasicConfigService.selectAssetBasicConfigDtoList(assetBasicConfigQuery);
        return getDataTable(list);
    }

    /**
     * 导出资产基础配置表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:export')")
    @Log(title = "资产基础配置表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetBasicConfigQuery assetBasicConfigQuery) {
        ExcelUtil<AssetBasicConfigDTO> util = new ExcelUtil<>(AssetBasicConfigDTO.class);
        List<AssetBasicConfigDTO> list = assetBasicConfigService.selectAssetBasicConfigDtoList(assetBasicConfigQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "资产基础配置表数据", response);
    }

    /**
     * 获取资产基础配置表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetBasicConfigService.selectAssetBasicConfigDtoById(id));
    }

    /**
     * 新增资产基础配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:add')")
    @Log(title = "资产基础配置表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody AssetBasicConfigDTO assetBasicConfigDto) {
        return toAjax(assetBasicConfigService.insertAssetBasicConfigDto(assetBasicConfigDto));
    }

    /**
     * 修改资产基础配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:edit')")
    @Log(title = "资产基础配置表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody AssetBasicConfigDTO assetBasicConfigDto) {
        return toAjax(assetBasicConfigService.updateAssetBasicConfigDto(assetBasicConfigDto));
    }

    /**
     * 删除资产基础配置表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:remove')")
    @Log(title = "资产基础配置表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetBasicConfigService.deleteAssetBasicConfigDtoByIds(ids));
    }

    /**
     * 导入资产基础配置表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:import')")
    @Log(title = "资产基础配置表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetBasicConfigDTO> util = new ExcelUtil(AssetBasicConfigDTO.class);
        List<AssetBasicConfigDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = assetBasicConfigService.importAssetBasicConfigDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取资产基础配置表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:basic:config:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetBasicConfigDTO> util = new ExcelUtil<>(AssetBasicConfigDTO.class);
        util.exportTemplateExcel(response, "资产基础配置表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资产基础配置表数据列表
     */
    private void convertDictValueToLabel(List<AssetBasicConfigDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetBasicConfigDTO dto : list) {
            // 转换资产小小类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                dto.setAssetSubSubCategory(assetSubSubCategoryLabel);
            }

            // 转换固收资产细分类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getFixedIncomeSubCategory())) {
                String fixedIncomeSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getFixedIncomeSubCategory(),
                        "ast_fixed_income_sub_category"
                );
                dto.setFixedIncomeSubCategory(fixedIncomeSubCategoryLabel);
            }

            // 转换行业统计标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getIndustryStatisticsFlag())) {
                String industryStatisticsFlagLabel = DictConvertUtil.convertValueToLabel(
                        dto.getIndustryStatisticsFlag(),
                        "ast_industry_statistics_flag"
                );
                dto.setIndustryStatisticsFlag(industryStatisticsFlagLabel);
            }

            // 转换单一资产统计标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getSingleAssetStatisticsFlag())) {
                String singleAssetStatisticsFlagLabel = DictConvertUtil.convertValueToLabel(
                        dto.getSingleAssetStatisticsFlag(),
                        "ast_single_asset_statistics_flag"
                );
                dto.setSingleAssetStatisticsFlag(singleAssetStatisticsFlagLabel);
            }

            // 转换五级分类资产统计标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getFiveLevelStatisticsFlag())) {
                String fiveLevelStatisticsFlagLabel = DictConvertUtil.convertValueToLabel(
                        dto.getFiveLevelStatisticsFlag(),
                        "ast_five_level_statistics_flag"
                );
                dto.setFiveLevelStatisticsFlag(fiveLevelStatisticsFlagLabel);
            }

            // 转换可计算现金流标识：0转为"否"，1转为"是"
            if (StringUtils.isNotEmpty(dto.getCalculableCashflowFlag())) {
                dto.setCalculableCashflowFlag("1".equals(dto.getCalculableCashflowFlag()) ? "是" : "否");
            }

            // 转换利差久期资产统计标识：0转为"否"，1转为"是"
            if (StringUtils.isNotEmpty(dto.getSpreadDurationStatisticsFlag())) {
                dto.setSpreadDurationStatisticsFlag("1".equals(dto.getSpreadDurationStatisticsFlag()) ? "是" : "否");
            }
        }
    }
}
