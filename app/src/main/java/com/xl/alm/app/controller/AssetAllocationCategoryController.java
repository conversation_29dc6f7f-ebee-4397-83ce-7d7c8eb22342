package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.dto.AssetAllocationCategoryDTO;
import com.xl.alm.app.query.AssetAllocationCategoryQuery;
import com.xl.alm.app.service.AssetAllocationCategoryService;
import com.xl.alm.app.util.DictConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产配置状况分类表 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/ast/asset/allocation/category")
public class AssetAllocationCategoryController extends BaseController {

    @Autowired
    private AssetAllocationCategoryService assetAllocationCategoryService;

    /**
     * 查询资产配置状况分类表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssetAllocationCategoryQuery assetAllocationCategoryQuery) {
        startPage();
        List<AssetAllocationCategoryDTO> list = assetAllocationCategoryService.selectAssetAllocationCategoryDtoList(assetAllocationCategoryQuery);
        return getDataTable(list);
    }

    /**
     * 导出资产配置状况分类表列表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:export')")
    @Log(title = "资产配置状况分类表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssetAllocationCategoryQuery assetAllocationCategoryQuery) {
        ExcelUtil<AssetAllocationCategoryDTO> util = new ExcelUtil<>(AssetAllocationCategoryDTO.class);
        List<AssetAllocationCategoryDTO> list = assetAllocationCategoryService.selectAssetAllocationCategoryDtoList(assetAllocationCategoryQuery);
        // 转换字典值为标签用于Excel导出
        convertDictValueToLabel(list);
        util.exportExcel(list, "资产配置状况分类表数据", response);
    }

    /**
     * 根据主键获取资产配置状况分类表详细信息
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(assetAllocationCategoryService.selectAssetAllocationCategoryDtoById(id));
    }

    /**
     * 新增资产配置状况分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:add')")
    @Log(title = "资产配置状况分类表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Validated @RequestBody AssetAllocationCategoryDTO assetAllocationCategoryDTO) {
        assetAllocationCategoryDTO.setCreateBy(getUsername());
        return toAjax(assetAllocationCategoryService.insertAssetAllocationCategoryDto(assetAllocationCategoryDTO));
    }

    /**
     * 修改资产配置状况分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:edit')")
    @Log(title = "资产配置状况分类表", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Validated @RequestBody AssetAllocationCategoryDTO assetAllocationCategoryDTO) {
        assetAllocationCategoryDTO.setUpdateBy(getUsername());
        return toAjax(assetAllocationCategoryService.updateAssetAllocationCategoryDto(assetAllocationCategoryDTO));
    }

    /**
     * 删除资产配置状况分类表
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:remove')")
    @Log(title = "资产配置状况分类表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(assetAllocationCategoryService.deleteAssetAllocationCategoryDtoByIds(ids));
    }

    /**
     * 导入资产配置状况分类表数据
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:import')")
    @Log(title = "资产配置状况分类表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AssetAllocationCategoryDTO> util = new ExcelUtil(AssetAllocationCategoryDTO.class);
        List<AssetAllocationCategoryDTO> dtoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        try {
            String message = assetAllocationCategoryService.importAssetAllocationCategoryDto(dtoList, updateSupport, operName);
            return Result.success(message);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取资产配置状况分类表导入模板
     */
    @PreAuthorize("@ss.hasPermi('ast:asset:allocation:category:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AssetAllocationCategoryDTO> util = new ExcelUtil<>(AssetAllocationCategoryDTO.class);
        util.exportTemplateExcel(response, "资产配置状况分类表");
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 资产配置状况分类表数据列表
     */
    private void convertDictValueToLabel(List<AssetAllocationCategoryDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (AssetAllocationCategoryDTO dto : list) {
            // 转换资产小小类：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getAssetSubSubCategory())) {
                String assetSubSubCategoryLabel = DictConvertUtil.convertValueToLabel(
                        dto.getAssetSubSubCategory(),
                        "ast_asset_sub_sub_category"
                );
                dto.setAssetSubSubCategory(assetSubSubCategoryLabel);
            }

            // 转换境内外标识：字典值转为中文标签
            if (StringUtils.isNotEmpty(dto.getDomesticForeign())) {
                String domesticForeignLabel = DictConvertUtil.convertValueToLabel(
                        dto.getDomesticForeign(),
                        "ast_domestic_foreign"
                );
                dto.setDomesticForeign(domesticForeignLabel);
            }

            // 转换分类级别：字典值转为中文标签
            if (dto.getCategoryLevel() != null) {
                String categoryLevelLabel = DictConvertUtil.convertValueToLabel(
                        dto.getCategoryLevel().toString(),
                        "ast_category_level"
                );
                // 注意：这里需要特殊处理，因为categoryLevel是Integer类型，但显示时需要显示中文标签
                // 可以考虑在DTO中添加一个categoryLevelLabel字段用于显示
            }
        }
    }
}
