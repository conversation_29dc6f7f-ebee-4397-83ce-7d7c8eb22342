package com.xl.alm.app.service;

import com.xl.alm.app.dto.AssetAllocationCategoryDTO;
import com.xl.alm.app.query.AssetAllocationCategoryQuery;

import java.util.List;

/**
 * 资产配置状况分类表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AssetAllocationCategoryService {

    /**
     * 查询资产配置状况分类表列表
     *
     * @param assetAllocationCategoryQuery 资产配置状况分类表查询条件
     * @return 资产配置状况分类表列表
     */
    List<AssetAllocationCategoryDTO> selectAssetAllocationCategoryDtoList(AssetAllocationCategoryQuery assetAllocationCategoryQuery);

    /**
     * 根据主键查询资产配置状况分类表
     *
     * @param id 主键
     * @return 资产配置状况分类表
     */
    AssetAllocationCategoryDTO selectAssetAllocationCategoryDtoById(Long id);

    /**
     * 新增资产配置状况分类表
     *
     * @param assetAllocationCategoryDTO 资产配置状况分类表
     * @return 影响行数
     */
    int insertAssetAllocationCategoryDto(AssetAllocationCategoryDTO assetAllocationCategoryDTO);

    /**
     * 修改资产配置状况分类表
     *
     * @param assetAllocationCategoryDTO 资产配置状况分类表
     * @return 影响行数
     */
    int updateAssetAllocationCategoryDto(AssetAllocationCategoryDTO assetAllocationCategoryDTO);

    /**
     * 根据主键删除资产配置状况分类表
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteAssetAllocationCategoryDtoById(Long id);

    /**
     * 批量删除资产配置状况分类表
     *
     * @param ids 主键数组
     * @return 影响行数
     */
    int deleteAssetAllocationCategoryDtoByIds(Long[] ids);

    /**
     * 批量新增资产配置状况分类表
     *
     * @param assetAllocationCategoryDtoList 资产配置状况分类表列表
     * @return 影响行数
     */
    int batchInsertAssetAllocationCategoryDto(List<AssetAllocationCategoryDTO> assetAllocationCategoryDtoList);

    /**
     * 导入资产配置状况分类表数据
     *
     * @param assetAllocationCategoryDtoList 资产配置状况分类表数据列表
     * @param updateSupport 是否支持更新
     * @param operName 操作人员
     * @return 导入结果信息
     */
    String importAssetAllocationCategoryDto(List<AssetAllocationCategoryDTO> assetAllocationCategoryDtoList, boolean updateSupport, String operName);
}
