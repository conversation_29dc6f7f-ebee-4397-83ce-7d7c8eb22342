package com.xl.alm.app.service;

import com.xl.alm.app.dto.AlmcfActualQuarterlyDTO;
import com.xl.alm.app.query.AlmcfActualQuarterlyQuery;

import java.util.List;

/**
 * ALMCF实际发生数本季度累计表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface AlmcfActualQuarterlyService {

    /**
     * 查询ALMCF实际发生数本季度累计表列表
     *
     * @param almcfActualQuarterlyQuery ALMCF实际发生数本季度累计表查询条件
     * @return ALMCF实际发生数本季度累计表列表
     */
    List<AlmcfActualQuarterlyDTO> selectAlmcfActualQuarterlyDtoList(AlmcfActualQuarterlyQuery almcfActualQuarterlyQuery);

    /**
     * 用id查询ALMCF实际发生数本季度累计表
     *
     * @param id id
     * @return ALMCF实际发生数本季度累计表
     */
    AlmcfActualQuarterlyDTO selectAlmcfActualQuarterlyDtoById(Long id);

    /**
     * 根据账期查询ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @return ALMCF实际发生数本季度累计表列表
     */
    List<AlmcfActualQuarterlyDTO> selectAlmcfActualQuarterlyDtoByPeriod(String accountingPeriod);

    /**
     * 根据账期和项目名称查询ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @param itemName 项目名称
     * @return ALMCF实际发生数本季度累计表
     */
    AlmcfActualQuarterlyDTO selectAlmcfActualQuarterlyDtoByCondition(String accountingPeriod, String itemName);

    /**
     * 新增ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDto ALMCF实际发生数本季度累计表
     * @return 影响行数
     */
    int insertAlmcfActualQuarterlyDto(AlmcfActualQuarterlyDTO almcfActualQuarterlyDto);

    /**
     * 批量新增ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDtoList ALMCF实际发生数本季度累计表列表
     * @return 影响行数
     */
    int batchInsertAlmcfActualQuarterlyDto(List<AlmcfActualQuarterlyDTO> almcfActualQuarterlyDtoList);

    /**
     * 更新ALMCF实际发生数本季度累计表
     *
     * @param almcfActualQuarterlyDto ALMCF实际发生数本季度累计表
     * @return 影响行数
     */
    int updateAlmcfActualQuarterlyDto(AlmcfActualQuarterlyDTO almcfActualQuarterlyDto);

    /**
     * 删除ALMCF实际发生数本季度累计表
     *
     * @param id id
     * @return 影响行数
     */
    int deleteAlmcfActualQuarterlyDtoById(Long id);

    /**
     * 批量删除ALMCF实际发生数本季度累计表
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteAlmcfActualQuarterlyDtoByIds(Long[] ids);

    /**
     * 根据账期删除ALMCF实际发生数本季度累计表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteAlmcfActualQuarterlyDtoByPeriod(String accountingPeriod);

    /**
     * 导入ALMCF实际发生数本季度累计表
     *
     * @param dtoList ALMCF实际发生数本季度累计表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    String importAlmcfActualQuarterlyDto(List<AlmcfActualQuarterlyDTO> dtoList, Boolean updateSupport, String username);

    /**
     * 计算ALMCF实际发生数本季度累计表
     *
     * @param accountingPeriod 账期
     * @param username 操作用户
     * @return 结果
     */
    String calculateAlmcfActualQuarterlyDto(String accountingPeriod, String username);
}
