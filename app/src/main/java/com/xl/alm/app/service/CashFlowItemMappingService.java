package com.xl.alm.app.service;

import com.xl.alm.app.dto.CashFlowItemMappingDTO;
import com.xl.alm.app.query.CashFlowItemMappingQuery;

import java.util.List;

/**
 * 现金流项目映射表 Service 接口
 *
 * <AUTHOR> Assistant
 */
public interface CashFlowItemMappingService {

    /**
     * 查询现金流项目映射表列表
     *
     * @param cashFlowItemMappingQuery 现金流项目映射表查询条件
     * @return 现金流项目映射表列表
     */
    List<CashFlowItemMappingDTO> selectCashFlowItemMappingDtoList(CashFlowItemMappingQuery cashFlowItemMappingQuery);

    /**
     * 用id查询现金流项目映射表
     *
     * @param id id
     * @return 现金流项目映射表
     */
    CashFlowItemMappingDTO selectCashFlowItemMappingDtoById(Long id);

    /**
     * 根据账期查询现金流项目映射表
     *
     * @param accountingPeriod 账期
     * @return 现金流项目映射表列表
     */
    List<CashFlowItemMappingDTO> selectCashFlowItemMappingDtoByPeriod(String accountingPeriod);

    /**
     * 根据现金流量表项目查询映射关系
     *
     * @param accountingPeriod 账期
     * @param cashFlowItem 现金流量表项目
     * @return 现金流项目映射表
     */
    CashFlowItemMappingDTO selectCashFlowItemMappingDtoByCondition(String accountingPeriod, String cashFlowItem);

    /**
     * 新增现金流项目映射表
     *
     * @param cashFlowItemMappingDto 现金流项目映射表
     * @return 影响行数
     */
    int insertCashFlowItemMappingDto(CashFlowItemMappingDTO cashFlowItemMappingDto);

    /**
     * 批量新增现金流项目映射表
     *
     * @param cashFlowItemMappingDtoList 现金流项目映射表列表
     * @return 影响行数
     */
    int batchInsertCashFlowItemMappingDto(List<CashFlowItemMappingDTO> cashFlowItemMappingDtoList);

    /**
     * 更新现金流项目映射表
     *
     * @param cashFlowItemMappingDto 现金流项目映射表
     * @return 影响行数
     */
    int updateCashFlowItemMappingDto(CashFlowItemMappingDTO cashFlowItemMappingDto);

    /**
     * 删除现金流项目映射表
     *
     * @param id id
     * @return 影响行数
     */
    int deleteCashFlowItemMappingDtoById(Long id);

    /**
     * 批量删除现金流项目映射表
     *
     * @param ids id数组
     * @return 影响行数
     */
    int deleteCashFlowItemMappingDtoByIds(Long[] ids);

    /**
     * 根据账期删除现金流项目映射表数据
     *
     * @param accountingPeriod 账期
     * @return 影响行数
     */
    int deleteCashFlowItemMappingDtoByPeriod(String accountingPeriod);

    /**
     * 导入现金流项目映射表
     *
     * @param dtoList 现金流项目映射表数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username 操作用户
     * @return 结果
     */
    String importCashFlowItemMappingDto(List<CashFlowItemMappingDTO> dtoList, Boolean updateSupport, String username);
}
