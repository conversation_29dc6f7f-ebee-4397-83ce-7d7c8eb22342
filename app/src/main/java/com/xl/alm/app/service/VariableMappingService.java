package com.xl.alm.app.service;

import com.xl.alm.app.dto.VariableMappingDTO;
import com.xl.alm.app.query.VariableMappingQuery;

import java.util.List;

/**
 * 变量映射表Service接口
 *
 * <AUTHOR> Assistant
 * @date 2025-06-16
 */
public interface VariableMappingService {

    /**
     * 查询变量映射表
     *
     * @param id 变量映射表主键
     * @return 变量映射表
     */
    VariableMappingDTO selectVariableMappingById(Long id);

    /**
     * 查询变量映射表列表
     *
     * @param query 变量映射表查询参数
     * @return 变量映射表集合
     */
    List<VariableMappingDTO> selectVariableMappingList(VariableMappingQuery query);

    /**
     * 新增变量映射表
     *
     * @param dto 变量映射表
     * @return 结果
     */
    int insertVariableMapping(VariableMappingDTO dto);

    /**
     * 修改变量映射表
     *
     * @param dto 变量映射表
     * @return 结果
     */
    int updateVariableMapping(VariableMappingDTO dto);

    /**
     * 批量删除变量映射表
     *
     * @param ids 需要删除的变量映射表主键集合
     * @return 结果
     */
    int deleteVariableMappingByIds(Long[] ids);

    /**
     * 删除变量映射表信息
     *
     * @param id 变量映射表主键
     * @return 结果
     */
    int deleteVariableMappingById(Long id);

    /**
     * 导入变量映射表数据
     *
     * @param dtoList 变量映射表数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    String importVariableMappingData(List<VariableMappingDTO> dtoList, Boolean isUpdateSupport, String operName);

    /**
     * 导出变量映射表模板
     *
     * @param response HTTP响应
     */
    void exportTemplate(javax.servlet.http.HttpServletResponse response) throws Exception;

    /**
     * 根据变量列表查询变量名称
     *
     * @param variableList 变量列表
     * @return 变量名称
     */
    String selectVariableNameByCode(String variableList);
}
