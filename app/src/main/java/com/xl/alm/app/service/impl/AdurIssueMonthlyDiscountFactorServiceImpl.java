package com.xl.alm.app.service.impl;

import com.jd.lightning.common.utils.StringUtils;
import com.xl.alm.app.dto.AdurIssueMonthlyDiscountFactorDTO;
import com.xl.alm.app.entity.AdurIssueMonthlyDiscountFactorEntity;
import com.xl.alm.app.mapper.AdurIssueMonthlyDiscountFactorMapper;
import com.xl.alm.app.query.AdurIssueMonthlyDiscountFactorQuery;
import com.xl.alm.app.service.AdurIssueMonthlyDiscountFactorService;
import com.xl.alm.app.util.EntityDtoConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * ADUR关键久期折现因子表含价差(第二个) Service 实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurIssueMonthlyDiscountFactorServiceImpl implements AdurIssueMonthlyDiscountFactorService {

    @Autowired
    private AdurIssueMonthlyDiscountFactorMapper adurIssueMonthlyDiscountFactorMapper;

    /**
     * 查询ADUR关键久期折现因子表含价差(第二个)列表
     *
     * @param adurIssueMonthlyDiscountFactorQuery ADUR关键久期折现因子表含价差(第二个)查询条件
     * @return ADUR关键久期折现因子表含价差(第二个)列表
     */
    @Override
    public List<AdurIssueMonthlyDiscountFactorDTO> selectAdurIssueMonthlyDiscountFactorDtoList(AdurIssueMonthlyDiscountFactorQuery adurIssueMonthlyDiscountFactorQuery) {
        List<AdurIssueMonthlyDiscountFactorEntity> entityList = adurIssueMonthlyDiscountFactorMapper.selectAdurIssueMonthlyDiscountFactorEntityList(adurIssueMonthlyDiscountFactorQuery);
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }
        return EntityDtoConvertUtil.convertToDTOList(entityList, AdurIssueMonthlyDiscountFactorDTO.class);
    }

    /**
     * 用id查询ADUR关键久期折现因子表含价差(第二个)
     *
     * @param id id
     * @return ADUR关键久期折现因子表含价差(第二个)
     */
    @Override
    public AdurIssueMonthlyDiscountFactorDTO selectAdurIssueMonthlyDiscountFactorDtoById(Long id) {
        AdurIssueMonthlyDiscountFactorEntity entity = adurIssueMonthlyDiscountFactorMapper.selectAdurIssueMonthlyDiscountFactorEntityById(id);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurIssueMonthlyDiscountFactorDTO.class);
    }

    /**
     * 根据条件查询ADUR关键久期折现因子表含价差(第二个)
     *
     * @param accountPeriod 账期
     * @param assetNumber 资产编号
     * @param keyTerm 关键期限
     * @param stressDirection 压力方向
     * @return ADUR关键久期折现因子表含价差(第二个)
     */
    @Override
    public AdurIssueMonthlyDiscountFactorDTO selectAdurIssueMonthlyDiscountFactorDtoByCondition(String accountPeriod, String assetNumber, String keyTerm, String stressDirection) {
        AdurIssueMonthlyDiscountFactorEntity entity = adurIssueMonthlyDiscountFactorMapper.selectAdurIssueMonthlyDiscountFactorEntityByCondition(accountPeriod, assetNumber, keyTerm, stressDirection);
        if (entity == null) {
            return null;
        }
        return EntityDtoConvertUtil.convertToDTO(entity, AdurIssueMonthlyDiscountFactorDTO.class);
    }

    /**
     * 新增ADUR关键久期折现因子表含价差(第二个)
     *
     * @param adurIssueMonthlyDiscountFactorDTO ADUR关键久期折现因子表含价差(第二个)
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdurIssueMonthlyDiscountFactorDto(AdurIssueMonthlyDiscountFactorDTO adurIssueMonthlyDiscountFactorDTO) {
        AdurIssueMonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(adurIssueMonthlyDiscountFactorDTO, AdurIssueMonthlyDiscountFactorEntity.class);
        return adurIssueMonthlyDiscountFactorMapper.insertAdurIssueMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 批量插入ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param adurIssueMonthlyDiscountFactorDtoList ADUR关键久期折现因子表含价差(第二个)列表
     * @return 影响行数
     */
    @Override
    @Transactional
    public int batchInsertAdurIssueMonthlyDiscountFactorDto(List<AdurIssueMonthlyDiscountFactorDTO> adurIssueMonthlyDiscountFactorDtoList) {
        if (adurIssueMonthlyDiscountFactorDtoList == null || adurIssueMonthlyDiscountFactorDtoList.isEmpty()) {
            return 0;
        }
        List<AdurIssueMonthlyDiscountFactorEntity> entityList = EntityDtoConvertUtil.convertToEntityList(adurIssueMonthlyDiscountFactorDtoList, AdurIssueMonthlyDiscountFactorEntity.class);
        return adurIssueMonthlyDiscountFactorMapper.batchInsertAdurIssueMonthlyDiscountFactorEntity(entityList);
    }

    /**
     * 更新ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param dto ADUR关键久期折现因子表含价差(第二个)
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdurIssueMonthlyDiscountFactorDto(AdurIssueMonthlyDiscountFactorDTO dto) {
        AdurIssueMonthlyDiscountFactorEntity entity = EntityDtoConvertUtil.convertToEntity(dto, AdurIssueMonthlyDiscountFactorEntity.class);
        return adurIssueMonthlyDiscountFactorMapper.updateAdurIssueMonthlyDiscountFactorEntity(entity);
    }

    /**
     * 删除指定id的ADUR关键久期折现因子表含价差(第二个)数据
     *
     * @param id id
     * @return 影响行数
     */
    @Override
    @Transactional
    public int deleteAdurIssueMonthlyDiscountFactorDtoById(Long id) {
        return adurIssueMonthlyDiscountFactorMapper.deleteAdurIssueMonthlyDiscountFactorEntityById(id);
    }

    /**
     * 批量删除ADUR关键久期折现因子表含价差(第二个)
     *
     * @param ids 需要删除的ADUR关键久期折现因子表含价差(第二个)主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurIssueMonthlyDiscountFactorDtoByIds(Long[] ids) {
        return adurIssueMonthlyDiscountFactorMapper.deleteAdurIssueMonthlyDiscountFactorEntityByIds(ids);
    }

    /**
     * 导入ADUR关键久期折现因子表含价差(第二个)
     *
     * @param dtoList       ADUR关键久期折现因子表含价差(第二个)数据列表
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param username      操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importAdurIssueMonthlyDiscountFactorDto(List<AdurIssueMonthlyDiscountFactorDTO> dtoList, Boolean updateSupport, String username) {
        if (StringUtils.isNull(dtoList) || dtoList.size() == 0) {
            throw new RuntimeException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdurIssueMonthlyDiscountFactorDTO dto : dtoList) {
            try {
                // 验证是否存在这个数据
                AdurIssueMonthlyDiscountFactorDTO existDto = this.selectAdurIssueMonthlyDiscountFactorDtoByCondition(
                    dto.getAccountPeriod(), dto.getAssetNumber(), dto.getKeyTerm(), dto.getStressDirection());
                if (StringUtils.isNull(existDto)) {
                    dto.setCreateBy(username);
                    this.insertAdurIssueMonthlyDiscountFactorDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入成功");
                } else if (updateSupport) {
                    dto.setId(existDto.getId());
                    dto.setUpdateBy(username);
                    this.updateAdurIssueMonthlyDiscountFactorDto(dto);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountPeriod() + " 资产编号 " + dto.getAssetNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 根据账期删除ADUR关键久期折现因子表含价差(第二个)
     *
     * @param accountPeriod 账期
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdurIssueMonthlyDiscountFactorDtoByAccountPeriod(String accountPeriod) {
        return adurIssueMonthlyDiscountFactorMapper.deleteAdurIssueMonthlyDiscountFactorEntityByAccountPeriod(accountPeriod);
    }
}
