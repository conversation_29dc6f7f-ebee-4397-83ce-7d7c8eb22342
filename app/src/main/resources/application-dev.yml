# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主库数据源
      master:
        driverClassName: com.p6spy.engine.spy.P6SpyDriver
        url: ***************************************************************************************************************************************************************************
        username: alm
        password: 123456
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 100
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: lightning
        login-password: 123456
      filter:
        stat:
          enabled: false
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  jpa:
    open-in-view: false
  #配置redis数据库连接
  redis:
    host: localhost
    port: 6379
    password: 123456
    timeout: 5000
    database: 2

# 日志配置
logging:
  level:
    com.jd: info
    org.springframework: warn
  config: classpath:logback-dev.xml


financeId:
  #数据库配置
  datasource:
    url: *********************************************************************************************************************************************************************
    type: mysql
    driver: com.mysql.cj.jdbc.Driver
    user: alm
    password: 123456

jeecg :
  jmreport:
    #多租户模式，默认值为空(created:按照创建人隔离、tenant:按照租户隔离) (v1.6.2+ 新增)
    saasMode: tenant
    # 平台上线安全配置(v1.6.2+ 新增)
    firewall:
      # 数据源安全 (开启后，不允许使用平台数据源、SQL解析不允许select * 查询、禁止测试数据源连接是否正确)
      # 数据源安全下，预留角色有权限使用 select * 和测试数据源链接
      dataSourceSafe: true
      # 低代码开发模式（dev:开发模式，prod:发布模式关闭报表设计，预留角色admin、lowdeveloper可设计, prodsf:发布安全模式 彻底关闭报表设计）
      lowCodeMode: prod
      # sql注入检查级别（strict:严格, basic:简单校验, none:不校验）
      sqlInjectionLevel: strict