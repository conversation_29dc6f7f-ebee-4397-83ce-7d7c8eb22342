<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.ShortTermProductSpreadMapper">

    <resultMap type="com.xl.alm.app.entity.ShortTermProductSpreadEntity" id="ShortTermProductSpreadEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="actuarialCode" column="actuarial_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="productName" column="product_name"/>
        <result property="designType" column="design_type"/>
        <result property="subAccount" column="sub_account"/>
        <result property="newScalePremium" column="new_scale_premium"/>
        <result property="accountingReserve" column="accounting_reserve"/>
        <result property="investmentBalance" column="investment_balance"/>
        <result property="accountingYieldRate" column="accounting_yield_rate"/>
        <result property="comprehensiveYieldRate" column="comprehensive_yield_rate"/>
        <result property="liabilityCostRate" column="liability_cost_rate"/>
        <result property="effectiveCostRate" column="effective_cost_rate"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectShortTermProductSpreadEntityVo">
        select id, accounting_period, actuarial_code, business_code, product_name, design_type, sub_account,
               new_scale_premium, accounting_reserve, investment_balance, accounting_yield_rate, 
               comprehensive_yield_rate, liability_cost_rate, effective_cost_rate, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_cost_short_term_product_spread
    </sql>

    <!-- 查询中短存续期产品利差列表 -->
    <select id="selectShortTermProductSpreadEntityList" parameterType="com.xl.alm.app.query.ShortTermProductSpreadQuery" resultMap="ShortTermProductSpreadEntityResult">
        <include refid="selectShortTermProductSpreadEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code like concat('%', #{actuarialCode}, '%')</if>
            <if test="businessCode != null and businessCode != ''">and business_code like concat('%', #{businessCode}, '%')</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            <if test="subAccount != null and subAccount != ''">and sub_account like concat('%', #{subAccount}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 用id查询中短存续期产品利差 -->
    <select id="selectShortTermProductSpreadEntityById" parameterType="Long" resultMap="ShortTermProductSpreadEntityResult">
        <include refid="selectShortTermProductSpreadEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和产品精算代码查询中短存续期产品利差 -->
    <select id="selectShortTermProductSpreadEntityByCondition" resultMap="ShortTermProductSpreadEntityResult">
        <include refid="selectShortTermProductSpreadEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code = #{actuarialCode}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增中短存续期产品利差 -->
    <insert id="insertShortTermProductSpreadEntity" parameterType="com.xl.alm.app.entity.ShortTermProductSpreadEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cost_short_term_product_spread (
            accounting_period, actuarial_code, business_code, product_name, design_type, sub_account,
            new_scale_premium, accounting_reserve, investment_balance, accounting_yield_rate,
            comprehensive_yield_rate, liability_cost_rate, effective_cost_rate, remark, create_by, update_by
        ) values (
            #{accountingPeriod}, #{actuarialCode}, #{businessCode}, #{productName}, #{designType}, #{subAccount},
            #{newScalePremium}, #{accountingReserve}, #{investmentBalance}, #{accountingYieldRate},
            #{comprehensiveYieldRate}, #{liabilityCostRate}, #{effectiveCostRate}, #{remark}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量插入中短存续期产品利差数据 -->
    <insert id="batchInsertShortTermProductSpreadEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_cost_short_term_product_spread (
            accounting_period, actuarial_code, business_code, product_name, design_type, sub_account,
            new_scale_premium, accounting_reserve, investment_balance, accounting_yield_rate,
            comprehensive_yield_rate, liability_cost_rate, effective_cost_rate, remark, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod},
            #{item.actuarialCode},
            #{item.businessCode},
            #{item.productName},
            #{item.designType},
            #{item.subAccount},
            #{item.newScalePremium},
            #{item.accountingReserve},
            #{item.investmentBalance},
            #{item.accountingYieldRate},
            #{item.comprehensiveYieldRate},
            #{item.liabilityCostRate},
            #{item.effectiveCostRate},
            #{item.remark},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 更新中短存续期产品利差 -->
    <update id="updateShortTermProductSpreadEntity" parameterType="com.xl.alm.app.entity.ShortTermProductSpreadEntity">
        update t_cost_short_term_product_spread
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code = #{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="subAccount != null">sub_account = #{subAccount},</if>
            <if test="newScalePremium != null">new_scale_premium = #{newScalePremium},</if>
            <if test="accountingReserve != null">accounting_reserve = #{accountingReserve},</if>
            <if test="investmentBalance != null">investment_balance = #{investmentBalance},</if>
            <if test="accountingYieldRate != null">accounting_yield_rate = #{accountingYieldRate},</if>
            <if test="comprehensiveYieldRate != null">comprehensive_yield_rate = #{comprehensiveYieldRate},</if>
            <if test="liabilityCostRate != null">liability_cost_rate = #{liabilityCostRate},</if>
            <if test="effectiveCostRate != null">effective_cost_rate = #{effectiveCostRate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除指定账期的中短存续期产品利差数据 -->
    <update id="deleteShortTermProductSpreadEntityByPeriod" parameterType="String">
        update t_cost_short_term_product_spread set is_del = 1, update_time = now()
        where accounting_period = #{accountingPeriod} and is_del = 0
    </update>

    <!-- 删除指定id的中短存续期产品利差数据 -->
    <update id="deleteShortTermProductSpreadEntityById" parameterType="Long">
        update t_cost_short_term_product_spread set is_del = 1, update_time = now()
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除中短存续期产品利差数据 -->
    <update id="deleteShortTermProductSpreadEntityByIds" parameterType="Long">
        update t_cost_short_term_product_spread set is_del = 1, update_time = now()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
