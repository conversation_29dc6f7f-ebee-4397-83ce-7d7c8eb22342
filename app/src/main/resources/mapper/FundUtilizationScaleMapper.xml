<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FundUtilizationScaleMapper">

    <resultMap type="com.xl.alm.app.entity.FundUtilizationScaleEntity" id="FundUtilizationScaleEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="itemClassificationLevel" column="item_classification_level"/>
        <result property="dataType" column="data_type"/>
        <result property="generalAccount" column="general_account"/>
        <result property="traditionalAccount" column="traditional_account"/>
        <result property="capitalSupplementBondAccount" column="capital_supplement_bond_account"/>
        <result property="bonusAccount" column="bonus_account"/>
        <result property="universalAccount" column="universal_account"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFundUtilizationScaleVo">
        select id, accounting_period, item_name, item_classification_level, data_type, 
               general_account, traditional_account, capital_supplement_bond_account, 
               bonus_account, universal_account, create_time, create_by, update_time, update_by, is_del
        from t_asm_fund_utilization_scale
    </sql>

    <!-- 查询资金运用规模表列表 -->
    <select id="selectFundUtilizationScaleList" parameterType="com.xl.alm.app.query.FundUtilizationScaleQuery" resultMap="FundUtilizationScaleEntityResult">
        <include refid="selectFundUtilizationScaleVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemClassificationLevel != null and itemClassificationLevel != ''">and item_classification_level = #{itemClassificationLevel}</if>
            <if test="dataType != null and dataType != ''">and data_type = #{dataType}</if>
            <if test="generalAccount != null">and general_account = #{generalAccount}</if>
            <if test="traditionalAccount != null">and traditional_account = #{traditionalAccount}</if>
            <if test="capitalSupplementBondAccount != null">and capital_supplement_bond_account = #{capitalSupplementBondAccount}</if>
            <if test="bonusAccount != null">and bonus_account = #{bonusAccount}</if>
            <if test="universalAccount != null">and universal_account = #{universalAccount}</if>
            and is_del = 0
        </where>
        order by accounting_period desc, item_classification_level asc, item_name asc, data_type asc
    </select>

    <!-- 用id查询资金运用规模表 -->
    <select id="selectFundUtilizationScaleById" parameterType="Long" resultMap="FundUtilizationScaleEntityResult">
        <include refid="selectFundUtilizationScaleVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、项目名称、项目分级标识和数据类型查询资金运用规模表 -->
    <select id="selectFundUtilizationScaleByCondition" resultMap="FundUtilizationScaleEntityResult">
        <include refid="selectFundUtilizationScaleVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            <if test="itemClassificationLevel != null and itemClassificationLevel != ''">and item_classification_level = #{itemClassificationLevel}</if>
            <if test="dataType != null and dataType != ''">and data_type = #{dataType}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增资金运用规模表 -->
    <insert id="insertFundUtilizationScale" parameterType="com.xl.alm.app.entity.FundUtilizationScaleEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_fund_utilization_scale
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemClassificationLevel != null and itemClassificationLevel != ''">item_classification_level,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="generalAccount != null">general_account,</if>
            <if test="traditionalAccount != null">traditional_account,</if>
            <if test="capitalSupplementBondAccount != null">capital_supplement_bond_account,</if>
            <if test="bonusAccount != null">bonus_account,</if>
            <if test="universalAccount != null">universal_account,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemClassificationLevel != null and itemClassificationLevel != ''">#{itemClassificationLevel},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="generalAccount != null">#{generalAccount},</if>
            <if test="traditionalAccount != null">#{traditionalAccount},</if>
            <if test="capitalSupplementBondAccount != null">#{capitalSupplementBondAccount},</if>
            <if test="bonusAccount != null">#{bonusAccount},</if>
            <if test="universalAccount != null">#{universalAccount},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增资金运用规模表 -->
    <insert id="batchInsertFundUtilizationScale" parameterType="java.util.List">
        insert into t_asm_fund_utilization_scale(accounting_period, item_name, item_classification_level, data_type, 
                                                  general_account, traditional_account, capital_supplement_bond_account, 
                                                  bonus_account, universal_account, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.itemClassificationLevel}, #{item.dataType}, 
             #{item.generalAccount}, #{item.traditionalAccount}, #{item.capitalSupplementBondAccount}, 
             #{item.bonusAccount}, #{item.universalAccount}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改资金运用规模表 -->
    <update id="updateFundUtilizationScale" parameterType="com.xl.alm.app.entity.FundUtilizationScaleEntity">
        update t_asm_fund_utilization_scale
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemClassificationLevel != null and itemClassificationLevel != ''">item_classification_level = #{itemClassificationLevel},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="generalAccount != null">general_account = #{generalAccount},</if>
            <if test="traditionalAccount != null">traditional_account = #{traditionalAccount},</if>
            <if test="capitalSupplementBondAccount != null">capital_supplement_bond_account = #{capitalSupplementBondAccount},</if>
            <if test="bonusAccount != null">bonus_account = #{bonusAccount},</if>
            <if test="universalAccount != null">universal_account = #{universalAccount},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <!-- 删除指定id的资金运用规模表数据 -->
    <update id="deleteFundUtilizationScaleById" parameterType="Long">
        update t_asm_fund_utilization_scale set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除资金运用规模表 -->
    <update id="deleteFundUtilizationScaleByIds" parameterType="Long">
        update t_asm_fund_utilization_scale set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 删除指定账期的资金运用规模表数据 -->
    <update id="deleteFundUtilizationScaleByPeriod" parameterType="String">
        update t_asm_fund_utilization_scale set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
