<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FinancialBudgetExpenseSplitMapper">

    <resultMap type="com.xl.alm.app.entity.FinancialBudgetExpenseSplitEntity" id="FinancialBudgetExpenseSplitResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="scenarioName" column="scenario_name"/>
        <result property="financialExpenseType" column="financial_expense_type"/>
        <result property="businessType" column="business_type"/>
        <result property="designType" column="design_type"/>
        <result property="cashFlowValueSet" column="cash_flow_value_set"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFinancialBudgetExpenseSplitVo">
        select id, accounting_period, scenario_name, financial_expense_type, business_type, design_type, 
               cash_flow_value_set, create_by, create_time, update_by, update_time, is_del
        from t_cft_financial_budget_expense_split
    </sql>

    <select id="selectFinancialBudgetExpenseSplitEntityList" parameterType="com.xl.alm.app.query.FinancialBudgetExpenseSplitQuery" resultMap="FinancialBudgetExpenseSplitResult">
        <include refid="selectFinancialBudgetExpenseSplitVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="scenarioName != null and scenarioName != ''">
                and scenario_name = #{scenarioName}
            </if>
            <if test="financialExpenseType != null and financialExpenseType != ''">
                and financial_expense_type = #{financialExpenseType}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="designType != null and designType != ''">
                and design_type = #{designType}
            </if>
        </where>
        order by accounting_period desc, scenario_name, financial_expense_type, business_type, design_type
    </select>

    <select id="selectFinancialBudgetExpenseSplitEntityById" parameterType="Long" resultMap="FinancialBudgetExpenseSplitResult">
        <include refid="selectFinancialBudgetExpenseSplitVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectFinancialBudgetExpenseSplitEntityByCondition" resultMap="FinancialBudgetExpenseSplitResult">
        <include refid="selectFinancialBudgetExpenseSplitVo"/>
        where accounting_period = #{accountingPeriod}
          and scenario_name = #{scenarioName}
          and financial_expense_type = #{financialExpenseType}
          and business_type = #{businessType}
          and design_type = #{designType}
          and is_del = 0
    </select>

    <insert id="insertFinancialBudgetExpenseSplitEntity" parameterType="com.xl.alm.app.entity.FinancialBudgetExpenseSplitEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_financial_budget_expense_split
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name,</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">financial_expense_type,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="designType != null and designType != ''">design_type,</if>
            <if test="cashFlowValueSet != null">cash_flow_value_set,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">#{scenarioName},</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">#{financialExpenseType},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="designType != null and designType != ''">#{designType},</if>
            <if test="cashFlowValueSet != null">#{cashFlowValueSet},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateFinancialBudgetExpenseSplitEntity" parameterType="com.xl.alm.app.entity.FinancialBudgetExpenseSplitEntity">
        update t_cft_financial_budget_expense_split
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name = #{scenarioName},</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">financial_expense_type = #{financialExpenseType},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="cashFlowValueSet != null">cash_flow_value_set = #{cashFlowValueSet},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialBudgetExpenseSplitEntityById" parameterType="Long">
        update t_cft_financial_budget_expense_split set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteFinancialBudgetExpenseSplitEntityByIds" parameterType="String">
        update t_cft_financial_budget_expense_split set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertFinancialBudgetExpenseSplitEntity" parameterType="java.util.List">
        insert into t_cft_financial_budget_expense_split(accounting_period, scenario_name, financial_expense_type, business_type, design_type, cash_flow_value_set, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.scenarioName}, #{item.financialExpenseType}, #{item.businessType}, #{item.designType}, #{item.cashFlowValueSet}, #{item.createBy})
        </foreach>
    </insert>

    <delete id="deleteFinancialBudgetExpenseSplitEntityByPeriod" parameterType="String">
        update t_cft_financial_budget_expense_split set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
