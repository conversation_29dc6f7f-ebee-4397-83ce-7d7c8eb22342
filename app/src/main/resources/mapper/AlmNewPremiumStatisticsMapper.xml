<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AlmNewPremiumStatisticsMapper">
    
    <resultMap type="com.xl.alm.app.entity.AlmNewPremiumStatisticsEntity" id="AlmNewPremiumStatisticsResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="statisticsType"    column="statistics_type"    />
        <result property="statisticsSubType"    column="statistics_sub_type"    />
        <result property="newPremiumTotal"    column="new_premium_total"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectAlmNewPremiumStatisticsVo">
        select id, accounting_period, statistics_type, statistics_sub_type, new_premium_total, remark, create_time, create_by, update_time, update_by, is_del from t_liab_alm_new_premium_statistics
    </sql>

    <select id="selectAlmNewPremiumStatisticsList" parameterType="com.xl.alm.app.entity.AlmNewPremiumStatisticsEntity" resultMap="AlmNewPremiumStatisticsResult">
        <include refid="selectAlmNewPremiumStatisticsVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="statisticsType != null  and statisticsType != ''"> and statistics_type = #{statisticsType}</if>
            <if test="statisticsSubType != null  and statisticsSubType != ''"> and statistics_sub_type = #{statisticsSubType}</if>
            <if test="newPremiumTotal != null "> and new_premium_total = #{newPremiumTotal}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            and is_del = 0
        </where>
        order by accounting_period desc, statistics_type, statistics_sub_type
    </select>
    
    <select id="selectAlmNewPremiumStatisticsById" parameterType="Long" resultMap="AlmNewPremiumStatisticsResult">
        <include refid="selectAlmNewPremiumStatisticsVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertAlmNewPremiumStatistics" parameterType="com.xl.alm.app.entity.AlmNewPremiumStatisticsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_alm_new_premium_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="statisticsType != null and statisticsType != ''">statistics_type,</if>
            <if test="statisticsSubType != null and statisticsSubType != ''">statistics_sub_type,</if>
            <if test="newPremiumTotal != null">new_premium_total,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="statisticsType != null and statisticsType != ''">#{statisticsType},</if>
            <if test="statisticsSubType != null and statisticsSubType != ''">#{statisticsSubType},</if>
            <if test="newPremiumTotal != null">#{newPremiumTotal},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAlmNewPremiumStatistics" parameterType="com.xl.alm.app.entity.AlmNewPremiumStatisticsEntity">
        update t_liab_alm_new_premium_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="statisticsType != null and statisticsType != ''">statistics_type = #{statisticsType},</if>
            <if test="statisticsSubType != null and statisticsSubType != ''">statistics_sub_type = #{statisticsSubType},</if>
            <if test="newPremiumTotal != null">new_premium_total = #{newPremiumTotal},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlmNewPremiumStatisticsById" parameterType="Long">
        update t_liab_alm_new_premium_statistics set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAlmNewPremiumStatisticsByIds" parameterType="String">
        update t_liab_alm_new_premium_statistics set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkUniqueRecord" resultType="int">
        select count(1) from t_liab_alm_new_premium_statistics 
        where accounting_period = #{accountingPeriod} 
        and statistics_type = #{statisticsType}
        and statistics_sub_type = #{statisticsSubType}
        and is_del = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="batchInsertAlmNewPremiumStatistics" parameterType="java.util.List">
        insert into t_liab_alm_new_premium_statistics(accounting_period, statistics_type, statistics_sub_type, new_premium_total, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.statisticsType}, #{item.statisticsSubType}, #{item.newPremiumTotal}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

</mapper>
