<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.LiabScaleDetailMapper">

    <resultMap type="com.xl.alm.app.entity.LiabScaleDetailEntity" id="LiabScaleDetailEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="actuarialCode" column="actuarial_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="productName" column="product_name"/>
        <result property="termFlag" column="term_flag"/>
        <result property="insuranceMainType" column="insurance_main_type"/>
        <result property="insuranceSubType" column="insurance_sub_type"/>
        <result property="designType" column="design_type"/>
        <result property="reasonableLiability" column="reasonable_liability"/>
        <result property="riskMargin" column="risk_margin"/>
        <result property="residualMargin" column="residual_margin"/>
        <result property="outstandingClaimReserveL" column="outstanding_claim_reserve_l"/>
        <result property="unearnedPremiumReserve" column="unearned_premium_reserve"/>
        <result property="outstandingClaimReserveS" column="outstanding_claim_reserve_s"/>
        <result property="investmentLinkedLiability" column="investment_linked_liability"/>
        <result property="receivableUnearnedPremiumReserve" column="receivable_unearned_premium_reserve"/>
        <result property="receivableOutstandingClaimReserve" column="receivable_outstanding_claim_reserve"/>
        <result property="receivableLifeInsuranceReserve" column="receivable_life_insurance_reserve"/>
        <result property="receivableLongTermHealthReserve" column="receivable_long_term_health_reserve"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectLiabScaleDetailEntityVo">
        select id, accounting_period, actuarial_code, business_code, product_name, term_flag,
               insurance_main_type, insurance_sub_type, design_type, reasonable_liability, risk_margin,
               residual_margin, outstanding_claim_reserve_l, unearned_premium_reserve, outstanding_claim_reserve_s,
               investment_linked_liability, receivable_unearned_premium_reserve, receivable_outstanding_claim_reserve,
               receivable_life_insurance_reserve, receivable_long_term_health_reserve, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_liab_scale_detail
    </sql>

    <!-- 查询负债规模明细表列表 -->
    <select id="selectLiabScaleDetailEntityList" parameterType="com.xl.alm.app.query.LiabScaleDetailQuery" resultMap="LiabScaleDetailEntityResult">
        <include refid="selectLiabScaleDetailEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code like concat('%', #{actuarialCode}, '%')</if>
            <if test="businessCode != null and businessCode != ''">and business_code like concat('%', #{businessCode}, '%')</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="termFlag != null and termFlag != ''">and term_flag = #{termFlag}</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">and insurance_main_type = #{insuranceMainType}</if>
            <if test="insuranceSubType != null and insuranceSubType != ''">and insurance_sub_type = #{insuranceSubType}</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            <if test="reasonableLiability != null">and reasonable_liability = #{reasonableLiability}</if>
            <if test="riskMargin != null">and risk_margin = #{riskMargin}</if>
            <if test="residualMargin != null">and residual_margin = #{residualMargin}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据id查询负债规模明细表 -->
    <select id="selectLiabScaleDetailEntityById" parameterType="Long" resultMap="LiabScaleDetailEntityResult">
        <include refid="selectLiabScaleDetailEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和精算代码查询负债规模明细表 -->
    <select id="selectLiabScaleDetailEntityByCondition" resultMap="LiabScaleDetailEntityResult">
        <include refid="selectLiabScaleDetailEntityVo"/>
        where accounting_period = #{accountingPeriod}
        and actuarial_code = #{actuarialCode}
        and is_del = 0
    </select>

    <!-- 新增负债规模明细表 -->
    <insert id="insertLiabScaleDetailEntity" parameterType="com.xl.alm.app.entity.LiabScaleDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_scale_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code,</if>
            <if test="businessCode != null and businessCode != ''">business_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="termFlag != null and termFlag != ''">term_flag,</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">insurance_main_type,</if>
            <if test="insuranceSubType != null and insuranceSubType != ''">insurance_sub_type,</if>
            <if test="designType != null and designType != ''">design_type,</if>
            <if test="reasonableLiability != null">reasonable_liability,</if>
            <if test="riskMargin != null">risk_margin,</if>
            <if test="residualMargin != null">residual_margin,</if>
            <if test="outstandingClaimReserveL != null">outstanding_claim_reserve_l,</if>
            <if test="unearnedPremiumReserve != null">unearned_premium_reserve,</if>
            <if test="outstandingClaimReserveS != null">outstanding_claim_reserve_s,</if>
            <if test="investmentLinkedLiability != null">investment_linked_liability,</if>
            <if test="receivableUnearnedPremiumReserve != null">receivable_unearned_premium_reserve,</if>
            <if test="receivableOutstandingClaimReserve != null">receivable_outstanding_claim_reserve,</if>
            <if test="receivableLifeInsuranceReserve != null">receivable_life_insurance_reserve,</if>
            <if test="receivableLongTermHealthReserve != null">receivable_long_term_health_reserve,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="actuarialCode != null and actuarialCode != ''">#{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">#{businessCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="termFlag != null and termFlag != ''">#{termFlag},</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">#{insuranceMainType},</if>
            <if test="insuranceSubType != null and insuranceSubType != ''">#{insuranceSubType},</if>
            <if test="designType != null and designType != ''">#{designType},</if>
            <if test="reasonableLiability != null">#{reasonableLiability},</if>
            <if test="riskMargin != null">#{riskMargin},</if>
            <if test="residualMargin != null">#{residualMargin},</if>
            <if test="outstandingClaimReserveL != null">#{outstandingClaimReserveL},</if>
            <if test="unearnedPremiumReserve != null">#{unearnedPremiumReserve},</if>
            <if test="outstandingClaimReserveS != null">#{outstandingClaimReserveS},</if>
            <if test="investmentLinkedLiability != null">#{investmentLinkedLiability},</if>
            <if test="receivableUnearnedPremiumReserve != null">#{receivableUnearnedPremiumReserve},</if>
            <if test="receivableOutstandingClaimReserve != null">#{receivableOutstandingClaimReserve},</if>
            <if test="receivableLifeInsuranceReserve != null">#{receivableLifeInsuranceReserve},</if>
            <if test="receivableLongTermHealthReserve != null">#{receivableLongTermHealthReserve},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 修改负债规模明细表 -->
    <update id="updateLiabScaleDetailEntity" parameterType="com.xl.alm.app.entity.LiabScaleDetailEntity">
        update t_liab_scale_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code = #{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="termFlag != null and termFlag != ''">term_flag = #{termFlag},</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">insurance_main_type = #{insuranceMainType},</if>
            <if test="insuranceSubType != null and insuranceSubType != ''">insurance_sub_type = #{insuranceSubType},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="reasonableLiability != null">reasonable_liability = #{reasonableLiability},</if>
            <if test="riskMargin != null">risk_margin = #{riskMargin},</if>
            <if test="residualMargin != null">residual_margin = #{residualMargin},</if>
            <if test="outstandingClaimReserveL != null">outstanding_claim_reserve_l = #{outstandingClaimReserveL},</if>
            <if test="unearnedPremiumReserve != null">unearned_premium_reserve = #{unearnedPremiumReserve},</if>
            <if test="outstandingClaimReserveS != null">outstanding_claim_reserve_s = #{outstandingClaimReserveS},</if>
            <if test="investmentLinkedLiability != null">investment_linked_liability = #{investmentLinkedLiability},</if>
            <if test="receivableUnearnedPremiumReserve != null">receivable_unearned_premium_reserve = #{receivableUnearnedPremiumReserve},</if>
            <if test="receivableOutstandingClaimReserve != null">receivable_outstanding_claim_reserve = #{receivableOutstandingClaimReserve},</if>
            <if test="receivableLifeInsuranceReserve != null">receivable_life_insurance_reserve = #{receivableLifeInsuranceReserve},</if>
            <if test="receivableLongTermHealthReserve != null">receivable_long_term_health_reserve = #{receivableLongTermHealthReserve},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除负债规模明细表 -->
    <update id="deleteLiabScaleDetailEntityById" parameterType="Long">
        update t_liab_scale_detail set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除负债规模明细表 -->
    <update id="deleteLiabScaleDetailEntityByIds" parameterType="String">
        update t_liab_scale_detail set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入负债规模明细表数据 -->
    <insert id="batchInsertLiabScaleDetailEntity" parameterType="java.util.List">
        insert into t_liab_scale_detail (accounting_period, actuarial_code, business_code, product_name, term_flag,
        insurance_main_type, insurance_sub_type, design_type, reasonable_liability, risk_margin, residual_margin,
        outstanding_claim_reserve_l, unearned_premium_reserve, outstanding_claim_reserve_s, investment_linked_liability,
        receivable_unearned_premium_reserve, receivable_outstanding_claim_reserve, receivable_life_insurance_reserve,
        receivable_long_term_health_reserve, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.actuarialCode}, #{item.businessCode}, #{item.productName}, #{item.termFlag},
            #{item.insuranceMainType}, #{item.insuranceSubType}, #{item.designType}, #{item.reasonableLiability},
            #{item.riskMargin}, #{item.residualMargin}, #{item.outstandingClaimReserveL}, #{item.unearnedPremiumReserve},
            #{item.outstandingClaimReserveS}, #{item.investmentLinkedLiability}, #{item.receivableUnearnedPremiumReserve},
            #{item.receivableOutstandingClaimReserve}, #{item.receivableLifeInsuranceReserve}, #{item.receivableLongTermHealthReserve},
            #{item.remark})
        </foreach>
    </insert>

    <!-- 删除指定账期的负债规模明细表数据 -->
    <update id="deleteLiabScaleDetailEntityByPeriod" parameterType="String">
        update t_liab_scale_detail set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
