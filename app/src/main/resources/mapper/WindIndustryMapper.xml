<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.WindIndustryMapper">

    <resultMap type="com.xl.alm.app.entity.WindIndustryEntity" id="WindIndustryResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="securityCode" column="security_code"/>
        <result property="securityName" column="security_name"/>
        <result property="industry" column="industry"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectWindIndustryVo">
        select id, accounting_period, security_code, security_name, industry,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_wind_industry
    </sql>

    <select id="selectWindIndustryList" parameterType="com.xl.alm.app.entity.WindIndustryEntity" resultMap="WindIndustryResult">
        <include refid="selectWindIndustryVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="securityCode != null and securityCode != ''">and security_code = #{securityCode}</if>
            <if test="securityName != null and securityName != ''">and security_name like concat('%', #{securityName}, '%')</if>
            <if test="industry != null and industry != ''">and industry like concat('%', #{industry}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <select id="selectWindIndustryById" parameterType="Long" resultMap="WindIndustryResult">
        <include refid="selectWindIndustryVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertWindIndustry" parameterType="com.xl.alm.app.entity.WindIndustryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_wind_industry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="securityName != null and securityName != ''">security_name,</if>
            <if test="industry != null and industry != ''">industry,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="securityName != null and securityName != ''">#{securityName},</if>
            <if test="industry != null and industry != ''">#{industry},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateWindIndustry" parameterType="com.xl.alm.app.entity.WindIndustryEntity">
        update t_ast_wind_industry
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="securityName != null and securityName != ''">security_name = #{securityName},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <update id="deleteWindIndustryById" parameterType="Long">
        update t_ast_wind_industry set is_del = 1, update_time = now()
        where id = #{id} and is_del = 0
    </update>

    <update id="deleteWindIndustryByIds" parameterType="Long">
        update t_ast_wind_industry set is_del = 1, update_time = now()
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>

    <insert id="batchInsertWindIndustry" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_wind_industry (
            accounting_period, security_code, security_name, industry, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod},
            #{item.securityCode},
            #{item.securityName},
            #{item.industry},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
    </insert>

    <update id="deleteWindIndustryByPeriod" parameterType="String">
        update t_ast_wind_industry set is_del = 1, update_time = now()
        where accounting_period = #{accountingPeriod} and is_del = 0
    </update>

</mapper>
