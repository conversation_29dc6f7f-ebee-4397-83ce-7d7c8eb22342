<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.Risk10DayVarMapper">

    <resultMap type="com.xl.alm.app.entity.Risk10DayVarEntity" id="Risk10DayVarEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="itemCategory" column="item_category"/>
        <result property="samplePeriod" column="sample_period"/>
        <result property="varValue" column="var_value"/>
        <result property="bookValue" column="book_value"/>
        <result property="varBookValueRatio" column="var_book_value_ratio"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectRisk10DayVarVo">
        select id, accounting_period, domestic_foreign, item_category, sample_period, var_value, book_value, var_book_value_ratio, create_by, create_time, update_by, update_time, is_del from t_asm_risk_10day_var
    </sql>

    <!-- 查询风险10日VaR值表列表 -->
    <select id="selectRisk10DayVarList" parameterType="com.xl.alm.app.query.Risk10DayVarQuery" resultMap="Risk10DayVarEntityResult">
        <include refid="selectRisk10DayVarVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="itemCategory != null and itemCategory != ''">and item_category like concat('%', #{itemCategory}, '%')</if>
            <if test="samplePeriod != null and samplePeriod != ''">and sample_period = #{samplePeriod}</if>
            <if test="varValue != null">and var_value = #{varValue}</if>
            <if test="bookValue != null">and book_value = #{bookValue}</if>
            <if test="varBookValueRatio != null">and var_book_value_ratio = #{varBookValueRatio}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询风险10日VaR值表 -->
    <select id="selectRisk10DayVarById" parameterType="Long" resultMap="Risk10DayVarEntityResult">
        <include refid="selectRisk10DayVarVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、境内外标识、项目分类和样本期限查询风险10日VaR值表 -->
    <select id="selectRisk10DayVarByCondition" resultMap="Risk10DayVarEntityResult">
        <include refid="selectRisk10DayVarVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="itemCategory != null and itemCategory != ''">and item_category = #{itemCategory}</if>
            <if test="samplePeriod != null and samplePeriod != ''">and sample_period = #{samplePeriod}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增风险10日VaR值表 -->
    <insert id="insertRisk10DayVar" parameterType="com.xl.alm.app.entity.Risk10DayVarEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_risk_10day_var
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="itemCategory != null and itemCategory != ''">item_category,</if>
            <if test="samplePeriod != null and samplePeriod != ''">sample_period,</if>
            <if test="varValue != null">var_value,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="varBookValueRatio != null">var_book_value_ratio,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="itemCategory != null and itemCategory != ''">#{itemCategory},</if>
            <if test="samplePeriod != null and samplePeriod != ''">#{samplePeriod},</if>
            <if test="varValue != null">#{varValue},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="varBookValueRatio != null">#{varBookValueRatio},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增风险10日VaR值表 -->
    <insert id="batchInsertRisk10DayVar" parameterType="java.util.List">
        insert into t_asm_risk_10day_var(accounting_period, domestic_foreign, item_category, sample_period, var_value, book_value, var_book_value_ratio, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.itemCategory}, #{item.samplePeriod}, #{item.varValue}, #{item.bookValue}, #{item.varBookValueRatio}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改风险10日VaR值表 -->
    <update id="updateRisk10DayVar" parameterType="com.xl.alm.app.entity.Risk10DayVarEntity">
        update t_asm_risk_10day_var
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="itemCategory != null and itemCategory != ''">item_category = #{itemCategory},</if>
            <if test="samplePeriod != null and samplePeriod != ''">sample_period = #{samplePeriod},</if>
            <if test="varValue != null">var_value = #{varValue},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="varBookValueRatio != null">var_book_value_ratio = #{varBookValueRatio},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除风险10日VaR值表 -->
    <update id="deleteRisk10DayVarById" parameterType="Long">
        update t_asm_risk_10day_var set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除风险10日VaR值表 -->
    <update id="deleteRisk10DayVarByIds" parameterType="String">
        update t_asm_risk_10day_var set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
