<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.SubAccountYieldRateMapper">

    <resultMap type="com.xl.alm.app.entity.SubAccountYieldRateEntity" id="SubAccountYieldRateEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="accountName" column="account_name"/>
        <result property="accountingYieldRate" column="accounting_yield_rate"/>
        <result property="comprehensiveYieldRate" column="comprehensive_yield_rate"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectSubAccountYieldRateEntityVo">
        select id, accounting_period, account_name, accounting_yield_rate, comprehensive_yield_rate, 
               remark, create_time, create_by, update_time, update_by, is_del
        from t_base_sub_account_yield_rate
    </sql>

    <!-- 查询子账户收益率列表 -->
    <select id="selectSubAccountYieldRateEntityList" parameterType="com.xl.alm.app.query.SubAccountYieldRateQuery" resultMap="SubAccountYieldRateEntityResult">
        <include refid="selectSubAccountYieldRateEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="accountingYieldRate != null">and accounting_yield_rate = #{accountingYieldRate}</if>
            <if test="comprehensiveYieldRate != null">and comprehensive_yield_rate = #{comprehensiveYieldRate}</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 用id查询子账户收益率 -->
    <select id="selectSubAccountYieldRateEntityById" parameterType="Long" resultMap="SubAccountYieldRateEntityResult">
        <include refid="selectSubAccountYieldRateEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据统计期间和子账户名称查询子账户收益率 -->
    <select id="selectSubAccountYieldRateEntityByCondition" resultMap="SubAccountYieldRateEntityResult">
        <include refid="selectSubAccountYieldRateEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null and accountName != ''">and account_name = #{accountName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增子账户收益率 -->
    <insert id="insertSubAccountYieldRateEntity" parameterType="com.xl.alm.app.entity.SubAccountYieldRateEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_sub_account_yield_rate (
            accounting_period, account_name, accounting_yield_rate, comprehensive_yield_rate, 
            remark, create_by, update_by
        ) values (
            #{accountingPeriod}, #{accountName}, #{accountingYieldRate}, #{comprehensiveYieldRate}, 
            #{remark}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量插入子账户收益率数据 -->
    <insert id="batchInsertSubAccountYieldRateEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_sub_account_yield_rate (
            accounting_period, account_name, accounting_yield_rate, comprehensive_yield_rate, 
            remark, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod},
            #{item.accountName},
            #{item.accountingYieldRate},
            #{item.comprehensiveYieldRate},
            #{item.remark},
            #{item.createBy},
            #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 更新子账户收益率 -->
    <update id="updateSubAccountYieldRateEntity" parameterType="com.xl.alm.app.entity.SubAccountYieldRateEntity">
        update t_base_sub_account_yield_rate
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="accountingYieldRate != null">accounting_yield_rate = #{accountingYieldRate},</if>
            <if test="comprehensiveYieldRate != null">comprehensive_yield_rate = #{comprehensiveYieldRate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除指定统计期间的子账户收益率数据 -->
    <update id="deleteSubAccountYieldRateEntityByPeriod" parameterType="String">
        update t_base_sub_account_yield_rate set is_del = 1, update_time = now() 
        where accounting_period = #{accountingPeriod} and is_del = 0
    </update>

    <!-- 删除指定id的子账户收益率数据 -->
    <update id="deleteSubAccountYieldRateEntityById" parameterType="Long">
        update t_base_sub_account_yield_rate set is_del = 1, update_time = now() 
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除子账户收益率数据 -->
    <update id="deleteSubAccountYieldRateEntityByIds" parameterType="Long">
        update t_base_sub_account_yield_rate set is_del = 1, update_time = now() 
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
