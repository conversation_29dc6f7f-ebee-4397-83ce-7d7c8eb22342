<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.CashFlowItemMappingMapper">

    <resultMap type="com.xl.alm.app.entity.CashFlowItemMappingEntity" id="CashFlowItemMappingEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="cashFlowItem" column="cash_flow_item"/>
        <result property="testItem" column="test_item"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectCashFlowItemMappingEntityVo">
        select id, accounting_period, cash_flow_item, test_item, remark, create_time, create_by, 
               update_time, update_by, is_del
        from t_base_cash_flow_item_mapping
    </sql>

    <!-- 查询现金流项目映射表列表 -->
    <select id="selectCashFlowItemMappingEntityList" parameterType="com.xl.alm.app.query.CashFlowItemMappingQuery" resultMap="CashFlowItemMappingEntityResult">
        <include refid="selectCashFlowItemMappingEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="cashFlowItem != null and cashFlowItem != ''">and cash_flow_item like concat('%', #{cashFlowItem}, '%')</if>
            <if test="testItem != null and testItem != ''">and test_item like concat('%', #{testItem}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据id查询现金流项目映射表 -->
    <select id="selectCashFlowItemMappingEntityById" parameterType="Long" resultMap="CashFlowItemMappingEntityResult">
        <include refid="selectCashFlowItemMappingEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期查询现金流项目映射表 -->
    <select id="selectCashFlowItemMappingEntityByPeriod" parameterType="String" resultMap="CashFlowItemMappingEntityResult">
        <include refid="selectCashFlowItemMappingEntityVo"/>
        where accounting_period = #{accountingPeriod} and is_del = 0
        order by cash_flow_item
    </select>

    <!-- 根据现金流量表项目查询映射关系 -->
    <select id="selectCashFlowItemMappingEntityByCondition" resultMap="CashFlowItemMappingEntityResult">
        <include refid="selectCashFlowItemMappingEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="cashFlowItem != null and cashFlowItem != ''">and cash_flow_item = #{cashFlowItem}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增现金流项目映射表 -->
    <insert id="insertCashFlowItemMappingEntity" parameterType="com.xl.alm.app.entity.CashFlowItemMappingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_cash_flow_item_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="cashFlowItem != null and cashFlowItem != ''">cash_flow_item,</if>
            <if test="testItem != null and testItem != ''">test_item,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="cashFlowItem != null and cashFlowItem != ''">#{cashFlowItem},</if>
            <if test="testItem != null and testItem != ''">#{testItem},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增现金流项目映射表 -->
    <insert id="batchInsertCashFlowItemMappingEntity" parameterType="java.util.List">
        insert into t_base_cash_flow_item_mapping(accounting_period, cash_flow_item, test_item, 
                                                  remark, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.cashFlowItem}, #{item.testItem}, 
             #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 更新现金流项目映射表 -->
    <update id="updateCashFlowItemMappingEntity" parameterType="com.xl.alm.app.entity.CashFlowItemMappingEntity">
        update t_base_cash_flow_item_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="cashFlowItem != null and cashFlowItem != ''">cash_flow_item = #{cashFlowItem},</if>
            <if test="testItem != null and testItem != ''">test_item = #{testItem},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <!-- 删除现金流项目映射表 -->
    <update id="deleteCashFlowItemMappingEntityById" parameterType="Long">
        update t_base_cash_flow_item_mapping set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除现金流项目映射表 -->
    <update id="deleteCashFlowItemMappingEntityByIds" parameterType="String">
        update t_base_cash_flow_item_mapping set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除现金流项目映射表数据 -->
    <update id="deleteCashFlowItemMappingEntityByPeriod" parameterType="String">
        update t_base_cash_flow_item_mapping set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
