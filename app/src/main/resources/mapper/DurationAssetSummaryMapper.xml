<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.DurationAssetSummaryMapper">
    
    <resultMap type="com.xl.alm.app.entity.DurationAssetSummaryEntity" id="DurationAssetSummaryResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="accountName"    column="account_name"    />
        <result property="marketValue"    column="market_value"    />
        <result property="bookBalance"    column="book_balance"    />
        <result property="bookValue"    column="book_value"    />
        <result property="bookValueSigma0"    column="book_value_sigma_0"    />
        <result property="bookValueSigma9"    column="book_value_sigma_9"    />
        <result property="bookValueSigma17"    column="book_value_sigma_17"    />
        <result property="bookValueSigma77"    column="book_value_sigma_77"    />
        <result property="evalMaturityYield"    column="eval_maturity_yield"    />
        <result property="evalPresentValue"    column="eval_present_value"    />
        <result property="assetModifiedDuration"    column="asset_modified_duration"    />
        <result property="assetEffectiveDuration"    column="asset_effective_duration"    />
        <result property="dv100"    column="dv10_0"    />
        <result property="dv1005"    column="dv10_0_5"    />
        <result property="dv101"    column="dv10_1"    />
        <result property="dv102"    column="dv10_2"    />
        <result property="dv103"    column="dv10_3"    />
        <result property="dv104"    column="dv10_4"    />
        <result property="dv105"    column="dv10_5"    />
        <result property="dv106"    column="dv10_6"    />
        <result property="dv107"    column="dv10_7"    />
        <result property="dv108"    column="dv10_8"    />
        <result property="dv1010"    column="dv10_10"    />
        <result property="dv1012"    column="dv10_12"    />
        <result property="dv1015"    column="dv10_15"    />
        <result property="dv1020"    column="dv10_20"    />
        <result property="dv1025"    column="dv10_25"    />
        <result property="dv1030"    column="dv10_30"    />
        <result property="dv1035"    column="dv10_35"    />
        <result property="dv1040"    column="dv10_40"    />
        <result property="dv1045"    column="dv10_45"    />
        <result property="dv1050"    column="dv10_50"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectDurationAssetSummaryVo">
        select id, account_period, account_name, market_value, book_balance, book_value, 
               book_value_sigma_0, book_value_sigma_9, book_value_sigma_17, book_value_sigma_77,
               eval_maturity_yield, eval_present_value, asset_modified_duration, asset_effective_duration,
               dv10_0, dv10_0_5, dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8,
               dv10_10, dv10_12, dv10_15, dv10_20, dv10_25, dv10_30, dv10_35, dv10_40, dv10_45, dv10_50,
               create_by, create_time, update_by, update_time, is_del
        from t_adur_duration_asset_summary
    </sql>

    <select id="selectDurationAssetSummaryEntityList" parameterType="com.xl.alm.app.query.DurationAssetSummaryQuery" resultMap="DurationAssetSummaryResult">
        <include refid="selectDurationAssetSummaryVo"/>
        <where>  
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            and is_del = #{isDel}
        </where>
        order by id desc
    </select>
    
    <select id="selectDurationAssetSummaryEntityById" parameterType="Long" resultMap="DurationAssetSummaryResult">
        <include refid="selectDurationAssetSummaryVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectDurationAssetSummaryEntityByCondition" resultMap="DurationAssetSummaryResult">
        <include refid="selectDurationAssetSummaryVo"/>
        where account_period = #{accountPeriod} and account_name = #{accountName} and is_del = 0
    </select>
        
    <insert id="insertDurationAssetSummaryEntity" parameterType="com.xl.alm.app.entity.DurationAssetSummaryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_duration_asset_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="bookValueSigma0 != null">book_value_sigma_0,</if>
            <if test="bookValueSigma9 != null">book_value_sigma_9,</if>
            <if test="bookValueSigma17 != null">book_value_sigma_17,</if>
            <if test="bookValueSigma77 != null">book_value_sigma_77,</if>
            <if test="evalMaturityYield != null">eval_maturity_yield,</if>
            <if test="evalPresentValue != null">eval_present_value,</if>
            <if test="assetModifiedDuration != null">asset_modified_duration,</if>
            <if test="assetEffectiveDuration != null">asset_effective_duration,</if>
            <if test="dv100 != null">dv10_0,</if>
            <if test="dv1005 != null">dv10_0_5,</if>
            <if test="dv101 != null">dv10_1,</if>
            <if test="dv102 != null">dv10_2,</if>
            <if test="dv103 != null">dv10_3,</if>
            <if test="dv104 != null">dv10_4,</if>
            <if test="dv105 != null">dv10_5,</if>
            <if test="dv106 != null">dv10_6,</if>
            <if test="dv107 != null">dv10_7,</if>
            <if test="dv108 != null">dv10_8,</if>
            <if test="dv1010 != null">dv10_10,</if>
            <if test="dv1012 != null">dv10_12,</if>
            <if test="dv1015 != null">dv10_15,</if>
            <if test="dv1020 != null">dv10_20,</if>
            <if test="dv1025 != null">dv10_25,</if>
            <if test="dv1030 != null">dv10_30,</if>
            <if test="dv1035 != null">dv10_35,</if>
            <if test="dv1040 != null">dv10_40,</if>
            <if test="dv1045 != null">dv10_45,</if>
            <if test="dv1050 != null">dv10_50,</if>
            <if test="createBy != null">create_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="bookValueSigma0 != null">#{bookValueSigma0},</if>
            <if test="bookValueSigma9 != null">#{bookValueSigma9},</if>
            <if test="bookValueSigma17 != null">#{bookValueSigma17},</if>
            <if test="bookValueSigma77 != null">#{bookValueSigma77},</if>
            <if test="evalMaturityYield != null">#{evalMaturityYield},</if>
            <if test="evalPresentValue != null">#{evalPresentValue},</if>
            <if test="assetModifiedDuration != null">#{assetModifiedDuration},</if>
            <if test="assetEffectiveDuration != null">#{assetEffectiveDuration},</if>
            <if test="dv100 != null">#{dv100},</if>
            <if test="dv1005 != null">#{dv1005},</if>
            <if test="dv101 != null">#{dv101},</if>
            <if test="dv102 != null">#{dv102},</if>
            <if test="dv103 != null">#{dv103},</if>
            <if test="dv104 != null">#{dv104},</if>
            <if test="dv105 != null">#{dv105},</if>
            <if test="dv106 != null">#{dv106},</if>
            <if test="dv107 != null">#{dv107},</if>
            <if test="dv108 != null">#{dv108},</if>
            <if test="dv1010 != null">#{dv1010},</if>
            <if test="dv1012 != null">#{dv1012},</if>
            <if test="dv1015 != null">#{dv1015},</if>
            <if test="dv1020 != null">#{dv1020},</if>
            <if test="dv1025 != null">#{dv1025},</if>
            <if test="dv1030 != null">#{dv1030},</if>
            <if test="dv1035 != null">#{dv1035},</if>
            <if test="dv1040 != null">#{dv1040},</if>
            <if test="dv1045 != null">#{dv1045},</if>
            <if test="dv1050 != null">#{dv1050},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <insert id="batchInsertDurationAssetSummaryEntity" parameterType="java.util.List">
        insert into t_adur_duration_asset_summary(
            account_period, account_name, market_value, book_balance, book_value,
            book_value_sigma_0, book_value_sigma_9, book_value_sigma_17, book_value_sigma_77,
            eval_maturity_yield, eval_present_value, asset_modified_duration, asset_effective_duration,
            dv10_0, dv10_0_5, dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8,
            dv10_10, dv10_12, dv10_15, dv10_20, dv10_25, dv10_30, dv10_35, dv10_40, dv10_45, dv10_50,
            create_by, is_del
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.accountName}, #{item.marketValue}, #{item.bookBalance}, #{item.bookValue},
                #{item.bookValueSigma0}, #{item.bookValueSigma9}, #{item.bookValueSigma17}, #{item.bookValueSigma77},
                #{item.evalMaturityYield}, #{item.evalPresentValue}, #{item.assetModifiedDuration}, #{item.assetEffectiveDuration},
                #{item.dv100}, #{item.dv1005}, #{item.dv101}, #{item.dv102}, #{item.dv103}, #{item.dv104}, #{item.dv105}, 
                #{item.dv106}, #{item.dv107}, #{item.dv108}, #{item.dv1010}, #{item.dv1012}, #{item.dv1015}, #{item.dv1020}, 
                #{item.dv1025}, #{item.dv1030}, #{item.dv1035}, #{item.dv1040}, #{item.dv1045}, #{item.dv1050},
                #{item.createBy}, #{item.isDel}
            )
        </foreach>
    </insert>

    <update id="updateDurationAssetSummaryEntity" parameterType="com.xl.alm.app.entity.DurationAssetSummaryEntity">
        update t_adur_duration_asset_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="bookValueSigma0 != null">book_value_sigma_0 = #{bookValueSigma0},</if>
            <if test="bookValueSigma9 != null">book_value_sigma_9 = #{bookValueSigma9},</if>
            <if test="bookValueSigma17 != null">book_value_sigma_17 = #{bookValueSigma17},</if>
            <if test="bookValueSigma77 != null">book_value_sigma_77 = #{bookValueSigma77},</if>
            <if test="evalMaturityYield != null">eval_maturity_yield = #{evalMaturityYield},</if>
            <if test="evalPresentValue != null">eval_present_value = #{evalPresentValue},</if>
            <if test="assetModifiedDuration != null">asset_modified_duration = #{assetModifiedDuration},</if>
            <if test="assetEffectiveDuration != null">asset_effective_duration = #{assetEffectiveDuration},</if>
            <if test="dv100 != null">dv10_0 = #{dv100},</if>
            <if test="dv1005 != null">dv10_0_5 = #{dv1005},</if>
            <if test="dv101 != null">dv10_1 = #{dv101},</if>
            <if test="dv102 != null">dv10_2 = #{dv102},</if>
            <if test="dv103 != null">dv10_3 = #{dv103},</if>
            <if test="dv104 != null">dv10_4 = #{dv104},</if>
            <if test="dv105 != null">dv10_5 = #{dv105},</if>
            <if test="dv106 != null">dv10_6 = #{dv106},</if>
            <if test="dv107 != null">dv10_7 = #{dv107},</if>
            <if test="dv108 != null">dv10_8 = #{dv108},</if>
            <if test="dv1010 != null">dv10_10 = #{dv1010},</if>
            <if test="dv1012 != null">dv10_12 = #{dv1012},</if>
            <if test="dv1015 != null">dv10_15 = #{dv1015},</if>
            <if test="dv1020 != null">dv10_20 = #{dv1020},</if>
            <if test="dv1025 != null">dv10_25 = #{dv1025},</if>
            <if test="dv1030 != null">dv10_30 = #{dv1030},</if>
            <if test="dv1035 != null">dv10_35 = #{dv1035},</if>
            <if test="dv1040 != null">dv10_40 = #{dv1040},</if>
            <if test="dv1045 != null">dv10_45 = #{dv1045},</if>
            <if test="dv1050 != null">dv10_50 = #{dv1050},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDurationAssetSummaryEntityById" parameterType="Long">
        update t_adur_duration_asset_summary set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteDurationAssetSummaryEntityByIds" parameterType="String">
        update t_adur_duration_asset_summary set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDurationAssetSummaryEntityByPeriod" parameterType="String">
        delete from t_adur_duration_asset_summary where account_period = #{accountPeriod}
    </delete>
</mapper>
