<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.IndustryConcentrationRiskMapper">

    <resultMap type="com.xl.alm.app.entity.IndustryConcentrationRiskEntity" id="IndustryConcentrationRiskResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="industryName" column="industry_name"/>
        <result property="industryStatisticsFlag" column="industry_statistics_flag"/>
        <result property="bookValue" column="book_value"/>
        <result property="weightPercentage" column="weight_percentage"/>
        <result property="industryConcentration" column="industry_concentration"/>
        <result property="counterpartyRanking" column="counterparty_ranking"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectIndustryConcentrationRiskVo">
        select id, accounting_period, industry_name, industry_statistics_flag, book_value, weight_percentage, industry_concentration, counterparty_ranking, create_time, create_by, update_time, update_by, is_del
        from t_acm_industry_concentration_risk
    </sql>

    <!-- 查询行业集中度风险表列表 -->
    <select id="selectIndustryConcentrationRiskList" parameterType="com.xl.alm.app.query.IndustryConcentrationRiskQuery" resultMap="IndustryConcentrationRiskResult">
        <include refid="selectIndustryConcentrationRiskVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="industryName != null and industryName != ''">
                and industry_name like concat('%', #{industryName}, '%')
            </if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">
                and industry_statistics_flag = #{industryStatisticsFlag}
            </if>
            <if test="counterpartyRanking != null and counterpartyRanking != ''">
                and counterparty_ranking = #{counterpartyRanking}
            </if>
        </where>
        order by accounting_period desc, industry_name
    </select>

    <!-- 用id查询行业集中度风险表 -->
    <select id="selectIndustryConcentrationRiskById" parameterType="Long" resultMap="IndustryConcentrationRiskResult">
        <include refid="selectIndustryConcentrationRiskVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询行业集中度风险表 -->
    <select id="selectIndustryConcentrationRiskByCondition" resultMap="IndustryConcentrationRiskResult">
        <include refid="selectIndustryConcentrationRiskVo"/>
        where accounting_period = #{accountingPeriod}
        and industry_name = #{industryName}
        and is_del = 0
    </select>

    <!-- 新增行业集中度风险表 -->
    <insert id="insertIndustryConcentrationRisk" parameterType="com.xl.alm.app.entity.IndustryConcentrationRiskEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_industry_concentration_risk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="industryName != null and industryName != ''">industry_name,</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">industry_statistics_flag,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="weightPercentage != null">weight_percentage,</if>
            <if test="industryConcentration != null">industry_concentration,</if>
            <if test="counterpartyRanking != null and counterpartyRanking != ''">counterparty_ranking,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="industryName != null and industryName != ''">#{industryName},</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">#{industryStatisticsFlag},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="weightPercentage != null">#{weightPercentage},</if>
            <if test="industryConcentration != null">#{industryConcentration},</if>
            <if test="counterpartyRanking != null and counterpartyRanking != ''">#{counterpartyRanking},</if>
        </trim>
    </insert>

    <!-- 修改行业集中度风险表 -->
    <update id="updateIndustryConcentrationRisk" parameterType="com.xl.alm.app.entity.IndustryConcentrationRiskEntity">
        update t_acm_industry_concentration_risk
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="industryName != null and industryName != ''">industry_name = #{industryName},</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">industry_statistics_flag = #{industryStatisticsFlag},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="weightPercentage != null">weight_percentage = #{weightPercentage},</if>
            <if test="industryConcentration != null">industry_concentration = #{industryConcentration},</if>
            <if test="counterpartyRanking != null and counterpartyRanking != ''">counterparty_ranking = #{counterpartyRanking},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量删除行业集中度风险表 -->
    <delete id="deleteIndustryConcentrationRiskByIds" parameterType="String">
        update t_acm_industry_concentration_risk set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除行业集中度风险表信息 -->
    <delete id="deleteIndustryConcentrationRiskById" parameterType="Long">
        update t_acm_industry_concentration_risk set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量插入行业集中度风险表数据 -->
    <insert id="batchInsertIndustryConcentrationRisk" parameterType="java.util.List">
        insert into t_acm_industry_concentration_risk
        (accounting_period, industry_name, industry_statistics_flag, book_value, weight_percentage, industry_concentration, counterparty_ranking)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.industryName}, #{item.industryStatisticsFlag}, #{item.bookValue}, #{item.weightPercentage}, #{item.industryConcentration}, #{item.counterpartyRanking})
        </foreach>
    </insert>

    <!-- 删除指定账期的行业集中度风险表数据 -->
    <delete id="deleteIndustryConcentrationRiskByPeriod" parameterType="String">
        update t_acm_industry_concentration_risk set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
