<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.VarAnalysisMapper">
    
    <resultMap type="com.xl.alm.app.entity.VarAnalysisEntity" id="VarAnalysisResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="dataType"    column="data_type"    />
        <result property="accountName"    column="account_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="dimensionName"    column="dimension_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="periodDate"    column="period_date"    />
        <result property="marketValue"    column="market_value"    />
        <result property="varAmount"    column="var_amount"    />
        <result property="incrementalVarAmount"    column="incremental_var_amount"    />
        <result property="ctePercentage"    column="cte_percentage"    />
        <result property="cteAmount"    column="cte_amount"    />
        <result property="componentVarPercentage"    column="component_var_percentage"    />
        <result property="componentVarContributionPercentage"    column="component_var_contribution_percentage"    />
        <result property="componentVarAmount"    column="component_var_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectVarAnalysisVo">
        select id, accounting_period, data_type, account_name, security_code, dimension_name, asset_name, period_date, market_value, var_amount, incremental_var_amount, cte_percentage, cte_amount, component_var_percentage, component_var_contribution_percentage, component_var_amount, create_time, create_by, update_time, update_by, is_del from t_ast_var_analysis
    </sql>

    <select id="selectVarAnalysisEntityList" parameterType="com.xl.alm.app.query.VarAnalysisQuery" resultMap="VarAnalysisResult">
        <include refid="selectVarAnalysisVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code like concat('%', #{securityCode}, '%')</if>
            <if test="dimensionName != null  and dimensionName != ''"> and dimension_name like concat('%', #{dimensionName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="periodDate != null  and periodDate != ''"> and period_date = #{periodDate}</if>
            <if test="marketValue != null "> and market_value = #{marketValue}</if>
            <if test="varAmount != null "> and var_amount = #{varAmount}</if>
            <if test="incrementalVarAmount != null "> and incremental_var_amount = #{incrementalVarAmount}</if>
            <if test="ctePercentage != null "> and cte_percentage = #{ctePercentage}</if>
            <if test="cteAmount != null "> and cte_amount = #{cteAmount}</if>
            <if test="componentVarPercentage != null "> and component_var_percentage = #{componentVarPercentage}</if>
            <if test="componentVarContributionPercentage != null "> and component_var_contribution_percentage = #{componentVarContributionPercentage}</if>
            <if test="componentVarAmount != null "> and component_var_amount = #{componentVarAmount}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectVarAnalysisEntityById" parameterType="Long" resultMap="VarAnalysisResult">
        <include refid="selectVarAnalysisVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertVarAnalysisEntity" parameterType="com.xl.alm.app.entity.VarAnalysisEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_var_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="dataType != null and dataType != ''">data_type,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="securityCode != null">security_code,</if>
            <if test="dimensionName != null and dimensionName != ''">dimension_name,</if>
            <if test="assetName != null">asset_name,</if>
            <if test="periodDate != null and periodDate != ''">period_date,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="varAmount != null">var_amount,</if>
            <if test="incrementalVarAmount != null">incremental_var_amount,</if>
            <if test="ctePercentage != null">cte_percentage,</if>
            <if test="cteAmount != null">cte_amount,</if>
            <if test="componentVarPercentage != null">component_var_percentage,</if>
            <if test="componentVarContributionPercentage != null">component_var_contribution_percentage,</if>
            <if test="componentVarAmount != null">component_var_amount,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="dataType != null and dataType != ''">#{dataType},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="securityCode != null">#{securityCode},</if>
            <if test="dimensionName != null and dimensionName != ''">#{dimensionName},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="periodDate != null and periodDate != ''">#{periodDate},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="varAmount != null">#{varAmount},</if>
            <if test="incrementalVarAmount != null">#{incrementalVarAmount},</if>
            <if test="ctePercentage != null">#{ctePercentage},</if>
            <if test="cteAmount != null">#{cteAmount},</if>
            <if test="componentVarPercentage != null">#{componentVarPercentage},</if>
            <if test="componentVarContributionPercentage != null">#{componentVarContributionPercentage},</if>
            <if test="componentVarAmount != null">#{componentVarAmount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateVarAnalysisEntity" parameterType="com.xl.alm.app.entity.VarAnalysisEntity">
        update t_ast_var_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="dataType != null and dataType != ''">data_type = #{dataType},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="securityCode != null">security_code = #{securityCode},</if>
            <if test="dimensionName != null and dimensionName != ''">dimension_name = #{dimensionName},</if>
            <if test="assetName != null">asset_name = #{assetName},</if>
            <if test="periodDate != null and periodDate != ''">period_date = #{periodDate},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="varAmount != null">var_amount = #{varAmount},</if>
            <if test="incrementalVarAmount != null">incremental_var_amount = #{incrementalVarAmount},</if>
            <if test="ctePercentage != null">cte_percentage = #{ctePercentage},</if>
            <if test="cteAmount != null">cte_amount = #{cteAmount},</if>
            <if test="componentVarPercentage != null">component_var_percentage = #{componentVarPercentage},</if>
            <if test="componentVarContributionPercentage != null">component_var_contribution_percentage = #{componentVarContributionPercentage},</if>
            <if test="componentVarAmount != null">component_var_amount = #{componentVarAmount},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVarAnalysisEntityById" parameterType="Long">
        update t_ast_var_analysis set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteVarAnalysisEntityByIds" parameterType="String">
        update t_ast_var_analysis set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertVarAnalysisEntity" parameterType="java.util.List">
        insert into t_ast_var_analysis(accounting_period, data_type, account_name, security_code, dimension_name, asset_name, period_date, market_value, var_amount, incremental_var_amount, cte_percentage, cte_amount, component_var_percentage, component_var_contribution_percentage, component_var_amount, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.dataType}, #{item.accountName}, #{item.securityCode}, #{item.dimensionName}, #{item.assetName}, #{item.periodDate}, #{item.marketValue}, #{item.varAmount}, #{item.incrementalVarAmount}, #{item.ctePercentage}, #{item.cteAmount}, #{item.componentVarPercentage}, #{item.componentVarContributionPercentage}, #{item.componentVarAmount}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <delete id="deleteByAccountingPeriodAndDataType">
        delete from t_ast_var_analysis
        where accounting_period = #{accountingPeriod}
        and data_type = #{dataType}
    </delete>

    <select id="checkDataExists" resultType="int">
        select count(1) from t_ast_var_analysis
        where accounting_period = #{accountingPeriod}
        and data_type = #{dataType}
        and account_name = #{accountName}
        and dimension_name = #{dimensionName}
        and is_del = 0
    </select>
</mapper>
