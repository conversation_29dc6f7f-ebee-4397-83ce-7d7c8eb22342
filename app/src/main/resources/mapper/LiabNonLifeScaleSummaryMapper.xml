<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.LiabNonLifeScaleSummaryMapper">

    <resultMap type="com.xl.alm.app.entity.LiabNonLifeScaleSummaryEntity" id="LiabNonLifeScaleSummaryEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="insuranceMainType" column="insurance_main_type"/>
        <result property="unearnedPremiumReserve" column="unearned_premium_reserve"/>
        <result property="outstandingClaimReserve" column="outstanding_claim_reserve"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectLiabNonLifeScaleSummaryEntityVo">
        select id, accounting_period, insurance_main_type, unearned_premium_reserve, outstanding_claim_reserve,
               remark, create_time, create_by, update_time, update_by, is_del
        from t_liab_non_life_scale_summary
    </sql>

    <!-- 查询非寿险负债规模汇总表列表 -->
    <select id="selectLiabNonLifeScaleSummaryEntityList" parameterType="com.xl.alm.app.query.LiabNonLifeScaleSummaryQuery" resultMap="LiabNonLifeScaleSummaryEntityResult">
        <include refid="selectLiabNonLifeScaleSummaryEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">and insurance_main_type = #{insuranceMainType}</if>
            <if test="unearnedPremiumReserve != null">and unearned_premium_reserve = #{unearnedPremiumReserve}</if>
            <if test="outstandingClaimReserve != null">and outstanding_claim_reserve = #{outstandingClaimReserve}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, insurance_main_type
    </select>

    <!-- 根据id查询非寿险负债规模汇总表 -->
    <select id="selectLiabNonLifeScaleSummaryEntityById" parameterType="Long" resultMap="LiabNonLifeScaleSummaryEntityResult">
        <include refid="selectLiabNonLifeScaleSummaryEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和险种主类查询非寿险负债规模汇总表 -->
    <select id="selectLiabNonLifeScaleSummaryEntityByCondition" resultMap="LiabNonLifeScaleSummaryEntityResult">
        <include refid="selectLiabNonLifeScaleSummaryEntityVo"/>
        where accounting_period = #{accountingPeriod}
        and insurance_main_type = #{insuranceMainType}
        and is_del = 0
    </select>

    <!-- 新增非寿险负债规模汇总表 -->
    <insert id="insertLiabNonLifeScaleSummaryEntity" parameterType="com.xl.alm.app.entity.LiabNonLifeScaleSummaryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_non_life_scale_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">insurance_main_type,</if>
            <if test="unearnedPremiumReserve != null">unearned_premium_reserve,</if>
            <if test="outstandingClaimReserve != null">outstanding_claim_reserve,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">#{insuranceMainType},</if>
            <if test="unearnedPremiumReserve != null">#{unearnedPremiumReserve},</if>
            <if test="outstandingClaimReserve != null">#{outstandingClaimReserve},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 修改非寿险负债规模汇总表 -->
    <update id="updateLiabNonLifeScaleSummaryEntity" parameterType="com.xl.alm.app.entity.LiabNonLifeScaleSummaryEntity">
        update t_liab_non_life_scale_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="insuranceMainType != null and insuranceMainType != ''">insurance_main_type = #{insuranceMainType},</if>
            <if test="unearnedPremiumReserve != null">unearned_premium_reserve = #{unearnedPremiumReserve},</if>
            <if test="outstandingClaimReserve != null">outstanding_claim_reserve = #{outstandingClaimReserve},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除非寿险负债规模汇总表 -->
    <update id="deleteLiabNonLifeScaleSummaryEntityById" parameterType="Long">
        update t_liab_non_life_scale_summary set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除非寿险负债规模汇总表 -->
    <update id="deleteLiabNonLifeScaleSummaryEntityByIds" parameterType="String">
        update t_liab_non_life_scale_summary set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入非寿险负债规模汇总表数据 -->
    <insert id="batchInsertLiabNonLifeScaleSummaryEntity" parameterType="java.util.List">
        insert into t_liab_non_life_scale_summary (accounting_period, insurance_main_type, unearned_premium_reserve,
        outstanding_claim_reserve, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.insuranceMainType}, #{item.unearnedPremiumReserve},
            #{item.outstandingClaimReserve}, #{item.remark})
        </foreach>
    </insert>

    <!-- 删除指定账期的非寿险负债规模汇总表数据 -->
    <update id="deleteLiabNonLifeScaleSummaryEntityByPeriod" parameterType="String">
        update t_liab_non_life_scale_summary set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
