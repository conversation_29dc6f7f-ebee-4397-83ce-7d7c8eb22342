<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.RenewalEvalMonthCfgMapper">

    <resultMap type="com.xl.alm.app.entity.RenewalEvalMonthCfgEntity" id="RenewalEvalMonthCfgEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="monthSeqInCurrQuarter" column="month_seq_in_curr_quarter"/>
        <result property="evalPointMonth" column="eval_point_month"/>
        <result property="monthOfLastYear" column="month_of_last_year"/>
        <result property="monthInCurrQuarter" column="month_in_curr_quarter"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectRenewalEvalMonthCfgEntityVo">
        select id, accounting_period, month_seq_in_curr_quarter, eval_point_month, month_of_last_year, 
               month_in_curr_quarter, create_time, create_by, update_time, update_by, is_del
        from t_base_renewal_eval_month_cfg
    </sql>

    <!-- 查询续保率评估月份配置列表 -->
    <select id="selectRenewalEvalMonthCfgEntityList" parameterType="com.xl.alm.app.query.RenewalEvalMonthCfgQuery" resultMap="RenewalEvalMonthCfgEntityResult">
        <include refid="selectRenewalEvalMonthCfgEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="monthSeqInCurrQuarter != null">and month_seq_in_curr_quarter = #{monthSeqInCurrQuarter}</if>
            <if test="evalPointMonth != null and evalPointMonth != ''">and eval_point_month = #{evalPointMonth}</if>
            <if test="monthOfLastYear != null and monthOfLastYear != ''">and month_of_last_year = #{monthOfLastYear}</if>
            <if test="monthInCurrQuarter != null and monthInCurrQuarter != ''">and month_in_curr_quarter = #{monthInCurrQuarter}</if>
            and is_del = 0
        </where>
        order by accounting_period desc, month_seq_in_curr_quarter asc
    </select>

    <!-- 用id查询续保率评估月份配置 -->
    <select id="selectRenewalEvalMonthCfgEntityById" parameterType="Long" resultMap="RenewalEvalMonthCfgEntityResult">
        <include refid="selectRenewalEvalMonthCfgEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和当季第几月查询续保率评估月份配置 -->
    <select id="selectRenewalEvalMonthCfgEntityByCondition" resultMap="RenewalEvalMonthCfgEntityResult">
        <include refid="selectRenewalEvalMonthCfgEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="monthSeqInCurrQuarter != null">and month_seq_in_curr_quarter = #{monthSeqInCurrQuarter}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增续保率评估月份配置 -->
    <insert id="insertRenewalEvalMonthCfgEntity" parameterType="com.xl.alm.app.entity.RenewalEvalMonthCfgEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_renewal_eval_month_cfg (
            accounting_period, month_seq_in_curr_quarter, eval_point_month, month_of_last_year, 
            month_in_curr_quarter, create_by, update_by
        ) values (
            #{accountingPeriod}, #{monthSeqInCurrQuarter}, #{evalPointMonth}, #{monthOfLastYear}, 
            #{monthInCurrQuarter}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量新增续保率评估月份配置 -->
    <insert id="batchInsertRenewalEvalMonthCfgEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_renewal_eval_month_cfg (
            accounting_period, month_seq_in_curr_quarter, eval_point_month, month_of_last_year, 
            month_in_curr_quarter, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod}, #{item.monthSeqInCurrQuarter}, #{item.evalPointMonth}, #{item.monthOfLastYear}, 
            #{item.monthInCurrQuarter}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 修改续保率评估月份配置 -->
    <update id="updateRenewalEvalMonthCfgEntity" parameterType="com.xl.alm.app.entity.RenewalEvalMonthCfgEntity">
        update t_base_renewal_eval_month_cfg
        <set>
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="monthSeqInCurrQuarter != null">month_seq_in_curr_quarter = #{monthSeqInCurrQuarter},</if>
            <if test="evalPointMonth != null and evalPointMonth != ''">eval_point_month = #{evalPointMonth},</if>
            <if test="monthOfLastYear != null and monthOfLastYear != ''">month_of_last_year = #{monthOfLastYear},</if>
            <if test="monthInCurrQuarter != null and monthInCurrQuarter != ''">month_in_curr_quarter = #{monthInCurrQuarter},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除续保率评估月份配置 -->
    <update id="deleteRenewalEvalMonthCfgEntityById" parameterType="Long">
        update t_base_renewal_eval_month_cfg set is_del = 1, update_time = now() 
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除续保率评估月份配置 -->
    <update id="deleteRenewalEvalMonthCfgEntityByIds" parameterType="Long">
        update t_base_renewal_eval_month_cfg set is_del = 1, update_time = now() 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
