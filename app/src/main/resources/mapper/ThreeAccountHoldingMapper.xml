<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.ThreeAccountHoldingMapper">
    
    <resultMap type="com.xl.alm.app.entity.ThreeAccountHoldingEntity" id="ThreeAccountHoldingResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="accountName"    column="account_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="securityName"    column="security_name"    />
        <result property="dimensionName2"    column="dimension_name_2"    />
        <result property="dimensionName3"    column="dimension_name_3"    />
        <result property="dimensionName4"    column="dimension_name_4"    />
        <result property="dimensionName5"    column="dimension_name_5"    />
        <result property="dimensionName6"    column="dimension_name_6"    />
        <result property="holdingQuantity"    column="holding_quantity"    />
        <result property="holdingFaceValue"    column="holding_face_value"    />
        <result property="cost"    column="cost"    />
        <result property="netCost"    column="net_cost"    />
        <result property="netMarketValue"    column="net_market_value"    />
        <result property="marketValue"    column="market_value"    />
        <result property="couponRate"    column="coupon_rate"    />
        <result property="annualPaymentFrequency"    column="annual_payment_frequency"    />
        <result property="valueDate"    column="value_date"    />
        <result property="maturityDate"    column="maturity_date"    />
        <result property="accountingType"    column="accounting_type"    />
        <result property="latestPurchaseDate"    column="latest_purchase_date"    />
        <result property="entityRating"    column="entity_rating"    />
        <result property="securityRating"    column="security_rating"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectThreeAccountHoldingVo">
        select id, accounting_period, account_name, security_code, security_name, dimension_name_2, dimension_name_3, dimension_name_4, dimension_name_5, dimension_name_6, holding_quantity, holding_face_value, cost, net_cost, net_market_value, market_value, coupon_rate, annual_payment_frequency, value_date, maturity_date, accounting_type, latest_purchase_date, entity_rating, security_rating, create_by, create_time, update_by, update_time, is_del from t_ast_three_account_holding
    </sql>

    <select id="selectThreeAccountHoldingEntityList" parameterType="com.xl.alm.app.query.ThreeAccountHoldingQuery" resultMap="ThreeAccountHoldingResult">
        <include refid="selectThreeAccountHoldingVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code like concat('%', #{securityCode}, '%')</if>
            <if test="securityName != null  and securityName != ''"> and security_name like concat('%', #{securityName}, '%')</if>
            <if test="dimensionName2 != null  and dimensionName2 != ''"> and dimension_name_2 like concat('%', #{dimensionName2}, '%')</if>
            <if test="dimensionName3 != null  and dimensionName3 != ''"> and dimension_name_3 like concat('%', #{dimensionName3}, '%')</if>
            <if test="dimensionName4 != null  and dimensionName4 != ''"> and dimension_name_4 like concat('%', #{dimensionName4}, '%')</if>
            <if test="dimensionName5 != null  and dimensionName5 != ''"> and dimension_name_5 like concat('%', #{dimensionName5}, '%')</if>
            <if test="dimensionName6 != null  and dimensionName6 != ''"> and dimension_name_6 like concat('%', #{dimensionName6}, '%')</if>
            <if test="accountingType != null  and accountingType != ''"> and accounting_type like concat('%', #{accountingType}, '%')</if>
            <if test="entityRating != null  and entityRating != ''"> and entity_rating like concat('%', #{entityRating}, '%')</if>
            <if test="securityRating != null  and securityRating != ''"> and security_rating like concat('%', #{securityRating}, '%')</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectThreeAccountHoldingEntityById" parameterType="Long" resultMap="ThreeAccountHoldingResult">
        <include refid="selectThreeAccountHoldingVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertThreeAccountHoldingEntity" parameterType="com.xl.alm.app.entity.ThreeAccountHoldingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_three_account_holding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="securityName != null and securityName != ''">security_name,</if>
            <if test="dimensionName2 != null and dimensionName2 != ''">dimension_name_2,</if>
            <if test="dimensionName3 != null and dimensionName3 != ''">dimension_name_3,</if>
            <if test="dimensionName4 != null and dimensionName4 != ''">dimension_name_4,</if>
            <if test="dimensionName5 != null and dimensionName5 != ''">dimension_name_5,</if>
            <if test="dimensionName6 != null and dimensionName6 != ''">dimension_name_6,</if>
            <if test="holdingQuantity != null">holding_quantity,</if>
            <if test="holdingFaceValue != null">holding_face_value,</if>
            <if test="cost != null">cost,</if>
            <if test="netCost != null">net_cost,</if>
            <if test="netMarketValue != null">net_market_value,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="couponRate != null">coupon_rate,</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency,</if>
            <if test="valueDate != null">value_date,</if>
            <if test="maturityDate != null">maturity_date,</if>
            <if test="accountingType != null and accountingType != ''">accounting_type,</if>
            <if test="latestPurchaseDate != null">latest_purchase_date,</if>
            <if test="entityRating != null and entityRating != ''">entity_rating,</if>
            <if test="securityRating != null and securityRating != ''">security_rating,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="securityName != null and securityName != ''">#{securityName},</if>
            <if test="dimensionName2 != null and dimensionName2 != ''">#{dimensionName2},</if>
            <if test="dimensionName3 != null and dimensionName3 != ''">#{dimensionName3},</if>
            <if test="dimensionName4 != null and dimensionName4 != ''">#{dimensionName4},</if>
            <if test="dimensionName5 != null and dimensionName5 != ''">#{dimensionName5},</if>
            <if test="dimensionName6 != null and dimensionName6 != ''">#{dimensionName6},</if>
            <if test="holdingQuantity != null">#{holdingQuantity},</if>
            <if test="holdingFaceValue != null">#{holdingFaceValue},</if>
            <if test="cost != null">#{cost},</if>
            <if test="netCost != null">#{netCost},</if>
            <if test="netMarketValue != null">#{netMarketValue},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="couponRate != null">#{couponRate},</if>
            <if test="annualPaymentFrequency != null">#{annualPaymentFrequency},</if>
            <if test="valueDate != null">#{valueDate},</if>
            <if test="maturityDate != null">#{maturityDate},</if>
            <if test="accountingType != null and accountingType != ''">#{accountingType},</if>
            <if test="latestPurchaseDate != null">#{latestPurchaseDate},</if>
            <if test="entityRating != null and entityRating != ''">#{entityRating},</if>
            <if test="securityRating != null and securityRating != ''">#{securityRating},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateThreeAccountHoldingEntity" parameterType="com.xl.alm.app.entity.ThreeAccountHoldingEntity">
        update t_ast_three_account_holding
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="securityName != null and securityName != ''">security_name = #{securityName},</if>
            <if test="dimensionName2 != null and dimensionName2 != ''">dimension_name_2 = #{dimensionName2},</if>
            <if test="dimensionName3 != null and dimensionName3 != ''">dimension_name_3 = #{dimensionName3},</if>
            <if test="dimensionName4 != null and dimensionName4 != ''">dimension_name_4 = #{dimensionName4},</if>
            <if test="dimensionName5 != null and dimensionName5 != ''">dimension_name_5 = #{dimensionName5},</if>
            <if test="dimensionName6 != null and dimensionName6 != ''">dimension_name_6 = #{dimensionName6},</if>
            <if test="holdingQuantity != null">holding_quantity = #{holdingQuantity},</if>
            <if test="holdingFaceValue != null">holding_face_value = #{holdingFaceValue},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="netCost != null">net_cost = #{netCost},</if>
            <if test="netMarketValue != null">net_market_value = #{netMarketValue},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="couponRate != null">coupon_rate = #{couponRate},</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency = #{annualPaymentFrequency},</if>
            <if test="valueDate != null">value_date = #{valueDate},</if>
            <if test="maturityDate != null">maturity_date = #{maturityDate},</if>
            <if test="accountingType != null and accountingType != ''">accounting_type = #{accountingType},</if>
            <if test="latestPurchaseDate != null">latest_purchase_date = #{latestPurchaseDate},</if>
            <if test="entityRating != null and entityRating != ''">entity_rating = #{entityRating},</if>
            <if test="securityRating != null and securityRating != ''">security_rating = #{securityRating},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteThreeAccountHoldingEntityById" parameterType="Long">
        update t_ast_three_account_holding set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteThreeAccountHoldingEntityByIds" parameterType="String">
        update t_ast_three_account_holding set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertThreeAccountHoldingEntity" parameterType="java.util.List">
        insert into t_ast_three_account_holding(accounting_period, account_name, security_code, security_name, dimension_name_2, dimension_name_3, dimension_name_4, dimension_name_5, dimension_name_6, holding_quantity, holding_face_value, cost, net_cost, net_market_value, market_value, coupon_rate, annual_payment_frequency, value_date, maturity_date, accounting_type, latest_purchase_date, entity_rating, security_rating, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.accountingPeriod}, #{item.accountName}, #{item.securityCode}, #{item.securityName}, #{item.dimensionName2}, #{item.dimensionName3}, #{item.dimensionName4}, #{item.dimensionName5}, #{item.dimensionName6}, #{item.holdingQuantity}, #{item.holdingFaceValue}, #{item.cost}, #{item.netCost}, #{item.netMarketValue}, #{item.marketValue}, #{item.couponRate}, #{item.annualPaymentFrequency}, #{item.valueDate}, #{item.maturityDate}, #{item.accountingType}, #{item.latestPurchaseDate}, #{item.entityRating}, #{item.securityRating}, #{item.createBy}, now(), #{item.updateBy}, now())
        </foreach>
    </insert>

    <delete id="deleteThreeAccountHoldingEntityByPeriod" parameterType="String">
        delete from t_ast_three_account_holding where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
