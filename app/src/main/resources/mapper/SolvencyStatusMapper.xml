<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.SolvencyStatusMapper">

    <resultMap type="com.xl.alm.app.entity.SolvencyStatusEntity" id="SolvencyStatusEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="rowNumber" column="row_number"/>
        <result property="itemName" column="item_name"/>
        <result property="endingBalance" column="ending_balance"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectSolvencyStatusEntityVo">
        select id, accounting_period, `row_number`, item_name, ending_balance, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_base_solvency_status
    </sql>

    <!-- 查询偿付能力状况表列表 -->
    <select id="selectSolvencyStatusEntityList" parameterType="com.xl.alm.app.query.SolvencyStatusQuery" resultMap="SolvencyStatusEntityResult">
        <include refid="selectSolvencyStatusEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="rowNumber != null">and `row_number` = #{rowNumber}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="endingBalance != null">and ending_balance = #{endingBalance}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, `row_number` asc
    </select>

    <!-- 用id查询偿付能力状况表 -->
    <select id="selectSolvencyStatusEntityById" parameterType="Long" resultMap="SolvencyStatusEntityResult">
        <include refid="selectSolvencyStatusEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和项目名称查询偿付能力状况表 -->
    <select id="selectSolvencyStatusEntityByCondition" resultMap="SolvencyStatusEntityResult">
        <include refid="selectSolvencyStatusEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增偿付能力状况表 -->
    <insert id="insertSolvencyStatusEntity" parameterType="com.xl.alm.app.entity.SolvencyStatusEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_solvency_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="rowNumber != null">`row_number`,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="endingBalance != null">ending_balance,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="rowNumber != null">#{rowNumber},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="endingBalance != null">#{endingBalance},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增偿付能力状况表 -->
    <insert id="batchInsertSolvencyStatusEntity" parameterType="java.util.List">
        insert into t_base_solvency_status(accounting_period, `row_number`, item_name, ending_balance, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.rowNumber}, #{item.itemName}, #{item.endingBalance}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改偿付能力状况表 -->
    <update id="updateSolvencyStatusEntity" parameterType="com.xl.alm.app.entity.SolvencyStatusEntity">
        update t_base_solvency_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="rowNumber != null">`row_number` = #{rowNumber},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="endingBalance != null">ending_balance = #{endingBalance},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除偿付能力状况表 -->
    <update id="deleteSolvencyStatusEntityById" parameterType="Long">
        update t_base_solvency_status set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除偿付能力状况表 -->
    <update id="deleteSolvencyStatusEntityByIds" parameterType="String">
        update t_base_solvency_status set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除偿付能力状况表数据 -->
    <update id="deleteSolvencyStatusEntityByPeriod" parameterType="String">
        update t_base_solvency_status set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
