<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.BankClassificationMapMapper">

    <resultMap type="com.xl.alm.app.entity.BankClassificationMapEntity" id="BankClassificationMapResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="almAssetName" column="alm_asset_name"/>
        <result property="bankClassification" column="bank_classification"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectBankClassificationMapVo">
        select id, accounting_period, alm_asset_name, bank_classification, create_time, create_by, update_time, update_by, is_del
        from t_ast_bank_classification_map
    </sql>

    <select id="selectBankClassificationMapEntityList" parameterType="com.xl.alm.app.query.BankClassificationMapQuery" resultMap="BankClassificationMapResult">
        <include refid="selectBankClassificationMapVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="almAssetName != null and almAssetName != ''">
                and alm_asset_name like concat('%', #{almAssetName}, '%')
            </if>
            <if test="bankClassification != null and bankClassification != ''">
                and bank_classification = #{bankClassification}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectBankClassificationMapEntityById" parameterType="Long" resultMap="BankClassificationMapResult">
        <include refid="selectBankClassificationMapVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertBankClassificationMapEntity" parameterType="com.xl.alm.app.entity.BankClassificationMapEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_bank_classification_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="almAssetName != null and almAssetName != ''">alm_asset_name,</if>
            <if test="bankClassification != null and bankClassification != ''">bank_classification,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="almAssetName != null and almAssetName != ''">#{almAssetName},</if>
            <if test="bankClassification != null and bankClassification != ''">#{bankClassification},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateBankClassificationMapEntity" parameterType="com.xl.alm.app.entity.BankClassificationMapEntity">
        update t_ast_bank_classification_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="almAssetName != null and almAssetName != ''">alm_asset_name = #{almAssetName},</if>
            <if test="bankClassification != null and bankClassification != ''">bank_classification = #{bankClassification},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBankClassificationMapEntityById" parameterType="Long">
        update t_ast_bank_classification_map set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteBankClassificationMapEntityByIds" parameterType="String">
        update t_ast_bank_classification_map set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBankClassificationMapEntityByPeriod" parameterType="String">
        update t_ast_bank_classification_map set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
