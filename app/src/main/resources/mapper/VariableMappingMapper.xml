<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.VariableMappingMapper">
    
    <resultMap type="com.xl.alm.app.entity.VariableMappingEntity" id="VariableMappingResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="variableList"    column="variable_list"    />
        <result property="variableName"    column="variable_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectVariableMappingVo">
        select id, accounting_period, variable_list, variable_name, create_by, create_time, update_by, update_time, is_del from t_cft_variable_mapping
    </sql>

    <select id="selectVariableMappingList" parameterType="com.xl.alm.app.entity.VariableMappingEntity" resultMap="VariableMappingResult">
        <include refid="selectVariableMappingVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''"> and accounting_period like concat('%', #{accountingPeriod}, '%')</if>
            <if test="variableList != null and variableList != ''"> and variable_list like concat('%', #{variableList}, '%')</if>
            <if test="variableName != null and variableName != ''"> and variable_name like concat('%', #{variableName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVariableMappingById" parameterType="Long" resultMap="VariableMappingResult">
        <include refid="selectVariableMappingVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectVariableMappingByCondition" resultMap="VariableMappingResult">
        <include refid="selectVariableMappingVo"/>
        where accounting_period = #{accountingPeriod}
        and variable_list = #{variableList}
        and is_del = 0
    </select>

    <select id="selectVariableNameByCode" parameterType="String" resultType="String">
        select variable_name from t_cft_variable_mapping
        where variable_list = #{variableList} and is_del = 0
        limit 1
    </select>
        
    <insert id="insertVariableMapping" parameterType="com.xl.alm.app.entity.VariableMappingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_variable_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="variableList != null and variableList != ''">variable_list,</if>
            <if test="variableName != null and variableName != ''">variable_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="variableList != null and variableList != ''">#{variableList},</if>
            <if test="variableName != null and variableName != ''">#{variableName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateVariableMapping" parameterType="com.xl.alm.app.entity.VariableMappingEntity">
        update t_cft_variable_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="variableList != null and variableList != ''">variable_list = #{variableList},</if>
            <if test="variableName != null and variableName != ''">variable_name = #{variableName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVariableMappingById" parameterType="Long">
        update t_cft_variable_mapping set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteVariableMappingByIds" parameterType="String">
        update t_cft_variable_mapping set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
