<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AlmcfActualYtdMapper">

    <resultMap type="com.xl.alm.app.entity.AlmcfActualYtdEntity" id="AlmcfActualYtdEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="traditionalAccount" column="traditional_account"/>
        <result property="bonusAccount" column="bonus_account"/>
        <result property="universalAccount" column="universal_account"/>
        <result property="investmentAccount" column="investment_account"/>
        <result property="ordinaryAccount" column="ordinary_account"/>
        <result property="totalAccount" column="total_account"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAlmcfActualYtdEntityVo">
        select id, accounting_period, item_name, traditional_account, bonus_account, 
               universal_account, investment_account, ordinary_account, total_account, 
               remark, create_time, create_by, update_time, update_by, is_del
        from t_cft_almcf_actual_ytd
    </sql>

    <!-- 查询ALMCF实际发生数本年累计表列表 -->
    <select id="selectAlmcfActualYtdEntityList" parameterType="com.xl.alm.app.query.AlmcfActualYtdQuery" resultMap="AlmcfActualYtdEntityResult">
        <include refid="selectAlmcfActualYtdEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="traditionalAccount != null">and traditional_account = #{traditionalAccount}</if>
            <if test="bonusAccount != null">and bonus_account = #{bonusAccount}</if>
            <if test="universalAccount != null">and universal_account = #{universalAccount}</if>
            <if test="investmentAccount != null">and investment_account = #{investmentAccount}</if>
            <if test="ordinaryAccount != null">and ordinary_account = #{ordinaryAccount}</if>
            <if test="totalAccount != null">and total_account = #{totalAccount}</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据id查询ALMCF实际发生数本年累计表 -->
    <select id="selectAlmcfActualYtdEntityById" parameterType="Long" resultMap="AlmcfActualYtdEntityResult">
        <include refid="selectAlmcfActualYtdEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期查询ALMCF实际发生数本年累计表 -->
    <select id="selectAlmcfActualYtdEntityByPeriod" parameterType="String" resultMap="AlmcfActualYtdEntityResult">
        <include refid="selectAlmcfActualYtdEntityVo"/>
        where accounting_period = #{accountingPeriod} and is_del = 0
        order by item_name
    </select>

    <!-- 根据账期和项目名称查询ALMCF实际发生数本年累计表 -->
    <select id="selectAlmcfActualYtdEntityByCondition" resultMap="AlmcfActualYtdEntityResult">
        <include refid="selectAlmcfActualYtdEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增ALMCF实际发生数本年累计表 -->
    <insert id="insertAlmcfActualYtdEntity" parameterType="com.xl.alm.app.entity.AlmcfActualYtdEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_almcf_actual_ytd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="traditionalAccount != null">traditional_account,</if>
            <if test="bonusAccount != null">bonus_account,</if>
            <if test="universalAccount != null">universal_account,</if>
            <if test="investmentAccount != null">investment_account,</if>
            <if test="ordinaryAccount != null">ordinary_account,</if>
            <if test="totalAccount != null">total_account,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="traditionalAccount != null">#{traditionalAccount},</if>
            <if test="bonusAccount != null">#{bonusAccount},</if>
            <if test="universalAccount != null">#{universalAccount},</if>
            <if test="investmentAccount != null">#{investmentAccount},</if>
            <if test="ordinaryAccount != null">#{ordinaryAccount},</if>
            <if test="totalAccount != null">#{totalAccount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增ALMCF实际发生数本年累计表 -->
    <insert id="batchInsertAlmcfActualYtdEntity" parameterType="java.util.List">
        insert into t_cft_almcf_actual_ytd(accounting_period, item_name, traditional_account, bonus_account,
                                           universal_account, investment_account, ordinary_account, total_account,
                                           remark, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.traditionalAccount}, #{item.bonusAccount},
             #{item.universalAccount}, #{item.investmentAccount}, #{item.ordinaryAccount}, #{item.totalAccount},
             #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 更新ALMCF实际发生数本年累计表 -->
    <update id="updateAlmcfActualYtdEntity" parameterType="com.xl.alm.app.entity.AlmcfActualYtdEntity">
        update t_cft_almcf_actual_ytd
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="traditionalAccount != null">traditional_account = #{traditionalAccount},</if>
            <if test="bonusAccount != null">bonus_account = #{bonusAccount},</if>
            <if test="universalAccount != null">universal_account = #{universalAccount},</if>
            <if test="investmentAccount != null">investment_account = #{investmentAccount},</if>
            <if test="ordinaryAccount != null">ordinary_account = #{ordinaryAccount},</if>
            <if test="totalAccount != null">total_account = #{totalAccount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <!-- 删除ALMCF实际发生数本年累计表 -->
    <update id="deleteAlmcfActualYtdEntityById" parameterType="Long">
        update t_cft_almcf_actual_ytd set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除ALMCF实际发生数本年累计表 -->
    <update id="deleteAlmcfActualYtdEntityByIds" parameterType="String">
        update t_cft_almcf_actual_ytd set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除ALMCF实际发生数本年累计表数据 -->
    <update id="deleteAlmcfActualYtdEntityByPeriod" parameterType="String">
        update t_cft_almcf_actual_ytd set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
