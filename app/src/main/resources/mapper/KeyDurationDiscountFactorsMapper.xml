<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.KeyDurationDiscountFactorsMapper">

    <resultMap type="com.xl.alm.app.entity.KeyDurationDiscountFactorsEntity" id="KeyDurationDiscountFactorsEntityResult">
        <id property="id" column="id"/>
        <result property="accountPeriod" column="account_period"/>
        <result property="curveType" column="curve_type"/>
        <result property="keyDuration" column="key_duration"/>
        <result property="stressDirection" column="stress_direction"/>
        <result property="durationType" column="duration_type"/>
        <result property="factorValSet" column="factor_val_set"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectKeyDurationDiscountFactorsEntityVo">
        select id, account_period, curve_type, key_duration, stress_direction, duration_type, factor_val_set, create_time, create_by, update_time, update_by, is_del
        from t_dur_key_duration_discount_factors
    </sql>

    <!-- 查询关键久期折现因子列表 -->
    <select id="selectKeyDurationDiscountFactorsEntityList" parameterType="com.xl.alm.app.query.KeyDurationDiscountFactorsQuery" resultMap="KeyDurationDiscountFactorsEntityResult">
        <include refid="selectKeyDurationDiscountFactorsEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountPeriod != null and accountPeriod != ''">and account_period = #{accountPeriod}</if>
            <if test="curveType != null and curveType != ''">and curve_type = #{curveType}</if>
            <if test="keyDuration != null and keyDuration != ''">and key_duration = #{keyDuration}</if>
            <if test="stressDirection != null and stressDirection != ''">and stress_direction = #{stressDirection}</if>
            <if test="durationType != null and durationType != ''">and duration_type = #{durationType}</if>
            and is_del = 0
        </where>
        order by
        CASE key_duration
            WHEN '0' THEN 1
            WHEN '0.5' THEN 2
            WHEN '1' THEN 3
            WHEN '2' THEN 4
            WHEN '3' THEN 5
            WHEN '4' THEN 6
            WHEN '5' THEN 7
            WHEN '6' THEN 8
            WHEN '7' THEN 9
            WHEN '8' THEN 10
            WHEN '10' THEN 11
            WHEN '12' THEN 12
            WHEN '15' THEN 13
            WHEN '20' THEN 14
            WHEN '25' THEN 15
            WHEN '30' THEN 16
            WHEN '35' THEN 17
            WHEN '40' THEN 18
            WHEN '45' THEN 19
            WHEN '50' THEN 20
            ELSE 100
        END ASC
    </select>

    <!-- 根据ID查询关键久期折现因子 -->
    <select id="selectKeyDurationDiscountFactorsEntityById" parameterType="Long" resultMap="KeyDurationDiscountFactorsEntityResult">
        <include refid="selectKeyDurationDiscountFactorsEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询关键久期折现因子 -->
    <select id="selectKeyDurationDiscountFactorsEntityByCondition" resultMap="KeyDurationDiscountFactorsEntityResult">
        <include refid="selectKeyDurationDiscountFactorsEntityVo"/>
        <where>
            account_period = #{accountPeriod}
            and curve_type = #{curveType}
            and key_duration = #{keyDuration}
            and stress_direction = #{stressDirection}
            and duration_type = #{durationType}
            and is_del = 0
        </where>
    </select>

    <!-- 新增关键久期折现因子 -->
    <insert id="insertKeyDurationDiscountFactorsEntity" parameterType="com.xl.alm.app.entity.KeyDurationDiscountFactorsEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_dur_key_duration_discount_factors (
            account_period, curve_type, key_duration, stress_direction, duration_type,
            factor_val_set, create_by, update_by
        ) values (
            #{accountPeriod}, #{curveType}, #{keyDuration}, #{stressDirection}, #{durationType},
            #{factorValSet}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量新增关键久期折现因子 -->
    <insert id="batchInsertKeyDurationDiscountFactorsEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_dur_key_duration_discount_factors (
            account_period, curve_type, key_duration, stress_direction, duration_type,
            factor_val_set, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountPeriod}, #{item.curveType}, #{item.keyDuration}, #{item.stressDirection}, #{item.durationType},
            #{item.factorValSet}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 修改关键久期折现因子 -->
    <update id="updateKeyDurationDiscountFactorsEntity" parameterType="com.xl.alm.app.entity.KeyDurationDiscountFactorsEntity">
        update t_dur_key_duration_discount_factors
        <set>
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="curveType != null and curveType != ''">curve_type = #{curveType},</if>
            <if test="keyDuration != null and keyDuration != ''">key_duration = #{keyDuration},</if>
            <if test="stressDirection != null and stressDirection != ''">stress_direction = #{stressDirection},</if>
            <if test="durationType != null and durationType != ''">duration_type = #{durationType},</if>
            <if test="factorValSet != null and factorValSet != ''">factor_val_set = #{factorValSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id}
    </update>

    <!-- 删除关键久期折现因子 -->
    <update id="deleteKeyDurationDiscountFactorsEntityById" parameterType="Long">
        update t_dur_key_duration_discount_factors set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除关键久期折现因子 -->
    <update id="deleteKeyDurationDiscountFactorsEntityByIds" parameterType="Long">
        update t_dur_key_duration_discount_factors set is_del = 1 where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 删除指定账期的关键久期折现因子 -->
    <update id="deleteKeyDurationDiscountFactorsEntityByPeriod" parameterType="String">
        update t_dur_key_duration_discount_factors set is_del = 1 where account_period = #{accountPeriod}
    </update>

</mapper>
