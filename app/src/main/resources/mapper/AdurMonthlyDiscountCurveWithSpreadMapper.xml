<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AdurMonthlyDiscountCurveWithSpreadMapper">
    
    <resultMap type="com.xl.alm.app.entity.AdurMonthlyDiscountCurveWithSpreadEntity" id="AdurMonthlyDiscountCurveWithSpreadResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="durationType"    column="duration_type"    />
        <result property="basisPointType"    column="basis_point_type"    />
        <result property="dateType"    column="date_type"    />
        <result property="date"    column="date"    />
        <result property="spreadType"    column="spread_type"    />
        <result property="spread"    column="spread"    />
        <result property="curveSubCategory"    column="curve_sub_category"    />
        <result property="assetNumber"    column="asset_number"    />
        <result property="accountName"    column="account_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="curveId"    column="curve_id"    />
        <result property="monthlyDiscountRateWithSpreadSet"    column="monthly_discount_rate_with_spread_set"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectAdurMonthlyDiscountCurveWithSpreadVo">
        select id, account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
               curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
               monthly_discount_rate_with_spread_set, create_by, create_time, update_by, update_time, is_del
        from t_adur_monthly_discount_curve_with_spread
    </sql>

    <select id="selectAdurMonthlyDiscountCurveWithSpreadEntityList" parameterType="com.xl.alm.app.query.AdurMonthlyDiscountCurveWithSpreadQuery" resultMap="AdurMonthlyDiscountCurveWithSpreadResult">
        <include refid="selectAdurMonthlyDiscountCurveWithSpreadVo"/>
        <where>  
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="durationType != null  and durationType != ''"> and duration_type = #{durationType}</if>
            <if test="basisPointType != null  and basisPointType != ''"> and basis_point_type = #{basisPointType}</if>
            <if test="dateType != null  and dateType != ''"> and date_type = #{dateType}</if>
            <if test="date != null"> and date = #{date}</if>
            <if test="spreadType != null  and spreadType != ''"> and spread_type = #{spreadType}</if>
            <if test="spread != null"> and spread = #{spread}</if>
            <if test="curveSubCategory != null  and curveSubCategory != ''"> and curve_sub_category = #{curveSubCategory}</if>
            <if test="assetNumber != null  and assetNumber != ''"> and asset_number = #{assetNumber}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code like concat('%', #{securityCode}, '%')</if>
            <if test="curveId != null  and curveId != ''"> and curve_id = #{curveId}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectAdurMonthlyDiscountCurveWithSpreadEntityById" parameterType="Long" resultMap="AdurMonthlyDiscountCurveWithSpreadResult">
        <include refid="selectAdurMonthlyDiscountCurveWithSpreadVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectAdurMonthlyDiscountCurveWithSpreadEntityByAccountPeriodAndAssetNumberAndDateType" resultMap="AdurMonthlyDiscountCurveWithSpreadResult">
        <include refid="selectAdurMonthlyDiscountCurveWithSpreadVo"/>
        where account_period = #{accountPeriod} and asset_number = #{assetNumber} and date_type = #{dateType} and is_del = 0
    </select>
        
    <insert id="insertAdurMonthlyDiscountCurveWithSpreadEntity" parameterType="com.xl.alm.app.entity.AdurMonthlyDiscountCurveWithSpreadEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_monthly_discount_curve_with_spread
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="durationType != null and durationType != ''">duration_type,</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type,</if>
            <if test="dateType != null and dateType != ''">date_type,</if>
            <if test="date != null">date,</if>
            <if test="spreadType != null and spreadType != ''">spread_type,</if>
            <if test="spread != null">spread,</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="monthlyDiscountRateWithSpreadSet != null">monthly_discount_rate_with_spread_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">#{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">#{basisPointType},</if>
            <if test="dateType != null and dateType != ''">#{dateType},</if>
            <if test="date != null">#{date},</if>
            <if test="spreadType != null and spreadType != ''">#{spreadType},</if>
            <if test="spread != null">#{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">#{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="monthlyDiscountRateWithSpreadSet != null">#{monthlyDiscountRateWithSpreadSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateAdurMonthlyDiscountCurveWithSpreadEntity" parameterType="com.xl.alm.app.entity.AdurMonthlyDiscountCurveWithSpreadEntity">
        update t_adur_monthly_discount_curve_with_spread
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">duration_type = #{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type = #{basisPointType},</if>
            <if test="dateType != null and dateType != ''">date_type = #{dateType},</if>
            <if test="date != null">date = #{date},</if>
            <if test="spreadType != null and spreadType != ''">spread_type = #{spreadType},</if>
            <if test="spread != null">spread = #{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category = #{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="monthlyDiscountRateWithSpreadSet != null">monthly_discount_rate_with_spread_set = #{monthlyDiscountRateWithSpreadSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdurMonthlyDiscountCurveWithSpreadEntityById" parameterType="Long">
        update t_adur_monthly_discount_curve_with_spread set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAdurMonthlyDiscountCurveWithSpreadEntityByIds" parameterType="String">
        update t_adur_monthly_discount_curve_with_spread set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAdurMonthlyDiscountCurveWithSpreadEntityByAccountPeriod" parameterType="String">
        delete from t_adur_monthly_discount_curve_with_spread where account_period = #{accountPeriod}
    </delete>

    <insert id="batchInsertAdurMonthlyDiscountCurveWithSpreadEntity" parameterType="java.util.List">
        insert into t_adur_monthly_discount_curve_with_spread(
            account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
            curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
            monthly_discount_rate_with_spread_set, create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.durationType}, #{item.basisPointType}, #{item.dateType}, #{item.date},
                #{item.spreadType}, #{item.spread}, #{item.curveSubCategory}, #{item.assetNumber}, #{item.accountName},
                #{item.assetName}, #{item.securityCode}, #{item.curveId}, #{item.monthlyDiscountRateWithSpreadSet}, #{item.createBy}
            )
        </foreach>
    </insert>

</mapper>
