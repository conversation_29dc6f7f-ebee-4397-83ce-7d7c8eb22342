<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.CashFlowStatementMapper">

    <resultMap type="com.xl.alm.app.entity.CashFlowStatementEntity" id="CashFlowStatementEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="levelType" column="level_type"/>
        <result property="itemName" column="item_name"/>
        <result property="companyOverall" column="company_overall"/>
        <result property="dividendAccount" column="dividend_account"/>
        <result property="universalAccount" column="universal_account"/>
        <result property="investmentLinkedAccount" column="investment_linked_account"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectCashFlowStatementEntityVo">
        select id, accounting_period, level_type, item_name, company_overall, dividend_account, 
               universal_account, investment_linked_account, remark, create_time, create_by, 
               update_time, update_by, is_del
        from t_base_cash_flow_statement
    </sql>

    <!-- 查询现金流量表列表 -->
    <select id="selectCashFlowStatementEntityList" parameterType="com.xl.alm.app.query.CashFlowStatementQuery" resultMap="CashFlowStatementEntityResult">
        <include refid="selectCashFlowStatementEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="levelType != null and levelType != ''">and level_type = #{levelType}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="companyOverall != null">and company_overall = #{companyOverall}</if>
            <if test="dividendAccount != null">and dividend_account = #{dividendAccount}</if>
            <if test="universalAccount != null">and universal_account = #{universalAccount}</if>
            <if test="investmentLinkedAccount != null">and investment_linked_account = #{investmentLinkedAccount}</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据id查询现金流量表 -->
    <select id="selectCashFlowStatementEntityById" parameterType="Long" resultMap="CashFlowStatementEntityResult">
        <include refid="selectCashFlowStatementEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和项目名称查询现金流量表 -->
    <select id="selectCashFlowStatementEntityByCondition" resultMap="CashFlowStatementEntityResult">
        <include refid="selectCashFlowStatementEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增现金流量表 -->
    <insert id="insertCashFlowStatementEntity" parameterType="com.xl.alm.app.entity.CashFlowStatementEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_cash_flow_statement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="levelType != null and levelType != ''">level_type,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="companyOverall != null">company_overall,</if>
            <if test="dividendAccount != null">dividend_account,</if>
            <if test="universalAccount != null">universal_account,</if>
            <if test="investmentLinkedAccount != null">investment_linked_account,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="levelType != null and levelType != ''">#{levelType},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="companyOverall != null">#{companyOverall},</if>
            <if test="dividendAccount != null">#{dividendAccount},</if>
            <if test="universalAccount != null">#{universalAccount},</if>
            <if test="investmentLinkedAccount != null">#{investmentLinkedAccount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增现金流量表 -->
    <insert id="batchInsertCashFlowStatementEntity" parameterType="java.util.List">
        insert into t_base_cash_flow_statement(accounting_period, level_type, item_name, company_overall,
                                               dividend_account, universal_account, investment_linked_account,
                                               remark, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.levelType}, #{item.itemName}, #{item.companyOverall},
             #{item.dividendAccount}, #{item.universalAccount}, #{item.investmentLinkedAccount},
             #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 更新现金流量表 -->
    <update id="updateCashFlowStatementEntity" parameterType="com.xl.alm.app.entity.CashFlowStatementEntity">
        update t_base_cash_flow_statement
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="levelType != null and levelType != ''">level_type = #{levelType},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="companyOverall != null">company_overall = #{companyOverall},</if>
            <if test="dividendAccount != null">dividend_account = #{dividendAccount},</if>
            <if test="universalAccount != null">universal_account = #{universalAccount},</if>
            <if test="investmentLinkedAccount != null">investment_linked_account = #{investmentLinkedAccount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <!-- 删除现金流量表 -->
    <update id="deleteCashFlowStatementEntityById" parameterType="Long">
        update t_base_cash_flow_statement set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除现金流量表 -->
    <update id="deleteCashFlowStatementEntityByIds" parameterType="String">
        update t_base_cash_flow_statement set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除现金流量表数据 -->
    <update id="deleteCashFlowStatementEntityByPeriod" parameterType="String">
        update t_base_cash_flow_statement set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
