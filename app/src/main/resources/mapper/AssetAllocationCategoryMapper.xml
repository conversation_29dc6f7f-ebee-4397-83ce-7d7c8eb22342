<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetAllocationCategoryMapper">

    <resultMap type="com.xl.alm.app.entity.AssetAllocationCategoryEntity" id="AssetAllocationCategoryResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="parentCategoryId" column="parent_category_id"/>
        <result property="categoryLevel" column="category_level"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetAllocationCategoryVo">
        select id, accounting_period, asset_sub_sub_category, domestic_foreign, category_id, category_name, parent_category_id, category_level, create_time, create_by, update_time, update_by, is_del from t_ast_asset_allocation_category
    </sql>

    <select id="selectAssetAllocationCategoryEntityList" parameterType="com.xl.alm.app.query.AssetAllocationCategoryQuery" resultMap="AssetAllocationCategoryResult">
        <include refid="selectAssetAllocationCategoryVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="domesticForeign != null and domesticForeign != ''">
                and domestic_foreign = #{domesticForeign}
            </if>
            <if test="categoryId != null and categoryId != ''">
                and category_id like concat('%', #{categoryId}, '%')
            </if>
            <if test="categoryName != null and categoryName != ''">
                and category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="parentCategoryId != null and parentCategoryId != ''">
                and parent_category_id = #{parentCategoryId}
            </if>
            <if test="categoryLevel != null">
                and category_level = #{categoryLevel}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAssetAllocationCategoryEntityById" parameterType="Long" resultMap="AssetAllocationCategoryResult">
        <include refid="selectAssetAllocationCategoryVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertAssetAllocationCategoryEntity" parameterType="com.xl.alm.app.entity.AssetAllocationCategoryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_asset_allocation_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetSubSubCategory != null">asset_sub_sub_category,</if>
            <if test="domesticForeign != null">domestic_foreign,</if>
            <if test="categoryId != null and categoryId != ''">category_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="parentCategoryId != null">parent_category_id,</if>
            <if test="categoryLevel != null">category_level,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetSubSubCategory != null">#{assetSubSubCategory},</if>
            <if test="domesticForeign != null">#{domesticForeign},</if>
            <if test="categoryId != null and categoryId != ''">#{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="parentCategoryId != null">#{parentCategoryId},</if>
            <if test="categoryLevel != null">#{categoryLevel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAssetAllocationCategoryEntity" parameterType="com.xl.alm.app.entity.AssetAllocationCategoryEntity">
        update t_ast_asset_allocation_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetSubSubCategory != null">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="domesticForeign != null">domestic_foreign = #{domesticForeign},</if>
            <if test="categoryId != null and categoryId != ''">category_id = #{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="parentCategoryId != null">parent_category_id = #{parentCategoryId},</if>
            <if test="categoryLevel != null">category_level = #{categoryLevel},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetAllocationCategoryEntityById" parameterType="Long">
        update t_ast_asset_allocation_category set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAssetAllocationCategoryEntityByIds" parameterType="String">
        update t_ast_asset_allocation_category set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAssetAllocationCategoryEntity" parameterType="java.util.List">
        insert into t_ast_asset_allocation_category (accounting_period, asset_sub_sub_category, domestic_foreign, category_id, category_name, parent_category_id, category_level, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.assetSubSubCategory}, #{item.domesticForeign}, #{item.categoryId}, #{item.categoryName}, #{item.parentCategoryId}, #{item.categoryLevel}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

</mapper>
