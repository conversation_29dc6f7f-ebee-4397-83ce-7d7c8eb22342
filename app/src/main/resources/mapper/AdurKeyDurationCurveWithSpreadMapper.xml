<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AdurKeyDurationCurveWithSpreadMapper">

    <resultMap type="com.xl.alm.app.entity.AdurKeyDurationCurveWithSpreadEntity" id="AdurKeyDurationCurveWithSpreadResult">
        <result property="id" column="id"/>
        <result property="accountPeriod" column="account_period"/>
        <result property="durationType" column="duration_type"/>
        <result property="basisPointType" column="basis_point_type"/>
        <result property="keyTerm" column="key_term"/>
        <result property="stressDirection" column="stress_direction"/>
        <result property="dateType" column="date_type"/>
        <result property="date" column="date"/>
        <result property="spreadType" column="spread_type"/>
        <result property="spread" column="spread"/>
        <result property="curveSubCategory" column="curve_sub_category"/>
        <result property="assetNumber" column="asset_number"/>
        <result property="accountName" column="account_name"/>
        <result property="assetName" column="asset_name"/>
        <result property="securityCode" column="security_code"/>
        <result property="curveId" column="curve_id"/>
        <result property="keyDurationCurveWithSpreadSet" column="key_duration_curve_with_spread_set"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAdurKeyDurationCurveWithSpreadVo">
        select id, account_period, duration_type, basis_point_type, key_term, stress_direction, date_type, date, 
               spread_type, spread, curve_sub_category, asset_number, account_name, asset_name, security_code, 
               curve_id, key_duration_curve_with_spread_set, create_time, create_by, update_time, update_by, is_del
        from t_adur_key_duration_curve_with_spread
    </sql>

    <select id="selectAdurKeyDurationCurveWithSpreadEntityList" parameterType="com.xl.alm.app.query.AdurKeyDurationCurveWithSpreadQuery" resultMap="AdurKeyDurationCurveWithSpreadResult">
        <include refid="selectAdurKeyDurationCurveWithSpreadVo"/>
        <where>
            is_del = 0
            <if test="id != null">and id = #{id}</if>
            <if test="accountPeriod != null and accountPeriod != ''">and account_period = #{accountPeriod}</if>
            <if test="durationType != null and durationType != ''">and duration_type = #{durationType}</if>
            <if test="basisPointType != null and basisPointType != ''">and basis_point_type = #{basisPointType}</if>
            <if test="keyTerm != null and keyTerm != ''">and key_term = #{keyTerm}</if>
            <if test="stressDirection != null and stressDirection != ''">and stress_direction = #{stressDirection}</if>
            <if test="dateType != null and dateType != ''">and date_type = #{dateType}</if>
            <if test="date != null">and date = #{date}</if>
            <if test="spreadType != null and spreadType != ''">and spread_type = #{spreadType}</if>
            <if test="spread != null">and spread = #{spread}</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">and curve_sub_category = #{curveSubCategory}</if>
            <if test="assetNumber != null and assetNumber != ''">and asset_number like concat('%', #{assetNumber}, '%')</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null and assetName != ''">and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null and securityCode != ''">and security_code like concat('%', #{securityCode}, '%')</if>
            <if test="curveId != null and curveId != ''">and curve_id = #{curveId}</if>
        </where>
        order by account_period desc, asset_number, duration_type, key_term, stress_direction
    </select>

    <select id="selectAdurKeyDurationCurveWithSpreadEntityById" parameterType="Long" resultMap="AdurKeyDurationCurveWithSpreadResult">
        <include refid="selectAdurKeyDurationCurveWithSpreadVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectAdurKeyDurationCurveWithSpreadEntityByCondition" resultMap="AdurKeyDurationCurveWithSpreadResult">
        <include refid="selectAdurKeyDurationCurveWithSpreadVo"/>
        where account_period = #{accountPeriod}
        and asset_number = #{assetNumber}
        and duration_type = #{durationType}
        and key_term = #{keyTerm}
        and stress_direction = #{stressDirection}
        and is_del = 0
        limit 1
    </select>

    <insert id="insertAdurKeyDurationCurveWithSpreadEntity" parameterType="com.xl.alm.app.entity.AdurKeyDurationCurveWithSpreadEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_key_duration_curve_with_spread
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="durationType != null and durationType != ''">duration_type,</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type,</if>
            <if test="keyTerm != null and keyTerm != ''">key_term,</if>
            <if test="stressDirection != null and stressDirection != ''">stress_direction,</if>
            <if test="dateType != null and dateType != ''">date_type,</if>
            <if test="date != null">date,</if>
            <if test="spreadType != null and spreadType != ''">spread_type,</if>
            <if test="spread != null">spread,</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="keyDurationCurveWithSpreadSet != null">key_duration_curve_with_spread_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">#{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">#{basisPointType},</if>
            <if test="keyTerm != null and keyTerm != ''">#{keyTerm},</if>
            <if test="stressDirection != null and stressDirection != ''">#{stressDirection},</if>
            <if test="dateType != null and dateType != ''">#{dateType},</if>
            <if test="date != null">#{date},</if>
            <if test="spreadType != null and spreadType != ''">#{spreadType},</if>
            <if test="spread != null">#{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">#{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="keyDurationCurveWithSpreadSet != null">#{keyDurationCurveWithSpreadSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <insert id="batchInsertAdurKeyDurationCurveWithSpreadEntity" parameterType="java.util.List">
        insert into t_adur_key_duration_curve_with_spread(account_period, duration_type, basis_point_type, key_term,
                                                          stress_direction, date_type, date, spread_type, spread,
                                                          curve_sub_category, asset_number, account_name, asset_name,
                                                          security_code, curve_id, key_duration_curve_with_spread_set,
                                                          create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountPeriod}, #{item.durationType}, #{item.basisPointType}, #{item.keyTerm}, #{item.stressDirection},
             #{item.dateType}, #{item.date}, #{item.spreadType}, #{item.spread}, #{item.curveSubCategory},
             #{item.assetNumber}, #{item.accountName}, #{item.assetName}, #{item.securityCode}, #{item.curveId},
             #{item.keyDurationCurveWithSpreadSet}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <update id="updateAdurKeyDurationCurveWithSpreadEntity" parameterType="com.xl.alm.app.entity.AdurKeyDurationCurveWithSpreadEntity">
        update t_adur_key_duration_curve_with_spread
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">duration_type = #{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type = #{basisPointType},</if>
            <if test="keyTerm != null and keyTerm != ''">key_term = #{keyTerm},</if>
            <if test="stressDirection != null and stressDirection != ''">stress_direction = #{stressDirection},</if>
            <if test="dateType != null and dateType != ''">date_type = #{dateType},</if>
            <if test="date != null">date = #{date},</if>
            <if test="spreadType != null and spreadType != ''">spread_type = #{spreadType},</if>
            <if test="spread != null">spread = #{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category = #{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="keyDurationCurveWithSpreadSet != null">key_duration_curve_with_spread_set = #{keyDurationCurveWithSpreadSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdurKeyDurationCurveWithSpreadEntityById" parameterType="Long">
        update t_adur_key_duration_curve_with_spread set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAdurKeyDurationCurveWithSpreadEntityByIds" parameterType="String">
        update t_adur_key_duration_curve_with_spread set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAdurKeyDurationCurveWithSpreadEntityByAccountPeriod" parameterType="String">
        update t_adur_key_duration_curve_with_spread set is_del = 1 where account_period = #{accountPeriod}
    </delete>

</mapper>
