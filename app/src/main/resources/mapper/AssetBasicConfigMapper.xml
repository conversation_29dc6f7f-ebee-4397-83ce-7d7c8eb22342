<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetBasicConfigMapper">

    <resultMap type="com.xl.alm.app.entity.AssetBasicConfigEntity" id="AssetBasicConfigResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="sequenceNumber" column="sequence_number"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="fixedIncomeSubCategory" column="fixed_income_sub_category"/>
        <result property="calculableCashflowFlag" column="calculable_cashflow_flag"/>
        <result property="creditRatingLogicFlag" column="credit_rating_logic_flag"/>
        <result property="industryStatisticsFlag" column="industry_statistics_flag"/>
        <result property="singleAssetStatisticsFlag" column="single_asset_statistics_flag"/>
        <result property="fiveLevelStatisticsFlag" column="five_level_statistics_flag"/>
        <result property="spreadDurationStatisticsFlag" column="spread_duration_statistics_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetBasicConfigVo">
        select id, accounting_period, sequence_number, asset_sub_sub_category, fixed_income_sub_category,
               calculable_cashflow_flag, credit_rating_logic_flag, industry_statistics_flag,
               single_asset_statistics_flag, five_level_statistics_flag, spread_duration_statistics_flag,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_asset_basic_config
    </sql>

    <select id="selectAssetBasicConfigEntityList" parameterType="com.xl.alm.app.query.AssetBasicConfigQuery" resultMap="AssetBasicConfigResult">
        <include refid="selectAssetBasicConfigVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="sequenceNumber != null">
                and sequence_number = #{sequenceNumber}
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="fixedIncomeSubCategory != null and fixedIncomeSubCategory != ''">
                and fixed_income_sub_category = #{fixedIncomeSubCategory}
            </if>
            <if test="calculableCashflowFlag != null and calculableCashflowFlag != ''">
                and calculable_cashflow_flag = #{calculableCashflowFlag}
            </if>
            <if test="creditRatingLogicFlag != null and creditRatingLogicFlag != ''">
                and credit_rating_logic_flag = #{creditRatingLogicFlag}
            </if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">
                and industry_statistics_flag = #{industryStatisticsFlag}
            </if>
            <if test="singleAssetStatisticsFlag != null and singleAssetStatisticsFlag != ''">
                and single_asset_statistics_flag = #{singleAssetStatisticsFlag}
            </if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">
                and five_level_statistics_flag = #{fiveLevelStatisticsFlag}
            </if>
            <if test="spreadDurationStatisticsFlag != null and spreadDurationStatisticsFlag != ''">
                and spread_duration_statistics_flag = #{spreadDurationStatisticsFlag}
            </if>
        </where>
        order by accounting_period desc, sequence_number asc
    </select>

    <select id="selectAssetBasicConfigEntityById" parameterType="Long" resultMap="AssetBasicConfigResult">
        <include refid="selectAssetBasicConfigVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertAssetBasicConfigEntity" parameterType="com.xl.alm.app.entity.AssetBasicConfigEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_asset_basic_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="sequenceNumber != null">sequence_number,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="fixedIncomeSubCategory != null">fixed_income_sub_category,</if>
            <if test="calculableCashflowFlag != null and calculableCashflowFlag != ''">calculable_cashflow_flag,</if>
            <if test="creditRatingLogicFlag != null and creditRatingLogicFlag != ''">credit_rating_logic_flag,</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">industry_statistics_flag,</if>
            <if test="singleAssetStatisticsFlag != null and singleAssetStatisticsFlag != ''">single_asset_statistics_flag,</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag,</if>
            <if test="spreadDurationStatisticsFlag != null and spreadDurationStatisticsFlag != ''">spread_duration_statistics_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="sequenceNumber != null">#{sequenceNumber},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="fixedIncomeSubCategory != null">#{fixedIncomeSubCategory},</if>
            <if test="calculableCashflowFlag != null and calculableCashflowFlag != ''">#{calculableCashflowFlag},</if>
            <if test="creditRatingLogicFlag != null and creditRatingLogicFlag != ''">#{creditRatingLogicFlag},</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">#{industryStatisticsFlag},</if>
            <if test="singleAssetStatisticsFlag != null and singleAssetStatisticsFlag != ''">#{singleAssetStatisticsFlag},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">#{fiveLevelStatisticsFlag},</if>
            <if test="spreadDurationStatisticsFlag != null and spreadDurationStatisticsFlag != ''">#{spreadDurationStatisticsFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAssetBasicConfigEntity" parameterType="com.xl.alm.app.entity.AssetBasicConfigEntity">
        update t_ast_asset_basic_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="sequenceNumber != null">sequence_number = #{sequenceNumber},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="fixedIncomeSubCategory != null">fixed_income_sub_category = #{fixedIncomeSubCategory},</if>
            <if test="calculableCashflowFlag != null and calculableCashflowFlag != ''">calculable_cashflow_flag = #{calculableCashflowFlag},</if>
            <if test="creditRatingLogicFlag != null and creditRatingLogicFlag != ''">credit_rating_logic_flag = #{creditRatingLogicFlag},</if>
            <if test="industryStatisticsFlag != null and industryStatisticsFlag != ''">industry_statistics_flag = #{industryStatisticsFlag},</if>
            <if test="singleAssetStatisticsFlag != null and singleAssetStatisticsFlag != ''">single_asset_statistics_flag = #{singleAssetStatisticsFlag},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag = #{fiveLevelStatisticsFlag},</if>
            <if test="spreadDurationStatisticsFlag != null and spreadDurationStatisticsFlag != ''">spread_duration_statistics_flag = #{spreadDurationStatisticsFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetBasicConfigEntityById" parameterType="Long">
        update t_ast_asset_basic_config set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAssetBasicConfigEntityByIds" parameterType="String">
        update t_ast_asset_basic_config set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAssetBasicConfigEntity" parameterType="java.util.List">
        insert into t_ast_asset_basic_config (accounting_period, sequence_number, asset_sub_sub_category,
                                            fixed_income_sub_category, calculable_cashflow_flag, credit_rating_logic_flag,
                                            industry_statistics_flag, single_asset_statistics_flag, five_level_statistics_flag,
                                            spread_duration_statistics_flag, create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.sequenceNumber}, #{item.assetSubSubCategory},
             #{item.fixedIncomeSubCategory}, #{item.calculableCashflowFlag}, #{item.creditRatingLogicFlag},
             #{item.industryStatisticsFlag}, #{item.singleAssetStatisticsFlag}, #{item.fiveLevelStatisticsFlag},
             #{item.spreadDurationStatisticsFlag}, #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

</mapper>
