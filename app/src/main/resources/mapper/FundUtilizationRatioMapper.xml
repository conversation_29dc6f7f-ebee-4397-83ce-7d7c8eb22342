<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FundUtilizationRatioMapper">

    <resultMap type="com.xl.alm.app.entity.FundUtilizationRatioEntity" id="FundUtilizationRatioEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="accountName" column="account_name"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFundUtilizationRatioVo">
        select id, accounting_period, asset_sub_sub_category, account_name, book_balance, remark, create_by, create_time, update_by, update_time, is_del from t_asm_fund_utilization_ratio
    </sql>

    <!-- 查询资金运用比例监管表列表 -->
    <select id="selectFundUtilizationRatioList" parameterType="com.xl.alm.app.query.FundUtilizationRatioQuery" resultMap="FundUtilizationRatioEntityResult">
        <include refid="selectFundUtilizationRatioVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">and asset_sub_sub_category like concat('%', #{assetSubSubCategory}, '%')</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="bookBalance != null">and book_balance = #{bookBalance}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询资金运用比例监管表 -->
    <select id="selectFundUtilizationRatioById" parameterType="Long" resultMap="FundUtilizationRatioEntityResult">
        <include refid="selectFundUtilizationRatioVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、资产小小类和账户名称查询资金运用比例监管表 -->
    <select id="selectFundUtilizationRatioByCondition" resultMap="FundUtilizationRatioEntityResult">
        <include refid="selectFundUtilizationRatioVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">and asset_sub_sub_category = #{assetSubSubCategory}</if>
            <if test="accountName != null and accountName != ''">and account_name = #{accountName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增资金运用比例监管表 -->
    <insert id="insertFundUtilizationRatio" parameterType="com.xl.alm.app.entity.FundUtilizationRatioEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_fund_utilization_ratio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增资金运用比例监管表 -->
    <insert id="batchInsertFundUtilizationRatio" parameterType="java.util.List">
        insert into t_asm_fund_utilization_ratio(accounting_period, asset_sub_sub_category, account_name, book_balance, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.assetSubSubCategory}, #{item.accountName}, #{item.bookBalance}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改资金运用比例监管表 -->
    <update id="updateFundUtilizationRatio" parameterType="com.xl.alm.app.entity.FundUtilizationRatioEntity">
        update t_asm_fund_utilization_ratio
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除资金运用比例监管表 -->
    <update id="deleteFundUtilizationRatioById" parameterType="Long">
        update t_asm_fund_utilization_ratio set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除资金运用比例监管表 -->
    <update id="deleteFundUtilizationRatioByIds" parameterType="String">
        update t_asm_fund_utilization_ratio set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
