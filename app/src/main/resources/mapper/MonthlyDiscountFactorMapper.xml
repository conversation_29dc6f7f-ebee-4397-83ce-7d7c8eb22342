<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.MonthlyDiscountFactorMapper">
    
    <resultMap type="com.xl.alm.app.entity.MonthlyDiscountFactorEntity" id="MonthlyDiscountFactorResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="durationType"    column="duration_type"    />
        <result property="basisPointType"    column="basis_point_type"    />
        <result property="dateType"    column="date_type"    />
        <result property="date"    column="date"    />
        <result property="spreadType"    column="spread_type"    />
        <result property="spread"    column="spread"    />
        <result property="curveSubCategory"    column="curve_sub_category"    />
        <result property="assetNumber"    column="asset_number"    />
        <result property="accountName"    column="account_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="curveId"    column="curve_id"    />
        <result property="monthlyDiscountFactorSet"    column="monthly_discount_factor_set"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectMonthlyDiscountFactorVo">
        select id, account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
               curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
               monthly_discount_factor_set, create_by, create_time, update_by, update_time, is_del
        from t_adur_monthly_discount_factor_with_spread
    </sql>

    <select id="selectMonthlyDiscountFactorEntityList" parameterType="com.xl.alm.app.query.MonthlyDiscountFactorQuery" resultMap="MonthlyDiscountFactorResult">
        <include refid="selectMonthlyDiscountFactorVo"/>
        <where>  
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="durationType != null  and durationType != ''"> and duration_type = #{durationType}</if>
            <if test="basisPointType != null  and basisPointType != ''"> and basis_point_type = #{basisPointType}</if>
            <if test="dateType != null  and dateType != ''"> and date_type = #{dateType}</if>
            <if test="date != null"> and date = #{date}</if>
            <if test="spreadType != null  and spreadType != ''"> and spread_type = #{spreadType}</if>
            <if test="spread != null"> and spread = #{spread}</if>
            <if test="curveSubCategory != null  and curveSubCategory != ''"> and curve_sub_category = #{curveSubCategory}</if>
            <if test="assetNumber != null  and assetNumber != ''"> and asset_number = #{assetNumber}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code = #{securityCode}</if>
            <if test="curveId != null  and curveId != ''"> and curve_id = #{curveId}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectMonthlyDiscountFactorEntityById" parameterType="Long" resultMap="MonthlyDiscountFactorResult">
        <include refid="selectMonthlyDiscountFactorVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectMonthlyDiscountFactorEntityByAccountPeriodAndAssetNumberAndDateType" resultMap="MonthlyDiscountFactorResult">
        <include refid="selectMonthlyDiscountFactorVo"/>
        where account_period = #{accountPeriod} and asset_number = #{assetNumber} and date_type = #{dateType} and is_del = 0
    </select>
        
    <insert id="insertMonthlyDiscountFactorEntity" parameterType="com.xl.alm.app.entity.MonthlyDiscountFactorEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_monthly_discount_factor_with_spread
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="durationType != null and durationType != ''">duration_type,</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type,</if>
            <if test="dateType != null and dateType != ''">date_type,</if>
            <if test="date != null">date,</if>
            <if test="spreadType != null and spreadType != ''">spread_type,</if>
            <if test="spread != null">spread,</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="monthlyDiscountFactorSet != null">monthly_discount_factor_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">#{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">#{basisPointType},</if>
            <if test="dateType != null and dateType != ''">#{dateType},</if>
            <if test="date != null">#{date},</if>
            <if test="spreadType != null and spreadType != ''">#{spreadType},</if>
            <if test="spread != null">#{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">#{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="monthlyDiscountFactorSet != null">#{monthlyDiscountFactorSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateMonthlyDiscountFactorEntity" parameterType="com.xl.alm.app.entity.MonthlyDiscountFactorEntity">
        update t_adur_monthly_discount_factor_with_spread
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="durationType != null and durationType != ''">duration_type = #{durationType},</if>
            <if test="basisPointType != null and basisPointType != ''">basis_point_type = #{basisPointType},</if>
            <if test="dateType != null and dateType != ''">date_type = #{dateType},</if>
            <if test="date != null">date = #{date},</if>
            <if test="spreadType != null and spreadType != ''">spread_type = #{spreadType},</if>
            <if test="spread != null">spread = #{spread},</if>
            <if test="curveSubCategory != null and curveSubCategory != ''">curve_sub_category = #{curveSubCategory},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="monthlyDiscountFactorSet != null">monthly_discount_factor_set = #{monthlyDiscountFactorSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyDiscountFactorEntityById" parameterType="Long">
        update t_adur_monthly_discount_factor_with_spread set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteMonthlyDiscountFactorEntityByIds" parameterType="String">
        update t_adur_monthly_discount_factor_with_spread set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMonthlyDiscountFactorEntityByAccountPeriod" parameterType="String">
        delete from t_adur_monthly_discount_factor_with_spread where account_period = #{accountPeriod}
    </delete>

    <insert id="batchInsertMonthlyDiscountFactorEntity" parameterType="java.util.List">
        insert into t_adur_monthly_discount_factor_with_spread(
            account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
            curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
            term_0, term_1, term_2, term_3, term_4, term_5, term_6, term_7, term_8, term_9, term_10,
            term_11, term_12, term_13, term_14, term_15, term_16, term_17, term_18, term_19, term_20,
            -- 注意：实际需要包含term_0到term_600共601个字段
            term_600,
            create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.durationType}, #{item.basisPointType}, #{item.dateType}, #{item.date},
                #{item.spreadType}, #{item.spread}, #{item.curveSubCategory}, #{item.assetNumber}, #{item.accountName},
                #{item.assetName}, #{item.securityCode}, #{item.curveId}, #{item.term0}, #{item.term1},
                #{item.term2}, #{item.term3}, #{item.term4}, #{item.term5}, #{item.term6}, #{item.term7},
                #{item.term8}, #{item.term9}, #{item.term10}, #{item.term11}, #{item.term12}, #{item.term13},
                #{item.term14}, #{item.term15}, #{item.term16}, #{item.term17}, #{item.term18}, #{item.term19},
                #{item.term20},
                -- 注意：实际需要包含term_0到term_600共601个字段
                #{item.term600},
                #{item.createBy}
            )
        </foreach>
    </insert>

</mapper>
