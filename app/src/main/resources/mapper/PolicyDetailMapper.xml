<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.PolicyDetailMapper">

    <resultMap type="com.xl.alm.app.entity.PolicyDetailEntity" id="PolicyDetailEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="polNo" column="pol_no"/>
        <result property="contNo" column="cont_no"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="effectiveMonth" column="effective_month"/>
        <result property="riskCode" column="risk_code"/>
        <result property="polStatusDesc" column="pol_status_desc"/>
        <result property="polStatusChangedTime" column="pol_status_changed_time"/>
        <result property="polStatusChangedMonth" column="pol_status_changed_month"/>
        <result property="designType" column="design_type"/>
        <result property="busTypeCode" column="bus_type_code"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectPolicyDetailEntityVo">
        select id, accounting_period, pol_no, cont_no, effective_date, effective_month, risk_code, pol_status_desc, pol_status_changed_time, pol_status_changed_month,
               design_type, bus_type_code, create_time, create_by, update_time, update_by, is_del
        from t_insu_policy_detail
    </sql>

    <!-- 查询保单数据明细列表 -->
    <select id="selectPolicyDetailEntityList" parameterType="com.xl.alm.app.query.PolicyDetailQuery" resultMap="PolicyDetailEntityResult">
        <include refid="selectPolicyDetailEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="polNo != null and polNo != ''">and pol_no = #{polNo}</if>
            <if test="contNo != null and contNo != ''">and cont_no = #{contNo}</if>
            <if test="effectiveDate != null">and effective_date = #{effectiveDate}</if>
            <if test="riskCode != null and riskCode != ''">and risk_code = #{riskCode}</if>
            <if test="polStatusDesc != null and polStatusDesc != ''">and pol_status_desc = #{polStatusDesc}</if>
            <if test="polStatusChangedTime != null">and pol_status_changed_time = #{polStatusChangedTime}</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            <if test="busTypeCode != null and busTypeCode != ''">and bus_type_code = #{busTypeCode}</if>
            <if test="beginTime != null and beginTime != ''">
                AND DATE_FORMAT(effective_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
            </if>
            <if test="endTime != null and endTime != ''">
                AND DATE_FORMAT(effective_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
            </if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 用id查询保单数据明细 -->
    <select id="selectPolicyDetailEntityById" parameterType="Long" resultMap="PolicyDetailEntityResult">
        <include refid="selectPolicyDetailEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据险种号查询保单数据明细 -->
    <select id="selectPolicyDetailEntityByPolNo" resultMap="PolicyDetailEntityResult">
        <include refid="selectPolicyDetailEntityVo"/>
        where pol_no = #{polNo} and is_del = 0
        limit 1
    </select>

    <!-- 新增保单数据明细 -->
    <insert id="insertPolicyDetailEntity" parameterType="com.xl.alm.app.entity.PolicyDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_insu_policy_detail (
            accounting_period, pol_no, cont_no, effective_date, effective_month, risk_code, pol_status_desc, pol_status_changed_time, pol_status_changed_month,
            design_type, bus_type_code, create_by, update_by
        ) values (
            #{accountingPeriod}, #{polNo}, #{contNo}, #{effectiveDate}, #{effectiveMonth}, #{riskCode}, #{polStatusDesc}, #{polStatusChangedTime}, #{polStatusChangedMonth},
            #{designType}, #{busTypeCode}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量新增保单数据明细 -->
    <insert id="batchInsertPolicyDetailEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_insu_policy_detail (
            accounting_period, pol_no, cont_no, effective_date, effective_month, risk_code, pol_status_desc, pol_status_changed_time, pol_status_changed_month,
            design_type, bus_type_code, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod}, #{item.polNo}, #{item.contNo}, #{item.effectiveDate}, #{item.effectiveMonth}, #{item.riskCode}, #{item.polStatusDesc}, #{item.polStatusChangedTime}, #{item.polStatusChangedMonth},
            #{item.designType},  #{item.busTypeCode}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 修改保单数据明细 -->
    <update id="updatePolicyDetailEntity" parameterType="com.xl.alm.app.entity.PolicyDetailEntity">
        update t_insu_policy_detail
        <set>
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="polNo != null and polNo != ''">pol_no = #{polNo},</if>
            <if test="contNo != null and contNo != ''">cont_no = #{contNo},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="effectiveMonth != null and effectiveMonth != ''">effective_month = #{effectiveMonth},</if>
            <if test="riskCode != null and riskCode != ''">risk_code = #{riskCode},</if>
            <if test="polStatusDesc != null and polStatusDesc != ''">pol_status_desc = #{polStatusDesc},</if>
            <if test="polStatusChangedTime != null">pol_status_changed_time = #{polStatusChangedTime},</if>
            <if test="polStatusChangedMonth != null and polStatusChangedMonth != ''">pol_status_changed_month = #{polStatusChangedMonth},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="busTypeCode != null and busTypeCode != ''">bus_type_code = #{busTypeCode},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除保单数据明细 -->
    <update id="deletePolicyDetailEntityById" parameterType="Long">
        update t_insu_policy_detail set is_del = 1, update_time = now() 
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除保单数据明细 -->
    <update id="deletePolicyDetailEntityByIds" parameterType="Long">
        update t_insu_policy_detail set is_del = 1, update_time = now() 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
