<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetDetailOverallMapper">

    <resultMap type="com.xl.alm.app.entity.AssetDetailOverallEntity" id="AssetDetailOverallResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="assetNumber" column="asset_number"/>
        <result property="accountName" column="account_name"/>
        <result property="securityCode" column="security_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="assetMajorCategory" column="asset_major_category"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="fixedIncomeSubCategory" column="fixed_income_sub_category"/>
        <result property="assetAllocationLevel1" column="asset_allocation_level1"/>
        <result property="assetAllocationLevel2" column="asset_allocation_level2"/>
        <result property="assetAllocationLevel3" column="asset_allocation_level3"/>
        <result property="windEntityRating" column="wind_entity_rating"/>
        <result property="windDebtRating" column="wind_debt_rating"/>
        <result property="riskEntityRating" column="risk_entity_rating"/>
        <result property="riskDebtRating" column="risk_debt_rating"/>
        <result property="creditRatingMaintenance" column="credit_rating_maintenance"/>
        <result property="creditRatingLogicFlag" column="credit_rating_logic_flag"/>
        <result property="creditRating" column="credit_rating"/>
        <result property="creditRatingCategory" column="credit_rating_category"/>
        <result property="holdingQuantity" column="holding_quantity"/>
        <result property="holdingFaceValue" column="holding_face_value"/>
        <result property="cost" column="cost"/>
        <result property="netCost" column="net_cost"/>
        <result property="netMarketValue" column="net_market_value"/>
        <result property="marketValue" column="market_value"/>
        <result property="var1Year" column="var_1_year"/>
        <result property="var3Year" column="var_3_year"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="assetImpairmentProvision" column="asset_impairment_provision"/>
        <result property="bookValue" column="book_value"/>
        <result property="couponRate" column="coupon_rate"/>
        <result property="annualPaymentFrequency" column="annual_payment_frequency"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="valueDate" column="value_date"/>
        <result property="purchaseDate" column="purchase_date"/>
        <result property="maturityDate" column="maturity_date"/>
        <result property="accountingType" column="accounting_type"/>
        <result property="remainingTerm" column="remaining_term"/>
        <result property="remainingTermCategory" column="remaining_term_category"/>
        <result property="remainingTermFlag" column="remaining_term_flag"/>
        <result property="industryStatisticsFlag" column="industry_statistics_flag"/>
        <result property="bondType" column="bond_type"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="almAssetName" column="alm_asset_name"/>
        <result property="bankClassification" column="bank_classification"/>
        <result property="fiveLevelClassification" column="five_level_classification"/>
        <result property="calculableCashflowFlag" column="calculable_cashflow_flag"/>
        <result property="spreadDurationStatisticsFlag" column="spread_duration_statistics_flag"/>
        <result property="singleAssetStatisticsFlag" column="single_asset_statistics_flag"/>
        <result property="fiveLevelStatisticsFlag" column="five_level_statistics_flag"/>
        <result property="assetLiquidityCategory" column="asset_liquidity_category"/>
        <result property="realizationCoefficient" column="realization_coefficient"/>
        <result property="discountCurveRating" column="discount_curve_rating"/>
        <result property="discountCurveFlag" column="discount_curve_flag"/>
        <result property="adjustedValueDate" column="adjusted_value_date"/>
        <result property="adjustedPurchaseDate" column="adjusted_purchase_date"/>
        <result property="adjustedMaturityDate" column="adjusted_maturity_date"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetDetailOverallVo">
        select id, accounting_period, asset_number, account_name, security_code, asset_name, asset_sub_sub_category,
               asset_major_category, domestic_foreign, fixed_income_sub_category, asset_allocation_level1,
               asset_allocation_level2, asset_allocation_level3, wind_entity_rating, wind_debt_rating,
               risk_entity_rating, risk_debt_rating, credit_rating_maintenance, credit_rating_logic_flag,
               credit_rating, credit_rating_category, holding_quantity, holding_face_value, cost, net_cost,
               net_market_value, market_value, var_1_year, var_3_year, book_balance, asset_impairment_provision,
               book_value, coupon_rate, annual_payment_frequency, payment_method, value_date, purchase_date,
               maturity_date, accounting_type, remaining_term, remaining_term_category, remaining_term_flag,
               industry_statistics_flag, bond_type, fixed_income_term_category, alm_asset_name, bank_classification,
               five_level_classification, calculable_cashflow_flag, spread_duration_statistics_flag,
               single_asset_statistics_flag, five_level_statistics_flag, asset_liquidity_category,
               realization_coefficient, discount_curve_rating, discount_curve_flag, adjusted_value_date,
               adjusted_purchase_date, adjusted_maturity_date, create_time, create_by, update_time, update_by, is_del
        from t_ast_asset_detail_overall
    </sql>

    <select id="selectAssetDetailOverallList" parameterType="com.xl.alm.app.query.AssetDetailOverallQuery" resultMap="AssetDetailOverallResult">
        <include refid="selectAssetDetailOverallVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="assetNumber != null and assetNumber != ''">
                and asset_number like concat('%', #{assetNumber}, '%')
            </if>
            <if test="accountName != null and accountName != ''">
                and account_name like concat('%', #{accountName}, '%')
            </if>
            <if test="securityCode != null and securityCode != ''">
                and security_code like concat('%', #{securityCode}, '%')
            </if>
            <if test="assetName != null and assetName != ''">
                and asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="assetMajorCategory != null and assetMajorCategory != ''">
                and asset_major_category = #{assetMajorCategory}
            </if>
            <if test="domesticForeign != null and domesticForeign != ''">
                and domestic_foreign = #{domesticForeign}
            </if>
            <if test="fixedIncomeSubCategory != null and fixedIncomeSubCategory != ''">
                and fixed_income_sub_category = #{fixedIncomeSubCategory}
            </if>
            <if test="assetAllocationLevel1 != null and assetAllocationLevel1 != ''">
                and asset_allocation_level1 like concat('%', #{assetAllocationLevel1}, '%')
            </if>
            <if test="assetAllocationLevel2 != null and assetAllocationLevel2 != ''">
                and asset_allocation_level2 like concat('%', #{assetAllocationLevel2}, '%')
            </if>
            <if test="assetAllocationLevel3 != null and assetAllocationLevel3 != ''">
                and asset_allocation_level3 like concat('%', #{assetAllocationLevel3}, '%')
            </if>
            <if test="windEntityRating != null and windEntityRating != ''">
                and wind_entity_rating = #{windEntityRating}
            </if>
            <if test="windDebtRating != null and windDebtRating != ''">
                and wind_debt_rating = #{windDebtRating}
            </if>
            <if test="riskEntityRating != null and riskEntityRating != ''">
                and risk_entity_rating = #{riskEntityRating}
            </if>
            <if test="riskDebtRating != null and riskDebtRating != ''">
                and risk_debt_rating = #{riskDebtRating}
            </if>
            <if test="creditRatingMaintenance != null and creditRatingMaintenance != ''">
                and credit_rating_maintenance = #{creditRatingMaintenance}
            </if>
            <if test="creditRatingLogicFlag != null and creditRatingLogicFlag != ''">
                and credit_rating_logic_flag = #{creditRatingLogicFlag}
            </if>
            <if test="creditRating != null and creditRating != ''">
                and credit_rating = #{creditRating}
            </if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">
                and credit_rating_category = #{creditRatingCategory}
            </if>
            <if test="holdingQuantityMin != null">
                and holding_quantity &gt;= #{holdingQuantityMin}
            </if>
            <if test="holdingQuantityMax != null">
                and holding_quantity &lt;= #{holdingQuantityMax}
            </if>
            <if test="marketValueMin != null">
                and market_value &gt;= #{marketValueMin}
            </if>
            <if test="marketValueMax != null">
                and market_value &lt;= #{marketValueMax}
            </if>
            <if test="annualPaymentFrequency != null">
                and annual_payment_frequency = #{annualPaymentFrequency}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and payment_method = #{paymentMethod}
            </if>
            <if test="valueDateStart != null">
                and value_date &gt;= #{valueDateStart}
            </if>
            <if test="valueDateEnd != null">
                and value_date &lt;= #{valueDateEnd}
            </if>
            <if test="purchaseDateStart != null">
                and purchase_date &gt;= #{purchaseDateStart}
            </if>
            <if test="purchaseDateEnd != null">
                and purchase_date &lt;= #{purchaseDateEnd}
            </if>
            <if test="maturityDateStart != null">
                and maturity_date &gt;= #{maturityDateStart}
            </if>
            <if test="maturityDateEnd != null">
                and maturity_date &lt;= #{maturityDateEnd}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAssetDetailOverallById" parameterType="Long" resultMap="AssetDetailOverallResult">
        <include refid="selectAssetDetailOverallVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertAssetDetailOverall" parameterType="com.xl.alm.app.entity.AssetDetailOverallEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_asset_detail_overall
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="securityCode != null">security_code,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="assetSubSubCategory != null">asset_sub_sub_category,</if>
            <if test="assetMajorCategory != null and assetMajorCategory != ''">asset_major_category,</if>
            <if test="domesticForeign != null">domestic_foreign,</if>
            <if test="fixedIncomeSubCategory != null">fixed_income_sub_category,</if>
            <if test="assetAllocationLevel1 != null">asset_allocation_level1,</if>
            <if test="assetAllocationLevel2 != null">asset_allocation_level2,</if>
            <if test="assetAllocationLevel3 != null">asset_allocation_level3,</if>
            <if test="windEntityRating != null">wind_entity_rating,</if>
            <if test="windDebtRating != null">wind_debt_rating,</if>
            <if test="riskEntityRating != null">risk_entity_rating,</if>
            <if test="riskDebtRating != null">risk_debt_rating,</if>
            <if test="creditRatingMaintenance != null">credit_rating_maintenance,</if>
            <if test="creditRatingLogicFlag != null">credit_rating_logic_flag,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="creditRatingCategory != null">credit_rating_category,</if>
            <if test="holdingQuantity != null">holding_quantity,</if>
            <if test="holdingFaceValue != null">holding_face_value,</if>
            <if test="cost != null">cost,</if>
            <if test="netCost != null">net_cost,</if>
            <if test="netMarketValue != null">net_market_value,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="var1Year != null">var_1_year,</if>
            <if test="var3Year != null">var_3_year,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="assetImpairmentProvision != null">asset_impairment_provision,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="couponRate != null">coupon_rate,</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="valueDate != null">value_date,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="maturityDate != null">maturity_date,</if>
            <if test="accountingType != null">accounting_type,</if>
            <if test="remainingTerm != null">remaining_term,</if>
            <if test="remainingTermCategory != null">remaining_term_category,</if>
            <if test="remainingTermFlag != null">remaining_term_flag,</if>
            <if test="industryStatisticsFlag != null">industry_statistics_flag,</if>
            <if test="bondType != null">bond_type,</if>
            <if test="fixedIncomeTermCategory != null">fixed_income_term_category,</if>
            <if test="almAssetName != null">alm_asset_name,</if>
            <if test="bankClassification != null">bank_classification,</if>
            <if test="fiveLevelClassification != null">five_level_classification,</if>
            <if test="calculableCashflowFlag != null">calculable_cashflow_flag,</if>
            <if test="spreadDurationStatisticsFlag != null">spread_duration_statistics_flag,</if>
            <if test="singleAssetStatisticsFlag != null">single_asset_statistics_flag,</if>
            <if test="fiveLevelStatisticsFlag != null">five_level_statistics_flag,</if>
            <if test="assetLiquidityCategory != null">asset_liquidity_category,</if>
            <if test="realizationCoefficient != null">realization_coefficient,</if>
            <if test="discountCurveRating != null">discount_curve_rating,</if>
            <if test="discountCurveFlag != null">discount_curve_flag,</if>
            <if test="adjustedValueDate != null">adjusted_value_date,</if>
            <if test="adjustedPurchaseDate != null">adjusted_purchase_date,</if>
            <if test="adjustedMaturityDate != null">adjusted_maturity_date,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="securityCode != null">#{securityCode},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetSubSubCategory != null">#{assetSubSubCategory},</if>
            <if test="assetMajorCategory != null and assetMajorCategory != ''">#{assetMajorCategory},</if>
            <if test="domesticForeign != null">#{domesticForeign},</if>
            <if test="fixedIncomeSubCategory != null">#{fixedIncomeSubCategory},</if>
            <if test="assetAllocationLevel1 != null">#{assetAllocationLevel1},</if>
            <if test="assetAllocationLevel2 != null">#{assetAllocationLevel2},</if>
            <if test="assetAllocationLevel3 != null">#{assetAllocationLevel3},</if>
            <if test="windEntityRating != null">#{windEntityRating},</if>
            <if test="windDebtRating != null">#{windDebtRating},</if>
            <if test="riskEntityRating != null">#{riskEntityRating},</if>
            <if test="riskDebtRating != null">#{riskDebtRating},</if>
            <if test="creditRatingMaintenance != null">#{creditRatingMaintenance},</if>
            <if test="creditRatingLogicFlag != null">#{creditRatingLogicFlag},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="creditRatingCategory != null">#{creditRatingCategory},</if>
            <if test="holdingQuantity != null">#{holdingQuantity},</if>
            <if test="holdingFaceValue != null">#{holdingFaceValue},</if>
            <if test="cost != null">#{cost},</if>
            <if test="netCost != null">#{netCost},</if>
            <if test="netMarketValue != null">#{netMarketValue},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="var1Year != null">#{var1Year},</if>
            <if test="var3Year != null">#{var3Year},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="assetImpairmentProvision != null">#{assetImpairmentProvision},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="couponRate != null">#{couponRate},</if>
            <if test="annualPaymentFrequency != null">#{annualPaymentFrequency},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="valueDate != null">#{valueDate},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="maturityDate != null">#{maturityDate},</if>
            <if test="accountingType != null">#{accountingType},</if>
            <if test="remainingTerm != null">#{remainingTerm},</if>
            <if test="remainingTermCategory != null">#{remainingTermCategory},</if>
            <if test="remainingTermFlag != null">#{remainingTermFlag},</if>
            <if test="industryStatisticsFlag != null">#{industryStatisticsFlag},</if>
            <if test="bondType != null">#{bondType},</if>
            <if test="fixedIncomeTermCategory != null">#{fixedIncomeTermCategory},</if>
            <if test="almAssetName != null">#{almAssetName},</if>
            <if test="bankClassification != null">#{bankClassification},</if>
            <if test="fiveLevelClassification != null">#{fiveLevelClassification},</if>
            <if test="calculableCashflowFlag != null">#{calculableCashflowFlag},</if>
            <if test="spreadDurationStatisticsFlag != null">#{spreadDurationStatisticsFlag},</if>
            <if test="singleAssetStatisticsFlag != null">#{singleAssetStatisticsFlag},</if>
            <if test="fiveLevelStatisticsFlag != null">#{fiveLevelStatisticsFlag},</if>
            <if test="assetLiquidityCategory != null">#{assetLiquidityCategory},</if>
            <if test="realizationCoefficient != null">#{realizationCoefficient},</if>
            <if test="discountCurveRating != null">#{discountCurveRating},</if>
            <if test="discountCurveFlag != null">#{discountCurveFlag},</if>
            <if test="adjustedValueDate != null">#{adjustedValueDate},</if>
            <if test="adjustedPurchaseDate != null">#{adjustedPurchaseDate},</if>
            <if test="adjustedMaturityDate != null">#{adjustedMaturityDate},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAssetDetailOverall" parameterType="com.xl.alm.app.entity.AssetDetailOverallEntity">
        update t_ast_asset_detail_overall
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="securityCode != null">security_code = #{securityCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="assetSubSubCategory != null">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="assetMajorCategory != null and assetMajorCategory != ''">asset_major_category = #{assetMajorCategory},</if>
            <if test="domesticForeign != null">domestic_foreign = #{domesticForeign},</if>
            <if test="fixedIncomeSubCategory != null">fixed_income_sub_category = #{fixedIncomeSubCategory},</if>
            <if test="assetAllocationLevel1 != null">asset_allocation_level1 = #{assetAllocationLevel1},</if>
            <if test="assetAllocationLevel2 != null">asset_allocation_level2 = #{assetAllocationLevel2},</if>
            <if test="assetAllocationLevel3 != null">asset_allocation_level3 = #{assetAllocationLevel3},</if>
            <if test="windEntityRating != null">wind_entity_rating = #{windEntityRating},</if>
            <if test="windDebtRating != null">wind_debt_rating = #{windDebtRating},</if>
            <if test="riskEntityRating != null">risk_entity_rating = #{riskEntityRating},</if>
            <if test="riskDebtRating != null">risk_debt_rating = #{riskDebtRating},</if>
            <if test="creditRatingMaintenance != null">credit_rating_maintenance = #{creditRatingMaintenance},</if>
            <if test="creditRatingLogicFlag != null">credit_rating_logic_flag = #{creditRatingLogicFlag},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="creditRatingCategory != null">credit_rating_category = #{creditRatingCategory},</if>
            <if test="holdingQuantity != null">holding_quantity = #{holdingQuantity},</if>
            <if test="holdingFaceValue != null">holding_face_value = #{holdingFaceValue},</if>
            <if test="cost != null">cost = #{cost},</if>
            <if test="netCost != null">net_cost = #{netCost},</if>
            <if test="netMarketValue != null">net_market_value = #{netMarketValue},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="var1Year != null">var_1_year = #{var1Year},</if>
            <if test="var3Year != null">var_3_year = #{var3Year},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="assetImpairmentProvision != null">asset_impairment_provision = #{assetImpairmentProvision},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="couponRate != null">coupon_rate = #{couponRate},</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency = #{annualPaymentFrequency},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="valueDate != null">value_date = #{valueDate},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="maturityDate != null">maturity_date = #{maturityDate},</if>
            <if test="accountingType != null">accounting_type = #{accountingType},</if>
            <if test="remainingTerm != null">remaining_term = #{remainingTerm},</if>
            <if test="remainingTermCategory != null">remaining_term_category = #{remainingTermCategory},</if>
            <if test="remainingTermFlag != null">remaining_term_flag = #{remainingTermFlag},</if>
            <if test="industryStatisticsFlag != null">industry_statistics_flag = #{industryStatisticsFlag},</if>
            <if test="bondType != null">bond_type = #{bondType},</if>
            <if test="fixedIncomeTermCategory != null">fixed_income_term_category = #{fixedIncomeTermCategory},</if>
            <if test="almAssetName != null">alm_asset_name = #{almAssetName},</if>
            <if test="bankClassification != null">bank_classification = #{bankClassification},</if>
            <if test="fiveLevelClassification != null">five_level_classification = #{fiveLevelClassification},</if>
            <if test="calculableCashflowFlag != null">calculable_cashflow_flag = #{calculableCashflowFlag},</if>
            <if test="spreadDurationStatisticsFlag != null">spread_duration_statistics_flag = #{spreadDurationStatisticsFlag},</if>
            <if test="singleAssetStatisticsFlag != null">single_asset_statistics_flag = #{singleAssetStatisticsFlag},</if>
            <if test="fiveLevelStatisticsFlag != null">five_level_statistics_flag = #{fiveLevelStatisticsFlag},</if>
            <if test="assetLiquidityCategory != null">asset_liquidity_category = #{assetLiquidityCategory},</if>
            <if test="realizationCoefficient != null">realization_coefficient = #{realizationCoefficient},</if>
            <if test="discountCurveRating != null">discount_curve_rating = #{discountCurveRating},</if>
            <if test="discountCurveFlag != null">discount_curve_flag = #{discountCurveFlag},</if>
            <if test="adjustedValueDate != null">adjusted_value_date = #{adjustedValueDate},</if>
            <if test="adjustedPurchaseDate != null">adjusted_purchase_date = #{adjustedPurchaseDate},</if>
            <if test="adjustedMaturityDate != null">adjusted_maturity_date = #{adjustedMaturityDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetDetailOverallById" parameterType="Long">
        update t_ast_asset_detail_overall set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAssetDetailOverallByIds" parameterType="Long">
        update t_ast_asset_detail_overall set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkUniqueKey" resultType="int">
        select count(1) from t_ast_asset_detail_overall
        where accounting_period = #{accountingPeriod}
          and asset_number = #{assetNumber}
          and account_name = #{accountName}
          and security_code = #{securityCode}
          and is_del = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>