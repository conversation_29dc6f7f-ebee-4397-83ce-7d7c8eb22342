<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.ThreeYearBusinessPlanMapper">

    <resultMap type="com.xl.alm.app.entity.ThreeYearBusinessPlanEntity" id="ThreeYearBusinessPlanEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="accountType" column="account_type"/>
        <result property="firstYear" column="first_year"/>
        <result property="secondYear" column="second_year"/>
        <result property="thirdYear" column="third_year"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectThreeYearBusinessPlanEntityVo">
        select id, accounting_period, account_type, first_year, second_year, third_year,
               create_time, create_by, update_time, update_by, remark, is_del
        from t_liab_three_year_business_plan
    </sql>

    <!-- 查询三年新业务规划列表 -->
    <select id="selectThreeYearBusinessPlanEntityList" parameterType="com.xl.alm.app.query.ThreeYearBusinessPlanQuery" resultMap="ThreeYearBusinessPlanEntityResult">
        <include refid="selectThreeYearBusinessPlanEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="accountType != null and accountType != ''">and account_type like concat('%', #{accountType}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据id查询三年新业务规划 -->
    <select id="selectThreeYearBusinessPlanEntityById" parameterType="Long" resultMap="ThreeYearBusinessPlanEntityResult">
        <include refid="selectThreeYearBusinessPlanEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 新增三年新业务规划 -->
    <insert id="insertThreeYearBusinessPlanEntity" parameterType="com.xl.alm.app.entity.ThreeYearBusinessPlanEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_three_year_business_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountType != null and accountType != ''">account_type,</if>
            <if test="firstYear != null">first_year,</if>
            <if test="secondYear != null">second_year,</if>
            <if test="thirdYear != null">third_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountType != null and accountType != ''">#{accountType},</if>
            <if test="firstYear != null">#{firstYear},</if>
            <if test="secondYear != null">#{secondYear},</if>
            <if test="thirdYear != null">#{thirdYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 批量新增三年新业务规划 -->
    <insert id="batchInsertThreeYearBusinessPlanEntity" parameterType="java.util.List">
        insert into t_liab_three_year_business_plan(accounting_period, account_type, first_year, second_year, third_year, create_by, remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.accountType}, #{item.firstYear}, #{item.secondYear}, #{item.thirdYear}, #{item.createBy}, #{item.remark})
        </foreach>
    </insert>

    <!-- 修改三年新业务规划 -->
    <update id="updateThreeYearBusinessPlanEntity" parameterType="com.xl.alm.app.entity.ThreeYearBusinessPlanEntity">
        update t_liab_three_year_business_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="firstYear != null">first_year = #{firstYear},</if>
            <if test="secondYear != null">second_year = #{secondYear},</if>
            <if test="thirdYear != null">third_year = #{thirdYear},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <!-- 删除三年新业务规划 -->
    <update id="deleteThreeYearBusinessPlanEntityById" parameterType="Long">
        update t_liab_three_year_business_plan set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除三年新业务规划 -->
    <update id="deleteThreeYearBusinessPlanEntityByIds" parameterType="String">
        update t_liab_three_year_business_plan set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除三年新业务规划 -->
    <update id="deleteThreeYearBusinessPlanEntityByPeriod" parameterType="String">
        update t_liab_three_year_business_plan set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
