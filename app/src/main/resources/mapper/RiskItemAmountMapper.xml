<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.RiskItemAmountMapper">

    <resultMap type="com.xl.alm.app.entity.RiskItemAmountEntity" id="RiskItemAmountResult">
        <id property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemCode" column="item_code"/>
        <result property="s05Amount" column="s05_amount"/>
        <result property="ir05Amount" column="ir05_amount"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectRiskItemAmountVo">
        select id, accounting_period, item_code, s05_amount, ir05_amount, sort_order, create_time, create_by, update_time, update_by, is_del
        from t_minc_risk_item_amount
    </sql>

    <select id="selectRiskItemAmountEntityList" parameterType="com.xl.alm.app.query.RiskItemAmountQuery" resultMap="RiskItemAmountResult">
        <include refid="selectRiskItemAmountVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                AND accounting_period = #{accountingPeriod}
            </if>
            <if test="itemCode != null and itemCode != ''">
                AND item_code like concat('%', #{itemCode}, '%')
            </if>
            <if test="itemName != null and itemName != ''">
                AND item_code IN (
                    SELECT dict_value FROM sys_dict_data
                    WHERE dict_type = 'minc_risk_item'
                    AND dict_label like concat('%', #{itemName}, '%')
                    AND status = '0'
                )
            </if>
        </where>
        order by
            -- 按照导入顺序排序：先按账期，再按导入时的行号
            accounting_period DESC,
            sort_order ASC
    </select>

    <select id="selectRiskItemAmountEntityById" parameterType="Long" resultMap="RiskItemAmountResult">
        <include refid="selectRiskItemAmountVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertRiskItemAmountEntity" parameterType="com.xl.alm.app.entity.RiskItemAmountEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_minc_risk_item_amount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null">accounting_period,</if>
            <if test="itemCode != null">item_code,</if>
            <if test="s05Amount != null">s05_amount,</if>
            <if test="ir05Amount != null">ir05_amount,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null">#{accountingPeriod},</if>
            <if test="itemCode != null">#{itemCode},</if>
            <if test="s05Amount != null">#{s05Amount},</if>
            <if test="ir05Amount != null">#{ir05Amount},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <insert id="batchInsertRiskItemAmountEntity" parameterType="java.util.List">
        insert into t_minc_risk_item_amount (
        accounting_period, item_code, s05_amount, ir05_amount, sort_order, create_by, update_by
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.accountingPeriod}, #{item.itemCode}, #{item.s05Amount}, #{item.ir05Amount}, #{item.sortOrder},
            #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <update id="updateRiskItemAmountEntity" parameterType="com.xl.alm.app.entity.RiskItemAmountEntity">
        update t_minc_risk_item_amount
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null">accounting_period = #{accountingPeriod},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="s05Amount != null">s05_amount = #{s05Amount},</if>
            <if test="ir05Amount != null">ir05_amount = #{ir05Amount},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = CURRENT_TIMESTAMP,
        </trim>
        where id = #{id} and is_del = 0
    </update>

    <update id="deleteRiskItemAmountEntityById" parameterType="Long">
        update t_minc_risk_item_amount set is_del = 1 where id = #{id}
    </update>

    <update id="deleteRiskItemAmountEntityByIds" parameterType="Long">
        update t_minc_risk_item_amount set is_del = 1 where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteRiskItemAmountEntityByPeriod" parameterType="String">
        update t_minc_risk_item_amount set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

    <update id="deleteRiskItemAmountEntityByCondition">
        update t_minc_risk_item_amount set is_del = 1
        where accounting_period = #{accountingPeriod} and item_code = #{itemCode}
    </update>

    <update id="deleteByAccountingPeriodAndItemCode">
        update t_minc_risk_item_amount set is_del = 1
        where accounting_period = #{accountingPeriod} and item_code = #{itemCode}
    </update>

</mapper>
