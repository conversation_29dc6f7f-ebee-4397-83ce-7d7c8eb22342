<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FixedIncomeCreditRatingMapper">

    <resultMap type="com.xl.alm.app.entity.FixedIncomeCreditRatingEntity" id="FixedIncomeCreditRatingEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="creditRatingCategory" column="credit_rating_category"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFixedIncomeCreditRatingVo">
        select id, accounting_period, domestic_foreign, fixed_income_term_category, credit_rating_category, book_balance, create_by, create_time, update_by, update_time, is_del from t_acm_fixed_income_credit_rating
    </sql>

    <!-- 查询固定收益类投资资产信用评级表列表 -->
    <select id="selectFixedIncomeCreditRatingList" parameterType="com.xl.alm.app.query.FixedIncomeCreditRatingQuery" resultMap="FixedIncomeCreditRatingEntityResult">
        <include refid="selectFixedIncomeCreditRatingVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">and fixed_income_term_category = #{fixedIncomeTermCategory}</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">and credit_rating_category = #{creditRatingCategory}</if>
            <if test="bookBalance != null">and book_balance = #{bookBalance}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询固定收益类投资资产信用评级表 -->
    <select id="selectFixedIncomeCreditRatingById" parameterType="Long" resultMap="FixedIncomeCreditRatingEntityResult">
        <include refid="selectFixedIncomeCreditRatingVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、境内外标识、固收资产剩余期限资产分类和信用评级分类查询固定收益类投资资产信用评级表 -->
    <select id="selectFixedIncomeCreditRatingByCondition" resultMap="FixedIncomeCreditRatingEntityResult">
        <include refid="selectFixedIncomeCreditRatingVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">and fixed_income_term_category = #{fixedIncomeTermCategory}</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">and credit_rating_category = #{creditRatingCategory}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增固定收益类投资资产信用评级表 -->
    <insert id="insertFixedIncomeCreditRating" parameterType="com.xl.alm.app.entity.FixedIncomeCreditRatingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_fixed_income_credit_rating
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category,</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">credit_rating_category,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">#{fixedIncomeTermCategory},</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">#{creditRatingCategory},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增固定收益类投资资产信用评级表 -->
    <insert id="batchInsertFixedIncomeCreditRating" parameterType="java.util.List">
        insert into t_acm_fixed_income_credit_rating(accounting_period, domestic_foreign, fixed_income_term_category, credit_rating_category, book_balance, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.fixedIncomeTermCategory}, #{item.creditRatingCategory}, #{item.bookBalance}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改固定收益类投资资产信用评级表 -->
    <update id="updateFixedIncomeCreditRating" parameterType="com.xl.alm.app.entity.FixedIncomeCreditRatingEntity">
        update t_acm_fixed_income_credit_rating
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category = #{fixedIncomeTermCategory},</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">credit_rating_category = #{creditRatingCategory},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除固定收益类投资资产信用评级表 -->
    <update id="deleteFixedIncomeCreditRatingById" parameterType="Long">
        update t_acm_fixed_income_credit_rating set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除固定收益类投资资产信用评级表 -->
    <update id="deleteFixedIncomeCreditRatingByIds" parameterType="String">
        update t_acm_fixed_income_credit_rating set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
