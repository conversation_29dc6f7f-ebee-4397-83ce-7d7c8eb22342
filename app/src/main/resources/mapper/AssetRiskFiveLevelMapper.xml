<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetRiskFiveLevelMapper">

    <resultMap type="com.xl.alm.app.entity.AssetRiskFiveLevelEntity" id="AssetRiskFiveLevelResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="fiveLevelStatisticsFlag" column="five_level_statistics_flag"/>
        <result property="fiveLevelClassification" column="five_level_classification"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetRiskFiveLevelVo">
        select id, accounting_period, item_name, five_level_statistics_flag, five_level_classification, book_balance, create_time, create_by, update_time, update_by, is_del
        from t_acm_asset_risk_five_level
    </sql>

    <!-- 查询保险资产风险五级分类状况表列表 -->
    <select id="selectAssetRiskFiveLevelList" parameterType="com.xl.alm.app.query.AssetRiskFiveLevelQuery" resultMap="AssetRiskFiveLevelResult">
        <include refid="selectAssetRiskFiveLevelVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">
                and five_level_statistics_flag = #{fiveLevelStatisticsFlag}
            </if>
            <if test="fiveLevelClassification != null and fiveLevelClassification != ''">
                and five_level_classification = #{fiveLevelClassification}
            </if>
        </where>
        order by accounting_period desc, item_name, five_level_statistics_flag, five_level_classification
    </select>

    <!-- 用id查询保险资产风险五级分类状况表 -->
    <select id="selectAssetRiskFiveLevelById" parameterType="Long" resultMap="AssetRiskFiveLevelResult">
        <include refid="selectAssetRiskFiveLevelVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询保险资产风险五级分类状况表 -->
    <select id="selectAssetRiskFiveLevelByCondition" resultMap="AssetRiskFiveLevelResult">
        <include refid="selectAssetRiskFiveLevelVo"/>
        where accounting_period = #{accountingPeriod}
        and item_name = #{itemName}
        and five_level_statistics_flag = #{fiveLevelStatisticsFlag}
        and five_level_classification = #{fiveLevelClassification}
        and is_del = 0
    </select>

    <!-- 新增保险资产风险五级分类状况表 -->
    <insert id="insertAssetRiskFiveLevel" parameterType="com.xl.alm.app.entity.AssetRiskFiveLevelEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_asset_risk_five_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag,</if>
            <if test="fiveLevelClassification != null and fiveLevelClassification != ''">five_level_classification,</if>
            <if test="bookBalance != null">book_balance,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">#{fiveLevelStatisticsFlag},</if>
            <if test="fiveLevelClassification != null and fiveLevelClassification != ''">#{fiveLevelClassification},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
        </trim>
    </insert>

    <!-- 修改保险资产风险五级分类状况表 -->
    <update id="updateAssetRiskFiveLevel" parameterType="com.xl.alm.app.entity.AssetRiskFiveLevelEntity">
        update t_acm_asset_risk_five_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag = #{fiveLevelStatisticsFlag},</if>
            <if test="fiveLevelClassification != null and fiveLevelClassification != ''">five_level_classification = #{fiveLevelClassification},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量删除保险资产风险五级分类状况表 -->
    <delete id="deleteAssetRiskFiveLevelByIds" parameterType="String">
        update t_acm_asset_risk_five_level set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除保险资产风险五级分类状况表信息 -->
    <delete id="deleteAssetRiskFiveLevelById" parameterType="Long">
        update t_acm_asset_risk_five_level set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量插入保险资产风险五级分类状况表数据 -->
    <insert id="batchInsertAssetRiskFiveLevel" parameterType="java.util.List">
        insert into t_acm_asset_risk_five_level
        (accounting_period, item_name, five_level_statistics_flag, five_level_classification, book_balance)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.fiveLevelStatisticsFlag}, #{item.fiveLevelClassification}, #{item.bookBalance})
        </foreach>
    </insert>

    <!-- 删除指定账期的保险资产风险五级分类状况表数据 -->
    <delete id="deleteAssetRiskFiveLevelByPeriod" parameterType="String">
        update t_acm_asset_risk_five_level set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
