<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.PaymentPeriodMappingMapper">

    <resultMap type="com.xl.alm.app.entity.PaymentPeriodMappingEntity" id="PaymentPeriodMappingEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="paymentPeriodCategory" column="payment_period_category"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectPaymentPeriodMappingEntityVo">
        select id, accounting_period, payment_period, payment_period_category, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_liab_payment_period_mapping
    </sql>

    <!-- 查询缴费年期映射列表 -->
    <select id="selectPaymentPeriodMappingEntityList" parameterType="com.xl.alm.app.query.PaymentPeriodMappingQuery" resultMap="PaymentPeriodMappingEntityResult">
        <include refid="selectPaymentPeriodMappingEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="paymentPeriod != null">and payment_period = #{paymentPeriod}</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">and payment_period_category like concat('%', #{paymentPeriodCategory}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, payment_period asc
    </select>

    <!-- 根据ID查询缴费年期映射 -->
    <select id="selectPaymentPeriodMappingEntityById" parameterType="Long" resultMap="PaymentPeriodMappingEntityResult">
        <include refid="selectPaymentPeriodMappingEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和缴费年期查询缴费年期映射 -->
    <select id="selectPaymentPeriodMappingEntityByCondition" resultMap="PaymentPeriodMappingEntityResult">
        <include refid="selectPaymentPeriodMappingEntityVo"/>
        where accounting_period = #{accountingPeriod} 
        and payment_period = #{paymentPeriod}
        and is_del = 0
    </select>

    <!-- 新增缴费年期映射 -->
    <insert id="insertPaymentPeriodMappingEntity" parameterType="com.xl.alm.app.entity.PaymentPeriodMappingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_payment_period_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="paymentPeriod != null">payment_period,</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">payment_period_category,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="paymentPeriod != null">#{paymentPeriod},</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">#{paymentPeriodCategory},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增缴费年期映射 -->
    <insert id="batchInsertPaymentPeriodMappingEntity" parameterType="java.util.List">
        insert into t_liab_payment_period_mapping(accounting_period, payment_period, payment_period_category, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.paymentPeriod}, #{item.paymentPeriodCategory}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改缴费年期映射 -->
    <update id="updatePaymentPeriodMappingEntity" parameterType="com.xl.alm.app.entity.PaymentPeriodMappingEntity">
        update t_liab_payment_period_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="paymentPeriod != null">payment_period = #{paymentPeriod},</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">payment_period_category = #{paymentPeriodCategory},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除缴费年期映射 -->
    <delete id="deletePaymentPeriodMappingEntityById" parameterType="Long">
        update t_liab_payment_period_mapping set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量删除缴费年期映射 -->
    <delete id="deletePaymentPeriodMappingEntityByIds" parameterType="String">
        update t_liab_payment_period_mapping set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
