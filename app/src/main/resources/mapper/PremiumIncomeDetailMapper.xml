<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.PremiumIncomeDetailMapper">

    <resultMap type="com.xl.alm.app.entity.PremiumIncomeDetailEntity" id="PremiumIncomeDetailEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="productCode" column="product_code"/>
        <result property="productName" column="product_name"/>
        <result property="channelCode" column="channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="accountCode" column="account_code"/>
        <result property="accountName" column="account_name"/>
        <result property="currentSinglePremium" column="current_single_premium"/>
        <result property="currentRegularPremium" column="current_regular_premium"/>
        <result property="currentRenewalPremium" column="current_renewal_premium"/>
        <result property="currentTotalPremium" column="current_total_premium"/>
        <result property="currentUlSingle" column="current_ul_single"/>
        <result property="currentUlRegular" column="current_ul_regular"/>
        <result property="currentUlRenewal" column="current_ul_renewal"/>
        <result property="currentUlInitialFee" column="current_ul_initial_fee"/>
        <result property="currentUlTotal" column="current_ul_total"/>
        <result property="currentScalePremium" column="current_scale_premium"/>
        <result property="currentInvestmentBalance" column="current_investment_balance"/>
        <result property="currentSurrender" column="current_surrender"/>
        <result property="currentUlWithdraw" column="current_ul_withdraw"/>
        <result property="currentClaim" column="current_claim"/>
        <result property="currentMedical" column="current_medical"/>
        <result property="currentMaturity" column="current_maturity"/>
        <result property="currentAnnuity" column="current_annuity"/>
        <result property="currentUlClaim" column="current_ul_claim"/>
        <result property="currentUlMedical" column="current_ul_medical"/>
        <result property="currentUlMaturity" column="current_ul_maturity"/>
        <result property="currentUlAnnuity" column="current_ul_annuity"/>
        <result property="currentTotalClaim" column="current_total_claim"/>
        <!-- 年累计字段映射 -->
        <result property="ytdSinglePremium" column="ytd_single_premium"/>
        <result property="ytdRegularPremium" column="ytd_regular_premium"/>
        <result property="ytdRenewalPremium" column="ytd_renewal_premium"/>
        <result property="ytdTotalPremium" column="ytd_total_premium"/>
        <result property="ytdUlSingle" column="ytd_ul_single"/>
        <result property="ytdUlRegular" column="ytd_ul_regular"/>
        <result property="ytdUlRenewal" column="ytd_ul_renewal"/>
        <result property="ytdUlInitialFee" column="ytd_ul_initial_fee"/>
        <result property="ytdUlTotal" column="ytd_ul_total"/>
        <result property="ytdScalePremium" column="ytd_scale_premium"/>
        <result property="ytdInvestmentBalance" column="ytd_investment_balance"/>
        <result property="ytdSurrender" column="ytd_surrender"/>
        <result property="ytdUlWithdraw" column="ytd_ul_withdraw"/>
        <result property="ytdClaim" column="ytd_claim"/>
        <result property="ytdMedical" column="ytd_medical"/>
        <result property="ytdMaturity" column="ytd_maturity"/>
        <result property="ytdAnnuity" column="ytd_annuity"/>
        <result property="ytdUlClaim" column="ytd_ul_claim"/>
        <result property="ytdUlMedical" column="ytd_ul_medical"/>
        <result property="ytdUlMaturity" column="ytd_ul_maturity"/>
        <result property="ytdUlAnnuity" column="ytd_ul_annuity"/>
        <result property="ytdTotalClaim" column="ytd_total_claim"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectPremiumIncomeDetailVo">
        select id, accounting_period, company_code, company_name, product_code, product_name,
               channel_code, channel_name, account_code, account_name, current_single_premium,
               current_regular_premium, current_renewal_premium, current_total_premium,
               current_ul_single, current_ul_regular, current_ul_renewal, current_ul_initial_fee,
               current_ul_total, current_scale_premium, current_investment_balance,
               current_surrender, current_ul_withdraw, current_claim, current_medical,
               current_maturity, current_annuity, current_ul_claim, current_ul_medical,
               current_ul_maturity, current_ul_annuity, current_total_claim,
               ytd_single_premium, ytd_regular_premium, ytd_renewal_premium, ytd_total_premium,
               ytd_ul_single, ytd_ul_regular, ytd_ul_renewal, ytd_ul_initial_fee, ytd_ul_total,
               ytd_scale_premium, ytd_investment_balance, ytd_surrender, ytd_ul_withdraw,
               ytd_claim, ytd_medical, ytd_maturity, ytd_annuity, ytd_ul_claim, ytd_ul_medical,
               ytd_ul_maturity, ytd_ul_annuity, ytd_total_claim, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_base_premium_income_detail
    </sql>

    <select id="selectPremiumIncomeDetailEntityList" parameterType="com.xl.alm.app.query.PremiumIncomeDetailQuery" resultMap="PremiumIncomeDetailEntityResult">
        <include refid="selectPremiumIncomeDetailVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="companyCode != null and companyCode != ''">and company_code = #{companyCode}</if>
            <if test="companyName != null and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="productCode != null and productCode != ''">and product_code = #{productCode}</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="channelCode != null and channelCode != ''">and channel_code = #{channelCode}</if>
            <if test="channelName != null and channelName != ''">and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="accountCode != null and accountCode != ''">and account_code = #{accountCode}</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <select id="selectPremiumIncomeDetailEntityById" parameterType="Long" resultMap="PremiumIncomeDetailEntityResult">
        <include refid="selectPremiumIncomeDetailVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertPremiumIncomeDetailEntity" parameterType="com.xl.alm.app.entity.PremiumIncomeDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_premium_income_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="productCode != null and productCode != ''">product_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="channelCode != null and channelCode != ''">channel_code,</if>
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="accountCode != null and accountCode != ''">account_code,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="currentSinglePremium != null">current_single_premium,</if>
            <if test="currentRegularPremium != null">current_regular_premium,</if>
            <if test="currentRenewalPremium != null">current_renewal_premium,</if>
            <if test="currentTotalPremium != null">current_total_premium,</if>
            <if test="currentUlSingle != null">current_ul_single,</if>
            <if test="currentUlRegular != null">current_ul_regular,</if>
            <if test="currentUlRenewal != null">current_ul_renewal,</if>
            <if test="currentUlInitialFee != null">current_ul_initial_fee,</if>
            <if test="currentUlTotal != null">current_ul_total,</if>
            <if test="currentScalePremium != null">current_scale_premium,</if>
            <if test="currentInvestmentBalance != null">current_investment_balance,</if>
            <if test="currentSurrender != null">current_surrender,</if>
            <if test="currentUlWithdraw != null">current_ul_withdraw,</if>
            <if test="currentClaim != null">current_claim,</if>
            <if test="currentMedical != null">current_medical,</if>
            <if test="currentMaturity != null">current_maturity,</if>
            <if test="currentAnnuity != null">current_annuity,</if>
            <if test="currentUlClaim != null">current_ul_claim,</if>
            <if test="currentUlMedical != null">current_ul_medical,</if>
            <if test="currentUlMaturity != null">current_ul_maturity,</if>
            <if test="currentUlAnnuity != null">current_ul_annuity,</if>
            <if test="currentTotalClaim != null">current_total_claim,</if>
            <if test="ytdSinglePremium != null">ytd_single_premium,</if>
            <if test="ytdRegularPremium != null">ytd_regular_premium,</if>
            <if test="ytdRenewalPremium != null">ytd_renewal_premium,</if>
            <if test="ytdTotalPremium != null">ytd_total_premium,</if>
            <if test="ytdUlSingle != null">ytd_ul_single,</if>
            <if test="ytdUlRegular != null">ytd_ul_regular,</if>
            <if test="ytdUlRenewal != null">ytd_ul_renewal,</if>
            <if test="ytdUlInitialFee != null">ytd_ul_initial_fee,</if>
            <if test="ytdUlTotal != null">ytd_ul_total,</if>
            <if test="ytdScalePremium != null">ytd_scale_premium,</if>
            <if test="ytdInvestmentBalance != null">ytd_investment_balance,</if>
            <if test="ytdSurrender != null">ytd_surrender,</if>
            <if test="ytdUlWithdraw != null">ytd_ul_withdraw,</if>
            <if test="ytdClaim != null">ytd_claim,</if>
            <if test="ytdMedical != null">ytd_medical,</if>
            <if test="ytdMaturity != null">ytd_maturity,</if>
            <if test="ytdAnnuity != null">ytd_annuity,</if>
            <if test="ytdUlClaim != null">ytd_ul_claim,</if>
            <if test="ytdUlMedical != null">ytd_ul_medical,</if>
            <if test="ytdUlMaturity != null">ytd_ul_maturity,</if>
            <if test="ytdUlAnnuity != null">ytd_ul_annuity,</if>
            <if test="ytdTotalClaim != null">ytd_total_claim,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            is_del
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="productCode != null and productCode != ''">#{productCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="accountCode != null and accountCode != ''">#{accountCode},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="currentSinglePremium != null">#{currentSinglePremium},</if>
            <if test="currentRegularPremium != null">#{currentRegularPremium},</if>
            <if test="currentRenewalPremium != null">#{currentRenewalPremium},</if>
            <if test="currentTotalPremium != null">#{currentTotalPremium},</if>
            <if test="currentUlSingle != null">#{currentUlSingle},</if>
            <if test="currentUlRegular != null">#{currentUlRegular},</if>
            <if test="currentUlRenewal != null">#{currentUlRenewal},</if>
            <if test="currentUlInitialFee != null">#{currentUlInitialFee},</if>
            <if test="currentUlTotal != null">#{currentUlTotal},</if>
            <if test="currentScalePremium != null">#{currentScalePremium},</if>
            <if test="currentInvestmentBalance != null">#{currentInvestmentBalance},</if>
            <if test="currentSurrender != null">#{currentSurrender},</if>
            <if test="currentUlWithdraw != null">#{currentUlWithdraw},</if>
            <if test="currentClaim != null">#{currentClaim},</if>
            <if test="currentMedical != null">#{currentMedical},</if>
            <if test="currentMaturity != null">#{currentMaturity},</if>
            <if test="currentAnnuity != null">#{currentAnnuity},</if>
            <if test="currentUlClaim != null">#{currentUlClaim},</if>
            <if test="currentUlMedical != null">#{currentUlMedical},</if>
            <if test="currentUlMaturity != null">#{currentUlMaturity},</if>
            <if test="currentUlAnnuity != null">#{currentUlAnnuity},</if>
            <if test="currentTotalClaim != null">#{currentTotalClaim},</if>
            <if test="ytdSinglePremium != null">#{ytdSinglePremium},</if>
            <if test="ytdRegularPremium != null">#{ytdRegularPremium},</if>
            <if test="ytdRenewalPremium != null">#{ytdRenewalPremium},</if>
            <if test="ytdTotalPremium != null">#{ytdTotalPremium},</if>
            <if test="ytdUlSingle != null">#{ytdUlSingle},</if>
            <if test="ytdUlRegular != null">#{ytdUlRegular},</if>
            <if test="ytdUlRenewal != null">#{ytdUlRenewal},</if>
            <if test="ytdUlInitialFee != null">#{ytdUlInitialFee},</if>
            <if test="ytdUlTotal != null">#{ytdUlTotal},</if>
            <if test="ytdScalePremium != null">#{ytdScalePremium},</if>
            <if test="ytdInvestmentBalance != null">#{ytdInvestmentBalance},</if>
            <if test="ytdSurrender != null">#{ytdSurrender},</if>
            <if test="ytdUlWithdraw != null">#{ytdUlWithdraw},</if>
            <if test="ytdClaim != null">#{ytdClaim},</if>
            <if test="ytdMedical != null">#{ytdMedical},</if>
            <if test="ytdMaturity != null">#{ytdMaturity},</if>
            <if test="ytdAnnuity != null">#{ytdAnnuity},</if>
            <if test="ytdUlClaim != null">#{ytdUlClaim},</if>
            <if test="ytdUlMedical != null">#{ytdUlMedical},</if>
            <if test="ytdUlMaturity != null">#{ytdUlMaturity},</if>
            <if test="ytdUlAnnuity != null">#{ytdUlAnnuity},</if>
            <if test="ytdTotalClaim != null">#{ytdTotalClaim},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            now(),
            0
        </trim>
    </insert>

    <insert id="batchInsertPremiumIncomeDetailEntity" parameterType="java.util.List">
        insert into t_base_premium_income_detail (
            accounting_period, company_code, company_name, product_code, product_name,
            channel_code, channel_name, account_code, account_name, current_single_premium,
            current_regular_premium, current_renewal_premium, current_total_premium,
            current_ul_single, current_ul_regular, current_ul_renewal, current_ul_initial_fee,
            current_ul_total, current_scale_premium, current_investment_balance,
            current_surrender, current_ul_withdraw, current_claim, current_medical,
            current_maturity, current_annuity, current_ul_claim, current_ul_medical,
            current_ul_maturity, current_ul_annuity, current_total_claim,
            ytd_single_premium, ytd_regular_premium, ytd_renewal_premium, ytd_total_premium,
            ytd_ul_single, ytd_ul_regular, ytd_ul_renewal, ytd_ul_initial_fee, ytd_ul_total,
            ytd_scale_premium, ytd_investment_balance, ytd_surrender, ytd_ul_withdraw,
            ytd_claim, ytd_medical, ytd_maturity, ytd_annuity, ytd_ul_claim, ytd_ul_medical,
            ytd_ul_maturity, ytd_ul_annuity, ytd_total_claim, remark,
            create_by, create_time, is_del
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountingPeriod}, #{item.companyCode}, #{item.companyName},
                #{item.productCode}, #{item.productName}, #{item.channelCode},
                #{item.channelName}, #{item.accountCode}, #{item.accountName},
                #{item.currentSinglePremium}, #{item.currentRegularPremium},
                #{item.currentRenewalPremium}, #{item.currentTotalPremium},
                #{item.currentUlSingle}, #{item.currentUlRegular}, #{item.currentUlRenewal},
                #{item.currentUlInitialFee}, #{item.currentUlTotal}, #{item.currentScalePremium},
                #{item.currentInvestmentBalance}, #{item.currentSurrender}, #{item.currentUlWithdraw},
                #{item.currentClaim}, #{item.currentMedical}, #{item.currentMaturity},
                #{item.currentAnnuity}, #{item.currentUlClaim}, #{item.currentUlMedical},
                #{item.currentUlMaturity}, #{item.currentUlAnnuity}, #{item.currentTotalClaim},
                #{item.ytdSinglePremium}, #{item.ytdRegularPremium}, #{item.ytdRenewalPremium},
                #{item.ytdTotalPremium}, #{item.ytdUlSingle}, #{item.ytdUlRegular},
                #{item.ytdUlRenewal}, #{item.ytdUlInitialFee}, #{item.ytdUlTotal},
                #{item.ytdScalePremium}, #{item.ytdInvestmentBalance}, #{item.ytdSurrender},
                #{item.ytdUlWithdraw}, #{item.ytdClaim}, #{item.ytdMedical}, #{item.ytdMaturity},
                #{item.ytdAnnuity}, #{item.ytdUlClaim}, #{item.ytdUlMedical}, #{item.ytdUlMaturity},
                #{item.ytdUlAnnuity}, #{item.ytdTotalClaim}, #{item.remark}, #{item.createBy}, now(), 0
            )
        </foreach>
    </insert>

    <update id="updatePremiumIncomeDetailEntity" parameterType="com.xl.alm.app.entity.PremiumIncomeDetailEntity">
        update t_base_premium_income_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="productCode != null and productCode != ''">product_code = #{productCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="accountCode != null and accountCode != ''">account_code = #{accountCode},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="currentSinglePremium != null">current_single_premium = #{currentSinglePremium},</if>
            <if test="currentRegularPremium != null">current_regular_premium = #{currentRegularPremium},</if>
            <if test="currentRenewalPremium != null">current_renewal_premium = #{currentRenewalPremium},</if>
            <if test="currentTotalPremium != null">current_total_premium = #{currentTotalPremium},</if>
            <if test="currentUlSingle != null">current_ul_single = #{currentUlSingle},</if>
            <if test="currentUlRegular != null">current_ul_regular = #{currentUlRegular},</if>
            <if test="currentUlRenewal != null">current_ul_renewal = #{currentUlRenewal},</if>
            <if test="currentUlInitialFee != null">current_ul_initial_fee = #{currentUlInitialFee},</if>
            <if test="currentUlTotal != null">current_ul_total = #{currentUlTotal},</if>
            <if test="currentScalePremium != null">current_scale_premium = #{currentScalePremium},</if>
            <if test="currentInvestmentBalance != null">current_investment_balance = #{currentInvestmentBalance},</if>
            <if test="currentSurrender != null">current_surrender = #{currentSurrender},</if>
            <if test="currentUlWithdraw != null">current_ul_withdraw = #{currentUlWithdraw},</if>
            <if test="currentClaim != null">current_claim = #{currentClaim},</if>
            <if test="currentMedical != null">current_medical = #{currentMedical},</if>
            <if test="currentMaturity != null">current_maturity = #{currentMaturity},</if>
            <if test="currentAnnuity != null">current_annuity = #{currentAnnuity},</if>
            <if test="currentUlClaim != null">current_ul_claim = #{currentUlClaim},</if>
            <if test="currentUlMedical != null">current_ul_medical = #{currentUlMedical},</if>
            <if test="currentUlMaturity != null">current_ul_maturity = #{currentUlMaturity},</if>
            <if test="currentUlAnnuity != null">current_ul_annuity = #{currentUlAnnuity},</if>
            <if test="currentTotalClaim != null">current_total_claim = #{currentTotalClaim},</if>
            <if test="ytdSinglePremium != null">ytd_single_premium = #{ytdSinglePremium},</if>
            <if test="ytdRegularPremium != null">ytd_regular_premium = #{ytdRegularPremium},</if>
            <if test="ytdRenewalPremium != null">ytd_renewal_premium = #{ytdRenewalPremium},</if>
            <if test="ytdTotalPremium != null">ytd_total_premium = #{ytdTotalPremium},</if>
            <if test="ytdUlSingle != null">ytd_ul_single = #{ytdUlSingle},</if>
            <if test="ytdUlRegular != null">ytd_ul_regular = #{ytdUlRegular},</if>
            <if test="ytdUlRenewal != null">ytd_ul_renewal = #{ytdUlRenewal},</if>
            <if test="ytdUlInitialFee != null">ytd_ul_initial_fee = #{ytdUlInitialFee},</if>
            <if test="ytdUlTotal != null">ytd_ul_total = #{ytdUlTotal},</if>
            <if test="ytdScalePremium != null">ytd_scale_premium = #{ytdScalePremium},</if>
            <if test="ytdInvestmentBalance != null">ytd_investment_balance = #{ytdInvestmentBalance},</if>
            <if test="ytdSurrender != null">ytd_surrender = #{ytdSurrender},</if>
            <if test="ytdUlWithdraw != null">ytd_ul_withdraw = #{ytdUlWithdraw},</if>
            <if test="ytdClaim != null">ytd_claim = #{ytdClaim},</if>
            <if test="ytdMedical != null">ytd_medical = #{ytdMedical},</if>
            <if test="ytdMaturity != null">ytd_maturity = #{ytdMaturity},</if>
            <if test="ytdAnnuity != null">ytd_annuity = #{ytdAnnuity},</if>
            <if test="ytdUlClaim != null">ytd_ul_claim = #{ytdUlClaim},</if>
            <if test="ytdUlMedical != null">ytd_ul_medical = #{ytdUlMedical},</if>
            <if test="ytdUlMaturity != null">ytd_ul_maturity = #{ytdUlMaturity},</if>
            <if test="ytdUlAnnuity != null">ytd_ul_annuity = #{ytdUlAnnuity},</if>
            <if test="ytdTotalClaim != null">ytd_total_claim = #{ytdTotalClaim},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePremiumIncomeDetailEntityById" parameterType="Long">
        update t_base_premium_income_detail set is_del = 1 where id = #{id}
    </delete>

    <delete id="deletePremiumIncomeDetailEntityByIds" parameterType="String">
        update t_base_premium_income_detail set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePremiumIncomeDetailEntityByPeriod" parameterType="String">
        update t_base_premium_income_detail set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
