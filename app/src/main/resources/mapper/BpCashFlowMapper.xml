<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.BpCashFlowMapper">

    <resultMap type="com.xl.alm.app.entity.BpCashFlowEntity" id="BpCashFlowEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="scenarioName" column="scenario_name"/>
        <result property="businessType" column="business_type"/>
        <result property="actuarialCode" column="actuarial_code"/>
        <result property="businessCode" column="business_code"/>
        <result property="productName" column="product_name"/>
        <result property="designType" column="design_type"/>
        <result property="variableList" column="variable_list"/>
        <result property="variableName" column="variable_name"/>
        <result property="cashFlowValueSet" column="cash_flow_value_set"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectBpCashFlowEntityVo">
        select id, accounting_period, scenario_name, business_type, actuarial_code, business_code, 
               product_name, design_type, variable_list, variable_name, cash_flow_value_set, 
               create_time, create_by, update_time, update_by, is_del
        from t_cft_bp_cash_flow
    </sql>

    <!-- 查询BP现金流量表列表 -->
    <select id="selectBpCashFlowEntityList" parameterType="com.xl.alm.app.query.BpCashFlowQuery" resultMap="BpCashFlowEntityResult">
        <include refid="selectBpCashFlowEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="scenarioName != null and scenarioName != ''">and scenario_name = #{scenarioName}</if>
            <if test="businessType != null and businessType != ''">and business_type = #{businessType}</if>
            <if test="actuarialCode != null and actuarialCode != ''">and actuarial_code = #{actuarialCode}</if>
            <if test="businessCode != null and businessCode != ''">and business_code = #{businessCode}</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            <if test="variableList != null and variableList != ''">and variable_list = #{variableList}</if>
            <if test="variableName != null and variableName != ''">and variable_name like concat('%', #{variableName}, '%')</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 根据主键查询BP现金流量表 -->
    <select id="selectBpCashFlowEntityById" parameterType="Long" resultMap="BpCashFlowEntityResult">
        <include refid="selectBpCashFlowEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询BP现金流量表（用于验证唯一性） -->
    <select id="selectBpCashFlowEntityByCondition" resultMap="BpCashFlowEntityResult">
        <include refid="selectBpCashFlowEntityVo"/>
        where accounting_period = #{accountingPeriod}
          and scenario_name = #{scenarioName}
          and business_type = #{businessType}
          and actuarial_code = #{actuarialCode}
          and variable_list = #{variableList}
          and is_del = 0
    </select>

    <!-- 新增BP现金流量表 -->
    <insert id="insertBpCashFlowEntity" parameterType="com.xl.alm.app.entity.BpCashFlowEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_bp_cash_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code,</if>
            <if test="businessCode != null and businessCode != ''">business_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="designType != null and designType != ''">design_type,</if>
            <if test="variableList != null and variableList != ''">variable_list,</if>
            <if test="variableName != null and variableName != ''">variable_name,</if>
            <if test="cashFlowValueSet != null">cash_flow_value_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">#{scenarioName},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="actuarialCode != null and actuarialCode != ''">#{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">#{businessCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="designType != null and designType != ''">#{designType},</if>
            <if test="variableList != null and variableList != ''">#{variableList},</if>
            <if test="variableName != null and variableName != ''">#{variableName},</if>
            <if test="cashFlowValueSet != null">#{cashFlowValueSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 修改BP现金流量表 -->
    <update id="updateBpCashFlowEntity" parameterType="com.xl.alm.app.entity.BpCashFlowEntity">
        update t_cft_bp_cash_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name = #{scenarioName},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="actuarialCode != null and actuarialCode != ''">actuarial_code = #{actuarialCode},</if>
            <if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="variableList != null and variableList != ''">variable_list = #{variableList},</if>
            <if test="variableName != null and variableName != ''">variable_name = #{variableName},</if>
            <if test="cashFlowValueSet != null">cash_flow_value_set = #{cashFlowValueSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <!-- 删除BP现金流量表 -->
    <update id="deleteBpCashFlowEntityById" parameterType="Long">
        update t_cft_bp_cash_flow set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除BP现金流量表 -->
    <update id="deleteBpCashFlowEntityByIds" parameterType="String">
        update t_cft_bp_cash_flow set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入BP现金流量表 -->
    <insert id="batchInsertBpCashFlowEntity" parameterType="java.util.List">
        insert into t_cft_bp_cash_flow
        (accounting_period, scenario_name, business_type, actuarial_code, business_code,
         product_name, design_type, variable_list, variable_name, cash_flow_value_set,
         create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.scenarioName}, #{item.businessType}, #{item.actuarialCode},
             #{item.businessCode}, #{item.productName}, #{item.designType}, #{item.variableList},
             #{item.variableName}, #{item.cashFlowValueSet}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 批量更新BP现金流量表 -->
    <update id="batchUpdateBpCashFlowEntity" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update t_cft_bp_cash_flow
            set scenario_name = #{item.scenarioName},
                business_type = #{item.businessType},
                business_code = #{item.businessCode},
                product_name = #{item.productName},
                design_type = #{item.designType},
                variable_name = #{item.variableName},
                cash_flow_value_set = #{item.cashFlowValueSet},
                update_by = #{item.updateBy},
                update_time = now()
            where id = #{item.id}
        </foreach>
    </update>

</mapper>
