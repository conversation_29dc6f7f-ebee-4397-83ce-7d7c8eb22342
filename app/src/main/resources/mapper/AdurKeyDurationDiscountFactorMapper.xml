<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AdurKeyDurationDiscountFactorMapper">
    
    <resultMap type="com.xl.alm.app.entity.AdurKeyDurationDiscountFactorEntity" id="AdurKeyDurationDiscountFactorResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="durationType"    column="duration_type"    />
        <result property="basisPointType"    column="basis_point_type"    />
        <result property="keyTerm"    column="key_term"    />
        <result property="stressDirection"    column="stress_direction"    />
        <result property="dateType"    column="date_type"    />
        <result property="date"    column="date"    />
        <result property="spreadType"    column="spread_type"    />
        <result property="spread"    column="spread"    />
        <result property="curveSubCategory"    column="curve_sub_category"    />
        <result property="assetNumber"    column="asset_number"    />
        <result property="accountName"    column="account_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="curveId"    column="curve_id"    />
        <result property="issueSpread"    column="issue_spread"    />
        <result property="keyDurationDiscountFactorSet"    column="key_duration_discount_factor_set"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectAdurKeyDurationDiscountFactorVo">
        select id, account_period, duration_type, basis_point_type, key_term, stress_direction, date_type, date,
               spread_type, spread, curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id, issue_spread,
               key_duration_discount_factor_set, create_by, create_time, update_by, update_time, is_del
        from t_adur_key_duration_discount_factor
    </sql>

    <select id="selectAdurKeyDurationDiscountFactorEntityList" parameterType="com.xl.alm.app.query.AdurKeyDurationDiscountFactorQuery" resultMap="AdurKeyDurationDiscountFactorResult">
        <include refid="selectAdurKeyDurationDiscountFactorVo"/>
        <where>
            is_del = 0
            <if test="accountPeriod != null and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="durationType != null and durationType != ''"> and duration_type = #{durationType}</if>
            <if test="basisPointType != null and basisPointType != ''"> and basis_point_type = #{basisPointType}</if>
            <if test="keyTerm != null and keyTerm != ''"> and key_term = #{keyTerm}</if>
            <if test="stressDirection != null and stressDirection != ''"> and stress_direction = #{stressDirection}</if>
            <if test="dateType != null and dateType != ''"> and date_type = #{dateType}</if>
            <if test="date != null"> and date = #{date}</if>
            <if test="spreadType != null and spreadType != ''"> and spread_type = #{spreadType}</if>
            <if test="curveSubCategory != null and curveSubCategory != ''"> and curve_sub_category = #{curveSubCategory}</if>
            <if test="assetNumber != null and assetNumber != ''"> and asset_number = #{assetNumber}</if>
            <if test="accountName != null and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null and securityCode != ''"> and security_code = #{securityCode}</if>
            <if test="curveId != null and curveId != ''"> and curve_id = #{curveId}</if>
        </where>
        order by account_period desc, asset_number, key_term, stress_direction
    </select>

    <select id="selectAdurKeyDurationDiscountFactorEntityById" parameterType="Long" resultMap="AdurKeyDurationDiscountFactorResult">
        <include refid="selectAdurKeyDurationDiscountFactorVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectAdurKeyDurationDiscountFactorEntityByCondition" resultMap="AdurKeyDurationDiscountFactorResult">
        <include refid="selectAdurKeyDurationDiscountFactorVo"/>
        where account_period = #{accountPeriod} and asset_number = #{assetNumber} 
              and key_term = #{keyTerm} and stress_direction = #{stressDirection} and is_del = 0
    </select>
        
    <insert id="insertAdurKeyDurationDiscountFactorEntity" parameterType="com.xl.alm.app.entity.AdurKeyDurationDiscountFactorEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_key_duration_discount_factor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null">account_period,</if>
            <if test="durationType != null">duration_type,</if>
            <if test="basisPointType != null">basis_point_type,</if>
            <if test="keyTerm != null">key_term,</if>
            <if test="stressDirection != null">stress_direction,</if>
            <if test="dateType != null">date_type,</if>
            <if test="date != null">date,</if>
            <if test="spreadType != null">spread_type,</if>
            <if test="spread != null">spread,</if>
            <if test="curveSubCategory != null">curve_sub_category,</if>
            <if test="assetNumber != null">asset_number,</if>
            <if test="accountName != null">account_name,</if>
            <if test="assetName != null">asset_name,</if>
            <if test="securityCode != null">security_code,</if>
            <if test="curveId != null">curve_id,</if>
            <if test="issueSpread != null">issue_spread,</if>
            <if test="keyDurationDiscountFactorSet != null">key_duration_discount_factor_set,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null">#{accountPeriod},</if>
            <if test="durationType != null">#{durationType},</if>
            <if test="basisPointType != null">#{basisPointType},</if>
            <if test="keyTerm != null">#{keyTerm},</if>
            <if test="stressDirection != null">#{stressDirection},</if>
            <if test="dateType != null">#{dateType},</if>
            <if test="date != null">#{date},</if>
            <if test="spreadType != null">#{spreadType},</if>
            <if test="spread != null">#{spread},</if>
            <if test="curveSubCategory != null">#{curveSubCategory},</if>
            <if test="assetNumber != null">#{assetNumber},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="securityCode != null">#{securityCode},</if>
            <if test="curveId != null">#{curveId},</if>
            <if test="issueSpread != null">#{issueSpread},</if>
            <if test="keyDurationDiscountFactorSet != null">#{keyDurationDiscountFactorSet},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <insert id="batchInsertAdurKeyDurationDiscountFactorEntity" parameterType="java.util.List">
        insert into t_adur_key_duration_discount_factor(account_period, duration_type, basis_point_type, key_term, stress_direction,
                                                        date_type, date, spread_type, spread, curve_sub_category, asset_number,
                                                        account_name, asset_name, security_code, curve_id, issue_spread, key_duration_discount_factor_set,
                                                        create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountPeriod}, #{item.durationType}, #{item.basisPointType}, #{item.keyTerm}, #{item.stressDirection},
             #{item.dateType}, #{item.date}, #{item.spreadType}, #{item.spread}, #{item.curveSubCategory}, #{item.assetNumber},
             #{item.accountName}, #{item.assetName}, #{item.securityCode}, #{item.curveId}, #{item.issueSpread}, #{item.keyDurationDiscountFactorSet},
             #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <update id="updateAdurKeyDurationDiscountFactorEntity" parameterType="com.xl.alm.app.entity.AdurKeyDurationDiscountFactorEntity">
        update t_adur_key_duration_discount_factor
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null">account_period = #{accountPeriod},</if>
            <if test="durationType != null">duration_type = #{durationType},</if>
            <if test="basisPointType != null">basis_point_type = #{basisPointType},</if>
            <if test="keyTerm != null">key_term = #{keyTerm},</if>
            <if test="stressDirection != null">stress_direction = #{stressDirection},</if>
            <if test="dateType != null">date_type = #{dateType},</if>
            <if test="date != null">date = #{date},</if>
            <if test="spreadType != null">spread_type = #{spreadType},</if>
            <if test="spread != null">spread = #{spread},</if>
            <if test="curveSubCategory != null">curve_sub_category = #{curveSubCategory},</if>
            <if test="assetNumber != null">asset_number = #{assetNumber},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="assetName != null">asset_name = #{assetName},</if>
            <if test="securityCode != null">security_code = #{securityCode},</if>
            <if test="curveId != null">curve_id = #{curveId},</if>
            <if test="issueSpread != null">issue_spread = #{issueSpread},</if>
            <if test="keyDurationDiscountFactorSet != null">key_duration_discount_factor_set = #{keyDurationDiscountFactorSet},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdurKeyDurationDiscountFactorEntityById" parameterType="Long">
        update t_adur_key_duration_discount_factor set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAdurKeyDurationDiscountFactorEntityByIds" parameterType="String">
        update t_adur_key_duration_discount_factor set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAdurKeyDurationDiscountFactorEntityByAccountPeriod" parameterType="String">
        delete from t_adur_key_duration_discount_factor where account_period = #{accountPeriod}
    </delete>

    <delete id="truncateAdurKeyDurationDiscountFactorEntity">
        truncate table t_adur_key_duration_discount_factor
    </delete>

</mapper>
