<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.CreditRatingMapMapper">

    <resultMap type="com.xl.alm.app.entity.CreditRatingMapEntity" id="CreditRatingMapResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="creditRating" column="credit_rating"/>
        <result property="creditRatingTableUsed" column="credit_rating_table_used"/>
        <result property="discountCurveRating" column="discount_curve_rating"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectCreditRatingMapVo">
        select id, accounting_period, credit_rating, credit_rating_table_used, discount_curve_rating,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_credit_rating_map
    </sql>

    <select id="selectCreditRatingMapEntityList" parameterType="com.xl.alm.app.query.CreditRatingMapQuery" resultMap="CreditRatingMapResult">
        <include refid="selectCreditRatingMapVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="creditRating != null and creditRating != ''">
                and credit_rating = #{creditRating}
            </if>
            <if test="creditRatingTableUsed != null and creditRatingTableUsed != ''">
                and credit_rating_table_used = #{creditRatingTableUsed}
            </if>
            <if test="discountCurveRating != null and discountCurveRating != ''">
                and discount_curve_rating = #{discountCurveRating}
            </if>
        </where>
        order by accounting_period desc, credit_rating
    </select>

    <select id="selectCreditRatingMapEntityById" parameterType="Long" resultMap="CreditRatingMapResult">
        <include refid="selectCreditRatingMapVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertCreditRatingMapEntity" parameterType="com.xl.alm.app.entity.CreditRatingMapEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_credit_rating_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="creditRating != null and creditRating != ''">credit_rating,</if>
            <if test="creditRatingTableUsed != null and creditRatingTableUsed != ''">credit_rating_table_used,</if>
            <if test="discountCurveRating != null and discountCurveRating != ''">discount_curve_rating,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="creditRating != null and creditRating != ''">#{creditRating},</if>
            <if test="creditRatingTableUsed != null and creditRatingTableUsed != ''">#{creditRatingTableUsed},</if>
            <if test="discountCurveRating != null and discountCurveRating != ''">#{discountCurveRating},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateCreditRatingMapEntity" parameterType="com.xl.alm.app.entity.CreditRatingMapEntity">
        update t_ast_credit_rating_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="creditRating != null and creditRating != ''">credit_rating = #{creditRating},</if>
            <if test="creditRatingTableUsed != null and creditRatingTableUsed != ''">credit_rating_table_used = #{creditRatingTableUsed},</if>
            <if test="discountCurveRating != null and discountCurveRating != ''">discount_curve_rating = #{discountCurveRating},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCreditRatingMapEntityById" parameterType="Long">
        update t_ast_credit_rating_map set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteCreditRatingMapEntityByIds" parameterType="String">
        update t_ast_credit_rating_map set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertCreditRatingMapEntity" parameterType="java.util.List">
        insert into t_ast_credit_rating_map (accounting_period, credit_rating, credit_rating_table_used, discount_curve_rating,
                                           create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.creditRating}, #{item.creditRatingTableUsed}, #{item.discountCurveRating},
             #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

    <delete id="deleteCreditRatingMapEntityByPeriod" parameterType="String">
        update t_ast_credit_rating_map set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_credit_rating_map
        where accounting_period = #{accountingPeriod}
          and credit_rating = #{creditRating}
          and is_del = 0
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

</mapper>
