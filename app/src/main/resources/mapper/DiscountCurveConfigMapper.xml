<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.DiscountCurveConfigMapper">

    <resultMap type="com.xl.alm.app.entity.DiscountCurveConfigEntity" id="DiscountCurveConfigResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="discountCurveRating" column="discount_curve_rating"/>
        <result property="discountCurveFlag" column="discount_curve_flag"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectDiscountCurveConfigVo">
        select id, accounting_period, asset_sub_sub_category, discount_curve_rating, discount_curve_flag, remark, create_time, create_by, update_time, update_by, is_del from t_ast_discount_curve_config
    </sql>

    <select id="selectDiscountCurveConfigEntityList" parameterType="com.xl.alm.app.query.DiscountCurveConfigQuery" resultMap="DiscountCurveConfigResult">
        <include refid="selectDiscountCurveConfigVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="discountCurveRating != null and discountCurveRating != ''">
                and discount_curve_rating = #{discountCurveRating}
            </if>
            <if test="discountCurveFlag != null">
                and discount_curve_flag = #{discountCurveFlag}
            </if>
            <if test="remark != null and remark != ''">
                and remark like concat('%', #{remark}, '%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectDiscountCurveConfigEntityById" parameterType="Long" resultMap="DiscountCurveConfigResult">
        <include refid="selectDiscountCurveConfigVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertDiscountCurveConfigEntity" parameterType="com.xl.alm.app.entity.DiscountCurveConfigEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_discount_curve_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="discountCurveRating != null">discount_curve_rating,</if>
            <if test="discountCurveFlag != null">discount_curve_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="discountCurveRating != null">#{discountCurveRating},</if>
            <if test="discountCurveFlag != null">#{discountCurveFlag},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateDiscountCurveConfigEntity" parameterType="com.xl.alm.app.entity.DiscountCurveConfigEntity">
        update t_ast_discount_curve_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="discountCurveRating != null">discount_curve_rating = #{discountCurveRating},</if>
            <if test="discountCurveFlag != null">discount_curve_flag = #{discountCurveFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiscountCurveConfigEntityById" parameterType="Long">
        update t_ast_discount_curve_config set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteDiscountCurveConfigEntityByIds" parameterType="String">
        update t_ast_discount_curve_config set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDiscountCurveConfigEntityByPeriod" parameterType="String">
        update t_ast_discount_curve_config set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

    <insert id="batchInsertDiscountCurveConfigEntity" parameterType="java.util.List">
        insert into t_ast_discount_curve_config(accounting_period, asset_sub_sub_category, discount_curve_rating, discount_curve_flag, remark, create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.assetSubSubCategory}, #{item.discountCurveRating}, #{item.discountCurveFlag}, #{item.remark}, #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_discount_curve_config
        where accounting_period = #{accountingPeriod}
        and asset_sub_sub_category = #{assetSubSubCategory}
        and (discount_curve_rating = #{discountCurveRating} or (discount_curve_rating is null and #{discountCurveRating} is null))
        and is_del = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
