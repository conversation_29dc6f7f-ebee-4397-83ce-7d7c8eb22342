<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.DepositInterbankCdMapper">

    <resultMap type="com.xl.alm.app.entity.DepositInterbankCdEntity" id="DepositInterbankCdEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="bankClassification" column="bank_classification"/>
        <result property="bookValue" column="book_value"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectDepositInterbankCdVo">
        select id, accounting_period, asset_sub_sub_category, bank_classification, book_value, create_by, create_time, update_by, update_time, is_del from t_acm_deposit_interbank_cd
    </sql>

    <!-- 查询存款及同业存单表列表 -->
    <select id="selectDepositInterbankCdList" parameterType="com.xl.alm.app.query.DepositInterbankCdQuery" resultMap="DepositInterbankCdEntityResult">
        <include refid="selectDepositInterbankCdVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">and asset_sub_sub_category = #{assetSubSubCategory}</if>
            <if test="bankClassification != null and bankClassification != ''">and bank_classification = #{bankClassification}</if>
            <if test="bookValue != null">and book_value = #{bookValue}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询存款及同业存单表 -->
    <select id="selectDepositInterbankCdById" parameterType="Long" resultMap="DepositInterbankCdEntityResult">
        <include refid="selectDepositInterbankCdVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、资产小小类和银行分类查询存款及同业存单表 -->
    <select id="selectDepositInterbankCdByCondition" resultMap="DepositInterbankCdEntityResult">
        <include refid="selectDepositInterbankCdVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">and asset_sub_sub_category = #{assetSubSubCategory}</if>
            <if test="bankClassification != null and bankClassification != ''">and bank_classification = #{bankClassification}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增存款及同业存单表 -->
    <insert id="insertDepositInterbankCd" parameterType="com.xl.alm.app.entity.DepositInterbankCdEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_deposit_interbank_cd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="bankClassification != null and bankClassification != ''">bank_classification,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="bankClassification != null and bankClassification != ''">#{bankClassification},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增存款及同业存单表 -->
    <insert id="batchInsertDepositInterbankCd" parameterType="java.util.List">
        insert into t_acm_deposit_interbank_cd(accounting_period, asset_sub_sub_category, bank_classification, book_value, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.assetSubSubCategory}, #{item.bankClassification}, #{item.bookValue}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改存款及同业存单表 -->
    <update id="updateDepositInterbankCd" parameterType="com.xl.alm.app.entity.DepositInterbankCdEntity">
        update t_acm_deposit_interbank_cd
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="bankClassification != null and bankClassification != ''">bank_classification = #{bankClassification},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除存款及同业存单表 -->
    <update id="deleteDepositInterbankCdById" parameterType="Long">
        update t_acm_deposit_interbank_cd set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除存款及同业存单表 -->
    <update id="deleteDepositInterbankCdByIds" parameterType="String">
        update t_acm_deposit_interbank_cd set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
