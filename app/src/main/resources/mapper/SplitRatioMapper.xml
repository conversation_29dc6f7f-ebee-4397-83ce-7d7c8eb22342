<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.SplitRatioMapper">

    <resultMap type="com.xl.alm.app.entity.SplitRatioEntity" id="SplitRatioResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="scenarioName" column="scenario_name"/>
        <result property="businessType" column="business_type"/>
        <result property="designType" column="design_type"/>
        <result property="splitRatioType" column="split_ratio_type"/>
        <result property="splitRatioValueSet" column="split_ratio_value_set"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectSplitRatioVo">
        select id, accounting_period, scenario_name, business_type, design_type, split_ratio_type, 
               split_ratio_value_set, create_by, create_time, update_by, update_time, is_del
        from t_cft_split_ratio
    </sql>

    <select id="selectSplitRatioEntityList" parameterType="com.xl.alm.app.query.SplitRatioQuery" resultMap="SplitRatioResult">
        <include refid="selectSplitRatioVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="scenarioName != null and scenarioName != ''">
                and scenario_name = #{scenarioName}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="designType != null and designType != ''">
                and design_type = #{designType}
            </if>
            <if test="splitRatioType != null and splitRatioType != ''">
                and split_ratio_type = #{splitRatioType}
            </if>
        </where>
        order by accounting_period desc, scenario_name, split_ratio_type, business_type, design_type
    </select>

    <select id="selectSplitRatioEntityById" parameterType="Long" resultMap="SplitRatioResult">
        <include refid="selectSplitRatioVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectSplitRatioEntityByCondition" resultMap="SplitRatioResult">
        <include refid="selectSplitRatioVo"/>
        where accounting_period = #{accountingPeriod}
          and scenario_name = #{scenarioName}
          and (business_type = #{businessType} or (business_type is null and #{businessType} is null))
          and (design_type = #{designType} or (design_type is null and #{designType} is null))
          and split_ratio_type = #{splitRatioType}
          and is_del = 0
    </select>

    <insert id="insertSplitRatioEntity" parameterType="com.xl.alm.app.entity.SplitRatioEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_split_ratio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="designType != null and designType != ''">design_type,</if>
            <if test="splitRatioType != null and splitRatioType != ''">split_ratio_type,</if>
            <if test="splitRatioValueSet != null">split_ratio_value_set,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">#{scenarioName},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="designType != null and designType != ''">#{designType},</if>
            <if test="splitRatioType != null and splitRatioType != ''">#{splitRatioType},</if>
            <if test="splitRatioValueSet != null">#{splitRatioValueSet},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateSplitRatioEntity" parameterType="com.xl.alm.app.entity.SplitRatioEntity">
        update t_cft_split_ratio
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name = #{scenarioName},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="splitRatioType != null and splitRatioType != ''">split_ratio_type = #{splitRatioType},</if>
            <if test="splitRatioValueSet != null">split_ratio_value_set = #{splitRatioValueSet},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSplitRatioEntityById" parameterType="Long">
        update t_cft_split_ratio set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteSplitRatioEntityByIds" parameterType="String">
        update t_cft_split_ratio set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertSplitRatioEntity" parameterType="java.util.List">
        insert into t_cft_split_ratio(accounting_period, scenario_name, business_type, design_type, split_ratio_type, split_ratio_value_set, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.scenarioName}, #{item.businessType}, #{item.designType}, #{item.splitRatioType}, #{item.splitRatioValueSet}, #{item.createBy})
        </foreach>
    </insert>

    <delete id="deleteSplitRatioEntityByPeriod" parameterType="String">
        update t_cft_split_ratio set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
