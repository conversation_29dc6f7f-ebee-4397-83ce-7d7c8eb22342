<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.PaymentMethodMapMapper">

    <resultMap type="com.xl.alm.app.entity.PaymentMethodMapEntity" id="PaymentMethodMapResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="annualPaymentFrequency" column="annual_payment_frequency"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectPaymentMethodMapVo">
        select id, accounting_period, annual_payment_frequency, payment_method, create_time, create_by, update_time, update_by, is_del
        from t_ast_payment_method_map
    </sql>

    <select id="selectPaymentMethodMapEntityList" parameterType="com.xl.alm.app.query.PaymentMethodMapQuery" resultMap="PaymentMethodMapResult">
        <include refid="selectPaymentMethodMapVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="annualPaymentFrequency != null">
                and annual_payment_frequency = #{annualPaymentFrequency}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and payment_method = #{paymentMethod}
            </if>
        </where>
        order by accounting_period desc, annual_payment_frequency asc
    </select>

    <select id="selectPaymentMethodMapEntityById" parameterType="Long" resultMap="PaymentMethodMapResult">
        <include refid="selectPaymentMethodMapVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertPaymentMethodMapEntity" parameterType="com.xl.alm.app.entity.PaymentMethodMapEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_payment_method_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="annualPaymentFrequency != null">#{annualPaymentFrequency},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updatePaymentMethodMapEntity" parameterType="com.xl.alm.app.entity.PaymentMethodMapEntity">
        update t_ast_payment_method_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="annualPaymentFrequency != null">annual_payment_frequency = #{annualPaymentFrequency},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentMethodMapEntityById" parameterType="Long">
        update t_ast_payment_method_map set is_del = 1 where id = #{id}
    </delete>

    <delete id="deletePaymentMethodMapEntityByIds" parameterType="String">
        update t_ast_payment_method_map set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkPaymentMethodMapExists" resultType="int">
        select count(1) from t_ast_payment_method_map
        where accounting_period = #{accountingPeriod}
        and annual_payment_frequency = #{annualPaymentFrequency}
        and is_del = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
