<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.MonthlyDiscountCurveMapper">
    
    <resultMap type="com.xl.alm.app.entity.MonthlyDiscountCurveEntity" id="MonthlyDiscountCurveResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="dateType"    column="date_type"    />
        <result property="date"    column="date"    />
        <result property="assetNumber"    column="asset_number"    />
        <result property="accountName"    column="account_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="curveId"    column="curve_id"    />
        <result property="monthlyDiscountRateSet"    column="monthly_discount_rate_set"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectMonthlyDiscountCurveVo">
        select id, account_period, date_type, date, asset_number, account_name, asset_name, security_code, curve_id,
               monthly_discount_rate_set, create_by, create_time, update_by, update_time, is_del
        from t_adur_monthly_discount_curve
    </sql>

    <select id="selectMonthlyDiscountCurveEntityList" parameterType="com.xl.alm.app.query.MonthlyDiscountCurveQuery" resultMap="MonthlyDiscountCurveResult">
        <include refid="selectMonthlyDiscountCurveVo"/>
        <where>  
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="dateType != null  and dateType != ''"> and date_type = #{dateType}</if>
            <if test="date != null"> and date = #{date}</if>
            <if test="assetNumber != null  and assetNumber != ''"> and asset_number = #{assetNumber}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code = #{securityCode}</if>
            <if test="curveId != null  and curveId != ''"> and curve_id = #{curveId}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectMonthlyDiscountCurveEntityById" parameterType="Long" resultMap="MonthlyDiscountCurveResult">
        <include refid="selectMonthlyDiscountCurveVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectMonthlyDiscountCurveEntityByAccountPeriodAndAssetNumberAndDateType" resultMap="MonthlyDiscountCurveResult">
        <include refid="selectMonthlyDiscountCurveVo"/>
        where account_period = #{accountPeriod} and asset_number = #{assetNumber} and date_type = #{dateType} and is_del = 0
    </select>
        
    <insert id="insertMonthlyDiscountCurveEntity" parameterType="com.xl.alm.app.entity.MonthlyDiscountCurveEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_monthly_discount_curve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="dateType != null and dateType != ''">date_type,</if>
            <if test="date != null">date,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="monthlyDiscountRateSet != null">monthly_discount_rate_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="dateType != null and dateType != ''">#{dateType},</if>
            <if test="date != null">#{date},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="monthlyDiscountRateSet != null">#{monthlyDiscountRateSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateMonthlyDiscountCurveEntity" parameterType="com.xl.alm.app.entity.MonthlyDiscountCurveEntity">
        update t_adur_monthly_discount_curve
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="dateType != null and dateType != ''">date_type = #{dateType},</if>
            <if test="date != null">date = #{date},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="monthlyDiscountRateSet != null">monthly_discount_rate_set = #{monthlyDiscountRateSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyDiscountCurveEntityById" parameterType="Long">
        update t_adur_monthly_discount_curve set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteMonthlyDiscountCurveEntityByIds" parameterType="String">
        update t_adur_monthly_discount_curve set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMonthlyDiscountCurveEntityByAccountPeriod" parameterType="String">
        delete from t_adur_monthly_discount_curve where account_period = #{accountPeriod}
    </delete>

    <insert id="batchInsertMonthlyDiscountCurveEntity" parameterType="java.util.List">
        insert into t_adur_monthly_discount_curve(
            account_period, date_type, date, asset_number, account_name, asset_name, security_code, curve_id,
            term_0, term_1, term_2, term_3, term_4, term_5, term_6, term_7, term_8, term_9, term_10,
            term_11, term_12, term_13, term_14, term_15, term_16, term_17, term_18, term_19, term_20,
            -- 注意：实际需要包含term_0到term_600共601个字段
            term_600,
            create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.dateType}, #{item.date}, #{item.assetNumber}, #{item.accountName},
                #{item.assetName}, #{item.securityCode}, #{item.curveId}, #{item.term0}, #{item.term1},
                #{item.term2}, #{item.term3}, #{item.term4}, #{item.term5}, #{item.term6}, #{item.term7},
                #{item.term8}, #{item.term9}, #{item.term10}, #{item.term11}, #{item.term12}, #{item.term13},
                #{item.term14}, #{item.term15}, #{item.term16}, #{item.term17}, #{item.term18}, #{item.term19},
                #{item.term20},
                -- 注意：实际需要包含term_0到term_600共601个字段
                #{item.term600},
                #{item.createBy}
            )
        </foreach>
    </insert>

</mapper>
