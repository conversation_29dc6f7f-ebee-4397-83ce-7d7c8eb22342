<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FixedIncomeTermCatMapper">

    <resultMap type="com.xl.alm.app.entity.FixedIncomeTermCatEntity" id="FixedIncomeTermCatResult">
        <result property="id" column="id"/>
        <result property="accountPeriod" column="account_period"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="bondType" column="bond_type"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFixedIncomeTermCatVo">
        select id, account_period, asset_sub_sub_category, domestic_foreign, bond_type, fixed_income_term_category,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_fixed_income_term_cat
    </sql>

    <select id="selectFixedIncomeTermCatEntityList" parameterType="com.xl.alm.app.query.FixedIncomeTermCatQuery" resultMap="FixedIncomeTermCatResult">
        <include refid="selectFixedIncomeTermCatVo"/>
        <where>
            is_del = 0
            <if test="accountPeriod != null and accountPeriod != ''">
                and account_period like concat('%', #{accountPeriod}, '%')
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="domesticForeign != null and domesticForeign != ''">
                and domestic_foreign = #{domesticForeign}
            </if>
            <if test="bondType != null and bondType != ''">
                and bond_type = #{bondType}
            </if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">
                and fixed_income_term_category = #{fixedIncomeTermCategory}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFixedIncomeTermCatEntityById" parameterType="Long" resultMap="FixedIncomeTermCatResult">
        <include refid="selectFixedIncomeTermCatVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertFixedIncomeTermCatEntity" parameterType="com.xl.alm.app.entity.FixedIncomeTermCatEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_fixed_income_term_cat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="bondType != null">bond_type,</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="bondType != null">#{bondType},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">#{fixedIncomeTermCategory},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateFixedIncomeTermCatEntity" parameterType="com.xl.alm.app.entity.FixedIncomeTermCatEntity">
        update t_ast_fixed_income_term_cat
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="bondType != null">bond_type = #{bondType},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category = #{fixedIncomeTermCategory},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFixedIncomeTermCatEntityById" parameterType="Long">
        update t_ast_fixed_income_term_cat set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteFixedIncomeTermCatEntityByIds" parameterType="Long">
        update t_ast_fixed_income_term_cat set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertFixedIncomeTermCatEntity" parameterType="java.util.List">
        insert into t_ast_fixed_income_term_cat (account_period, asset_sub_sub_category, domestic_foreign, bond_type, fixed_income_term_category,
                                                create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountPeriod}, #{item.assetSubSubCategory}, #{item.domesticForeign}, #{item.bondType}, #{item.fixedIncomeTermCategory},
             #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_fixed_income_term_cat
        where account_period = #{accountPeriod}
          and asset_sub_sub_category = #{assetSubSubCategory}
          and domestic_foreign = #{domesticForeign}
          and (bond_type = #{bondType} or (bond_type is null and #{bondType} is null))
          and is_del = 0
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

</mapper>
