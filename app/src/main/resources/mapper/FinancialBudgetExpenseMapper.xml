<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FinancialBudgetExpenseMapper">

    <resultMap type="com.xl.alm.app.entity.FinancialBudgetExpenseEntity" id="FinancialBudgetExpenseResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="scenarioName" column="scenario_name"/>
        <result property="financialExpenseType" column="financial_expense_type"/>
        <result property="date" column="date"/>
        <result property="amount" column="amount"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFinancialBudgetExpenseVo">
        select id, accounting_period, scenario_name, financial_expense_type, date, amount, create_by, create_time, update_by, update_time, is_del
        from t_cft_financial_budget_expense
    </sql>

    <select id="selectFinancialBudgetExpenseEntityList" parameterType="com.xl.alm.app.query.FinancialBudgetExpenseQuery" resultMap="FinancialBudgetExpenseResult">
        <include refid="selectFinancialBudgetExpenseVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="scenarioName != null and scenarioName != ''">
                and scenario_name like concat('%', #{scenarioName}, '%')
            </if>
            <if test="financialExpenseType != null and financialExpenseType != ''">
                and financial_expense_type = #{financialExpenseType}
            </if>
            <if test="date != null and date != ''">
                and date = #{date}
            </if>
            <if test="amount != null">
                and amount = #{amount}
            </if>
        </where>
        order by accounting_period desc, scenario_name, financial_expense_type, date
    </select>

    <select id="selectFinancialBudgetExpenseEntityById" parameterType="Long" resultMap="FinancialBudgetExpenseResult">
        <include refid="selectFinancialBudgetExpenseVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertFinancialBudgetExpenseEntity" parameterType="com.xl.alm.app.entity.FinancialBudgetExpenseEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_financial_budget_expense
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name,</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">financial_expense_type,</if>
            <if test="date != null and date != ''">date,</if>
            <if test="amount != null">amount,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">#{scenarioName},</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">#{financialExpenseType},</if>
            <if test="date != null and date != ''">#{date},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateFinancialBudgetExpenseEntity" parameterType="com.xl.alm.app.entity.FinancialBudgetExpenseEntity">
        update t_cft_financial_budget_expense
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name = #{scenarioName},</if>
            <if test="financialExpenseType != null and financialExpenseType != ''">financial_expense_type = #{financialExpenseType},</if>
            <if test="date != null and date != ''">date = #{date},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinancialBudgetExpenseEntityById" parameterType="Long">
        update t_cft_financial_budget_expense set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteFinancialBudgetExpenseEntityByIds" parameterType="String">
        update t_cft_financial_budget_expense set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertFinancialBudgetExpenseEntity" parameterType="java.util.List">
        insert into t_cft_financial_budget_expense(accounting_period, scenario_name, financial_expense_type, date, amount, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.scenarioName}, #{item.financialExpenseType}, #{item.date}, #{item.amount}, #{item.createBy})
        </foreach>
    </insert>

    <delete id="deleteFinancialBudgetExpenseEntityByPeriod" parameterType="String">
        update t_cft_financial_budget_expense set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
