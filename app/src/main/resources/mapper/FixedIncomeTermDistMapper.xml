<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FixedIncomeTermDistMapper">

    <resultMap type="com.xl.alm.app.entity.FixedIncomeTermDistEntity" id="FixedIncomeTermDistEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="remainingTermFlag" column="remaining_term_flag"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFixedIncomeTermDistVo">
        select id, accounting_period, domestic_foreign, fixed_income_term_category, remaining_term_flag, book_balance, create_by, create_time, update_by, update_time, is_del from t_asm_fixed_income_term_dist
    </sql>

    <!-- 查询固定收益类投资资产剩余期限分布表列表 -->
    <select id="selectFixedIncomeTermDistList" parameterType="com.xl.alm.app.query.FixedIncomeTermDistQuery" resultMap="FixedIncomeTermDistEntityResult">
        <include refid="selectFixedIncomeTermDistVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">and fixed_income_term_category like concat('%', #{fixedIncomeTermCategory}, '%')</if>
            <if test="remainingTermFlag != null and remainingTermFlag != ''">and remaining_term_flag = #{remainingTermFlag}</if>
            <if test="bookBalance != null">and book_balance = #{bookBalance}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询固定收益类投资资产剩余期限分布表 -->
    <select id="selectFixedIncomeTermDistById" parameterType="Long" resultMap="FixedIncomeTermDistEntityResult">
        <include refid="selectFixedIncomeTermDistVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、境内外标识、固收资产剩余期限资产分类和剩余期限标识查询固定收益类投资资产剩余期限分布表 -->
    <select id="selectFixedIncomeTermDistByCondition" resultMap="FixedIncomeTermDistEntityResult">
        <include refid="selectFixedIncomeTermDistVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="domesticForeign != null and domesticForeign != ''">and domestic_foreign = #{domesticForeign}</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">and fixed_income_term_category = #{fixedIncomeTermCategory}</if>
            <if test="remainingTermFlag != null and remainingTermFlag != ''">and remaining_term_flag = #{remainingTermFlag}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增固定收益类投资资产剩余期限分布表 -->
    <insert id="insertFixedIncomeTermDist" parameterType="com.xl.alm.app.entity.FixedIncomeTermDistEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_fixed_income_term_dist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category,</if>
            <if test="remainingTermFlag != null and remainingTermFlag != ''">remaining_term_flag,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">#{fixedIncomeTermCategory},</if>
            <if test="remainingTermFlag != null and remainingTermFlag != ''">#{remainingTermFlag},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增固定收益类投资资产剩余期限分布表 -->
    <insert id="batchInsertFixedIncomeTermDist" parameterType="java.util.List">
        insert into t_asm_fixed_income_term_dist(accounting_period, domestic_foreign, fixed_income_term_category, remaining_term_flag, book_balance, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.fixedIncomeTermCategory}, #{item.remainingTermFlag}, #{item.bookBalance}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改固定收益类投资资产剩余期限分布表 -->
    <update id="updateFixedIncomeTermDist" parameterType="com.xl.alm.app.entity.FixedIncomeTermDistEntity">
        update t_asm_fixed_income_term_dist
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category = #{fixedIncomeTermCategory},</if>
            <if test="remainingTermFlag != null and remainingTermFlag != ''">remaining_term_flag = #{remainingTermFlag},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除固定收益类投资资产剩余期限分布表 -->
    <update id="deleteFixedIncomeTermDistById" parameterType="Long">
        update t_asm_fixed_income_term_dist set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除固定收益类投资资产剩余期限分布表 -->
    <update id="deleteFixedIncomeTermDistByIds" parameterType="String">
        update t_asm_fixed_income_term_dist set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
