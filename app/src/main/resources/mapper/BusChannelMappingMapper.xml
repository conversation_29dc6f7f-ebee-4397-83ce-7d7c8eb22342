<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.BusChannelMappingMapper">

    <resultMap type="com.xl.alm.app.entity.BusChannelMappingEntity" id="BusChannelMappingEntityResult">
        <result property="id" column="id"/>
        <result property="busTypeCode" column="bus_type_code"/>
        <result property="channelTypeCode" column="channel_type_code"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectBusChannelMappingEntityVo">
        select id, bus_type_code, channel_type_code, create_time, create_by, update_time, update_by, is_del
        from t_insu_bus_channel_mapping
    </sql>

    <!-- 查询渠道码映射配置列表 -->
    <select id="selectBusChannelMappingEntityList" parameterType="com.xl.alm.app.query.BusChannelMappingQuery" resultMap="BusChannelMappingEntityResult">
        <include refid="selectBusChannelMappingEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="busTypeCode != null and busTypeCode != ''">and bus_type_code = #{busTypeCode}</if>
            <if test="channelTypeCode != null and channelTypeCode != ''">and channel_type_code = #{channelTypeCode}</if>
            and is_del = 0
        </where>
        order by create_time desc
    </select>

    <!-- 用id查询渠道码映射配置 -->
    <select id="selectBusChannelMappingEntityById" parameterType="Long" resultMap="BusChannelMappingEntityResult">
        <include refid="selectBusChannelMappingEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据业务类型编码和渠道类型编码查询渠道码映射配置 -->
    <select id="selectBusChannelMappingEntityByCondition" resultMap="BusChannelMappingEntityResult">
        <include refid="selectBusChannelMappingEntityVo"/>
        <where>
            <if test="busTypeCode != null and busTypeCode != ''">and bus_type_code = #{busTypeCode}</if>
            <if test="channelTypeCode != null and channelTypeCode != ''">and channel_type_code = #{channelTypeCode}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增渠道码映射配置 -->
    <insert id="insertBusChannelMappingEntity" parameterType="com.xl.alm.app.entity.BusChannelMappingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_insu_bus_channel_mapping (
            bus_type_code, channel_type_code, create_by, update_by
        ) values (
            #{busTypeCode}, #{channelTypeCode}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量新增渠道码映射配置 -->
    <insert id="batchInsertBusChannelMappingEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_insu_bus_channel_mapping (
            bus_type_code, channel_type_code, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.busTypeCode}, #{item.channelTypeCode}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 修改渠道码映射配置 -->
    <update id="updateBusChannelMappingEntity" parameterType="com.xl.alm.app.entity.BusChannelMappingEntity">
        update t_insu_bus_channel_mapping
        <set>
            <if test="busTypeCode != null and busTypeCode != ''">bus_type_code = #{busTypeCode},</if>
            <if test="channelTypeCode != null and channelTypeCode != ''">channel_type_code = #{channelTypeCode},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除渠道码映射配置 -->
    <update id="deleteBusChannelMappingEntityById" parameterType="Long">
        update t_insu_bus_channel_mapping set is_del = 1, update_time = now() 
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除渠道码映射配置 -->
    <update id="deleteBusChannelMappingEntityByIds" parameterType="Long">
        update t_insu_bus_channel_mapping set is_del = 1, update_time = now() 
        where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>
</mapper>
