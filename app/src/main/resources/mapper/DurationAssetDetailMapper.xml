<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.DurationAssetDetailMapper">
    
    <resultMap type="com.xl.alm.app.entity.DurationAssetDetailEntity" id="DurationAssetDetailResult">
        <result property="id"    column="id"    />
        <result property="accountPeriod"    column="account_period"    />
        <result property="assetNumber"    column="asset_number"    />
        <result property="accountName"    column="account_name"    />
        <result property="assetName"    column="asset_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="assetSubCategory"    column="asset_sub_category"    />
        <result property="holdingFaceValue"    column="holding_face_value"    />
        <result property="marketValue"    column="market_value"    />
        <result property="bookBalance"    column="book_balance"    />
        <result property="bookValue"    column="book_value"    />
        <result property="couponRate"    column="coupon_rate"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="curveId"    column="curve_id"    />
        <result property="adjustedValueDate"    column="adjusted_value_date"    />
        <result property="adjustedPurchaseDate"    column="adjusted_purchase_date"    />
        <result property="adjustedMaturityDate"    column="adjusted_maturity_date"    />
        <result property="issueSpreadCalcFlag"    column="issue_spread_calc_flag"    />
        <result property="spreadDurationStatFlag"    column="spread_duration_stat_flag"    />
        <result property="evalSpread"    column="eval_spread"    />
        <result property="spreadDuration"    column="spread_duration"    />
        <result property="bookValueSigma9"    column="book_value_sigma_9"    />
        <result property="bookValueSigma17"    column="book_value_sigma_17"    />
        <result property="bookValueSigma77"    column="book_value_sigma_77"    />
        <result property="issuePresentValue"    column="issue_present_value"    />
        <result property="issueSpread"    column="issue_spread"    />
        <result property="evalPresentValue"    column="eval_present_value"    />
        <result property="evalMaturityYield"    column="eval_maturity_yield"    />
        <result property="assetModifiedDuration"    column="asset_modified_duration"    />
        <result property="evalPresentValuePlus50bp"    column="eval_present_value_plus_50bp"    />
        <result property="evalPresentValueMinus50bp"    column="eval_present_value_minus_50bp"    />
        <result property="assetEffectiveDuration"    column="asset_effective_duration"    />
        <result property="dv101"    column="dv10_1"    />
        <result property="dv102"    column="dv10_2"    />
        <result property="dv103"    column="dv10_3"    />
        <result property="dv104"    column="dv10_4"    />
        <result property="dv105"    column="dv10_5"    />
        <result property="dv106"    column="dv10_6"    />
        <result property="dv107"    column="dv10_7"    />
        <result property="dv108"    column="dv10_8"    />
        <result property="dv109"    column="dv10_9"    />
        <result property="dv1010"    column="dv10_10"    />
        <result property="dv1011"    column="dv10_11"    />
        <result property="dv1012"    column="dv10_12"    />
        <result property="dv1013"    column="dv10_13"    />
        <result property="dv1014"    column="dv10_14"    />
        <result property="dv1015"    column="dv10_15"    />
        <result property="dv1016"    column="dv10_16"    />
        <result property="dv1017"    column="dv10_17"    />
        <result property="dv1018"    column="dv10_18"    />
        <result property="dv1019"    column="dv10_19"    />
        <result property="dv1020"    column="dv10_20"    />
        <result property="dv101Up"    column="dv10_1_up"    />
        <result property="dv102Up"    column="dv10_2_up"    />
        <result property="dv103Up"    column="dv10_3_up"    />
        <result property="dv104Up"    column="dv10_4_up"    />
        <result property="dv105Up"    column="dv10_5_up"    />
        <result property="dv106Up"    column="dv10_6_up"    />
        <result property="dv107Up"    column="dv10_7_up"    />
        <result property="dv108Up"    column="dv10_8_up"    />
        <result property="dv109Up"    column="dv10_9_up"    />
        <result property="dv1010Up"    column="dv10_10_up"    />
        <result property="dv1011Up"    column="dv10_11_up"    />
        <result property="dv1012Up"    column="dv10_12_up"    />
        <result property="dv1013Up"    column="dv10_13_up"    />
        <result property="dv1014Up"    column="dv10_14_up"    />
        <result property="dv1015Up"    column="dv10_15_up"    />
        <result property="dv1016Up"    column="dv10_16_up"    />
        <result property="dv1017Up"    column="dv10_17_up"    />
        <result property="dv1018Up"    column="dv10_18_up"    />
        <result property="dv1019Up"    column="dv10_19_up"    />
        <result property="dv1020Up"    column="dv10_20_up"    />
        <result property="dv101Down"    column="dv10_1_down"    />
        <result property="dv102Down"    column="dv10_2_down"    />
        <result property="dv103Down"    column="dv10_3_down"    />
        <result property="dv104Down"    column="dv10_4_down"    />
        <result property="dv105Down"    column="dv10_5_down"    />
        <result property="dv106Down"    column="dv10_6_down"    />
        <result property="dv107Down"    column="dv10_7_down"    />
        <result property="dv108Down"    column="dv10_8_down"    />
        <result property="dv109Down"    column="dv10_9_down"    />
        <result property="dv1010Down"    column="dv10_10_down"    />
        <result property="dv1011Down"    column="dv10_11_down"    />
        <result property="dv1012Down"    column="dv10_12_down"    />
        <result property="dv1013Down"    column="dv10_13_down"    />
        <result property="dv1014Down"    column="dv10_14_down"    />
        <result property="dv1015Down"    column="dv10_15_down"    />
        <result property="dv1016Down"    column="dv10_16_down"    />
        <result property="dv1017Down"    column="dv10_17_down"    />
        <result property="dv1018Down"    column="dv10_18_down"    />
        <result property="dv1019Down"    column="dv10_19_down"    />
        <result property="dv1020Down"    column="dv10_20_down"    />
        <result property="issueCashflowSet"    column="issue_cashflow_set"    />
        <result property="evalCashflowSet"    column="eval_cashflow_set"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectDurationAssetDetailVo">
        select id, account_period, asset_number, account_name, asset_name, security_code, asset_sub_category, 
               holding_face_value, market_value, book_balance, book_value, coupon_rate, payment_method, curve_id,
               adjusted_value_date, adjusted_purchase_date, adjusted_maturity_date, issue_spread_calc_flag, 
               spread_duration_stat_flag, eval_spread, spread_duration, book_value_sigma_9, book_value_sigma_17, 
               book_value_sigma_77, issue_present_value, issue_spread, eval_present_value, eval_maturity_yield, 
               asset_modified_duration, eval_present_value_plus_50bp, eval_present_value_minus_50bp, 
               asset_effective_duration, dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8, 
               dv10_9, dv10_10, dv10_11, dv10_12, dv10_13, dv10_14, dv10_15, dv10_16, dv10_17, dv10_18, 
               dv10_19, dv10_20, dv10_1_up, dv10_2_up, dv10_3_up, dv10_4_up, dv10_5_up, dv10_6_up, dv10_7_up, 
               dv10_8_up, dv10_9_up, dv10_10_up, dv10_11_up, dv10_12_up, dv10_13_up, dv10_14_up, dv10_15_up, 
               dv10_16_up, dv10_17_up, dv10_18_up, dv10_19_up, dv10_20_up, dv10_1_down, dv10_2_down, dv10_3_down, 
               dv10_4_down, dv10_5_down, dv10_6_down, dv10_7_down, dv10_8_down, dv10_9_down, dv10_10_down, 
               dv10_11_down, dv10_12_down, dv10_13_down, dv10_14_down, dv10_15_down, dv10_16_down, dv10_17_down, 
               dv10_18_down, dv10_19_down, dv10_20_down, issue_cashflow_set, eval_cashflow_set, 
               create_by, create_time, update_by, update_time, is_del 
        from t_adur_duration_asset_detail
    </sql>

    <select id="selectDurationAssetDetailEntityList" parameterType="com.xl.alm.app.query.DurationAssetDetailQuery" resultMap="DurationAssetDetailResult">
        <include refid="selectDurationAssetDetailVo"/>
        <where>  
            <if test="accountPeriod != null  and accountPeriod != ''"> and account_period = #{accountPeriod}</if>
            <if test="assetNumber != null  and assetNumber != ''"> and asset_number = #{assetNumber}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code = #{securityCode}</if>
            <if test="assetSubCategory != null  and assetSubCategory != ''"> and asset_sub_category like concat('%', #{assetSubCategory}, '%')</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="curveId != null  and curveId != ''"> and curve_id = #{curveId}</if>
            <if test="issueSpreadCalcFlag != null  and issueSpreadCalcFlag != ''"> and issue_spread_calc_flag = #{issueSpreadCalcFlag}</if>
            <if test="spreadDurationStatFlag != null  and spreadDurationStatFlag != ''"> and spread_duration_stat_flag = #{spreadDurationStatFlag}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectDurationAssetDetailEntityById" parameterType="Long" resultMap="DurationAssetDetailResult">
        <include refid="selectDurationAssetDetailVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectDurationAssetDetailEntityByAccountPeriodAndAssetNumber" resultMap="DurationAssetDetailResult">
        <include refid="selectDurationAssetDetailVo"/>
        where account_period = #{accountPeriod} and asset_number = #{assetNumber} and is_del = 0
    </select>
        
    <insert id="insertDurationAssetDetailEntity" parameterType="com.xl.alm.app.entity.DurationAssetDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_adur_duration_asset_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period,</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="assetSubCategory != null and assetSubCategory != ''">asset_sub_category,</if>
            <if test="holdingFaceValue != null">holding_face_value,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="bookBalance != null">book_balance,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="couponRate != null">coupon_rate,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="adjustedValueDate != null">adjusted_value_date,</if>
            <if test="adjustedPurchaseDate != null">adjusted_purchase_date,</if>
            <if test="adjustedMaturityDate != null">adjusted_maturity_date,</if>
            <if test="issueSpreadCalcFlag != null and issueSpreadCalcFlag != ''">issue_spread_calc_flag,</if>
            <if test="spreadDurationStatFlag != null and spreadDurationStatFlag != ''">spread_duration_stat_flag,</if>
            <if test="evalSpread != null">eval_spread,</if>
            <if test="spreadDuration != null">spread_duration,</if>
            <if test="bookValueSigma9 != null">book_value_sigma_9,</if>
            <if test="bookValueSigma17 != null">book_value_sigma_17,</if>
            <if test="bookValueSigma77 != null">book_value_sigma_77,</if>
            <if test="issuePresentValue != null">issue_present_value,</if>
            <if test="issueSpread != null">issue_spread,</if>
            <if test="evalPresentValue != null">eval_present_value,</if>
            <if test="evalMaturityYield != null">eval_maturity_yield,</if>
            <if test="assetModifiedDuration != null">asset_modified_duration,</if>
            <if test="evalPresentValuePlus50bp != null">eval_present_value_plus_50bp,</if>
            <if test="evalPresentValueMinus50bp != null">eval_present_value_minus_50bp,</if>
            <if test="assetEffectiveDuration != null">asset_effective_duration,</if>
            <if test="dv101 != null">dv10_1,</if>
            <if test="dv102 != null">dv10_2,</if>
            <if test="dv103 != null">dv10_3,</if>
            <if test="dv104 != null">dv10_4,</if>
            <if test="dv105 != null">dv10_5,</if>
            <if test="dv106 != null">dv10_6,</if>
            <if test="dv107 != null">dv10_7,</if>
            <if test="dv108 != null">dv10_8,</if>
            <if test="dv109 != null">dv10_9,</if>
            <if test="dv1010 != null">dv10_10,</if>
            <if test="dv1011 != null">dv10_11,</if>
            <if test="dv1012 != null">dv10_12,</if>
            <if test="dv1013 != null">dv10_13,</if>
            <if test="dv1014 != null">dv10_14,</if>
            <if test="dv1015 != null">dv10_15,</if>
            <if test="dv1016 != null">dv10_16,</if>
            <if test="dv1017 != null">dv10_17,</if>
            <if test="dv1018 != null">dv10_18,</if>
            <if test="dv1019 != null">dv10_19,</if>
            <if test="dv1020 != null">dv10_20,</if>
            <if test="dv101Up != null">dv10_1_up,</if>
            <if test="dv102Up != null">dv10_2_up,</if>
            <if test="dv103Up != null">dv10_3_up,</if>
            <if test="dv104Up != null">dv10_4_up,</if>
            <if test="dv105Up != null">dv10_5_up,</if>
            <if test="dv106Up != null">dv10_6_up,</if>
            <if test="dv107Up != null">dv10_7_up,</if>
            <if test="dv108Up != null">dv10_8_up,</if>
            <if test="dv109Up != null">dv10_9_up,</if>
            <if test="dv1010Up != null">dv10_10_up,</if>
            <if test="dv1011Up != null">dv10_11_up,</if>
            <if test="dv1012Up != null">dv10_12_up,</if>
            <if test="dv1013Up != null">dv10_13_up,</if>
            <if test="dv1014Up != null">dv10_14_up,</if>
            <if test="dv1015Up != null">dv10_15_up,</if>
            <if test="dv1016Up != null">dv10_16_up,</if>
            <if test="dv1017Up != null">dv10_17_up,</if>
            <if test="dv1018Up != null">dv10_18_up,</if>
            <if test="dv1019Up != null">dv10_19_up,</if>
            <if test="dv1020Up != null">dv10_20_up,</if>
            <if test="dv101Down != null">dv10_1_down,</if>
            <if test="dv102Down != null">dv10_2_down,</if>
            <if test="dv103Down != null">dv10_3_down,</if>
            <if test="dv104Down != null">dv10_4_down,</if>
            <if test="dv105Down != null">dv10_5_down,</if>
            <if test="dv106Down != null">dv10_6_down,</if>
            <if test="dv107Down != null">dv10_7_down,</if>
            <if test="dv108Down != null">dv10_8_down,</if>
            <if test="dv109Down != null">dv10_9_down,</if>
            <if test="dv1010Down != null">dv10_10_down,</if>
            <if test="dv1011Down != null">dv10_11_down,</if>
            <if test="dv1012Down != null">dv10_12_down,</if>
            <if test="dv1013Down != null">dv10_13_down,</if>
            <if test="dv1014Down != null">dv10_14_down,</if>
            <if test="dv1015Down != null">dv10_15_down,</if>
            <if test="dv1016Down != null">dv10_16_down,</if>
            <if test="dv1017Down != null">dv10_17_down,</if>
            <if test="dv1018Down != null">dv10_18_down,</if>
            <if test="dv1019Down != null">dv10_19_down,</if>
            <if test="dv1020Down != null">dv10_20_down,</if>
            <if test="issueCashflowSet != null and issueCashflowSet != ''">issue_cashflow_set,</if>
            <if test="evalCashflowSet != null and evalCashflowSet != ''">eval_cashflow_set,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">#{accountPeriod},</if>
            <if test="assetNumber != null and assetNumber != ''">#{assetNumber},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="assetSubCategory != null and assetSubCategory != ''">#{assetSubCategory},</if>
            <if test="holdingFaceValue != null">#{holdingFaceValue},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="couponRate != null">#{couponRate},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="adjustedValueDate != null">#{adjustedValueDate},</if>
            <if test="adjustedPurchaseDate != null">#{adjustedPurchaseDate},</if>
            <if test="adjustedMaturityDate != null">#{adjustedMaturityDate},</if>
            <if test="issueSpreadCalcFlag != null and issueSpreadCalcFlag != ''">#{issueSpreadCalcFlag},</if>
            <if test="spreadDurationStatFlag != null and spreadDurationStatFlag != ''">#{spreadDurationStatFlag},</if>
            <if test="evalSpread != null">#{evalSpread},</if>
            <if test="spreadDuration != null">#{spreadDuration},</if>
            <if test="bookValueSigma9 != null">#{bookValueSigma9},</if>
            <if test="bookValueSigma17 != null">#{bookValueSigma17},</if>
            <if test="bookValueSigma77 != null">#{bookValueSigma77},</if>
            <if test="issuePresentValue != null">#{issuePresentValue},</if>
            <if test="issueSpread != null">#{issueSpread},</if>
            <if test="evalPresentValue != null">#{evalPresentValue},</if>
            <if test="evalMaturityYield != null">#{evalMaturityYield},</if>
            <if test="assetModifiedDuration != null">#{assetModifiedDuration},</if>
            <if test="evalPresentValuePlus50bp != null">#{evalPresentValuePlus50bp},</if>
            <if test="evalPresentValueMinus50bp != null">#{evalPresentValueMinus50bp},</if>
            <if test="assetEffectiveDuration != null">#{assetEffectiveDuration},</if>
            <if test="dv101 != null">#{dv101},</if>
            <if test="dv102 != null">#{dv102},</if>
            <if test="dv103 != null">#{dv103},</if>
            <if test="dv104 != null">#{dv104},</if>
            <if test="dv105 != null">#{dv105},</if>
            <if test="dv106 != null">#{dv106},</if>
            <if test="dv107 != null">#{dv107},</if>
            <if test="dv108 != null">#{dv108},</if>
            <if test="dv109 != null">#{dv109},</if>
            <if test="dv1010 != null">#{dv1010},</if>
            <if test="dv1011 != null">#{dv1011},</if>
            <if test="dv1012 != null">#{dv1012},</if>
            <if test="dv1013 != null">#{dv1013},</if>
            <if test="dv1014 != null">#{dv1014},</if>
            <if test="dv1015 != null">#{dv1015},</if>
            <if test="dv1016 != null">#{dv1016},</if>
            <if test="dv1017 != null">#{dv1017},</if>
            <if test="dv1018 != null">#{dv1018},</if>
            <if test="dv1019 != null">#{dv1019},</if>
            <if test="dv1020 != null">#{dv1020},</if>
            <if test="dv101Up != null">#{dv101Up},</if>
            <if test="dv102Up != null">#{dv102Up},</if>
            <if test="dv103Up != null">#{dv103Up},</if>
            <if test="dv104Up != null">#{dv104Up},</if>
            <if test="dv105Up != null">#{dv105Up},</if>
            <if test="dv106Up != null">#{dv106Up},</if>
            <if test="dv107Up != null">#{dv107Up},</if>
            <if test="dv108Up != null">#{dv108Up},</if>
            <if test="dv109Up != null">#{dv109Up},</if>
            <if test="dv1010Up != null">#{dv1010Up},</if>
            <if test="dv1011Up != null">#{dv1011Up},</if>
            <if test="dv1012Up != null">#{dv1012Up},</if>
            <if test="dv1013Up != null">#{dv1013Up},</if>
            <if test="dv1014Up != null">#{dv1014Up},</if>
            <if test="dv1015Up != null">#{dv1015Up},</if>
            <if test="dv1016Up != null">#{dv1016Up},</if>
            <if test="dv1017Up != null">#{dv1017Up},</if>
            <if test="dv1018Up != null">#{dv1018Up},</if>
            <if test="dv1019Up != null">#{dv1019Up},</if>
            <if test="dv1020Up != null">#{dv1020Up},</if>
            <if test="dv101Down != null">#{dv101Down},</if>
            <if test="dv102Down != null">#{dv102Down},</if>
            <if test="dv103Down != null">#{dv103Down},</if>
            <if test="dv104Down != null">#{dv104Down},</if>
            <if test="dv105Down != null">#{dv105Down},</if>
            <if test="dv106Down != null">#{dv106Down},</if>
            <if test="dv107Down != null">#{dv107Down},</if>
            <if test="dv108Down != null">#{dv108Down},</if>
            <if test="dv109Down != null">#{dv109Down},</if>
            <if test="dv1010Down != null">#{dv1010Down},</if>
            <if test="dv1011Down != null">#{dv1011Down},</if>
            <if test="dv1012Down != null">#{dv1012Down},</if>
            <if test="dv1013Down != null">#{dv1013Down},</if>
            <if test="dv1014Down != null">#{dv1014Down},</if>
            <if test="dv1015Down != null">#{dv1015Down},</if>
            <if test="dv1016Down != null">#{dv1016Down},</if>
            <if test="dv1017Down != null">#{dv1017Down},</if>
            <if test="dv1018Down != null">#{dv1018Down},</if>
            <if test="dv1019Down != null">#{dv1019Down},</if>
            <if test="dv1020Down != null">#{dv1020Down},</if>
            <if test="issueCashflowSet != null and issueCashflowSet != ''">#{issueCashflowSet},</if>
            <if test="evalCashflowSet != null and evalCashflowSet != ''">#{evalCashflowSet},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateDurationAssetDetailEntity" parameterType="com.xl.alm.app.entity.DurationAssetDetailEntity">
        update t_adur_duration_asset_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountPeriod != null and accountPeriod != ''">account_period = #{accountPeriod},</if>
            <if test="assetNumber != null and assetNumber != ''">asset_number = #{assetNumber},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="assetSubCategory != null and assetSubCategory != ''">asset_sub_category = #{assetSubCategory},</if>
            <if test="holdingFaceValue != null">holding_face_value = #{holdingFaceValue},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="couponRate != null">coupon_rate = #{couponRate},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="adjustedValueDate != null">adjusted_value_date = #{adjustedValueDate},</if>
            <if test="adjustedPurchaseDate != null">adjusted_purchase_date = #{adjustedPurchaseDate},</if>
            <if test="adjustedMaturityDate != null">adjusted_maturity_date = #{adjustedMaturityDate},</if>
            <if test="issueSpreadCalcFlag != null and issueSpreadCalcFlag != ''">issue_spread_calc_flag = #{issueSpreadCalcFlag},</if>
            <if test="spreadDurationStatFlag != null and spreadDurationStatFlag != ''">spread_duration_stat_flag = #{spreadDurationStatFlag},</if>
            <if test="evalSpread != null">eval_spread = #{evalSpread},</if>
            <if test="spreadDuration != null">spread_duration = #{spreadDuration},</if>
            <if test="bookValueSigma9 != null">book_value_sigma_9 = #{bookValueSigma9},</if>
            <if test="bookValueSigma17 != null">book_value_sigma_17 = #{bookValueSigma17},</if>
            <if test="bookValueSigma77 != null">book_value_sigma_77 = #{bookValueSigma77},</if>
            <if test="issuePresentValue != null">issue_present_value = #{issuePresentValue},</if>
            <if test="issueSpread != null">issue_spread = #{issueSpread},</if>
            <if test="evalPresentValue != null">eval_present_value = #{evalPresentValue},</if>
            <if test="evalMaturityYield != null">eval_maturity_yield = #{evalMaturityYield},</if>
            <if test="assetModifiedDuration != null">asset_modified_duration = #{assetModifiedDuration},</if>
            <if test="evalPresentValuePlus50bp != null">eval_present_value_plus_50bp = #{evalPresentValuePlus50bp},</if>
            <if test="evalPresentValueMinus50bp != null">eval_present_value_minus_50bp = #{evalPresentValueMinus50bp},</if>
            <if test="assetEffectiveDuration != null">asset_effective_duration = #{assetEffectiveDuration},</if>
            <if test="dv101 != null">dv10_1 = #{dv101},</if>
            <if test="dv102 != null">dv10_2 = #{dv102},</if>
            <if test="dv103 != null">dv10_3 = #{dv103},</if>
            <if test="dv104 != null">dv10_4 = #{dv104},</if>
            <if test="dv105 != null">dv10_5 = #{dv105},</if>
            <if test="dv106 != null">dv10_6 = #{dv106},</if>
            <if test="dv107 != null">dv10_7 = #{dv107},</if>
            <if test="dv108 != null">dv10_8 = #{dv108},</if>
            <if test="dv109 != null">dv10_9 = #{dv109},</if>
            <if test="dv1010 != null">dv10_10 = #{dv1010},</if>
            <if test="dv1011 != null">dv10_11 = #{dv1011},</if>
            <if test="dv1012 != null">dv10_12 = #{dv1012},</if>
            <if test="dv1013 != null">dv10_13 = #{dv1013},</if>
            <if test="dv1014 != null">dv10_14 = #{dv1014},</if>
            <if test="dv1015 != null">dv10_15 = #{dv1015},</if>
            <if test="dv1016 != null">dv10_16 = #{dv1016},</if>
            <if test="dv1017 != null">dv10_17 = #{dv1017},</if>
            <if test="dv1018 != null">dv10_18 = #{dv1018},</if>
            <if test="dv1019 != null">dv10_19 = #{dv1019},</if>
            <if test="dv1020 != null">dv10_20 = #{dv1020},</if>
            <if test="dv101Up != null">dv10_1_up = #{dv101Up},</if>
            <if test="dv102Up != null">dv10_2_up = #{dv102Up},</if>
            <if test="dv103Up != null">dv10_3_up = #{dv103Up},</if>
            <if test="dv104Up != null">dv10_4_up = #{dv104Up},</if>
            <if test="dv105Up != null">dv10_5_up = #{dv105Up},</if>
            <if test="dv106Up != null">dv10_6_up = #{dv106Up},</if>
            <if test="dv107Up != null">dv10_7_up = #{dv107Up},</if>
            <if test="dv108Up != null">dv10_8_up = #{dv108Up},</if>
            <if test="dv109Up != null">dv10_9_up = #{dv109Up},</if>
            <if test="dv1010Up != null">dv10_10_up = #{dv1010Up},</if>
            <if test="dv1011Up != null">dv10_11_up = #{dv1011Up},</if>
            <if test="dv1012Up != null">dv10_12_up = #{dv1012Up},</if>
            <if test="dv1013Up != null">dv10_13_up = #{dv1013Up},</if>
            <if test="dv1014Up != null">dv10_14_up = #{dv1014Up},</if>
            <if test="dv1015Up != null">dv10_15_up = #{dv1015Up},</if>
            <if test="dv1016Up != null">dv10_16_up = #{dv1016Up},</if>
            <if test="dv1017Up != null">dv10_17_up = #{dv1017Up},</if>
            <if test="dv1018Up != null">dv10_18_up = #{dv1018Up},</if>
            <if test="dv1019Up != null">dv10_19_up = #{dv1019Up},</if>
            <if test="dv1020Up != null">dv10_20_up = #{dv1020Up},</if>
            <if test="dv101Down != null">dv10_1_down = #{dv101Down},</if>
            <if test="dv102Down != null">dv10_2_down = #{dv102Down},</if>
            <if test="dv103Down != null">dv10_3_down = #{dv103Down},</if>
            <if test="dv104Down != null">dv10_4_down = #{dv104Down},</if>
            <if test="dv105Down != null">dv10_5_down = #{dv105Down},</if>
            <if test="dv106Down != null">dv10_6_down = #{dv106Down},</if>
            <if test="dv107Down != null">dv10_7_down = #{dv107Down},</if>
            <if test="dv108Down != null">dv10_8_down = #{dv108Down},</if>
            <if test="dv109Down != null">dv10_9_down = #{dv109Down},</if>
            <if test="dv1010Down != null">dv10_10_down = #{dv1010Down},</if>
            <if test="dv1011Down != null">dv10_11_down = #{dv1011Down},</if>
            <if test="dv1012Down != null">dv10_12_down = #{dv1012Down},</if>
            <if test="dv1013Down != null">dv10_13_down = #{dv1013Down},</if>
            <if test="dv1014Down != null">dv10_14_down = #{dv1014Down},</if>
            <if test="dv1015Down != null">dv10_15_down = #{dv1015Down},</if>
            <if test="dv1016Down != null">dv10_16_down = #{dv1016Down},</if>
            <if test="dv1017Down != null">dv10_17_down = #{dv1017Down},</if>
            <if test="dv1018Down != null">dv10_18_down = #{dv1018Down},</if>
            <if test="dv1019Down != null">dv10_19_down = #{dv1019Down},</if>
            <if test="dv1020Down != null">dv10_20_down = #{dv1020Down},</if>
            <if test="issueCashflowSet != null and issueCashflowSet != ''">issue_cashflow_set = #{issueCashflowSet},</if>
            <if test="evalCashflowSet != null and evalCashflowSet != ''">eval_cashflow_set = #{evalCashflowSet},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDurationAssetDetailEntityById" parameterType="Long">
        update t_adur_duration_asset_detail set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteDurationAssetDetailEntityByIds" parameterType="String">
        update t_adur_duration_asset_detail set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDurationAssetDetailEntityByAccountPeriod" parameterType="String">
        delete from t_adur_duration_asset_detail where account_period = #{accountPeriod}
    </delete>

    <insert id="batchInsertDurationAssetDetailEntity" parameterType="java.util.List">
        insert into t_adur_duration_asset_detail(
            account_period, asset_number, account_name, asset_name, security_code, asset_sub_category,
            holding_face_value, market_value, book_balance, book_value, coupon_rate, payment_method, curve_id,
            adjusted_value_date, adjusted_purchase_date, adjusted_maturity_date, issue_spread_calc_flag,
            spread_duration_stat_flag, eval_spread, spread_duration, book_value_sigma_9, book_value_sigma_17,
            book_value_sigma_77, issue_present_value, issue_spread, eval_present_value, eval_maturity_yield,
            asset_modified_duration, eval_present_value_plus_50bp, eval_present_value_minus_50bp,
            asset_effective_duration, issue_cashflow_set, eval_cashflow_set, create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.assetNumber}, #{item.accountName}, #{item.assetName},
                #{item.securityCode}, #{item.assetSubCategory}, #{item.holdingFaceValue}, #{item.marketValue},
                #{item.bookBalance}, #{item.bookValue}, #{item.couponRate}, #{item.paymentMethod}, #{item.curveId},
                #{item.adjustedValueDate}, #{item.adjustedPurchaseDate}, #{item.adjustedMaturityDate},
                #{item.issueSpreadCalcFlag}, #{item.spreadDurationStatFlag}, #{item.evalSpread}, #{item.spreadDuration},
                #{item.bookValueSigma9}, #{item.bookValueSigma17}, #{item.bookValueSigma77}, #{item.issuePresentValue},
                #{item.issueSpread}, #{item.evalPresentValue}, #{item.evalMaturityYield}, #{item.assetModifiedDuration},
                #{item.evalPresentValuePlus50bp}, #{item.evalPresentValueMinus50bp}, #{item.assetEffectiveDuration},
                #{item.issueCashflowSet}, #{item.evalCashflowSet}, #{item.createBy}
            )
        </foreach>
    </insert>

</mapper>
