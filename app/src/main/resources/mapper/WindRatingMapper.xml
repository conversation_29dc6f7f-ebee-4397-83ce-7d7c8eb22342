<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.WindRatingMapper">

    <resultMap type="com.xl.alm.app.dto.WindRatingDTO" id="WindRatingResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="securityCode"    column="security_code"    />
        <result property="securityName"    column="security_name"    />
        <result property="entityRating"    column="entity_rating"    />
        <result property="bondRating"    column="bond_rating"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWindRatingVo">
        select id, accounting_period, security_code, security_name, entity_rating, bond_rating, is_del, create_by, create_time, update_by, update_time from t_ast_wind_rating
    </sql>

    <select id="selectWindRatingDtoList" parameterType="com.xl.alm.app.query.WindRatingQuery" resultMap="WindRatingResult">
        <include refid="selectWindRatingVo"/>
        <where>  
            <if test="query.accountingPeriod != null  and query.accountingPeriod != ''"> and accounting_period = #{query.accountingPeriod}</if>
            <if test="query.securityCode != null  and query.securityCode != ''"> and security_code = #{query.securityCode}</if>
            <if test="query.securityName != null  and query.securityName != ''"> and security_name like concat('%', #{query.securityName}, '%')</if>
            <if test="query.entityRating != null  and query.entityRating != ''"> and entity_rating = #{query.entityRating}</if>
            <if test="query.bondRating != null  and query.bondRating != ''"> and bond_rating = #{query.bondRating}</if>
            <if test="query.isDel != null "> and is_del = #{query.isDel}</if>
        </where>
    </select>
    
    <select id="selectWindRatingDtoById" parameterType="Long" resultMap="WindRatingResult">
        <include refid="selectWindRatingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWindRatingDto" parameterType="com.xl.alm.app.dto.WindRatingDTO" useGeneratedKeys="true" keyProperty="dto.id">
        insert into t_ast_wind_rating
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dto.accountingPeriod != null and dto.accountingPeriod != ''">accounting_period,</if>
            <if test="dto.securityCode != null and dto.securityCode != ''">security_code,</if>
            <if test="dto.securityName != null and dto.securityName != ''">security_name,</if>
            <if test="dto.entityRating != null">entity_rating,</if>
            <if test="dto.bondRating != null">bond_rating,</if>
            <if test="dto.isDel != null">is_del,</if>
            <if test="dto.createBy != null">create_by,</if>
            <if test="dto.createTime != null">create_time,</if>
            <if test="dto.updateBy != null">update_by,</if>
            <if test="dto.updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dto.accountingPeriod != null and dto.accountingPeriod != ''">#{dto.accountingPeriod},</if>
            <if test="dto.securityCode != null and dto.securityCode != ''">#{dto.securityCode},</if>
            <if test="dto.securityName != null and dto.securityName != ''">#{dto.securityName},</if>
            <if test="dto.entityRating != null">#{dto.entityRating},</if>
            <if test="dto.bondRating != null">#{dto.bondRating},</if>
            <if test="dto.isDel != null">#{dto.isDel},</if>
            <if test="dto.createBy != null">#{dto.createBy},</if>
            <if test="dto.createTime != null">#{dto.createTime},</if>
            <if test="dto.updateBy != null">#{dto.updateBy},</if>
            <if test="dto.updateTime != null">#{dto.updateTime},</if>
         </trim>
    </insert>

    <update id="updateWindRatingDto" parameterType="com.xl.alm.app.dto.WindRatingDTO">
        update t_ast_wind_rating
        <trim prefix="SET" suffixOverrides=",">
            <if test="dto.accountingPeriod != null and dto.accountingPeriod != ''">accounting_period = #{dto.accountingPeriod},</if>
            <if test="dto.securityCode != null and dto.securityCode != ''">security_code = #{dto.securityCode},</if>
            <if test="dto.securityName != null and dto.securityName != ''">security_name = #{dto.securityName},</if>
            <if test="dto.entityRating != null">entity_rating = #{dto.entityRating},</if>
            <if test="dto.bondRating != null">bond_rating = #{dto.bondRating},</if>
            <if test="dto.isDel != null">is_del = #{dto.isDel},</if>
            <if test="dto.createBy != null">create_by = #{dto.createBy},</if>
            <if test="dto.createTime != null">create_time = #{dto.createTime},</if>
            <if test="dto.updateBy != null">update_by = #{dto.updateBy},</if>
            <if test="dto.updateTime != null">update_time = #{dto.updateTime},</if>
        </trim>
        where id = #{dto.id}
    </update>

    <delete id="deleteWindRatingDtoById" parameterType="Long">
        delete from t_ast_wind_rating where id = #{id}
    </delete>

    <delete id="deleteWindRatingDtoByIds" parameterType="String">
        delete from t_ast_wind_rating where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWindRatingDtoByPeriod" parameterType="String">
        delete from t_ast_wind_rating where accounting_period = #{accountingPeriod}
    </delete>

    <insert id="batchInsertWindRatingDto" parameterType="java.util.List">
        insert into t_ast_wind_rating(accounting_period, security_code, security_name, entity_rating, bond_rating, is_del, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.securityCode}, #{item.securityName}, #{item.entityRating}, #{item.bondRating}, #{item.isDel}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
