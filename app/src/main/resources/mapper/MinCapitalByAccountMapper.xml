<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.MinCapitalByAccountMapper">

    <resultMap type="com.xl.alm.app.entity.MinCapitalByAccountEntity" id="MinCapitalByAccountEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemCode" column="item_code"/>
        <result property="accountCode" column="account_code"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 关联查询结果映射 -->
    <resultMap type="com.xl.alm.app.dto.MinCapitalByAccountDTO" id="MinCapitalByAccountDTOResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemCode" column="item_code"/>
        <result property="accountCode" column="account_code"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <!-- 关联字段 -->
        <result property="itemName" column="item_name"/>
        <result property="riskType" column="risk_type"/>
        <result property="accountName" column="account_name"/>
    </resultMap>

    <sql id="selectMinCapitalByAccountEntityVo">
        select id, accounting_period, item_code, account_code, amount,
               create_time, create_by, update_time, update_by, is_del
        from t_minc_min_capital_by_account
    </sql>

    <!-- 查询市场及信用最低资本表列表（关联字典表，项目名称直接使用item_code） -->
    <select id="selectMinCapitalByAccountDtoList" parameterType="com.xl.alm.app.query.MinCapitalByAccountQuery" resultMap="MinCapitalByAccountDTOResult">
        select s.id, s.accounting_period, s.item_code, s.account_code, s.amount,
               s.create_time, s.create_by, s.update_time, s.update_by, s.is_del,
               s.item_code as item_name,
               CASE
                   WHEN s.item_code = '市场风险最低资本' THEN '市场风险'
                   WHEN s.item_code = '信用风险最低资本' THEN '信用风险'
                   ELSE d.risk_type
               END as risk_type,
               dict.dict_label as account_name
        from t_minc_min_capital_by_account s
        left join t_minc_item_definition d on s.item_code = d.item_code and d.is_del = 0
        left join sys_dict_data dict on s.account_code = dict.dict_value and dict.dict_type = 'minc_account'
        <where>
            <if test="id != null">and s.id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and s.accounting_period = #{accountingPeriod}</if>
            <if test="itemCode != null and itemCode != ''">and s.item_code like concat('%', #{itemCode}, '%')</if>
            <if test="accountCode != null and accountCode != ''">and s.account_code = #{accountCode}</if>
            <if test="isDel != null">and s.is_del = #{isDel}</if>
            <if test="isDel == null">and s.is_del = 0</if>
            <!-- 只显示传统、分红、万能、普通账户 -->
            and s.account_code in ('AC001', 'AC002', 'AC003', 'AC006')
        </where>
        order by s.create_time desc
    </select>

    <!-- 查询市场及信用最低资本表列表（原始查询，用于内部操作） -->
    <select id="selectMinCapitalByAccountEntityList" parameterType="com.xl.alm.app.query.MinCapitalByAccountQuery" resultMap="MinCapitalByAccountEntityResult">
        <include refid="selectMinCapitalByAccountEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemCode != null and itemCode != ''">and item_code like concat('%', #{itemCode}, '%')</if>
            <if test="accountCode != null and accountCode != ''">and account_code = #{accountCode}</if>
            <if test="isDel != null">and is_del = #{isDel}</if>
            <if test="isDel == null">and is_del = 0</if>
        </where>
        order by create_time desc
    </select>

    <!-- 查询市场及信用最低资本表详情 -->
    <select id="selectMinCapitalByAccountEntityById" parameterType="Long" resultMap="MinCapitalByAccountEntityResult">
        <include refid="selectMinCapitalByAccountEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、项目编码和账户编码查询市场及信用最低资本表详情 -->
    <select id="selectMinCapitalByAccountEntityByUniqueKey" resultMap="MinCapitalByAccountEntityResult">
        <include refid="selectMinCapitalByAccountEntityVo"/>
        where accounting_period = #{accountingPeriod}
          and item_code = #{itemCode}
          and account_code = #{accountCode}
          and (is_del = 0 or is_del is null)
    </select>

    <!-- 根据账期、项目编码和账户编码查询有效的市场及信用最低资本表详情（用于检查） -->
    <select id="selectValidMinCapitalByAccountEntityByUniqueKey" resultMap="MinCapitalByAccountEntityResult">
        <include refid="selectMinCapitalByAccountEntityVo"/>
        where accounting_period = #{accountingPeriod}
          and item_code = #{itemCode}
          and account_code = #{accountCode}
          and COALESCE(is_del, 0) = 0
    </select>

    <!-- 根据账期、项目编码和账户编码查询任何记录（包括已删除的，用于调试） -->
    <select id="selectAnyMinCapitalByAccountEntityByUniqueKey" resultMap="MinCapitalByAccountEntityResult">
        <include refid="selectMinCapitalByAccountEntityVo"/>
        where accounting_period = #{accountingPeriod}
          and item_code = #{itemCode}
          and account_code = #{accountCode}
    </select>

    <!-- 新增市场及信用最低资本表 -->
    <insert id="insertMinCapitalByAccountEntity" parameterType="com.xl.alm.app.entity.MinCapitalByAccountEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_minc_min_capital_by_account (
            accounting_period, item_code, account_code, amount,
            create_by, update_by, is_del
        ) values (
            #{accountingPeriod}, #{itemCode}, #{accountCode}, #{amount},
            #{createBy}, #{updateBy}, COALESCE(#{isDel}, 0)
        )
    </insert>

    <!-- 批量新增市场及信用最低资本表 -->
    <insert id="batchInsertMinCapitalByAccountEntity" parameterType="java.util.List">
        insert into t_minc_min_capital_by_account (
            accounting_period, item_code, account_code, amount,
            create_by, update_by, is_del
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.accountingPeriod}, #{item.itemCode}, #{item.accountCode}, #{item.amount},
            #{item.createBy}, #{item.updateBy}, COALESCE(#{item.isDel}, 0)
            )
        </foreach>
    </insert>

    <!-- 修改市场及信用最低资本表 -->
    <update id="updateMinCapitalByAccountEntity" parameterType="com.xl.alm.app.entity.MinCapitalByAccountEntity">
        update t_minc_min_capital_by_account
        <set>
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="accountCode != null and accountCode != ''">account_code = #{accountCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 删除市场及信用最低资本表 -->
    <delete id="deleteMinCapitalByAccountEntityById" parameterType="Long">
        update t_minc_min_capital_by_account set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量删除市场及信用最低资本表 -->
    <delete id="deleteMinCapitalByAccountEntityByIds" parameterType="String">
        update t_minc_min_capital_by_account set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 物理删除市场及信用最低资本表 -->
    <delete id="physicalDeleteByUniqueKey">
        delete from t_minc_min_capital_by_account
        where accounting_period = #{accountingPeriod}
          and item_code = #{itemCode}
          and account_code = #{accountCode}
    </delete>

</mapper>
