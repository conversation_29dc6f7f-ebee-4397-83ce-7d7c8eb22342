<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.WindYieldCurveMapper">
    
    <resultMap type="com.xl.alm.app.entity.WindYieldCurveEntity" id="WindYieldCurveResult">
        <result property="id"    column="id"    />
        <result property="curveName"    column="curve_name"    />
        <result property="curveId"    column="curve_id"    />
        <result property="date"    column="date"    />
        <result property="term0"    column="term_0"    />
        <result property="term1"    column="term_1"    />
        <result property="term2"    column="term_2"    />
        <result property="term3"    column="term_3"    />
        <result property="term4"    column="term_4"    />
        <result property="term5"    column="term_5"    />
        <result property="term6"    column="term_6"    />
        <result property="term7"    column="term_7"    />
        <result property="term8"    column="term_8"    />
        <result property="term9"    column="term_9"    />
        <result property="term10"    column="term_10"    />
        <result property="term11"    column="term_11"    />
        <result property="term12"    column="term_12"    />
        <result property="term13"    column="term_13"    />
        <result property="term14"    column="term_14"    />
        <result property="term15"    column="term_15"    />
        <result property="term16"    column="term_16"    />
        <result property="term17"    column="term_17"    />
        <result property="term18"    column="term_18"    />
        <result property="term19"    column="term_19"    />
        <result property="term20"    column="term_20"    />
        <result property="term21"    column="term_21"    />
        <result property="term22"    column="term_22"    />
        <result property="term23"    column="term_23"    />
        <result property="term24"    column="term_24"    />
        <result property="term25"    column="term_25"    />
        <result property="term26"    column="term_26"    />
        <result property="term27"    column="term_27"    />
        <result property="term28"    column="term_28"    />
        <result property="term29"    column="term_29"    />
        <result property="term30"    column="term_30"    />
        <result property="term31"    column="term_31"    />
        <result property="term32"    column="term_32"    />
        <result property="term33"    column="term_33"    />
        <result property="term34"    column="term_34"    />
        <result property="term35"    column="term_35"    />
        <result property="term36"    column="term_36"    />
        <result property="term37"    column="term_37"    />
        <result property="term38"    column="term_38"    />
        <result property="term39"    column="term_39"    />
        <result property="term40"    column="term_40"    />
        <result property="term41"    column="term_41"    />
        <result property="term42"    column="term_42"    />
        <result property="term43"    column="term_43"    />
        <result property="term44"    column="term_44"    />
        <result property="term45"    column="term_45"    />
        <result property="term46"    column="term_46"    />
        <result property="term47"    column="term_47"    />
        <result property="term48"    column="term_48"    />
        <result property="term49"    column="term_49"    />
        <result property="term50"    column="term_50"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectWindYieldCurveVo">
        select id, curve_name, curve_id, date, term_0, term_1, term_2, term_3, term_4, term_5, term_6, term_7, term_8, term_9, term_10, term_11, term_12, term_13, term_14, term_15, term_16, term_17, term_18, term_19, term_20, term_21, term_22, term_23, term_24, term_25, term_26, term_27, term_28, term_29, term_30, term_31, term_32, term_33, term_34, term_35, term_36, term_37, term_38, term_39, term_40, term_41, term_42, term_43, term_44, term_45, term_46, term_47, term_48, term_49, term_50, create_by, create_time, update_by, update_time, is_del from t_base_wind_yield_curve
    </sql>

    <select id="selectWindYieldCurveEntityList" parameterType="com.xl.alm.app.query.WindYieldCurveQuery" resultMap="WindYieldCurveResult">
        <include refid="selectWindYieldCurveVo"/>
        <where>  
            <if test="curveName != null  and curveName != ''"> and curve_name like concat('%', #{curveName}, '%')</if>
            <if test="curveId != null  and curveId != ''"> and curve_id = #{curveId}</if>
            <if test="date != null "> and date = #{date}</if>
            <if test="beginDate != null and beginDate != ''"> and date &gt;= #{beginDate}</if>
            <if test="endDate != null and endDate != ''"> and date &lt;= #{endDate}</if>
            and is_del = #{isDel}
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWindYieldCurveEntityById" parameterType="Long" resultMap="WindYieldCurveResult">
        <include refid="selectWindYieldCurveVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectWindYieldCurveEntityByCondition" resultMap="WindYieldCurveResult">
        <include refid="selectWindYieldCurveVo"/>
        where curve_name = #{curveName} and curve_id = #{curveId} and date = #{date} and is_del = 0
    </select>
        
    <insert id="insertWindYieldCurveEntity" parameterType="com.xl.alm.app.entity.WindYieldCurveEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_base_wind_yield_curve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="curveName != null and curveName != ''">curve_name,</if>
            <if test="curveId != null and curveId != ''">curve_id,</if>
            <if test="date != null">date,</if>
            <if test="term0 != null">term_0,</if>
            <if test="term1 != null">term_1,</if>
            <if test="term2 != null">term_2,</if>
            <if test="term3 != null">term_3,</if>
            <if test="term4 != null">term_4,</if>
            <if test="term5 != null">term_5,</if>
            <if test="term6 != null">term_6,</if>
            <if test="term7 != null">term_7,</if>
            <if test="term8 != null">term_8,</if>
            <if test="term9 != null">term_9,</if>
            <if test="term10 != null">term_10,</if>
            <if test="term11 != null">term_11,</if>
            <if test="term12 != null">term_12,</if>
            <if test="term13 != null">term_13,</if>
            <if test="term14 != null">term_14,</if>
            <if test="term15 != null">term_15,</if>
            <if test="term16 != null">term_16,</if>
            <if test="term17 != null">term_17,</if>
            <if test="term18 != null">term_18,</if>
            <if test="term19 != null">term_19,</if>
            <if test="term20 != null">term_20,</if>
            <if test="term21 != null">term_21,</if>
            <if test="term22 != null">term_22,</if>
            <if test="term23 != null">term_23,</if>
            <if test="term24 != null">term_24,</if>
            <if test="term25 != null">term_25,</if>
            <if test="term26 != null">term_26,</if>
            <if test="term27 != null">term_27,</if>
            <if test="term28 != null">term_28,</if>
            <if test="term29 != null">term_29,</if>
            <if test="term30 != null">term_30,</if>
            <if test="term31 != null">term_31,</if>
            <if test="term32 != null">term_32,</if>
            <if test="term33 != null">term_33,</if>
            <if test="term34 != null">term_34,</if>
            <if test="term35 != null">term_35,</if>
            <if test="term36 != null">term_36,</if>
            <if test="term37 != null">term_37,</if>
            <if test="term38 != null">term_38,</if>
            <if test="term39 != null">term_39,</if>
            <if test="term40 != null">term_40,</if>
            <if test="term41 != null">term_41,</if>
            <if test="term42 != null">term_42,</if>
            <if test="term43 != null">term_43,</if>
            <if test="term44 != null">term_44,</if>
            <if test="term45 != null">term_45,</if>
            <if test="term46 != null">term_46,</if>
            <if test="term47 != null">term_47,</if>
            <if test="term48 != null">term_48,</if>
            <if test="term49 != null">term_49,</if>
            <if test="term50 != null">term_50,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="curveName != null and curveName != ''">#{curveName},</if>
            <if test="curveId != null and curveId != ''">#{curveId},</if>
            <if test="date != null">#{date},</if>
            <if test="term0 != null">#{term0},</if>
            <if test="term1 != null">#{term1},</if>
            <if test="term2 != null">#{term2},</if>
            <if test="term3 != null">#{term3},</if>
            <if test="term4 != null">#{term4},</if>
            <if test="term5 != null">#{term5},</if>
            <if test="term6 != null">#{term6},</if>
            <if test="term7 != null">#{term7},</if>
            <if test="term8 != null">#{term8},</if>
            <if test="term9 != null">#{term9},</if>
            <if test="term10 != null">#{term10},</if>
            <if test="term11 != null">#{term11},</if>
            <if test="term12 != null">#{term12},</if>
            <if test="term13 != null">#{term13},</if>
            <if test="term14 != null">#{term14},</if>
            <if test="term15 != null">#{term15},</if>
            <if test="term16 != null">#{term16},</if>
            <if test="term17 != null">#{term17},</if>
            <if test="term18 != null">#{term18},</if>
            <if test="term19 != null">#{term19},</if>
            <if test="term20 != null">#{term20},</if>
            <if test="term21 != null">#{term21},</if>
            <if test="term22 != null">#{term22},</if>
            <if test="term23 != null">#{term23},</if>
            <if test="term24 != null">#{term24},</if>
            <if test="term25 != null">#{term25},</if>
            <if test="term26 != null">#{term26},</if>
            <if test="term27 != null">#{term27},</if>
            <if test="term28 != null">#{term28},</if>
            <if test="term29 != null">#{term29},</if>
            <if test="term30 != null">#{term30},</if>
            <if test="term31 != null">#{term31},</if>
            <if test="term32 != null">#{term32},</if>
            <if test="term33 != null">#{term33},</if>
            <if test="term34 != null">#{term34},</if>
            <if test="term35 != null">#{term35},</if>
            <if test="term36 != null">#{term36},</if>
            <if test="term37 != null">#{term37},</if>
            <if test="term38 != null">#{term38},</if>
            <if test="term39 != null">#{term39},</if>
            <if test="term40 != null">#{term40},</if>
            <if test="term41 != null">#{term41},</if>
            <if test="term42 != null">#{term42},</if>
            <if test="term43 != null">#{term43},</if>
            <if test="term44 != null">#{term44},</if>
            <if test="term45 != null">#{term45},</if>
            <if test="term46 != null">#{term46},</if>
            <if test="term47 != null">#{term47},</if>
            <if test="term48 != null">#{term48},</if>
            <if test="term49 != null">#{term49},</if>
            <if test="term50 != null">#{term50},</if>
         </trim>
    </insert>

    <insert id="batchInsertWindYieldCurveEntity" parameterType="java.util.List">
        insert into t_base_wind_yield_curve(curve_name, curve_id, date, term_0, term_1, term_2, term_3, term_4, term_5, term_6, term_7, term_8, term_9, term_10, term_11, term_12, term_13, term_14, term_15, term_16, term_17, term_18, term_19, term_20, term_21, term_22, term_23, term_24, term_25, term_26, term_27, term_28, term_29, term_30, term_31, term_32, term_33, term_34, term_35, term_36, term_37, term_38, term_39, term_40, term_41, term_42, term_43, term_44, term_45, term_46, term_47, term_48, term_49, term_50) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.curveName}, #{item.curveId}, #{item.date}, #{item.term0}, #{item.term1}, #{item.term2}, #{item.term3}, #{item.term4}, #{item.term5}, #{item.term6}, #{item.term7}, #{item.term8}, #{item.term9}, #{item.term10}, #{item.term11}, #{item.term12}, #{item.term13}, #{item.term14}, #{item.term15}, #{item.term16}, #{item.term17}, #{item.term18}, #{item.term19}, #{item.term20}, #{item.term21}, #{item.term22}, #{item.term23}, #{item.term24}, #{item.term25}, #{item.term26}, #{item.term27}, #{item.term28}, #{item.term29}, #{item.term30}, #{item.term31}, #{item.term32}, #{item.term33}, #{item.term34}, #{item.term35}, #{item.term36}, #{item.term37}, #{item.term38}, #{item.term39}, #{item.term40}, #{item.term41}, #{item.term42}, #{item.term43}, #{item.term44}, #{item.term45}, #{item.term46}, #{item.term47}, #{item.term48}, #{item.term49}, #{item.term50})
        </foreach>
    </insert>

    <update id="updateWindYieldCurveEntity" parameterType="com.xl.alm.app.entity.WindYieldCurveEntity">
        update t_base_wind_yield_curve
        <trim prefix="SET" suffixOverrides=",">
            <if test="curveName != null and curveName != ''">curve_name = #{curveName},</if>
            <if test="curveId != null and curveId != ''">curve_id = #{curveId},</if>
            <if test="date != null">date = #{date},</if>
            <if test="term0 != null">term_0 = #{term0},</if>
            <if test="term1 != null">term_1 = #{term1},</if>
            <if test="term2 != null">term_2 = #{term2},</if>
            <if test="term3 != null">term_3 = #{term3},</if>
            <if test="term4 != null">term_4 = #{term4},</if>
            <if test="term5 != null">term_5 = #{term5},</if>
            <if test="term6 != null">term_6 = #{term6},</if>
            <if test="term7 != null">term_7 = #{term7},</if>
            <if test="term8 != null">term_8 = #{term8},</if>
            <if test="term9 != null">term_9 = #{term9},</if>
            <if test="term10 != null">term_10 = #{term10},</if>
            <if test="term11 != null">term_11 = #{term11},</if>
            <if test="term12 != null">term_12 = #{term12},</if>
            <if test="term13 != null">term_13 = #{term13},</if>
            <if test="term14 != null">term_14 = #{term14},</if>
            <if test="term15 != null">term_15 = #{term15},</if>
            <if test="term16 != null">term_16 = #{term16},</if>
            <if test="term17 != null">term_17 = #{term17},</if>
            <if test="term18 != null">term_18 = #{term18},</if>
            <if test="term19 != null">term_19 = #{term19},</if>
            <if test="term20 != null">term_20 = #{term20},</if>
            <if test="term21 != null">term_21 = #{term21},</if>
            <if test="term22 != null">term_22 = #{term22},</if>
            <if test="term23 != null">term_23 = #{term23},</if>
            <if test="term24 != null">term_24 = #{term24},</if>
            <if test="term25 != null">term_25 = #{term25},</if>
            <if test="term26 != null">term_26 = #{term26},</if>
            <if test="term27 != null">term_27 = #{term27},</if>
            <if test="term28 != null">term_28 = #{term28},</if>
            <if test="term29 != null">term_29 = #{term29},</if>
            <if test="term30 != null">term_30 = #{term30},</if>
            <if test="term31 != null">term_31 = #{term31},</if>
            <if test="term32 != null">term_32 = #{term32},</if>
            <if test="term33 != null">term_33 = #{term33},</if>
            <if test="term34 != null">term_34 = #{term34},</if>
            <if test="term35 != null">term_35 = #{term35},</if>
            <if test="term36 != null">term_36 = #{term36},</if>
            <if test="term37 != null">term_37 = #{term37},</if>
            <if test="term38 != null">term_38 = #{term38},</if>
            <if test="term39 != null">term_39 = #{term39},</if>
            <if test="term40 != null">term_40 = #{term40},</if>
            <if test="term41 != null">term_41 = #{term41},</if>
            <if test="term42 != null">term_42 = #{term42},</if>
            <if test="term43 != null">term_43 = #{term43},</if>
            <if test="term44 != null">term_44 = #{term44},</if>
            <if test="term45 != null">term_45 = #{term45},</if>
            <if test="term46 != null">term_46 = #{term46},</if>
            <if test="term47 != null">term_47 = #{term47},</if>
            <if test="term48 != null">term_48 = #{term48},</if>
            <if test="term49 != null">term_49 = #{term49},</if>
            <if test="term50 != null">term_50 = #{term50},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWindYieldCurveEntityById" parameterType="Long">
        update t_base_wind_yield_curve set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteWindYieldCurveEntityByIds">
        update t_base_wind_yield_curve set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
