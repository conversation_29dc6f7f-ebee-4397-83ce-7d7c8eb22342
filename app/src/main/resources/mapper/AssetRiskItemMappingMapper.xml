<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetRiskItemMappingMapper">

    <resultMap type="com.xl.alm.app.entity.AssetRiskItemMappingEntity" id="AssetRiskItemMappingResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="fiveLevelStatisticsFlag" column="five_level_statistics_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetRiskItemMappingVo">
        select id, accounting_period, item_name, five_level_statistics_flag, create_time, create_by, update_time, update_by, is_del
        from t_acm_asset_risk_item_mapping
    </sql>

    <!-- 查询保险资产风险项目映射表列表 -->
    <select id="selectAssetRiskItemMappingList" parameterType="com.xl.alm.app.query.AssetRiskItemMappingQuery" resultMap="AssetRiskItemMappingResult">
        <include refid="selectAssetRiskItemMappingVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">
                and five_level_statistics_flag = #{fiveLevelStatisticsFlag}
            </if>
        </where>
        order by accounting_period desc, item_name, five_level_statistics_flag
    </select>

    <!-- 用id查询保险资产风险项目映射表 -->
    <select id="selectAssetRiskItemMappingById" parameterType="Long" resultMap="AssetRiskItemMappingResult">
        <include refid="selectAssetRiskItemMappingVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询保险资产风险项目映射表 -->
    <select id="selectAssetRiskItemMappingByCondition" resultMap="AssetRiskItemMappingResult">
        <include refid="selectAssetRiskItemMappingVo"/>
        where accounting_period = #{accountingPeriod}
        and item_name = #{itemName}
        and five_level_statistics_flag = #{fiveLevelStatisticsFlag}
        and is_del = 0
    </select>

    <!-- 新增保险资产风险项目映射表 -->
    <insert id="insertAssetRiskItemMapping" parameterType="com.xl.alm.app.entity.AssetRiskItemMappingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_asset_risk_item_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">#{fiveLevelStatisticsFlag},</if>
        </trim>
    </insert>

    <!-- 修改保险资产风险项目映射表 -->
    <update id="updateAssetRiskItemMapping" parameterType="com.xl.alm.app.entity.AssetRiskItemMappingEntity">
        update t_acm_asset_risk_item_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="fiveLevelStatisticsFlag != null and fiveLevelStatisticsFlag != ''">five_level_statistics_flag = #{fiveLevelStatisticsFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量删除保险资产风险项目映射表 -->
    <delete id="deleteAssetRiskItemMappingByIds" parameterType="String">
        update t_acm_asset_risk_item_mapping set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除保险资产风险项目映射表信息 -->
    <delete id="deleteAssetRiskItemMappingById" parameterType="Long">
        update t_acm_asset_risk_item_mapping set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量插入保险资产风险项目映射表数据 -->
    <insert id="batchInsertAssetRiskItemMapping" parameterType="java.util.List">
        insert into t_acm_asset_risk_item_mapping
        (accounting_period, item_name, five_level_statistics_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.fiveLevelStatisticsFlag})
        </foreach>
    </insert>

    <!-- 删除指定账期的保险资产风险项目映射表数据 -->
    <delete id="deleteAssetRiskItemMappingByPeriod" parameterType="String">
        update t_acm_asset_risk_item_mapping set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
