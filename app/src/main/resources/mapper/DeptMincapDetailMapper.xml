<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.DeptMincapDetailMapper">

    <resultMap type="com.xl.alm.app.entity.DeptMincapDetailEntity" id="DeptMincapDetailEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="department" column="department"/>
        <result property="itemCode" column="item_code"/>
        <result property="accountCode" column="account_code"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectDeptMincapDetailEntityVo">
        select id, accounting_period, department, item_code, account_code, amount,
               create_time, create_by, update_time, update_by, is_del
        from t_minc_dept_mincap_detail
    </sql>

    <!-- 查询分部门最低资本明细列表 -->
    <select id="selectDeptMincapDetailEntityList" parameterType="com.xl.alm.app.query.DeptMincapDetailQuery" resultMap="DeptMincapDetailEntityResult">
        <include refid="selectDeptMincapDetailEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="department != null and department != ''">and department like concat('%', #{department}, '%')</if>
            <if test="itemCode != null and itemCode != ''">
                and (item_code like concat('%', #{itemCode}, '%')
                     or item_code IN (
                         SELECT dict_value FROM sys_dict_data
                         WHERE dict_type = 'minc_risk_item'
                         AND dict_label like concat('%', #{itemCode}, '%')
                         AND status = '0'
                     ))
            </if>
            <if test="accountCode != null and accountCode != ''">and account_code = #{accountCode}</if>
            <if test="isDel != null">and is_del = #{isDel}</if>
            <if test="isDel == null">and is_del = 0</if>
        </where>
        order by
            -- 首先按部门排序
            department,
            -- 然后按层级深度排序
            CHAR_LENGTH(item_code) - CHAR_LENGTH(REPLACE(item_code, '_', '')) + 1,
            -- 再按风险类型前缀排序
            CASE SUBSTRING(item_code, 1, 2)
                WHEN 'NR' THEN 1
                WHEN 'MR' THEN 2
                WHEN 'CR' THEN 3
                WHEN 'IR' THEN 4
                WHEN 'OR' THEN 5
                WHEN 'LR' THEN 6
                ELSE 99
            END,
            -- 按项目编码排序
            item_code,
            -- 最后按账户编码排序
            account_code
    </select>

    <!-- 查询分部门最低资本明细详情 -->
    <select id="selectDeptMincapDetailEntityById" parameterType="Long" resultMap="DeptMincapDetailEntityResult">
        <include refid="selectDeptMincapDetailEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 新增分部门最低资本明细 -->
    <insert id="insertDeptMincapDetailEntity" parameterType="com.xl.alm.app.entity.DeptMincapDetailEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_minc_dept_mincap_detail (
            accounting_period, department, item_code, account_code, amount,
            create_by, update_by
        ) values (
            #{accountingPeriod}, #{department}, #{itemCode}, #{accountCode}, #{amount},
            #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 批量新增分部门最低资本明细 -->
    <insert id="batchInsertDeptMincapDetailEntity" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into t_minc_dept_mincap_detail (
            accounting_period, department, item_code, account_code, amount,
            create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod}, #{item.department}, #{item.itemCode}, #{item.accountCode}, #{item.amount},
            #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 修改分部门最低资本明细 -->
    <update id="updateDeptMincapDetailEntity" parameterType="com.xl.alm.app.entity.DeptMincapDetailEntity">
        update t_minc_dept_mincap_detail
        <set>
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="accountCode != null and accountCode != ''">account_code = #{accountCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where id = #{id} and is_del = 0
    </update>

    <!-- 删除分部门最低资本明细 -->
    <update id="deleteDeptMincapDetailEntityById" parameterType="Long">
        update t_minc_dept_mincap_detail set is_del = 1, update_time = now()
        where id = #{id} and is_del = 0
    </update>

    <!-- 批量删除分部门最低资本明细 -->
    <update id="deleteDeptMincapDetailEntityByIds" parameterType="Long">
        update t_minc_dept_mincap_detail set is_del = 1, update_time = now()
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and is_del = 0
    </update>

    <!-- 根据账期删除分部门最低资本明细 -->
    <update id="deleteDeptMincapDetailEntityByPeriod" parameterType="String">
        update t_minc_dept_mincap_detail set is_del = 1, update_time = now()
        where accounting_period = #{accountingPeriod} and is_del = 0
    </update>

    <!-- 逻辑删除分部门最低资本明细 -->
    <update id="logicDeleteDeptMincapDetailEntity">
        update t_minc_dept_mincap_detail set is_del = 1, update_time = now()
        where accounting_period = #{accountingPeriod}
        and department = #{department}
        and item_code = #{itemCode}
        and is_del = 0
    </update>

    <!-- 物理删除分部门最低资本明细 -->
    <delete id="physicalDeleteDeptMincapDetailEntity">
        delete from t_minc_dept_mincap_detail
        where accounting_period = #{accountingPeriod}
        and department = #{department}
        and item_code = #{itemCode}
    </delete>

    <!-- 根据条件物理删除分部门最低资本明细 -->
    <delete id="physicalDeleteByCondition">
        delete from t_minc_dept_mincap_detail
        where accounting_period = #{accountingPeriod}
        and department = #{department}
        and item_code = #{itemCode}
    </delete>
</mapper>
