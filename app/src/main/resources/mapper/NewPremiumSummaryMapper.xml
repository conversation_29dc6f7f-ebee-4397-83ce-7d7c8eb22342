<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.NewPremiumSummaryMapper">

    <resultMap type="com.xl.alm.app.entity.NewPremiumSummaryEntity" id="NewPremiumSummaryEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="businessCode" column="business_code"/>
        <result property="productName" column="product_name"/>
        <result property="designType" column="design_type"/>
        <result property="shortTermFlag" column="short_term_flag"/>
        <result property="termType" column="term_type"/>
        <result property="paymentFrequency" column="payment_frequency"/>
        <result property="paymentPeriod" column="payment_period"/>
        <result property="paymentPeriodCategory" column="payment_period_category"/>
        <result property="originalPremium" column="original_premium"/>
        <result property="initialFee" column="initial_fee"/>
        <result property="newPremiumTotal" column="new_premium_total"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectNewPremiumSummaryEntityVo">
        select id, accounting_period, business_code, product_name, design_type, short_term_flag, term_type,
               payment_frequency, payment_period, payment_period_category, original_premium, initial_fee, 
               new_premium_total, remark, create_time, create_by, update_time, update_by, is_del
        from t_liab_new_premium_summary
    </sql>

    <!-- 查询新单保费汇总列表 -->
    <select id="selectNewPremiumSummaryEntityList" parameterType="com.xl.alm.app.query.NewPremiumSummaryQuery" resultMap="NewPremiumSummaryEntityResult">
        <include refid="selectNewPremiumSummaryEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="businessCode != null and businessCode != ''">and business_code like concat('%', #{businessCode}, '%')</if>
            <if test="productName != null and productName != ''">and product_name like concat('%', #{productName}, '%')</if>
            <if test="designType != null and designType != ''">and design_type = #{designType}</if>
            <if test="shortTermFlag != null and shortTermFlag != ''">and short_term_flag = #{shortTermFlag}</if>
            <if test="termType != null and termType != ''">and term_type = #{termType}</if>
            <if test="paymentFrequency != null and paymentFrequency != ''">and payment_frequency = #{paymentFrequency}</if>
            <if test="paymentPeriod != null">and payment_period = #{paymentPeriod}</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">and payment_period_category like concat('%', #{paymentPeriodCategory}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, business_code asc, payment_frequency asc, payment_period asc
    </select>

    <!-- 根据ID查询新单保费汇总 -->
    <select id="selectNewPremiumSummaryEntityById" parameterType="Long" resultMap="NewPremiumSummaryEntityResult">
        <include refid="selectNewPremiumSummaryEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期、业务代码、缴费频率和缴费年期查询新单保费汇总 -->
    <select id="selectNewPremiumSummaryEntityByCondition" resultMap="NewPremiumSummaryEntityResult">
        <include refid="selectNewPremiumSummaryEntityVo"/>
        where accounting_period = #{accountingPeriod} 
        and business_code = #{businessCode}
        and payment_frequency = #{paymentFrequency}
        and payment_period = #{paymentPeriod}
        and is_del = 0
    </select>

    <!-- 新增新单保费汇总 -->
    <insert id="insertNewPremiumSummaryEntity" parameterType="com.xl.alm.app.entity.NewPremiumSummaryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_liab_new_premium_summary(
            accounting_period, business_code, product_name, design_type, short_term_flag,
            term_type, payment_frequency, payment_period, payment_period_category,
            original_premium, initial_fee, new_premium_total, remark, create_by, update_by
        ) values (
            #{accountingPeriod},
            #{businessCode},
            #{productName},
            #{designType},
            #{shortTermFlag},
            #{termType},
            #{paymentFrequency},
            #{paymentPeriod},
            #{paymentPeriodCategory},
            #{originalPremium},
            #{initialFee},
            #{newPremiumTotal},
            #{remark},
            #{createBy},
            #{updateBy}
        )
    </insert>

    <!-- 批量新增新单保费汇总 -->
    <insert id="batchInsertNewPremiumSummaryEntity" parameterType="java.util.List">
        insert into t_liab_new_premium_summary(accounting_period, business_code, product_name, design_type, short_term_flag, 
        term_type, payment_frequency, payment_period, payment_period_category, original_premium, initial_fee, 
        new_premium_total, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.businessCode}, #{item.productName}, #{item.designType}, #{item.shortTermFlag}, 
            #{item.termType}, #{item.paymentFrequency}, #{item.paymentPeriod}, #{item.paymentPeriodCategory}, 
            #{item.originalPremium}, #{item.initialFee}, #{item.newPremiumTotal}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改新单保费汇总 -->
    <update id="updateNewPremiumSummaryEntity" parameterType="com.xl.alm.app.entity.NewPremiumSummaryEntity">
        update t_liab_new_premium_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="businessCode != null and businessCode != ''">business_code = #{businessCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="shortTermFlag != null and shortTermFlag != ''">short_term_flag = #{shortTermFlag},</if>
            <if test="termType != null and termType != ''">term_type = #{termType},</if>
            <if test="paymentFrequency != null and paymentFrequency != ''">payment_frequency = #{paymentFrequency},</if>
            <if test="paymentPeriod != null">payment_period = #{paymentPeriod},</if>
            <if test="paymentPeriodCategory != null and paymentPeriodCategory != ''">payment_period_category = #{paymentPeriodCategory},</if>
            <if test="originalPremium != null">original_premium = #{originalPremium},</if>
            <if test="initialFee != null">initial_fee = #{initialFee},</if>
            <if test="newPremiumTotal != null">new_premium_total = #{newPremiumTotal},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除新单保费汇总 -->
    <delete id="deleteNewPremiumSummaryEntityById" parameterType="Long">
        update t_liab_new_premium_summary set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量删除新单保费汇总 -->
    <delete id="deleteNewPremiumSummaryEntityByIds" parameterType="String">
        update t_liab_new_premium_summary set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
