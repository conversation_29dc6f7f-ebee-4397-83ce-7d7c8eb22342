<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AccountNameMapMapper">

    <resultMap type="com.xl.alm.app.entity.AccountNameMapEntity" id="AccountNameMapResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNameMapping" column="account_name_mapping"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <resultMap type="com.xl.alm.app.dto.AccountNameMapDTO" id="AccountNameMapDTOResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNameMapping" column="account_name_mapping"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAccountNameMapVo">
        select id, accounting_period, account_name, account_name_mapping,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_account_name_map
    </sql>

    <select id="selectAccountNameMapEntityList" parameterType="com.xl.alm.app.query.AccountNameMapQuery" resultMap="AccountNameMapResult">
        <include refid="selectAccountNameMapVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="accountNameMapping != null and accountNameMapping != ''">and account_name_mapping = #{accountNameMapping}</if>
            <if test="isDel != null">and is_del = #{isDel}</if>
        </where>
        order by accounting_period desc, account_name
    </select>

    <select id="selectAccountNameMapDtoList" parameterType="com.xl.alm.app.query.AccountNameMapQuery" resultMap="AccountNameMapDTOResult">
        <include refid="selectAccountNameMapVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null and accountName != ''">and account_name like concat('%', #{accountName}, '%')</if>
            <if test="accountNameMapping != null and accountNameMapping != ''">and account_name_mapping = #{accountNameMapping}</if>
            <if test="isDel != null">and is_del = #{isDel}</if>
        </where>
        order by accounting_period desc, account_name
    </select>

    <select id="selectAccountNameMapEntityById" parameterType="Long" resultMap="AccountNameMapResult">
        <include refid="selectAccountNameMapVo"/>
        where id = #{id}
    </select>

    <insert id="insertAccountNameMapEntity" parameterType="com.xl.alm.app.entity.AccountNameMapEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_account_name_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="accountNameMapping != null and accountNameMapping != ''">account_name_mapping,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="accountNameMapping != null and accountNameMapping != ''">#{accountNameMapping},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAccountNameMapEntity" parameterType="com.xl.alm.app.entity.AccountNameMapEntity">
        update t_ast_account_name_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="accountNameMapping != null and accountNameMapping != ''">account_name_mapping = #{accountNameMapping},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountNameMapEntityById" parameterType="Long">
        update t_ast_account_name_map set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAccountNameMapEntityByIds" parameterType="String">
        update t_ast_account_name_map set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAccountNameMapEntity" parameterType="java.util.List">
        insert into t_ast_account_name_map (accounting_period, account_name, account_name_mapping,
                                          create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.accountName}, #{item.accountNameMapping},
             #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_account_name_map
        where accounting_period = #{accountingPeriod}
          and account_name = #{accountName}
          and is_del = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
