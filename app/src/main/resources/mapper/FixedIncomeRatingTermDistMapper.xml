<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FixedIncomeRatingTermDistMapper">

    <resultMap type="com.xl.alm.app.entity.FixedIncomeRatingTermDistEntity" id="FixedIncomeRatingTermDistResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="creditRatingCategory" column="credit_rating_category"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFixedIncomeRatingTermDistVo">
        select id, accounting_period, domestic_foreign, credit_rating_category, fixed_income_term_category, book_balance, create_time, create_by, update_time, update_by, is_del
        from t_acm_fixed_income_rating_term_dist
    </sql>

    <!-- 查询固定收益类投资资产外部评级剩余期限分布表列表 -->
    <select id="selectFixedIncomeRatingTermDistList" parameterType="com.xl.alm.app.query.FixedIncomeRatingTermDistQuery" resultMap="FixedIncomeRatingTermDistResult">
        <include refid="selectFixedIncomeRatingTermDistVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="domesticForeign != null and domesticForeign != ''">
                and domestic_foreign = #{domesticForeign}
            </if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">
                and credit_rating_category = #{creditRatingCategory}
            </if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">
                and fixed_income_term_category = #{fixedIncomeTermCategory}
            </if>
        </where>
        order by accounting_period desc, domestic_foreign, credit_rating_category, fixed_income_term_category
    </select>

    <!-- 用id查询固定收益类投资资产外部评级剩余期限分布表 -->
    <select id="selectFixedIncomeRatingTermDistById" parameterType="Long" resultMap="FixedIncomeRatingTermDistResult">
        <include refid="selectFixedIncomeRatingTermDistVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据条件查询固定收益类投资资产外部评级剩余期限分布表 -->
    <select id="selectFixedIncomeRatingTermDistByCondition" resultMap="FixedIncomeRatingTermDistResult">
        <include refid="selectFixedIncomeRatingTermDistVo"/>
        where accounting_period = #{accountingPeriod}
        and domestic_foreign = #{domesticForeign}
        and credit_rating_category = #{creditRatingCategory}
        and fixed_income_term_category = #{fixedIncomeTermCategory}
        and is_del = 0
    </select>

    <!-- 新增固定收益类投资资产外部评级剩余期限分布表 -->
    <insert id="insertFixedIncomeRatingTermDist" parameterType="com.xl.alm.app.entity.FixedIncomeRatingTermDistEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_acm_fixed_income_rating_term_dist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">credit_rating_category,</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category,</if>
            <if test="bookBalance != null">book_balance,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">#{creditRatingCategory},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">#{fixedIncomeTermCategory},</if>
            <if test="bookBalance != null">#{bookBalance},</if>
        </trim>
    </insert>

    <!-- 修改固定收益类投资资产外部评级剩余期限分布表 -->
    <update id="updateFixedIncomeRatingTermDist" parameterType="com.xl.alm.app.entity.FixedIncomeRatingTermDistEntity">
        update t_acm_fixed_income_rating_term_dist
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="creditRatingCategory != null and creditRatingCategory != ''">credit_rating_category = #{creditRatingCategory},</if>
            <if test="fixedIncomeTermCategory != null and fixedIncomeTermCategory != ''">fixed_income_term_category = #{fixedIncomeTermCategory},</if>
            <if test="bookBalance != null">book_balance = #{bookBalance},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量删除固定收益类投资资产外部评级剩余期限分布表 -->
    <delete id="deleteFixedIncomeRatingTermDistByIds" parameterType="String">
        update t_acm_fixed_income_rating_term_dist set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 删除固定收益类投资资产外部评级剩余期限分布表信息 -->
    <delete id="deleteFixedIncomeRatingTermDistById" parameterType="Long">
        update t_acm_fixed_income_rating_term_dist set is_del = 1 where id = #{id}
    </delete>

    <!-- 批量插入固定收益类投资资产外部评级剩余期限分布表数据 -->
    <insert id="batchInsertFixedIncomeRatingTermDist" parameterType="java.util.List">
        insert into t_acm_fixed_income_rating_term_dist
        (accounting_period, domestic_foreign, credit_rating_category, fixed_income_term_category, book_balance)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.creditRatingCategory}, #{item.fixedIncomeTermCategory}, #{item.bookBalance})
        </foreach>
    </insert>

    <!-- 删除指定账期的固定收益类投资资产外部评级剩余期限分布表数据 -->
    <delete id="deleteFixedIncomeRatingTermDistByPeriod" parameterType="String">
        update t_acm_fixed_income_rating_term_dist set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
