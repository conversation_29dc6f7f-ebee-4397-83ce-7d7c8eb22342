<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.BusinessCashFlowForecastMapper">

    <resultMap type="com.xl.alm.app.entity.BusinessCashFlowForecastEntity" id="BusinessCashFlowForecastResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="scenarioName" column="scenario_name"/>
        <result property="designType" column="design_type"/>
        <result property="businessType" column="business_type"/>
        <result property="item" column="item"/>
        <result property="futureFirstQuarter" column="future_first_quarter"/>
        <result property="futureSecondQuarter" column="future_second_quarter"/>
        <result property="futureThirdQuarter" column="future_third_quarter"/>
        <result property="futureFourthQuarter" column="future_fourth_quarter"/>
        <result property="futureSecondYearRemainingQuarters" column="future_second_year_remaining_quarters"/>
        <result property="futureThirdYear" column="future_third_year"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectBusinessCashFlowForecastVo">
        select id, accounting_period, scenario_name, design_type, business_type, item, 
               future_first_quarter, future_second_quarter, future_third_quarter, future_fourth_quarter, 
               future_second_year_remaining_quarters, future_third_year, 
               create_by, create_time, update_by, update_time, is_del
        from t_cft_business_cash_flow_forecast
    </sql>

    <select id="selectBusinessCashFlowForecastEntityList" parameterType="com.xl.alm.app.query.BusinessCashFlowForecastQuery" resultMap="BusinessCashFlowForecastResult">
        <include refid="selectBusinessCashFlowForecastVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="scenarioName != null and scenarioName != ''">
                and scenario_name = #{scenarioName}
            </if>
            <if test="designType != null and designType != ''">
                and design_type = #{designType}
            </if>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="item != null and item != ''">
                and item = #{item}
            </if>
        </where>
        order by accounting_period desc, scenario_name, design_type, business_type, item
    </select>

    <select id="selectBusinessCashFlowForecastEntityById" parameterType="Long" resultMap="BusinessCashFlowForecastResult">
        <include refid="selectBusinessCashFlowForecastVo"/>
        where id = #{id} and is_del = 0
    </select>

    <select id="selectBusinessCashFlowForecastEntityByCondition" resultMap="BusinessCashFlowForecastResult">
        <include refid="selectBusinessCashFlowForecastVo"/>
        where accounting_period = #{accountingPeriod}
          and scenario_name = #{scenarioName}
          and design_type = #{designType}
          and business_type = #{businessType}
          and item = #{item}
          and is_del = 0
    </select>

    <insert id="insertBusinessCashFlowForecastEntity" parameterType="com.xl.alm.app.entity.BusinessCashFlowForecastEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_cft_business_cash_flow_forecast
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name,</if>
            <if test="designType != null and designType != ''">design_type,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="item != null and item != ''">item,</if>
            <if test="futureFirstQuarter != null">future_first_quarter,</if>
            <if test="futureSecondQuarter != null">future_second_quarter,</if>
            <if test="futureThirdQuarter != null">future_third_quarter,</if>
            <if test="futureFourthQuarter != null">future_fourth_quarter,</if>
            <if test="futureSecondYearRemainingQuarters != null">future_second_year_remaining_quarters,</if>
            <if test="futureThirdYear != null">future_third_year,</if>
            <if test="createBy != null">create_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">#{scenarioName},</if>
            <if test="designType != null and designType != ''">#{designType},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="item != null and item != ''">#{item},</if>
            <if test="futureFirstQuarter != null">#{futureFirstQuarter},</if>
            <if test="futureSecondQuarter != null">#{futureSecondQuarter},</if>
            <if test="futureThirdQuarter != null">#{futureThirdQuarter},</if>
            <if test="futureFourthQuarter != null">#{futureFourthQuarter},</if>
            <if test="futureSecondYearRemainingQuarters != null">#{futureSecondYearRemainingQuarters},</if>
            <if test="futureThirdYear != null">#{futureThirdYear},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateBusinessCashFlowForecastEntity" parameterType="com.xl.alm.app.entity.BusinessCashFlowForecastEntity">
        update t_cft_business_cash_flow_forecast
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="scenarioName != null and scenarioName != ''">scenario_name = #{scenarioName},</if>
            <if test="designType != null and designType != ''">design_type = #{designType},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="item != null and item != ''">item = #{item},</if>
            <if test="futureFirstQuarter != null">future_first_quarter = #{futureFirstQuarter},</if>
            <if test="futureSecondQuarter != null">future_second_quarter = #{futureSecondQuarter},</if>
            <if test="futureThirdQuarter != null">future_third_quarter = #{futureThirdQuarter},</if>
            <if test="futureFourthQuarter != null">future_fourth_quarter = #{futureFourthQuarter},</if>
            <if test="futureSecondYearRemainingQuarters != null">future_second_year_remaining_quarters = #{futureSecondYearRemainingQuarters},</if>
            <if test="futureThirdYear != null">future_third_year = #{futureThirdYear},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessCashFlowForecastEntityById" parameterType="Long">
        update t_cft_business_cash_flow_forecast set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteBusinessCashFlowForecastEntityByIds" parameterType="String">
        update t_cft_business_cash_flow_forecast set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertBusinessCashFlowForecastEntity" parameterType="java.util.List">
        insert into t_cft_business_cash_flow_forecast(accounting_period, scenario_name, design_type, business_type, item, 
                                                      future_first_quarter, future_second_quarter, future_third_quarter, future_fourth_quarter, 
                                                      future_second_year_remaining_quarters, future_third_year, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.scenarioName}, #{item.designType}, #{item.businessType}, #{item.item}, 
             #{item.futureFirstQuarter}, #{item.futureSecondQuarter}, #{item.futureThirdQuarter}, #{item.futureFourthQuarter}, 
             #{item.futureSecondYearRemainingQuarters}, #{item.futureThirdYear}, #{item.createBy})
        </foreach>
    </insert>

    <delete id="deleteBusinessCashFlowForecastEntityByPeriod" parameterType="String">
        update t_cft_business_cash_flow_forecast set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
