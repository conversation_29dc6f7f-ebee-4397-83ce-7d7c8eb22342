<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetLiquidityCoeffMapper">
    
    <resultMap type="com.xl.alm.app.entity.AssetLiquidityCoeffEntity" id="AssetLiquidityCoeffResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="assetSubSubCategory"    column="asset_sub_sub_category"    />
        <result property="bondType"    column="bond_type"    />
        <result property="accountingClassification"    column="accounting_classification"    />
        <result property="creditRating"    column="credit_rating"    />
        <result property="assetLiquidityCategory"    column="asset_liquidity_category"    />
        <result property="realizationCoefficient"    column="realization_coefficient"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectAssetLiquidityCoeffVo">
        select id, accounting_period, asset_sub_sub_category, bond_type, accounting_classification, credit_rating, asset_liquidity_category, realization_coefficient, create_time, create_by, update_time, update_by, is_del from t_ast_asset_liquidity_coeff
    </sql>

    <select id="selectAssetLiquidityCoeffList" parameterType="com.xl.alm.app.query.AssetLiquidityCoeffQuery" resultMap="AssetLiquidityCoeffResult">
        <include refid="selectAssetLiquidityCoeffVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="assetSubSubCategory != null  and assetSubSubCategory != ''"> and asset_sub_sub_category = #{assetSubSubCategory}</if>
            <if test="bondType != null  and bondType != ''"> and bond_type = #{bondType}</if>
            <if test="accountingClassification != null  and accountingClassification != ''"> and accounting_classification = #{accountingClassification}</if>
            <if test="creditRating != null  and creditRating != ''"> and credit_rating = #{creditRating}</if>
            <if test="assetLiquidityCategory != null  and assetLiquidityCategory != ''"> and asset_liquidity_category = #{assetLiquidityCategory}</if>
            <if test="realizationCoefficient != null "> and realization_coefficient = #{realizationCoefficient}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>
    
    <select id="selectAssetLiquidityCoeffById" parameterType="Long" resultMap="AssetLiquidityCoeffResult">
        <include refid="selectAssetLiquidityCoeffVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertAssetLiquidityCoeff" parameterType="com.xl.alm.app.entity.AssetLiquidityCoeffEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_asset_liquidity_coeff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="bondType != null">bond_type,</if>
            <if test="accountingClassification != null">accounting_classification,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="assetLiquidityCategory != null and assetLiquidityCategory != ''">asset_liquidity_category,</if>
            <if test="realizationCoefficient != null">realization_coefficient,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="bondType != null">#{bondType},</if>
            <if test="accountingClassification != null">#{accountingClassification},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="assetLiquidityCategory != null and assetLiquidityCategory != ''">#{assetLiquidityCategory},</if>
            <if test="realizationCoefficient != null">#{realizationCoefficient},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAssetLiquidityCoeff" parameterType="com.xl.alm.app.entity.AssetLiquidityCoeffEntity">
        update t_ast_asset_liquidity_coeff
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="bondType != null">bond_type = #{bondType},</if>
            <if test="accountingClassification != null">accounting_classification = #{accountingClassification},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="assetLiquidityCategory != null and assetLiquidityCategory != ''">asset_liquidity_category = #{assetLiquidityCategory},</if>
            <if test="realizationCoefficient != null">realization_coefficient = #{realizationCoefficient},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetLiquidityCoeffById" parameterType="Long">
        update t_ast_asset_liquidity_coeff set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAssetLiquidityCoeffByIds" parameterType="String">
        update t_ast_asset_liquidity_coeff set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAssetLiquidityCoeff" parameterType="java.util.List">
        insert into t_ast_asset_liquidity_coeff(accounting_period, asset_sub_sub_category, bond_type, accounting_classification, credit_rating, asset_liquidity_category, realization_coefficient, create_by, update_by) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.accountingPeriod}, #{item.assetSubSubCategory}, #{item.bondType}, #{item.accountingClassification}, #{item.creditRating}, #{item.assetLiquidityCategory}, #{item.realizationCoefficient}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <delete id="deleteAssetLiquidityCoeffByPeriod" parameterType="String">
        update t_ast_asset_liquidity_coeff set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_asset_liquidity_coeff
        where accounting_period = #{accountingPeriod}
        and asset_sub_sub_category = #{assetSubSubCategory}
        <if test="bondType != null">
            and bond_type = #{bondType}
        </if>
        <if test="bondType == null">
            and bond_type is null
        </if>
        <if test="accountingClassification != null">
            and accounting_classification = #{accountingClassification}
        </if>
        <if test="accountingClassification == null">
            and accounting_classification is null
        </if>
        <if test="creditRating != null">
            and credit_rating = #{creditRating}
        </if>
        <if test="creditRating == null">
            and credit_rating is null
        </if>
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
        and is_del = 0
    </select>

</mapper>
