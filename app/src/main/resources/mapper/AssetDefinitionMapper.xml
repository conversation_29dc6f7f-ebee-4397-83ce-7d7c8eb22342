<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetDefinitionMapper">

    <resultMap type="com.xl.alm.app.entity.AssetDefinitionEntity" id="AssetDefinitionResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="accountName" column="account_name"/>
        <result property="assetName" column="asset_name"/>
        <result property="securityCode" column="security_code"/>
        <result property="assetSubSubCategory" column="asset_sub_sub_category"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="creditRating" column="credit_rating"/>
        <result property="industryCategory" column="industry_category"/>
        <result property="almAssetName" column="alm_asset_name"/>
        <result property="fiveLevelClassification" column="five_level_classification"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetDefinitionVo">
        select id, accounting_period, account_name, asset_name, security_code, asset_sub_sub_category,
               domestic_foreign, credit_rating, industry_category, alm_asset_name, five_level_classification,
               create_time, create_by, update_time, update_by, is_del
        from t_ast_asset_definition
    </sql>

    <select id="selectAssetDefinitionEntityList" parameterType="com.xl.alm.app.query.AssetDefinitionQuery" resultMap="AssetDefinitionResult">
        <include refid="selectAssetDefinitionVo"/>
        <where>
            is_del = 0
            <if test="accountingPeriod != null and accountingPeriod != ''">
                and accounting_period = #{accountingPeriod}
            </if>
            <if test="accountName != null and accountName != ''">
                and account_name like concat('%', #{accountName}, '%')
            </if>
            <if test="assetName != null and assetName != ''">
                and asset_name like concat('%', #{assetName}, '%')
            </if>
            <if test="securityCode != null and securityCode != ''">
                and security_code like concat('%', #{securityCode}, '%')
            </if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">
                and asset_sub_sub_category = #{assetSubSubCategory}
            </if>
            <if test="domesticForeign != null and domesticForeign != ''">
                and domestic_foreign = #{domesticForeign}
            </if>
            <if test="creditRating != null and creditRating != ''">
                and credit_rating = #{creditRating}
            </if>
            <if test="industryCategory != null and industryCategory != ''">
                and industry_category like concat('%', #{industryCategory}, '%')
            </if>
            <if test="almAssetName != null and almAssetName != ''">
                and alm_asset_name like concat('%', #{almAssetName}, '%')
            </if>
            <if test="fiveLevelClassification != null and fiveLevelClassification != ''">
                and five_level_classification = #{fiveLevelClassification}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAssetDefinitionEntityById" parameterType="Long" resultMap="AssetDefinitionResult">
        <include refid="selectAssetDefinitionVo"/>
        where id = #{id} and is_del = 0
    </select>

    <insert id="insertAssetDefinitionEntity" parameterType="com.xl.alm.app.entity.AssetDefinitionEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_asset_definition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="securityCode != null and securityCode != ''">security_code,</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category,</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign,</if>
            <if test="creditRating != null">credit_rating,</if>
            <if test="industryCategory != null">industry_category,</if>
            <if test="almAssetName != null">alm_asset_name,</if>
            <if test="fiveLevelClassification != null">five_level_classification,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="securityCode != null and securityCode != ''">#{securityCode},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">#{assetSubSubCategory},</if>
            <if test="domesticForeign != null and domesticForeign != ''">#{domesticForeign},</if>
            <if test="creditRating != null">#{creditRating},</if>
            <if test="industryCategory != null">#{industryCategory},</if>
            <if test="almAssetName != null">#{almAssetName},</if>
            <if test="fiveLevelClassification != null">#{fiveLevelClassification},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAssetDefinitionEntity" parameterType="com.xl.alm.app.entity.AssetDefinitionEntity">
        update t_ast_asset_definition
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="assetSubSubCategory != null and assetSubSubCategory != ''">asset_sub_sub_category = #{assetSubSubCategory},</if>
            <if test="domesticForeign != null and domesticForeign != ''">domestic_foreign = #{domesticForeign},</if>
            <if test="creditRating != null">credit_rating = #{creditRating},</if>
            <if test="industryCategory != null">industry_category = #{industryCategory},</if>
            <if test="almAssetName != null">alm_asset_name = #{almAssetName},</if>
            <if test="fiveLevelClassification != null">five_level_classification = #{fiveLevelClassification},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetDefinitionEntityById" parameterType="Long">
        update t_ast_asset_definition set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAssetDefinitionEntityByIds" parameterType="Long">
        update t_ast_asset_definition set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAssetDefinitionEntity" parameterType="java.util.List">
        insert into t_ast_asset_definition (accounting_period, account_name, asset_name, security_code, asset_sub_sub_category,
                                          domestic_foreign, credit_rating, industry_category, alm_asset_name, five_level_classification,
                                          create_by, update_by, is_del)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.accountName}, #{item.assetName}, #{item.securityCode}, #{item.assetSubSubCategory},
             #{item.domesticForeign}, #{item.creditRating}, #{item.industryCategory}, #{item.almAssetName}, #{item.fiveLevelClassification},
             #{item.createBy}, #{item.updateBy}, #{item.isDel})
        </foreach>
    </insert>

    <delete id="deleteAssetDefinitionEntityByPeriod" parameterType="String">
        update t_ast_asset_definition set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

    <select id="checkDuplicateRecord" resultType="int">
        select count(1) from t_ast_asset_definition
        where accounting_period = #{accountingPeriod}
          and account_name = #{accountName}
          and asset_name = #{assetName}
          and security_code = #{securityCode}
          and is_del = 0
        <if test="excludeId != null">
            and id != #{excludeId}
        </if>
    </select>

    <select id="selectSecurityCodeByExactMatch" resultType="String">
        SELECT security_code
        FROM t_ast_asset_definition
        WHERE accounting_period = #{accountingPeriod}
          AND account_name = #{accountName}
          AND asset_name = #{assetName}
          AND is_del = 0
        LIMIT 1
    </select>

    <select id="selectSecurityCodeByFuzzyMatch" resultType="String">
        SELECT security_code
        FROM t_ast_asset_definition
        WHERE accounting_period = #{accountingPeriod}
          AND account_name = #{accountName}
          AND REPLACE(REPLACE(REPLACE(asset_name, ' ', ''), '-', ''), '_', '') LIKE CONCAT('%', #{cleanAssetName}, '%')
          AND is_del = 0
        LIMIT 1
    </select>

</mapper>
