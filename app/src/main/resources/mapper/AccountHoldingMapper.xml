<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AccountHoldingMapper">
    
    <resultMap type="com.xl.alm.app.entity.AccountHoldingEntity" id="AccountHoldingResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="accountName"    column="account_name"    />
        <result property="securityCode"    column="security_code"    />
        <result property="assetName"    column="asset_name"    />
        <result property="marketValue"    column="market_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectAccountHoldingVo">
        select id, accounting_period, account_name, security_code, asset_name, market_value, create_by, create_time, update_by, update_time, is_del from t_ast_account_holding
    </sql>

    <select id="selectAccountHoldingEntityList" parameterType="com.xl.alm.app.query.AccountHoldingQuery" resultMap="AccountHoldingResult">
        <include refid="selectAccountHoldingVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period like concat('%', #{accountingPeriod}, '%')</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="securityCode != null  and securityCode != ''"> and security_code like concat('%', #{securityCode}, '%')</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="marketValue != null "> and market_value = #{marketValue}</if>
            and is_del = 0
        </where>
    </select>
    
    <select id="selectAccountHoldingEntityById" parameterType="Long" resultMap="AccountHoldingResult">
        <include refid="selectAccountHoldingVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertAccountHoldingEntity" parameterType="com.xl.alm.app.entity.AccountHoldingEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_ast_account_holding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            security_code,
            <if test="assetName != null and assetName != ''">asset_name,</if>
            <if test="marketValue != null">market_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            #{securityCode},
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="marketValue != null">#{marketValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAccountHoldingEntity" parameterType="com.xl.alm.app.entity.AccountHoldingEntity">
        update t_ast_account_holding
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="securityCode != null and securityCode != ''">security_code = #{securityCode},</if>
            <if test="assetName != null and assetName != ''">asset_name = #{assetName},</if>
            <if test="marketValue != null">market_value = #{marketValue},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountHoldingEntityById" parameterType="Long">
        update t_ast_account_holding set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteAccountHoldingEntityByIds" parameterType="String">
        update t_ast_account_holding set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertAccountHoldingEntity" parameterType="java.util.List">
        insert into t_ast_account_holding(accounting_period, account_name, security_code, asset_name, market_value, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.accountName}, #{item.securityCode}, #{item.assetName}, #{item.marketValue}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <delete id="deleteAccountHoldingEntityByPeriod" parameterType="String">
        update t_ast_account_holding set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

</mapper>
