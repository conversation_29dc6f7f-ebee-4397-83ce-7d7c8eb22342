<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.AssetScaleSolvencyMapper">

    <resultMap type="com.xl.alm.app.entity.AssetScaleSolvencyEntity" id="AssetScaleSolvencyEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="lastYearEnd" column="last_year_end"/>
        <result property="lastQuarterEnd" column="last_quarter_end"/>
        <result property="currentQuarterEnd" column="current_quarter_end"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAssetScaleSolvencyEntityVo">
        select id, accounting_period, item_name, last_year_end, last_quarter_end, current_quarter_end, remark,
               create_time, create_by, update_time, update_by, is_del
        from t_asm_asset_scale_solvency
    </sql>

    <!-- 查询资产规模与偿付能力表列表 -->
    <select id="selectAssetScaleSolvencyEntityList" parameterType="com.xl.alm.app.query.AssetScaleSolvencyQuery" resultMap="AssetScaleSolvencyEntityResult">
        <include refid="selectAssetScaleSolvencyEntityVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="lastYearEnd != null">and last_year_end = #{lastYearEnd}</if>
            <if test="lastQuarterEnd != null">and last_quarter_end = #{lastQuarterEnd}</if>
            <if test="currentQuarterEnd != null">and current_quarter_end = #{currentQuarterEnd}</if>
            <if test="remark != null and remark != ''">and remark like concat('%', #{remark}, '%')</if>
            and is_del = 0
        </where>
        order by accounting_period desc, item_name asc
    </select>

    <!-- 用id查询资产规模与偿付能力表 -->
    <select id="selectAssetScaleSolvencyEntityById" parameterType="Long" resultMap="AssetScaleSolvencyEntityResult">
        <include refid="selectAssetScaleSolvencyEntityVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和项目名称查询资产规模与偿付能力表 -->
    <select id="selectAssetScaleSolvencyEntityByCondition" resultMap="AssetScaleSolvencyEntityResult">
        <include refid="selectAssetScaleSolvencyEntityVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增资产规模与偿付能力表 -->
    <insert id="insertAssetScaleSolvencyEntity" parameterType="com.xl.alm.app.entity.AssetScaleSolvencyEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_asset_scale_solvency
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="lastYearEnd != null">last_year_end,</if>
            <if test="lastQuarterEnd != null">last_quarter_end,</if>
            <if test="currentQuarterEnd != null">current_quarter_end,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="lastYearEnd != null">#{lastYearEnd},</if>
            <if test="lastQuarterEnd != null">#{lastQuarterEnd},</if>
            <if test="currentQuarterEnd != null">#{currentQuarterEnd},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增资产规模与偿付能力表 -->
    <insert id="batchInsertAssetScaleSolvencyEntity" parameterType="java.util.List">
        insert into t_asm_asset_scale_solvency(accounting_period, item_name, last_year_end, last_quarter_end, current_quarter_end, remark, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.lastYearEnd}, #{item.lastQuarterEnd}, #{item.currentQuarterEnd}, #{item.remark}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改资产规模与偿付能力表 -->
    <update id="updateAssetScaleSolvencyEntity" parameterType="com.xl.alm.app.entity.AssetScaleSolvencyEntity">
        update t_asm_asset_scale_solvency
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="lastYearEnd != null">last_year_end = #{lastYearEnd},</if>
            <if test="lastQuarterEnd != null">last_quarter_end = #{lastQuarterEnd},</if>
            <if test="currentQuarterEnd != null">current_quarter_end = #{currentQuarterEnd},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除资产规模与偿付能力表 -->
    <update id="deleteAssetScaleSolvencyEntityById" parameterType="Long">
        update t_asm_asset_scale_solvency set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除资产规模与偿付能力表 -->
    <update id="deleteAssetScaleSolvencyEntityByIds" parameterType="String">
        update t_asm_asset_scale_solvency set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据账期删除资产规模与偿付能力表数据 -->
    <update id="deleteAssetScaleSolvencyEntityByPeriod" parameterType="String">
        update t_asm_asset_scale_solvency set is_del = 1 where accounting_period = #{accountingPeriod}
    </update>

</mapper>
