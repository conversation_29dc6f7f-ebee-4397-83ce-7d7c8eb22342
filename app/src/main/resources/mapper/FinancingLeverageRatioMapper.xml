<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.FinancingLeverageRatioMapper">

    <resultMap type="com.xl.alm.app.entity.FinancingLeverageRatioEntity" id="FinancingLeverageRatioEntityResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="itemName" column="item_name"/>
        <result property="bookValue" column="book_value"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectFinancingLeverageRatioVo">
        select id, accounting_period, item_name, book_value, create_by, create_time, update_by, update_time, is_del from t_asm_financing_leverage_ratio
    </sql>

    <!-- 查询融资杠杆比例表列表 -->
    <select id="selectFinancingLeverageRatioList" parameterType="com.xl.alm.app.query.FinancingLeverageRatioQuery" resultMap="FinancingLeverageRatioEntityResult">
        <include refid="selectFinancingLeverageRatioVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name like concat('%', #{itemName}, '%')</if>
            <if test="bookValue != null">and book_value = #{bookValue}</if>
            and is_del = 0
        </where>
        order by id desc
    </select>

    <!-- 根据id查询融资杠杆比例表 -->
    <select id="selectFinancingLeverageRatioById" parameterType="Long" resultMap="FinancingLeverageRatioEntityResult">
        <include refid="selectFinancingLeverageRatioVo"/>
        where id = #{id} and is_del = 0
    </select>

    <!-- 根据账期和项目名称查询融资杠杆比例表 -->
    <select id="selectFinancingLeverageRatioByCondition" resultMap="FinancingLeverageRatioEntityResult">
        <include refid="selectFinancingLeverageRatioVo"/>
        <where>
            <if test="accountingPeriod != null and accountingPeriod != ''">and accounting_period = #{accountingPeriod}</if>
            <if test="itemName != null and itemName != ''">and item_name = #{itemName}</if>
            and is_del = 0
        </where>
        limit 1
    </select>

    <!-- 新增融资杠杆比例表 -->
    <insert id="insertFinancingLeverageRatio" parameterType="com.xl.alm.app.entity.FinancingLeverageRatioEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_asm_financing_leverage_ratio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="bookValue != null">book_value,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="bookValue != null">#{bookValue},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <!-- 批量新增融资杠杆比例表 -->
    <insert id="batchInsertFinancingLeverageRatio" parameterType="java.util.List">
        insert into t_asm_financing_leverage_ratio(accounting_period, item_name, book_value, create_by, update_by)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.accountingPeriod}, #{item.itemName}, #{item.bookValue}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 修改融资杠杆比例表 -->
    <update id="updateFinancingLeverageRatio" parameterType="com.xl.alm.app.entity.FinancingLeverageRatioEntity">
        update t_asm_financing_leverage_ratio
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="bookValue != null">book_value = #{bookValue},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 删除融资杠杆比例表 -->
    <update id="deleteFinancingLeverageRatioById" parameterType="Long">
        update t_asm_financing_leverage_ratio set is_del = 1 where id = #{id}
    </update>

    <!-- 批量删除融资杠杆比例表 -->
    <update id="deleteFinancingLeverageRatioByIds" parameterType="String">
        update t_asm_financing_leverage_ratio set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
