package com.xl.alm.app.controller;

import com.xl.alm.app.dto.AdurMonthlyDiscountCurveWithSpreadDTO;
import com.xl.alm.app.service.AdurMonthlyDiscountCurveWithSpreadService;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TB0006月度折现曲线表含价差导出功能测试类
 * 测试月度折现曲线利率含价差值集字段的Excel导出展开功能
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class AdurMonthlyDiscountCurveWithSpreadControllerExportTest {

    @MockBean
    private AdurMonthlyDiscountCurveWithSpreadService adurMonthlyDiscountCurveWithSpreadService;

    private AdurMonthlyDiscountCurveWithSpreadController controller;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        controller = new AdurMonthlyDiscountCurveWithSpreadController();
        // 使用反射设置service
        try {
            java.lang.reflect.Field serviceField = AdurMonthlyDiscountCurveWithSpreadController.class.getDeclaredField("adurMonthlyDiscountCurveWithSpreadService");
            serviceField.setAccessible(true);
            serviceField.set(controller, adurMonthlyDiscountCurveWithSpreadService);
        } catch (Exception e) {
            fail("设置service失败: " + e.getMessage());
        }
        
        response = new MockHttpServletResponse();
    }

    @Test
    @DisplayName("测试月度折现曲线利率含价差值集导出功能")
    void testExportWithMonthlyDiscountRateSet() {
        // 准备测试数据
        List<AdurMonthlyDiscountCurveWithSpreadDTO> testData = createTestDataWithRateSet();
        
        // Mock service返回
        when(adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoList(any()))
            .thenReturn(testData);

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                eq(testData),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("monthlyDiscountRateWithSpreadSet")
            ));
        }
    }

    @Test
    @DisplayName("测试月度折现曲线含价差JSON格式解析")
    void testMonthlyDiscountRateWithSpreadJsonParsing() {
        // 创建包含利率JSON的测试数据
        String testRateJson = createTestMonthlyDiscountRateWithSpreadJson();
        
        // 验证JSON格式正确
        assertNotNull(testRateJson);
        assertTrue(testRateJson.contains("\"0\""));
        assertTrue(testRateJson.contains("0.027459"));
        
        System.out.println("测试月度折现曲线含价差利率JSON格式:");
        System.out.println(testRateJson);
    }

    @Test
    @DisplayName("测试字典转换功能")
    void testDictConversion() {
        // 创建包含字典值的测试数据
        List<AdurMonthlyDiscountCurveWithSpreadDTO> testData = createTestDataWithDictValues();
        
        // 验证字典值设置正确
        AdurMonthlyDiscountCurveWithSpreadDTO dto = testData.get(0);
        assertEquals("01", dto.getDurationType()); // 原始字典值
        assertEquals("01", dto.getBasisPointType()); // 原始字典值
        assertEquals("01", dto.getDateType()); // 原始字典值
        assertEquals("01", dto.getSpreadType()); // 原始字典值
        assertEquals("01", dto.getAccountName()); // 原始字典值
        
        System.out.println("测试字典转换前的数据:");
        System.out.println("久期类型: " + dto.getDurationType());
        System.out.println("基点类型: " + dto.getBasisPointType());
        System.out.println("日期类型: " + dto.getDateType());
        System.out.println("价差类型: " + dto.getSpreadType());
        System.out.println("账户名称: " + dto.getAccountName());
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportWithEmptyData() {
        // Mock service返回空列表
        when(adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoList(any()))
            .thenReturn(new ArrayList<>());

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                any(List.class),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("monthlyDiscountRateWithSpreadSet")
            ));
        }
    }

    /**
     * 创建包含利率含价差值集的测试数据
     */
    private List<AdurMonthlyDiscountCurveWithSpreadDTO> createTestDataWithRateSet() {
        List<AdurMonthlyDiscountCurveWithSpreadDTO> testData = new ArrayList<>();
        
        // 创建第一条测试记录
        AdurMonthlyDiscountCurveWithSpreadDTO dto1 = new AdurMonthlyDiscountCurveWithSpreadDTO();
        dto1.setId(1L);
        dto1.setAccountPeriod("202407");
        dto1.setDurationType("修正久期"); // 已转换的中文标签
        dto1.setBasisPointType("0bp"); // 已转换的中文标签
        dto1.setDateType("发行时点"); // 已转换的中文标签
        dto1.setDate(new Date());
        dto1.setSpreadType("发行时点价差"); // 已转换的中文标签
        dto1.setSpread(new BigDecimal("0.001500"));
        dto1.setCurveSubCategory("1");
        dto1.setAssetNumber("ASSET001");
        dto1.setAccountName("传统账户"); // 已转换的中文标签
        dto1.setAssetName("测试资产1");
        dto1.setSecurityCode("SEC001");
        dto1.setCurveId("CURVE001");
        dto1.setMonthlyDiscountRateWithSpreadSet(createTestMonthlyDiscountRateWithSpreadJson());
        
        testData.add(dto1);
        
        // 创建第二条测试记录
        AdurMonthlyDiscountCurveWithSpreadDTO dto2 = new AdurMonthlyDiscountCurveWithSpreadDTO();
        dto2.setId(2L);
        dto2.setAccountPeriod("202407");
        dto2.setDurationType("有效久期"); // 已转换的中文标签
        dto2.setBasisPointType("+50bp"); // 已转换的中文标签
        dto2.setDateType("评估时点"); // 已转换的中文标签
        dto2.setDate(new Date());
        dto2.setSpreadType("评估时点价差"); // 已转换的中文标签
        dto2.setSpread(new BigDecimal("0.002000"));
        dto2.setCurveSubCategory("3");
        dto2.setAssetNumber("ASSET002");
        dto2.setAccountName("分红账户"); // 已转换的中文标签
        dto2.setAssetName("测试资产2");
        dto2.setSecurityCode("SEC002");
        dto2.setCurveId("CURVE002");
        dto2.setMonthlyDiscountRateWithSpreadSet(createTestMonthlyDiscountRateWithSpreadJson2());
        
        testData.add(dto2);
        
        return testData;
    }

    /**
     * 创建包含字典值的测试数据（用于测试字典转换）
     */
    private List<AdurMonthlyDiscountCurveWithSpreadDTO> createTestDataWithDictValues() {
        List<AdurMonthlyDiscountCurveWithSpreadDTO> testData = new ArrayList<>();
        
        AdurMonthlyDiscountCurveWithSpreadDTO dto = new AdurMonthlyDiscountCurveWithSpreadDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202407");
        dto.setDurationType("01"); // 原始字典值
        dto.setBasisPointType("01"); // 原始字典值
        dto.setDateType("01"); // 原始字典值
        dto.setDate(new Date());
        dto.setSpreadType("01"); // 原始字典值
        dto.setSpread(new BigDecimal("0.001500"));
        dto.setCurveSubCategory("1");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("01"); // 原始字典值
        dto.setAssetName("测试资产");
        dto.setSecurityCode("SEC001");
        dto.setCurveId("CURVE001");
        dto.setMonthlyDiscountRateWithSpreadSet(createTestMonthlyDiscountRateWithSpreadJson());
        
        testData.add(dto);
        return testData;
    }

    /**
     * 创建测试月度折现曲线含价差利率JSON数据
     */
    private String createTestMonthlyDiscountRateWithSpreadJson() {
        return "{\n" +
               "  \"0\": 0.027459,\n" +
               "  \"1\": 0.027459,\n" +
               "  \"2\": 0.027459,\n" +
               "  \"3\": 0.027459,\n" +
               "  \"6\": 0.030000,\n" +
               "  \"12\": 0.033000,\n" +
               "  \"24\": 0.037000,\n" +
               "  \"36\": 0.040000,\n" +
               "  \"48\": 0.043000,\n" +
               "  \"60\": 0.045000,\n" +
               "  \"120\": 0.050000,\n" +
               "  \"240\": 0.055000,\n" +
               "  \"360\": 0.057000,\n" +
               "  \"480\": 0.059000,\n" +
               "  \"600\": 0.060000\n" +
               "}";
    }

    /**
     * 创建第二个测试月度折现曲线含价差利率JSON数据
     */
    private String createTestMonthlyDiscountRateWithSpreadJson2() {
        return "{\n" +
               "  \"0\": 0.025000,\n" +
               "  \"1\": 0.025500,\n" +
               "  \"2\": 0.026000,\n" +
               "  \"3\": 0.026500,\n" +
               "  \"6\": 0.028000,\n" +
               "  \"12\": 0.031000,\n" +
               "  \"24\": 0.035000,\n" +
               "  \"36\": 0.038000,\n" +
               "  \"48\": 0.041000,\n" +
               "  \"60\": 0.043000,\n" +
               "  \"120\": 0.048000,\n" +
               "  \"240\": 0.053000,\n" +
               "  \"360\": 0.055000,\n" +
               "  \"480\": 0.057000,\n" +
               "  \"600\": 0.058000\n" +
               "}";
    }
}
