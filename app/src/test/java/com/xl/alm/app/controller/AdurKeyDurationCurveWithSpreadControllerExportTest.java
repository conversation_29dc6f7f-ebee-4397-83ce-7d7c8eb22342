package com.xl.alm.app.controller;

import com.xl.alm.app.dto.AdurKeyDurationCurveWithSpreadDTO;
import com.xl.alm.app.service.AdurKeyDurationCurveWithSpreadService;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * ADUR关键久期折现曲线表含价差控制器导出功能测试类
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class AdurKeyDurationCurveWithSpreadControllerExportTest {

    @MockBean
    private AdurKeyDurationCurveWithSpreadService adurKeyDurationCurveWithSpreadService;

    private AdurKeyDurationCurveWithSpreadController controller;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        controller = new AdurKeyDurationCurveWithSpreadController();
        // 使用反射设置service
        try {
            java.lang.reflect.Field serviceField = AdurKeyDurationCurveWithSpreadController.class.getDeclaredField("adurKeyDurationCurveWithSpreadService");
            serviceField.setAccessible(true);
            serviceField.set(controller, adurKeyDurationCurveWithSpreadService);
        } catch (Exception e) {
            fail("设置service失败: " + e.getMessage());
        }
        
        response = new MockHttpServletResponse();
    }

    @Test
    @DisplayName("测试关键久期折现曲线表含价差值集导出功能")
    void testExportWithKeyDurationCurveWithSpreadSet() {
        // 准备测试数据
        List<AdurKeyDurationCurveWithSpreadDTO> testData = createTestDataWithCurveSet();
        
        // Mock service返回
        when(adurKeyDurationCurveWithSpreadService.selectAdurKeyDurationCurveWithSpreadDtoList(any()))
            .thenReturn(testData);

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                eq(testData),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("keyDurationCurveWithSpreadSet")
            ));
        }
    }

    @Test
    @DisplayName("测试关键久期折现曲线表含价差JSON格式解析")
    void testKeyDurationCurveWithSpreadSetJsonFormat() {
        // 创建包含JSON数据的测试数据
        List<AdurKeyDurationCurveWithSpreadDTO> testData = createTestDataWithCurveSet();
        
        // 验证JSON格式正确性
        AdurKeyDurationCurveWithSpreadDTO dto = testData.get(0);
        String jsonStr = dto.getKeyDurationCurveWithSpreadSet();
        
        // 验证JSON格式符合预期：{"0":1,"1":0.**********,"2":0.**********,...}
        assertNotNull(jsonStr);
        assertTrue(jsonStr.startsWith("{"));
        assertTrue(jsonStr.endsWith("}"));
        assertTrue(jsonStr.contains("\"0\":"));
        assertTrue(jsonStr.contains("\"1\":"));
        assertTrue(jsonStr.contains("\"2\":"));
    }

    @Test
    @DisplayName("测试字典转换功能")
    void testDictConversion() {
        // 创建包含字典值的测试数据
        List<AdurKeyDurationCurveWithSpreadDTO> testData = createTestDataWithDictValues();
        
        // 验证字典值设置正确
        AdurKeyDurationCurveWithSpreadDTO dto = testData.get(0);
        assertEquals("01", dto.getDurationType()); // 原始字典值
        assertEquals("01", dto.getBasisPointType()); // 原始字典值
        assertEquals("01", dto.getKeyTerm()); // 原始字典值
        assertEquals("01", dto.getStressDirection()); // 原始字典值
        assertEquals("01", dto.getDateType()); // 原始字典值
        assertEquals("01", dto.getSpreadType()); // 原始字典值
        assertEquals("01", dto.getAccountName()); // 原始字典值
        
        // 注意：字典转换在控制器的convertDictValueToLabel方法中进行
        // 这里只验证原始值设置正确
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportWithEmptyData() {
        // Mock service返回空列表
        when(adurKeyDurationCurveWithSpreadService.selectAdurKeyDurationCurveWithSpreadDtoList(any()))
            .thenReturn(new ArrayList<>());

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                any(List.class),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("keyDurationCurveWithSpreadSet")
            ));
        }
    }

    /**
     * 创建包含关键久期折现曲线表含价差值集的测试数据
     */
    private List<AdurKeyDurationCurveWithSpreadDTO> createTestDataWithCurveSet() {
        List<AdurKeyDurationCurveWithSpreadDTO> list = new ArrayList<>();
        
        AdurKeyDurationCurveWithSpreadDTO dto = new AdurKeyDurationCurveWithSpreadDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202406");
        dto.setDurationType("关键久期");
        dto.setBasisPointType("100基点");
        dto.setKeyTerm("12个月");
        dto.setStressDirection("上升");
        dto.setDateType("评估日");
        dto.setDate(new Date());
        dto.setSpreadType("信用价差");
        dto.setSpread(new BigDecimal("0.005"));
        dto.setCurveSubCategory("AAA");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("传统险账户");
        dto.setAssetName("测试资产");
        dto.setSecurityCode("000001");
        dto.setCurveId("CURVE001");
        
        // 设置关键久期折现曲线表含价差值集JSON数据（简单key-value格式）
        dto.setKeyDurationCurveWithSpreadSet("{\"0\":1,\"1\":0.**********,\"2\":0.**********,\"3\":0.**********,\"4\":0.**********,\"5\":0.**********}");
        
        list.add(dto);
        return list;
    }

    /**
     * 创建包含字典值的测试数据
     */
    private List<AdurKeyDurationCurveWithSpreadDTO> createTestDataWithDictValues() {
        List<AdurKeyDurationCurveWithSpreadDTO> list = new ArrayList<>();
        
        AdurKeyDurationCurveWithSpreadDTO dto = new AdurKeyDurationCurveWithSpreadDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202406");
        dto.setDurationType("01"); // 字典值
        dto.setBasisPointType("01"); // 字典值
        dto.setKeyTerm("01"); // 字典值
        dto.setStressDirection("01"); // 字典值
        dto.setDateType("01"); // 字典值
        dto.setDate(new Date());
        dto.setSpreadType("01"); // 字典值
        dto.setSpread(new BigDecimal("0.005"));
        dto.setCurveSubCategory("AAA");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("01"); // 字典值
        dto.setAssetName("测试资产");
        dto.setSecurityCode("000001");
        dto.setCurveId("CURVE001");
        dto.setKeyDurationCurveWithSpreadSet("{\"0\":1,\"1\":0.**********,\"2\":0.**********}");
        
        list.add(dto);
        return list;
    }
}
