package com.xl.alm.app.service;

import com.xl.alm.app.dto.AdurKeyDurationParameterDTO;
import com.xl.alm.app.query.AdurKeyDurationParameterQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * ADUR关键久期参数分页测试
 *
 * <AUTHOR> Assistant
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AdurKeyDurationParameterPaginationTest {

    @Autowired
    private AdurKeyDurationParameterService adurKeyDurationParameterService;

    @Test
    public void testPagination() {
        // 创建查询条件
        AdurKeyDurationParameterQuery query = new AdurKeyDurationParameterQuery();
        query.setPageNum(1);
        query.setPageSize(10);

        // 查询数据
        List<AdurKeyDurationParameterDTO> list = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(query);

        System.out.println("=== 分页测试结果 ===");
        System.out.println("查询条件 - 页码：" + query.getPageNum() + "，页面大小：" + query.getPageSize());
        System.out.println("返回数据条数：" + list.size());

        if (!list.isEmpty()) {
            System.out.println("第一条数据：账期=" + list.get(0).getAccountPeriod() + "，关键期限点=" + list.get(0).getKeyDuration());
            if (list.size() > 1) {
                System.out.println("最后一条数据：账期=" + list.get(list.size() - 1).getAccountPeriod() + "，关键期限点=" + list.get(list.size() - 1).getKeyDuration());
            }
        }

        // 测试第二页
        query.setPageNum(2);
        List<AdurKeyDurationParameterDTO> list2 = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(query);
        System.out.println("第二页数据条数：" + list2.size());

        // 不设置分页参数的查询
        AdurKeyDurationParameterQuery queryAll = new AdurKeyDurationParameterQuery();
        List<AdurKeyDurationParameterDTO> listAll = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(queryAll);
        System.out.println("不分页查询总数据条数：" + listAll.size());
    }

    @Test
    public void testQueryConditions() {
        // 测试不同的查询条件
        AdurKeyDurationParameterQuery query = new AdurKeyDurationParameterQuery();
        
        // 查询所有数据
        List<AdurKeyDurationParameterDTO> allList = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(query);
        System.out.println("总数据条数：" + allList.size());

        // 按账期查询
        if (!allList.isEmpty()) {
            String firstAccountPeriod = allList.get(0).getAccountPeriod();
            query.setAccountPeriod(firstAccountPeriod);
            List<AdurKeyDurationParameterDTO> filteredList = adurKeyDurationParameterService.selectAdurKeyDurationParameterDtoList(query);
            System.out.println("账期 " + firstAccountPeriod + " 的数据条数：" + filteredList.size());
        }
    }
}
