package com.xl.alm.app.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ValueSetExcelExporter文件名设置测试类
 * 验证中文文件名的HTTP响应头设置是否正确
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class ValueSetExcelExporterFileNameTest {

    @Test
    @DisplayName("测试中文文件名编码")
    void testChineseFileNameEncoding() throws UnsupportedEncodingException {
        // 测试各种中文文件名
        String[] chineseFileNames = {
            "久期资产明细表_1753837847067",
            "月度折现曲线表不含价差_1753837847067",
            "月度折现曲线表含价差_1753837847067"
        };
        
        for (String fileName : chineseFileNames) {
            // 模拟ValueSetExcelExporter中的编码逻辑
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            
            // 验证编码结果
            assertNotNull(encodedFileName);
            assertFalse(encodedFileName.isEmpty());
            
            // 验证编码后不包含空格（应该被替换为%20）
            assertFalse(encodedFileName.contains(" "));
            
            // 验证编码后不包含+号（应该被替换为%20）
            assertFalse(encodedFileName.contains("+"));
            
            System.out.println("原始文件名: " + fileName);
            System.out.println("编码后文件名: " + encodedFileName);
            System.out.println("完整Content-Disposition: attachment;filename*=UTF-8''" + encodedFileName + ".xlsx");
            System.out.println("---");
        }
    }

    @Test
    @DisplayName("测试HTTP响应头设置")
    void testHttpResponseHeaders() {
        MockHttpServletResponse response = new MockHttpServletResponse();
        String testFileName = "月度折现曲线表含价差_1753837847067";
        
        try {
            // 模拟ValueSetExcelExporter中的响应头设置逻辑
            String fileName = URLEncoder.encode(testFileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + fileName + ".xlsx");
            
            // 验证响应头设置
            assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", response.getContentType());
            assertEquals("utf-8", response.getCharacterEncoding());
            
            String contentDisposition = response.getHeader("Content-Disposition");
            assertNotNull(contentDisposition);
            assertTrue(contentDisposition.startsWith("attachment;filename*=UTF-8''"));
            assertTrue(contentDisposition.endsWith(".xlsx"));
            
            System.out.println("Content-Type: " + response.getContentType());
            System.out.println("Character-Encoding: " + response.getCharacterEncoding());
            System.out.println("Content-Disposition: " + contentDisposition);
            
        } catch (UnsupportedEncodingException e) {
            fail("文件名编码失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试文件名特殊字符处理")
    void testSpecialCharactersInFileName() throws UnsupportedEncodingException {
        // 测试包含特殊字符的文件名
        String[] specialFileNames = {
            "测试文件_123",
            "文件名包含空格 test_456",
            "文件名包含+号test_789",
            "文件名包含%号test_101112"
        };
        
        for (String fileName : specialFileNames) {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            
            // 验证特殊字符被正确编码
            if (fileName.contains(" ")) {
                assertTrue(encodedFileName.contains("%20"), "空格应该被编码为%20");
            }
            
            // 验证+号被替换为%20
            assertFalse(encodedFileName.contains("+"), "不应该包含+号");
            
            System.out.println("特殊字符文件名: " + fileName + " -> " + encodedFileName);
        }
    }

    @Test
    @DisplayName("验证Content-Disposition头格式")
    void testContentDispositionHeaderFormat() throws UnsupportedEncodingException {
        String testFileName = "月度折现曲线表含价差_1753837847067";
        String encodedFileName = URLEncoder.encode(testFileName, "UTF-8").replaceAll("\\+", "%20");
        String contentDisposition = "attachment;filename*=UTF-8''" + encodedFileName + ".xlsx";
        
        // 验证Content-Disposition头格式符合RFC 6266标准
        assertTrue(contentDisposition.startsWith("attachment;"));
        assertTrue(contentDisposition.contains("filename*=UTF-8''"));
        assertTrue(contentDisposition.endsWith(".xlsx"));
        
        // 验证不包含旧格式的filename=
        assertFalse(contentDisposition.contains("filename="));
        
        System.out.println("标准Content-Disposition格式: " + contentDisposition);
    }

    @Test
    @DisplayName("测试时间戳文件名生成")
    void testTimestampFileName() throws UnsupportedEncodingException {
        long timestamp = System.currentTimeMillis();
        String[] baseNames = {
            "久期资产明细表",
            "月度折现曲线表不含价差", 
            "月度折现曲线表含价差"
        };
        
        for (String baseName : baseNames) {
            String fullFileName = baseName + "_" + timestamp;
            String encodedFileName = URLEncoder.encode(fullFileName, "UTF-8").replaceAll("\\+", "%20");
            
            // 验证时间戳被正确包含
            assertTrue(fullFileName.contains(String.valueOf(timestamp)));
            assertTrue(encodedFileName.contains(String.valueOf(timestamp)));
            
            System.out.println("时间戳文件名: " + fullFileName);
            System.out.println("编码后: " + encodedFileName);
        }
    }

    @Test
    @DisplayName("对比新旧Content-Disposition格式")
    void testOldVsNewContentDispositionFormat() throws UnsupportedEncodingException {
        String testFileName = "月度折现曲线表含价差_1753837847067";
        String encodedFileName = URLEncoder.encode(testFileName, "UTF-8").replaceAll("\\+", "%20");
        
        // 旧格式（可能导致下载为download.txt）
        String oldFormat = "attachment;filename=" + encodedFileName + ".xlsx";
        
        // 新格式（符合RFC 6266标准）
        String newFormat = "attachment;filename*=UTF-8''" + encodedFileName + ".xlsx";
        
        System.out.println("旧格式（可能有问题）: " + oldFormat);
        System.out.println("新格式（推荐使用）: " + newFormat);
        
        // 验证新格式包含UTF-8编码声明
        assertTrue(newFormat.contains("UTF-8''"));
        assertTrue(newFormat.contains("filename*="));
        
        // 验证旧格式不包含编码声明
        assertFalse(oldFormat.contains("UTF-8"));
        assertTrue(oldFormat.contains("filename="));
    }
}
