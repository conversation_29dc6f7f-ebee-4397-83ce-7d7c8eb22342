package com.xl.alm.app.controller;

import com.xl.alm.app.dto.AdurMonthlyDiscountFactorWithSpreadDTO;
import com.xl.alm.app.service.AdurMonthlyDiscountFactorWithSpreadService;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * ADUR月度折现因子表含价差控制器导出功能测试类
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class AdurMonthlyDiscountFactorWithSpreadControllerExportTest {

    @MockBean
    private AdurMonthlyDiscountFactorWithSpreadService adurMonthlyDiscountFactorWithSpreadService;

    private AdurMonthlyDiscountFactorWithSpreadController controller;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        controller = new AdurMonthlyDiscountFactorWithSpreadController();
        // 使用反射设置service
        try {
            java.lang.reflect.Field serviceField = AdurMonthlyDiscountFactorWithSpreadController.class.getDeclaredField("adurMonthlyDiscountFactorWithSpreadService");
            serviceField.setAccessible(true);
            serviceField.set(controller, adurMonthlyDiscountFactorWithSpreadService);
        } catch (Exception e) {
            fail("设置service失败: " + e.getMessage());
        }
        
        response = new MockHttpServletResponse();
    }

    @Test
    @DisplayName("测试月度折现因子表含价差值集导出功能")
    void testExportWithMonthlyDiscountFactorSet() {
        // 准备测试数据
        List<AdurMonthlyDiscountFactorWithSpreadDTO> testData = createTestDataWithFactorSet();
        
        // Mock service返回
        when(adurMonthlyDiscountFactorWithSpreadService.selectAdurMonthlyDiscountFactorWithSpreadDtoList(any()))
            .thenReturn(testData);

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                eq(testData),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("monthlyDiscountFactorSet")
            ));
        }
    }

    @Test
    @DisplayName("测试月度折现因子表含价差JSON格式解析")
    void testMonthlyDiscountFactorSetJsonFormat() {
        // 创建包含JSON数据的测试数据
        List<AdurMonthlyDiscountFactorWithSpreadDTO> testData = createTestDataWithFactorSet();
        
        // 验证JSON格式正确性
        AdurMonthlyDiscountFactorWithSpreadDTO dto = testData.get(0);
        String jsonStr = dto.getMonthlyDiscountFactorSet();
        
        // 验证JSON格式符合预期：{"0":1,"1":0.**********,"2":0.**********,...}
        assertNotNull(jsonStr);
        assertTrue(jsonStr.startsWith("{"));
        assertTrue(jsonStr.endsWith("}"));
        assertTrue(jsonStr.contains("\"0\":"));
        assertTrue(jsonStr.contains("\"1\":"));
        assertTrue(jsonStr.contains("\"2\":"));
    }

    @Test
    @DisplayName("测试字典转换功能")
    void testDictConversion() {
        // 创建包含字典值的测试数据
        List<AdurMonthlyDiscountFactorWithSpreadDTO> testData = createTestDataWithDictValues();
        
        // 验证字典值设置正确
        AdurMonthlyDiscountFactorWithSpreadDTO dto = testData.get(0);
        assertEquals("01", dto.getDurationType()); // 原始字典值
        assertEquals("01", dto.getBasisPointType()); // 原始字典值
        assertEquals("01", dto.getDateType()); // 原始字典值
        assertEquals("01", dto.getSpreadType()); // 原始字典值
        assertEquals("01", dto.getAccountName()); // 原始字典值
        
        // 注意：字典转换在控制器的convertDictValueToLabel方法中进行
        // 这里只验证原始值设置正确
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportWithEmptyData() {
        // Mock service返回空列表
        when(adurMonthlyDiscountFactorWithSpreadService.selectAdurMonthlyDiscountFactorWithSpreadDtoList(any()))
            .thenReturn(new ArrayList<>());

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            // 注意：文件名包含时间戳，所以使用any(String.class)匹配
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                any(List.class),
                any(String.class), // 文件名包含时间戳，使用any匹配
                eq(response),
                eq("monthlyDiscountFactorSet")
            ));
        }
    }

    /**
     * 创建包含月度折现因子值集的测试数据
     */
    private List<AdurMonthlyDiscountFactorWithSpreadDTO> createTestDataWithFactorSet() {
        List<AdurMonthlyDiscountFactorWithSpreadDTO> list = new ArrayList<>();
        
        AdurMonthlyDiscountFactorWithSpreadDTO dto = new AdurMonthlyDiscountFactorWithSpreadDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202406");
        dto.setDurationType("修正久期");
        dto.setBasisPointType("100基点");
        dto.setDateType("评估日");
        dto.setDate(new Date());
        dto.setSpreadType("信用价差");
        dto.setSpread(new BigDecimal("0.005"));
        dto.setCurveSubCategory("AAA");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("传统险账户");
        dto.setAssetName("测试资产");
        dto.setSecurityCode("000001");
        dto.setCurveId("CURVE001");
        
        // 设置月度折现因子值集JSON数据（简单key-value格式）
        dto.setMonthlyDiscountFactorSet("{\"0\":1,\"1\":0.**********,\"2\":0.**********,\"3\":0.**********,\"4\":0.**********,\"5\":0.**********}");
        
        list.add(dto);
        return list;
    }

    /**
     * 创建包含字典值的测试数据
     */
    private List<AdurMonthlyDiscountFactorWithSpreadDTO> createTestDataWithDictValues() {
        List<AdurMonthlyDiscountFactorWithSpreadDTO> list = new ArrayList<>();
        
        AdurMonthlyDiscountFactorWithSpreadDTO dto = new AdurMonthlyDiscountFactorWithSpreadDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202406");
        dto.setDurationType("01"); // 字典值
        dto.setBasisPointType("01"); // 字典值
        dto.setDateType("01"); // 字典值
        dto.setDate(new Date());
        dto.setSpreadType("01"); // 字典值
        dto.setSpread(new BigDecimal("0.005"));
        dto.setCurveSubCategory("AAA");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("01"); // 字典值
        dto.setAssetName("测试资产");
        dto.setSecurityCode("000001");
        dto.setCurveId("CURVE001");
        dto.setMonthlyDiscountFactorSet("{\"0\":1,\"1\":0.**********,\"2\":0.**********}");
        
        list.add(dto);
        return list;
    }
}
