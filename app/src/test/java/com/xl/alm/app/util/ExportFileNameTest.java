package com.xl.alm.app.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 导出文件名格式测试类
 * 验证各个表的导出文件名格式是否正确
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class ExportFileNameTest {

    @Test
    @DisplayName("测试TB0003久期资产明细表文件名格式")
    void testTB0003FileName() {
        // 模拟生成文件名的逻辑
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "久期资产明细表_" + timestamp;
        
        // 验证文件名格式
        assertTrue(fileName.startsWith("久期资产明细表_"));
        assertTrue(fileName.contains(timestamp));
        assertTrue(timestamp.matches("\\d{13}")); // 13位时间戳
        
        System.out.println("TB0003文件名示例: " + fileName + ".xlsx");
    }

    @Test
    @DisplayName("测试TB0005月度折现曲线表不含价差文件名格式")
    void testTB0005FileName() {
        // 模拟生成文件名的逻辑
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "月度折现曲线表不含价差_" + timestamp;
        
        // 验证文件名格式
        assertTrue(fileName.startsWith("月度折现曲线表不含价差_"));
        assertTrue(fileName.contains(timestamp));
        assertTrue(timestamp.matches("\\d{13}")); // 13位时间戳
        
        System.out.println("TB0005文件名示例: " + fileName + ".xlsx");
    }

    @Test
    @DisplayName("测试TB0006月度折现曲线表含价差文件名格式")
    void testTB0006FileName() {
        // 模拟生成文件名的逻辑
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "月度折现曲线表含价差_" + timestamp;
        
        // 验证文件名格式
        assertTrue(fileName.startsWith("月度折现曲线表含价差_"));
        assertTrue(fileName.contains(timestamp));
        assertTrue(timestamp.matches("\\d{13}")); // 13位时间戳
        
        System.out.println("TB0006文件名示例: " + fileName + ".xlsx");
    }

    @Test
    @DisplayName("测试时间戳格式和唯一性")
    void testTimestampFormat() throws InterruptedException {
        // 生成第一个时间戳
        String timestamp1 = String.valueOf(System.currentTimeMillis());
        
        // 等待1毫秒确保时间戳不同
        Thread.sleep(1);
        
        // 生成第二个时间戳
        String timestamp2 = String.valueOf(System.currentTimeMillis());
        
        // 验证时间戳格式
        assertTrue(timestamp1.matches("\\d{13}"), "时间戳应该是13位数字");
        assertTrue(timestamp2.matches("\\d{13}"), "时间戳应该是13位数字");
        
        // 验证时间戳唯一性
        assertNotEquals(timestamp1, timestamp2, "连续生成的时间戳应该不同");
        
        System.out.println("时间戳1: " + timestamp1);
        System.out.println("时间戳2: " + timestamp2);
    }

    @Test
    @DisplayName("测试文件名中文字符支持")
    void testChineseCharacterSupport() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        // 测试各种中文文件名
        String[] fileNames = {
            "久期资产明细表_" + timestamp,
            "月度折现曲线表不含价差_" + timestamp,
            "月度折现曲线表含价差_" + timestamp
        };
        
        for (String fileName : fileNames) {
            // 验证文件名包含中文字符
            assertTrue(fileName.matches(".*[\\u4e00-\\u9fa5].*"), "文件名应该包含中文字符");
            
            // 验证文件名格式正确
            assertTrue(fileName.contains("_"), "文件名应该包含下划线分隔符");
            assertTrue(fileName.endsWith(timestamp), "文件名应该以时间戳结尾");
            
            System.out.println("中文文件名: " + fileName + ".xlsx");
        }
    }

    @Test
    @DisplayName("测试文件名长度合理性")
    void testFileNameLength() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        String[] fileNames = {
            "久期资产明细表_" + timestamp,
            "月度折现曲线表不含价差_" + timestamp,
            "月度折现曲线表含价差_" + timestamp
        };
        
        for (String fileName : fileNames) {
            // 验证文件名长度合理（不超过100个字符）
            assertTrue(fileName.length() < 100, "文件名长度应该合理: " + fileName);
            
            // 验证文件名不为空
            assertFalse(fileName.trim().isEmpty(), "文件名不应该为空");
            
            System.out.println("文件名长度: " + fileName.length() + " - " + fileName);
        }
    }

    @Test
    @DisplayName("验证实际导出文件名示例")
    void testActualExportFileNameExamples() {
        long currentTime = System.currentTimeMillis();
        
        // 模拟实际导出时的文件名
        String tb0003FileName = "久期资产明细表_" + currentTime;
        String tb0005FileName = "月度折现曲线表不含价差_" + currentTime;
        String tb0006FileName = "月度折现曲线表含价差_" + currentTime;
        
        System.out.println("=== 实际导出文件名示例 ===");
        System.out.println("TB0003: " + tb0003FileName + ".xlsx");
        System.out.println("TB0005: " + tb0005FileName + ".xlsx");
        System.out.println("TB0006: " + tb0006FileName + ".xlsx");
        
        // 验证文件名符合预期格式
        assertEquals("久期资产明细表_" + currentTime, tb0003FileName);
        assertEquals("月度折现曲线表不含价差_" + currentTime, tb0005FileName);
        assertEquals("月度折现曲线表含价差_" + currentTime, tb0006FileName);
        
        // 验证时间戳部分相同（同一时刻生成）
        assertTrue(tb0003FileName.endsWith(String.valueOf(currentTime)));
        assertTrue(tb0005FileName.endsWith(String.valueOf(currentTime)));
        assertTrue(tb0006FileName.endsWith(String.valueOf(currentTime)));
    }
}
