package com.xl.alm.app.dto;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TB0006月度折现曲线表含价差DTO字段映射测试类
 * 验证DTO字段名与Entity字段名的一致性
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class AdurMonthlyDiscountCurveWithSpreadDTOFieldMappingTest {

    @Test
    @DisplayName("测试DTO字段名正确性")
    void testDTOFieldNames() throws NoSuchFieldException {
        // 验证关键字段存在
        Field monthlyDiscountRateWithSpreadSetField = AdurMonthlyDiscountCurveWithSpreadDTO.class.getDeclaredField("monthlyDiscountRateWithSpreadSet");
        assertNotNull(monthlyDiscountRateWithSpreadSetField);
        assertEquals(String.class, monthlyDiscountRateWithSpreadSetField.getType());
        
        System.out.println("✅ monthlyDiscountRateWithSpreadSet字段存在且类型正确");
    }

    @Test
    @DisplayName("测试DTO字段设置和获取")
    void testDTOFieldSetterGetter() {
        AdurMonthlyDiscountCurveWithSpreadDTO dto = new AdurMonthlyDiscountCurveWithSpreadDTO();
        
        // 设置基本字段
        dto.setId(1L);
        dto.setAccountPeriod("202407");
        dto.setDurationType("01");
        dto.setBasisPointType("01");
        dto.setDateType("01");
        dto.setDate(new Date());
        dto.setSpreadType("01");
        dto.setSpread(new BigDecimal("0.001500"));
        dto.setCurveSubCategory("1");
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("01");
        dto.setAssetName("测试资产");
        dto.setSecurityCode("SEC001");
        dto.setCurveId("CURVE001");
        
        // 设置JSON字段
        String testJson = "{\"0\":0.027459,\"1\":0.027459,\"2\":0.027459}";
        dto.setMonthlyDiscountRateWithSpreadSet(testJson);
        
        // 验证字段设置成功
        assertEquals(1L, dto.getId());
        assertEquals("202407", dto.getAccountPeriod());
        assertEquals("01", dto.getDurationType());
        assertEquals("01", dto.getBasisPointType());
        assertEquals("01", dto.getDateType());
        assertNotNull(dto.getDate());
        assertEquals("01", dto.getSpreadType());
        assertEquals(new BigDecimal("0.001500"), dto.getSpread());
        assertEquals("1", dto.getCurveSubCategory());
        assertEquals("ASSET001", dto.getAssetNumber());
        assertEquals("01", dto.getAccountName());
        assertEquals("测试资产", dto.getAssetName());
        assertEquals("SEC001", dto.getSecurityCode());
        assertEquals("CURVE001", dto.getCurveId());
        assertEquals(testJson, dto.getMonthlyDiscountRateWithSpreadSet());
        
        System.out.println("✅ 所有字段设置和获取正常");
        System.out.println("JSON字段内容: " + dto.getMonthlyDiscountRateWithSpreadSet());
    }

    @Test
    @DisplayName("测试JSON字段内容格式")
    void testJsonFieldFormat() {
        AdurMonthlyDiscountCurveWithSpreadDTO dto = new AdurMonthlyDiscountCurveWithSpreadDTO();
        
        // 设置完整的JSON数据
        String completeJson = "{\n" +
                "  \"0\": 0.027459,\n" +
                "  \"1\": 0.027459,\n" +
                "  \"2\": 0.027459,\n" +
                "  \"3\": 0.027459,\n" +
                "  \"6\": 0.030000,\n" +
                "  \"12\": 0.033000,\n" +
                "  \"24\": 0.037000,\n" +
                "  \"36\": 0.040000,\n" +
                "  \"48\": 0.043000,\n" +
                "  \"60\": 0.045000,\n" +
                "  \"120\": 0.050000,\n" +
                "  \"240\": 0.055000,\n" +
                "  \"360\": 0.057000,\n" +
                "  \"480\": 0.059000,\n" +
                "  \"600\": 0.060000\n" +
                "}";
        
        dto.setMonthlyDiscountRateWithSpreadSet(completeJson);
        
        // 验证JSON内容
        String retrievedJson = dto.getMonthlyDiscountRateWithSpreadSet();
        assertNotNull(retrievedJson);
        assertTrue(retrievedJson.contains("\"0\""));
        assertTrue(retrievedJson.contains("0.027459"));
        assertTrue(retrievedJson.contains("\"600\""));
        assertTrue(retrievedJson.contains("0.060000"));
        
        System.out.println("✅ JSON字段格式验证通过");
        System.out.println("完整JSON内容:");
        System.out.println(retrievedJson);
    }

    @Test
    @DisplayName("测试空JSON字段处理")
    void testEmptyJsonField() {
        AdurMonthlyDiscountCurveWithSpreadDTO dto = new AdurMonthlyDiscountCurveWithSpreadDTO();
        
        // 测试null值
        dto.setMonthlyDiscountRateWithSpreadSet(null);
        assertNull(dto.getMonthlyDiscountRateWithSpreadSet());
        
        // 测试空字符串
        dto.setMonthlyDiscountRateWithSpreadSet("");
        assertEquals("", dto.getMonthlyDiscountRateWithSpreadSet());
        
        // 测试空JSON对象
        dto.setMonthlyDiscountRateWithSpreadSet("{}");
        assertEquals("{}", dto.getMonthlyDiscountRateWithSpreadSet());
        
        System.out.println("✅ 空JSON字段处理正常");
    }

    @Test
    @DisplayName("验证字段名与数据库映射一致性")
    void testFieldNameConsistency() {
        // 这个测试确保DTO字段名与Entity字段名一致
        // 从而保证Mapper映射能够正确工作
        
        String expectedFieldName = "monthlyDiscountRateWithSpreadSet";
        
        try {
            Field dtoField = AdurMonthlyDiscountCurveWithSpreadDTO.class.getDeclaredField(expectedFieldName);
            assertNotNull(dtoField, "DTO中应该存在字段: " + expectedFieldName);
            assertEquals(String.class, dtoField.getType(), "字段类型应该是String");
            
            System.out.println("✅ DTO字段名与预期一致: " + expectedFieldName);
            System.out.println("✅ 字段类型正确: " + dtoField.getType().getSimpleName());
            
        } catch (NoSuchFieldException e) {
            fail("DTO中缺少必要字段: " + expectedFieldName + ", 错误: " + e.getMessage());
        }
    }
}
