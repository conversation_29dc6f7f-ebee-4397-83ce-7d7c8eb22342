package com.xl.alm.app.util;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

/**
 * JSON处理逻辑验证测试
 *
 * <AUTHOR> Assistant
 */
public class JsonProcessingTest {

    @Test
    public void testJsonProcessingLogic() {
        // 模拟真实的JSON数据结构
        JSONObject testJson = new JSONObject();
        
        // 添加1-1273的数据（模拟老表的真实数据结构）
        for (int i = 1; i <= 1273; i++) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("date", "2025-01-01");
            valueObj.put("val", 0.01 + (i * 0.001));
            testJson.put(String.valueOf(i), valueObj);
        }
        
        System.out.println("=== 原始JSON数据分析 ===");
        System.out.println("总键数：" + testJson.size());
        System.out.println("键范围：1-1273");
        
        // 检查是否包含键"0"
        boolean hasKey0 = testJson.containsKey("0");
        System.out.println("包含键'0'：" + hasKey0);
        
        // 检查关键键是否存在
        int[] checkKeys = {0, 1, 100, 200, 300, 400, 500, 600, 700, 1273};
        for (int key : checkKeys) {
            boolean exists = testJson.containsKey(String.valueOf(key));
            System.out.println("键'" + key + "'存在：" + exists);
        }
        
        // 测试处理逻辑
        String originalJson = testJson.toJSONString();
        String processedJson = AdurDataMigrationUtil.processKeyDurationParameterValSet(originalJson);
        JSONObject processedObj = JSONObject.parseObject(processedJson);
        
        System.out.println("\n=== 处理后JSON数据分析 ===");
        System.out.println("处理后键数：" + processedObj.size());
        
        // 统计0-600范围的键数
        int count0to600 = 0;
        for (int i = 0; i <= 600; i++) {
            if (processedObj.containsKey(String.valueOf(i))) {
                count0to600++;
            }
        }
        System.out.println("0-600范围内的键数：" + count0to600);
        
        // 检查处理后的关键键
        for (int key : checkKeys) {
            boolean exists = processedObj.containsKey(String.valueOf(key));
            System.out.println("处理后键'" + key + "'存在：" + exists);
        }
        
        // 验证结果
        System.out.println("\n=== 验证结果 ===");
        if (hasKey0) {
            // 如果原始数据包含键"0"，处理后应该有601个键（0-600）
            System.out.println("原始数据包含键'0'，期望处理后有601个键");
            assert processedObj.size() == 601 : "处理后应该有601个键（0-600）";
        } else {
            // 如果原始数据不包含键"0"，处理后应该有600个键（1-600）
            System.out.println("原始数据不包含键'0'，期望处理后有600个键");
            assert processedObj.size() == 600 : "处理后应该有600个键（1-600）";
        }
        
        System.out.println("测试通过！实际处理后键数：" + processedObj.size());
    }
    
    @Test
    public void testWithKey0() {
        // 测试包含键"0"的情况
        JSONObject testJson = new JSONObject();
        
        // 添加0-1273的数据
        for (int i = 0; i <= 1273; i++) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("date", "2025-01-01");
            valueObj.put("val", 0.01 + (i * 0.001));
            testJson.put(String.valueOf(i), valueObj);
        }
        
        String originalJson = testJson.toJSONString();
        String processedJson = AdurDataMigrationUtil.processKeyDurationParameterValSet(originalJson);
        JSONObject processedObj = JSONObject.parseObject(processedJson);
        
        System.out.println("包含键'0'的测试：");
        System.out.println("原始键数：" + testJson.size());
        System.out.println("处理后键数：" + processedObj.size());
        
        // 应该有601个键（0-600）
        assert processedObj.size() == 601 : "应该有601个键（0-600）";
        assert processedObj.containsKey("0") : "应该包含键'0'";
        assert processedObj.containsKey("600") : "应该包含键'600'";
        assert !processedObj.containsKey("601") : "不应该包含键'601'";
        
        System.out.println("测试通过！");
    }

    @Test
    public void testJsonSortingOrder() {
        System.out.println("\n=== JSON排序测试 ===");

        // 创建乱序的JSON数据
        JSONObject testJson = new JSONObject();

        // 故意添加乱序的数据
        int[] randomOrder = {500, 100, 300, 0, 600, 200, 50, 400, 1, 599, 2, 598, 3, 597};
        for (int i : randomOrder) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("date", "2025-01-01");
            valueObj.put("val", 0.01 + (i * 0.001));
            testJson.put(String.valueOf(i), valueObj);
        }

        // 添加1-1273的其他数据
        for (int i = 4; i <= 1273; i++) {
            if (java.util.Arrays.stream(randomOrder).noneMatch(x -> x == i)) {
                JSONObject valueObj = new JSONObject();
                valueObj.put("date", "2025-01-01");
                valueObj.put("val", 0.01 + (i * 0.001));
                testJson.put(String.valueOf(i), valueObj);
            }
        }

        String originalJson = testJson.toJSONString();
        System.out.println("原始JSON（乱序）前200字符：" + originalJson.substring(0, Math.min(200, originalJson.length())));

        // 测试处理和排序逻辑
        String processedJson = AdurDataMigrationUtil.processKeyDurationParameterValSet(originalJson);
        System.out.println("处理后JSON（应该有序）前200字符：" + processedJson.substring(0, Math.min(200, processedJson.length())));

        // 验证排序结果
        String orderReport = AdurDataMigrationUtil.validateJsonOrder(processedJson);
        System.out.println("JSON顺序验证报告：\n" + orderReport);

        // 验证是否按0-600顺序排列
        JSONObject processedObj = JSONObject.parseObject(processedJson);

        // 检查JSON字符串中键的顺序
        String jsonString = processedObj.toJSONString();
        int lastIndex = -1;
        boolean isOrdered = true;

        for (int i = 0; i <= 600; i++) {
            String keyPattern = "\"" + i + "\":";
            int currentIndex = jsonString.indexOf(keyPattern);
            if (currentIndex != -1) {
                if (currentIndex <= lastIndex) {
                    isOrdered = false;
                    System.out.println("键" + i + "的位置不正确，位置：" + currentIndex + "，上一个键位置：" + lastIndex);
                    break;
                }
                lastIndex = currentIndex;
            }
        }

        System.out.println("JSON是否按0-600顺序排列：" + isOrdered);
        assert isOrdered : "JSON应该按0-600顺序排列";

        System.out.println("JSON排序测试通过！");
    }
}
