// 测试字典转换功能的简单代码片段
// 可以在Controller中临时添加这个方法来测试

@GetMapping("/testDictConversion")
public Result testDictConversion() {
    log.info("开始测试字典转换功能");
    
    // 测试各个字典类型的转换
    String[] testValues = {"01", "02", "03", "04"};
    String[] dictTypes = {
        "adur_duration_type",
        "adur_basis_point_type", 
        "adur_date_type",
        "adur_spread_type",
        "adur_account_name"
    };
    
    for (String dictType : dictTypes) {
        log.info("测试字典类型: {}", dictType);
        for (String value : testValues) {
            String label = DictConvertUtil.convertValueToLabel(value, dictType);
            log.info("  {} -> {}", value, label);
        }
    }
    
    return Result.success("字典转换测试完成，请查看日志");
}

// 使用方法：
// 1. 将上述方法临时添加到MonthlyDiscountFactorController中
// 2. 访问 http://localhost:8095/adur/monthly/discount/factor/testDictConversion
// 3. 查看控制台日志输出，确认字典转换是否正常工作
// 4. 测试完成后删除该方法
