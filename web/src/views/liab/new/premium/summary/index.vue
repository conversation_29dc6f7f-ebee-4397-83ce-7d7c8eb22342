<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务代码" prop="businessCode">
        <el-input
          v-model="queryParams.businessCode"
          placeholder="请输入业务代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select
          v-model="queryParams.designType"
          placeholder="请选择设计类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否中短" prop="shortTermFlag">
        <el-select
          v-model="queryParams.shortTermFlag"
          placeholder="请选择是否中短"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.dur_is_short_term"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="缴费频率" prop="paymentFrequency">
        <el-select
          v-model="queryParams.paymentFrequency"
          placeholder="请选择缴费频率"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.liab_payment_frequency"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['liab:new:premium:summary:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['liab:new:premium:summary:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['liab:new:premium:summary:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['liab:new:premium:summary:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['liab:new:premium:summary:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="newPremiumSummaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="业务代码" align="center" prop="businessCode" />
      <el-table-column label="产品名称" align="center" prop="productName" :show-overflow-tooltip="true" />
      <el-table-column label="设计类型" align="center" prop="designType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="是否中短" align="center" prop="shortTermFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.dur_is_short_term" :value="scope.row.shortTermFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="缴费频率" align="center" prop="paymentFrequency">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.liab_payment_frequency" :value="scope.row.paymentFrequency"/>
        </template>
      </el-table-column>
      <el-table-column label="缴费年期" align="center" prop="paymentPeriod" />
      <el-table-column label="缴费年期分类" align="center" prop="paymentPeriodCategory" />
      <el-table-column label="原保费" align="center" prop="originalPremium" />
      <el-table-column label="初始费用" align="center" prop="initialFee" />
      <el-table-column label="新单保费合计" align="center" prop="newPremiumTotal" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['liab:new:premium:summary:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['liab:new:premium:summary:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改新单保费汇总对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期，格式YYYYMM" />
        </el-form-item>
        <el-form-item label="业务代码" prop="businessCode">
          <el-input v-model="form.businessCode" placeholder="请输入业务代码" />
        </el-form-item>
        <el-form-item label="缴费频率" prop="paymentFrequency">
          <el-select v-model="form.paymentFrequency" placeholder="请选择缴费频率">
            <el-option label="趸交" value="01" />
            <el-option label="期交" value="02" />
          </el-select>
        </el-form-item>
        <el-form-item label="缴费年期" prop="paymentPeriod">
          <el-input v-model="form.paymentPeriod" placeholder="请输入缴费年期" />
        </el-form-item>
        <el-form-item label="原保费" prop="originalPremium">
          <el-input v-model="form.originalPremium" placeholder="请输入原保费金额，可为0或负数" />
        </el-form-item>
        <el-form-item label="初始费用" prop="initialFee">
          <el-input v-model="form.initialFee" placeholder="请输入初始费用金额，可为0或负数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新单保费汇总导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          <br/>
          <span style="font-size:12px;color:#909399;">
            导入说明：缴费频率支持填写"趸交"或"期交"；原保费和初始费用可为0或负数；支持重复数据导入
          </span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNewPremiumSummary, getNewPremiumSummary, delNewPremiumSummary, addNewPremiumSummary, updateNewPremiumSummary } from "@/api/liab/newPremiumSummary";
import { getToken } from "@/utils/auth";

export default {
  name: "NewPremiumSummary",
  dicts: ['cost_design_type', 'dur_is_short_term', 'liab_payment_frequency'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新单保费汇总表格数据
      newPremiumSummaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        businessCode: null,
        productName: null,
        designType: null,
        shortTermFlag: null,
        paymentFrequency: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        businessCode: [
          { required: true, message: "业务代码不能为空", trigger: "blur" },
          { max: 20, message: "业务代码长度不能超过20个字符", trigger: "blur" }
        ],
        paymentFrequency: [
          { required: true, message: "缴费频率不能为空", trigger: "change" }
        ],
        paymentPeriod: [
          { required: true, message: "缴费年期不能为空", trigger: "blur" }
        ],
        originalPremium: [
          { pattern: /^-?[0-9]{1,20}(\.[0-9]{1,10})?$/, message: "原保费格式不正确，整数部分最多20位，小数部分最多10位", trigger: "blur" }
        ],
        initialFee: [
          { pattern: /^-?[0-9]{1,20}(\.[0-9]{1,10})?$/, message: "初始费用格式不正确，整数部分最多20位，小数部分最多10位", trigger: "blur" }
        ],
        remark: [
          { max: 500, message: "备注信息长度不能超过500个字符", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "新单保费汇总数据导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/liab/new/premium/summary/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询新单保费汇总列表 */
    getList() {
      this.loading = true;
      listNewPremiumSummary(this.queryParams).then(response => {
        this.newPremiumSummaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        businessCode: null,
        paymentFrequency: null,
        paymentPeriod: null,
        originalPremium: null,
        initialFee: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加新单保费汇总";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getNewPremiumSummary(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改新单保费汇总";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNewPremiumSummary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNewPremiumSummary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除新单保费汇总编号为"' + ids + '"的数据项？').then(function() {
        return delNewPremiumSummary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('liab/new/premium/summary/export', {
        ...this.queryParams
      }, `新单保费汇总_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "新单保费汇总数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('liab/new/premium/summary/importTemplate', {}, `新单保费汇总数据模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
