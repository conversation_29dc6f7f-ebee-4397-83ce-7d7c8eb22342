<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计类型" prop="statisticsType">
        <el-select
          v-model="queryParams.statisticsType"
          placeholder="请选择统计类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.liab_alm_statistics_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计类型细分" prop="statisticsSubType">
        <el-input
          v-model="queryParams.statisticsSubType"
          placeholder="请输入统计类型细分"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['liab:alm:new:premium:statistics:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['liab:alm:new:premium:statistics:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['liab:alm:new:premium:statistics:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['liab:alm:new:premium:statistics:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['liab:alm:new:premium:statistics:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="almNewPremiumStatisticsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="统计类型" align="center" prop="statisticsType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.liab_alm_statistics_type" :value="scope.row.statisticsType"/>
        </template>
      </el-table-column>
      <el-table-column label="统计类型细分" align="center" prop="statisticsSubType" />
      <el-table-column label="新单保费合计金额" align="center" prop="newPremiumTotal" />
      <el-table-column label="备注信息" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['liab:alm:new:premium:statistics:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['liab:alm:new:premium:statistics:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改ALM新单保费统计表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期，格式：YYYYMM" />
        </el-form-item>
        <el-form-item label="统计类型" prop="statisticsType">
          <el-select v-model="form.statisticsType" placeholder="请选择统计类型" @change="handleStatisticsTypeChange">
            <el-option
              v-for="dict in dict.type.liab_alm_statistics_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计类型细分" prop="statisticsSubType">
          <el-select v-model="form.statisticsSubType" placeholder="请选择统计类型细分" :disabled="!form.statisticsType">
            <el-option
              v-for="dict in getStatisticsSubTypeOptions()"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="新单保费合计金额" prop="newPremiumTotal">
          <el-input v-model="form.newPremiumTotal" placeholder="请输入新单保费合计金额" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAlmNewPremiumStatistics, getAlmNewPremiumStatistics, delAlmNewPremiumStatistics, addAlmNewPremiumStatistics, updateAlmNewPremiumStatistics } from "@/api/liab/almNewPremiumStatistics";
import { getToken } from "@/utils/auth";

export default {
  name: "AlmNewPremiumStatistics",
  dicts: ['liab_alm_statistics_type', 'liab_alm_statistics_sub_type', 'cost_design_type', 'cost_term_type', 'cost_business_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ALM新单保费统计表表格数据
      almNewPremiumStatisticsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        statisticsType: null,
        statisticsSubType: null,
        newPremiumTotal: null,
        remark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { max: 6, message: "账期长度不能超过6个字符", trigger: "blur" }
        ],
        statisticsType: [
          { required: true, message: "统计类型不能为空", trigger: "change" }
        ],
        statisticsSubType: [
          { required: true, message: "统计类型细分不能为空", trigger: "blur" },
          { max: 100, message: "统计类型细分长度不能超过100个字符", trigger: "blur" }
        ],
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/liab/alm/new/premium/statistics/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询ALM新单保费统计表列表 */
    getList() {
      this.loading = true;
      listAlmNewPremiumStatistics(this.queryParams).then(response => {
        this.almNewPremiumStatisticsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        statisticsType: null,
        statisticsSubType: null,
        newPremiumTotal: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加ALM新单保费统计表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAlmNewPremiumStatistics(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改ALM新单保费统计表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAlmNewPremiumStatistics(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAlmNewPremiumStatistics(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除ALM新单保费统计表编号为"' + ids + '"的数据项？').then(function() {
        return delAlmNewPremiumStatistics(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('liab/alm/new/premium/statistics/export', {
        ...this.queryParams
      }, `ALM新单保费统计表_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "ALM新单保费统计表导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('liab/alm/new/premium/statistics/importTemplate', {
      }, `ALM新单保费统计表导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 统计类型变化时清空统计类型细分
    handleStatisticsTypeChange() {
      this.form.statisticsSubType = null;
    },
    // 根据统计类型获取对应的统计类型细分选项
    getStatisticsSubTypeOptions() {
      const statisticsType = this.form.statisticsType;
      if (!statisticsType) {
        return [];
      }

      // 根据统计类型返回对应的字典选项
      switch (statisticsType) {
        case '01': // 缴费年期
          return this.dict.type.liab_alm_statistics_sub_type.filter(item =>
            ['趸交', '3年期及以内', '3-5年期（含5年期）', '5-10年期（含10年期）', '10年期以上'].includes(item.label)
          );
        case '02': // 设计类型
          return this.dict.type.cost_design_type || [];
        case '03': // 设计类型区分中短
          return this.dict.type.liab_alm_statistics_sub_type.filter(item =>
            ['传统险中短', '分红险中短', '万能险中短', '投连险中短'].includes(item.label)
          );
        case '04': // 业务类别
          // 合并长短期标识和业务类型字典
          const termTypeOptions = this.dict.type.cost_term_type || [];
          const businessTypeOptions = this.dict.type.cost_business_type || [];
          return [...termTypeOptions, ...businessTypeOptions];
        default:
          return [];
      }
    }
  }
};
</script>
