<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期，格式YYYYMM"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select
          v-model="queryParams.designType"
          placeholder="请选择设计类型"
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['liab:scale:summary:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['liab:scale:summary:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['liab:scale:summary:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['liab:scale:summary:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['liab:scale:summary:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="liabScaleSummaryList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      :scroll-x="true"
      border
      stripe
      size="mini"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" width="80" fixed="left" />
      <el-table-column label="设计类型" align="center" prop="designType" width="100" fixed="left">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="合理估计负债" align="center" prop="reasonableLiability" width="160" />
      <el-table-column label="风险边际" align="center" prop="riskMargin" width="140" />
      <el-table-column label="剩余边际" align="center" prop="residualMargin" width="140" />
      <el-table-column label="长期险未决赔款准备金" align="center" prop="outstandingClaimReserveL" width="180" />
      <el-table-column label="未到期责任准备金" align="center" prop="unearnedPremiumReserve" width="160" />
      <el-table-column label="短期险未决赔款准备金" align="center" prop="outstandingClaimReserveS" width="180" />
      <el-table-column label="万能投连险负债规模" align="center" prop="investmentLinkedLiability" width="170" />
      <el-table-column label="应收分保未到期责任准备金" align="center" prop="receivableUnearnedPremiumReserve" width="200" />
      <el-table-column label="应收分保未决赔款准备金" align="center" prop="receivableOutstandingClaimReserve" width="190" />
      <el-table-column label="应收分保寿险责任准备金" align="center" prop="receivableLifeInsuranceReserve" width="190" />
      <el-table-column label="应收分保长期健康险责任准备金" align="center" prop="receivableLongTermHealthReserve" width="220" />
      <el-table-column label="备注信息" align="center" prop="remark" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['liab:scale:summary:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['liab:scale:summary:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改负债规模汇总表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入账期，格式YYYYMM" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计类型" prop="designType">
              <el-select v-model="form.designType" placeholder="请选择设计类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.cost_design_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="合理估计负债" prop="reasonableLiability">
              <el-input v-model="form.reasonableLiability" placeholder="请输入合理估计负债" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险边际" prop="riskMargin">
              <el-input v-model="form.riskMargin" placeholder="请输入风险边际" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="剩余边际" prop="residualMargin">
              <el-input v-model="form.residualMargin" placeholder="请输入剩余边际" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长期险未决赔款准备金" prop="outstandingClaimReserveL">
              <el-input v-model="form.outstandingClaimReserveL" placeholder="请输入长期险未决赔款准备金" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="未到期责任准备金" prop="unearnedPremiumReserve">
              <el-input v-model="form.unearnedPremiumReserve" placeholder="请输入未到期责任准备金" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="短期险未决赔款准备金" prop="outstandingClaimReserveS">
              <el-input v-model="form.outstandingClaimReserveS" placeholder="请输入短期险未决赔款准备金" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="万能投连险负债规模" prop="investmentLinkedLiability">
              <el-input v-model="form.investmentLinkedLiability" placeholder="请输入万能投连险负债规模" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应收分保未到期责任准备金" prop="receivableUnearnedPremiumReserve">
              <el-input v-model="form.receivableUnearnedPremiumReserve" placeholder="请输入应收分保未到期责任准备金" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应收分保未决赔款准备金" prop="receivableOutstandingClaimReserve">
              <el-input v-model="form.receivableOutstandingClaimReserve" placeholder="请输入应收分保未决赔款准备金" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="应收分保寿险责任准备金" prop="receivableLifeInsuranceReserve">
              <el-input v-model="form.receivableLifeInsuranceReserve" placeholder="请输入应收分保寿险责任准备金" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应收分保长期健康险责任准备金" prop="receivableLongTermHealthReserve">
              <el-input v-model="form.receivableLongTermHealthReserve" placeholder="请输入应收分保长期健康险责任准备金" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="负债规模汇总详情" :visible.sync="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="账期">{{ detailForm.accountingPeriod }}</el-descriptions-item>
        <el-descriptions-item label="设计类型">
          <dict-tag :options="dict.type.cost_design_type" :value="detailForm.designType"/>
        </el-descriptions-item>
        <el-descriptions-item label="合理估计负债">{{ detailForm.reasonableLiability || '0' }}</el-descriptions-item>
        <el-descriptions-item label="风险边际">{{ detailForm.riskMargin || '0' }}</el-descriptions-item>
        <el-descriptions-item label="剩余边际">{{ detailForm.residualMargin || '0' }}</el-descriptions-item>
        <el-descriptions-item label="长期险未决赔款准备金">{{ detailForm.outstandingClaimReserveL || '0' }}</el-descriptions-item>
        <el-descriptions-item label="未到期责任准备金">{{ detailForm.unearnedPremiumReserve || '0' }}</el-descriptions-item>
        <el-descriptions-item label="短期险未决赔款准备金">{{ detailForm.outstandingClaimReserveS || '0' }}</el-descriptions-item>
        <el-descriptions-item label="万能投连险负债规模">{{ detailForm.investmentLinkedLiability || '0' }}</el-descriptions-item>
        <el-descriptions-item label="应收分保未到期责任准备金">{{ detailForm.receivableUnearnedPremiumReserve || '0' }}</el-descriptions-item>
        <el-descriptions-item label="应收分保未决赔款准备金">{{ detailForm.receivableOutstandingClaimReserve || '0' }}</el-descriptions-item>
        <el-descriptions-item label="应收分保寿险责任准备金">{{ detailForm.receivableLifeInsuranceReserve || '0' }}</el-descriptions-item>
        <el-descriptions-item label="应收分保长期健康险责任准备金" :span="2">{{ detailForm.receivableLongTermHealthReserve || '0' }}</el-descriptions-item>
        <el-descriptions-item label="备注信息" :span="3">{{ detailForm.remark || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailForm.createBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailForm.updateTime }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLiabScaleSummary, getLiabScaleSummary, delLiabScaleSummary, addLiabScaleSummary, updateLiabScaleSummary, getImportTemplate } from "@/api/liab/scale/summary";
import { getToken } from "@/utils/auth";
import { saveAs } from 'file-saver';

export default {
  name: "LiabScaleSummary",
  dicts: ['cost_design_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 负债规模汇总表表格数据
      liabScaleSummaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情表单数据
      detailForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        designType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        designType: [
          { required: true, message: "设计类型不能为空", trigger: "change" }
        ],
        remark: [
          { max: 500, message: "备注信息长度不能超过500个字符", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "导入负债规模汇总表数据",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已有数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/liab/scale/summary/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询负债规模汇总表列表 */
    getList() {
      this.loading = true;
      listLiabScaleSummary(this.queryParams).then(response => {
        this.liabScaleSummaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        designType: null,
        reasonableLiability: null,
        riskMargin: null,
        residualMargin: null,
        outstandingClaimReserveL: null,
        unearnedPremiumReserve: null,
        outstandingClaimReserveS: null,
        investmentLinkedLiability: null,
        receivableUnearnedPremiumReserve: null,
        receivableOutstandingClaimReserve: null,
        receivableLifeInsuranceReserve: null,
        receivableLongTermHealthReserve: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加负债规模汇总表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getLiabScaleSummary(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改负债规模汇总表";
      });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      const id = row.id;
      getLiabScaleSummary(id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLiabScaleSummary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLiabScaleSummary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除负债规模汇总表编号为"' + ids + '"的数据项？').then(function() {
        return delLiabScaleSummary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'liab/scale/summary/export',
        {
          ...this.queryParams,
        },
        `liab_scale_summary_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      // 显示加载中提示
      this.$modal.loading("正在下载模板，请稍候...");

      getImportTemplate().then(response => {
        this.$modal.closeLoading();
        // 使用saveAs直接下载
        try {
          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          saveAs(blob, '负债规模汇总表模板.xlsx');
        } catch (error) {
          console.error("Excel下载失败", error);
          this.$modal.msgError("Excel下载失败：" + error.message);
        }
      }).catch(error => {
        this.$modal.closeLoading();
        this.$modal.msgError("下载模板失败：" + (error.message || error));
        console.error("下载模板失败", error);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  font-size: 12px;
  table-layout: fixed;
}

.el-table .cell {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 6px !important;
  line-height: 1.3;
  word-break: keep-all;
  word-wrap: normal;
}

.el-table th {
  padding: 6px 0 !important;
  font-weight: 600;
  white-space: nowrap !important;
}

.el-table td {
  padding: 4px 0 !important;
  white-space: nowrap !important;
}

/* 确保表格行高度一致且紧凑 */
.el-table__row {
  height: 36px !important;
}

.el-table__header-wrapper .el-table__row {
  height: 36px !important;
}

/* 操作按钮样式 */
.el-button--mini {
  padding: 3px 6px !important;
  font-size: 11px;
  margin: 0 1px;
  height: 24px;
  line-height: 1.2;
}

/* 字典标签样式 */
.el-tag {
  font-size: 11px;
  padding: 1px 4px;
  height: 20px !important;
  line-height: 18px;
  white-space: nowrap !important;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 数值列样式 */
.el-table__body .cell {
  text-align: center;
}

/* 防止表格内容换行的全局设置 */
.el-table__body-wrapper,
.el-table__header-wrapper,
.el-table__fixed-body-wrapper,
.el-table__fixed-header-wrapper {
  overflow-x: auto;
}

/* 确保固定列也不换行 */
.el-table__fixed .cell,
.el-table__fixed-right .cell {
  white-space: nowrap !important;
}

/* 强制所有业务数据不换行 - 针对具体列 */
.el-table__body .el-table__row .cell {
  white-space: nowrap !important;
  word-break: keep-all !important;
  word-wrap: normal !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 针对数值类型的列强制不换行 */
.el-table__body .el-table__row .cell:nth-child(n) {
  white-space: pre !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 强制表格内所有文本内容不换行 */
.el-table * {
  white-space: nowrap !important;
}

/* 特别针对金额字段的样式 */
.el-table__body td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 0 !important;
  width: auto !important;
}

/* 确保表格单元格内容不会因为内容长度而换行 */
.el-table .cell,
.el-table .cell * {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 最强制的不换行规则 - 覆盖所有可能的换行情况 */
.el-table tbody tr td,
.el-table tbody tr td .cell,
.el-table tbody tr td .cell *,
.el-table tbody tr td div,
.el-table tbody tr td span,
.el-table tbody tr td p {
  white-space: nowrap !important;
  word-break: keep-all !important;
  word-wrap: normal !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important;
  max-width: 100% !important;
  vertical-align: top !important;
}

/* 针对数字和金额的特殊处理 */
.el-table tbody tr td[class*="amount"],
.el-table tbody tr td[class*="money"],
.el-table tbody tr td[class*="number"],
.el-table tbody tr td[class*="value"] {
  font-family: 'Courier New', monospace !important;
  white-space: nowrap !important;
  text-align: right !important;
}

/* 强制所有表格内容使用单行显示 */
.el-table .el-table__body .el-table__row .el-table__cell .cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1.3 !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

/* 超强制数字不换行规则 - 针对长数字 */
.el-table .cell {
  min-width: 0 !important;
  width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  font-variant-numeric: tabular-nums !important;
}

/* 针对数字内容的特殊处理 */
.el-table td .cell {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  letter-spacing: -0.5px !important;
  white-space: nowrap !important;
  word-spacing: -1px !important;
}

/* 确保表格列宽度被严格遵守 */
.el-table .el-table__cell {
  overflow: hidden !important;
  white-space: nowrap !important;
}

/* 强制数字类型字段不换行 */
.el-table td[class*="reasonable"],
.el-table td[class*="liability"],
.el-table td[class*="margin"],
.el-table td[class*="reserve"],
.el-table td[class*="amount"] {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  min-width: 140px !important;
}

/* 最终兜底规则 - 确保所有数字内容不换行 */
.el-table tbody tr td .cell,
.el-table tbody tr td .cell span,
.el-table tbody tr td .cell div {
  white-space: nowrap !important;
  display: inline-block !important;
  max-width: 100% !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  vertical-align: middle !important;
}
</style>
