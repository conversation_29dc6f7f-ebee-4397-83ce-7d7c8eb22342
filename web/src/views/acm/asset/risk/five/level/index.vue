<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="itemName">
        <el-select
          v-model="queryParams.itemName"
          placeholder="请选择项目名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.acm_risk_item_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="五级分类资产统计标识" prop="fiveLevelStatisticsFlag">
        <el-select
          v-model="queryParams.fiveLevelStatisticsFlag"
          placeholder="请选择五级分类资产统计标识"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="dict in dict.type.ast_five_level_statistics_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="五级分类" prop="fiveLevelClassification">
        <el-select
          v-model="queryParams.fiveLevelClassification"
          placeholder="请选择五级分类"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_five_level_classification"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['acm:asset:risk:five:level:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['acm:asset:risk:five:level:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['acm:asset:risk:five:level:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['acm:asset:risk:five:level:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['acm:asset:risk:five:level:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assetRiskFiveLevelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="项目名称" align="center" prop="itemName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.acm_risk_item_name" :value="scope.row.itemName"/>
        </template>
      </el-table-column>
      <el-table-column label="五级分类资产统计标识" align="center" prop="fiveLevelStatisticsFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_five_level_statistics_flag" :value="scope.row.fiveLevelStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="五级分类" align="center" prop="fiveLevelClassification">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_five_level_classification" :value="scope.row.fiveLevelClassification"/>
        </template>
      </el-table-column>
      <el-table-column label="账面余额金额" align="center" prop="bookBalance" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['acm:asset:risk:five:level:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['acm:asset:risk:five:level:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改保险资产风险五级分类状况表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
        </el-form-item>
        <el-form-item label="项目名称" prop="itemName">
          <el-select v-model="form.itemName" placeholder="请选择项目名称">
            <el-option
              v-for="dict in dict.type.acm_risk_item_name"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="五级分类资产统计标识" prop="fiveLevelStatisticsFlag">
          <el-select v-model="form.fiveLevelStatisticsFlag" placeholder="请选择五级分类资产统计标识">
            <el-option
              v-for="dict in dict.type.ast_five_level_statistics_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="五级分类" prop="fiveLevelClassification">
          <el-select v-model="form.fiveLevelClassification" placeholder="请选择五级分类">
            <el-option
              v-for="dict in dict.type.ast_five_level_classification"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账面余额金额" prop="bookBalance">
          <el-input v-model="form.bookBalance" placeholder="请输入账面余额金额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 保险资产风险五级分类状况表导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssetRiskFiveLevel, getAssetRiskFiveLevel, delAssetRiskFiveLevel, addAssetRiskFiveLevel, updateAssetRiskFiveLevel } from "@/api/acm/assetRiskFiveLevel";

export default {
  name: "AssetRiskFiveLevel",
  dicts: ['acm_risk_item_name', 'ast_five_level_statistics_flag', 'ast_five_level_classification'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 保险资产风险五级分类状况表表格数据
      assetRiskFiveLevelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        itemName: null,
        fiveLevelStatisticsFlag: null,
        fiveLevelClassification: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { max: 6, message: "账期长度不能超过6个字符", trigger: "blur" }
        ],
        itemName: [
          { required: true, message: "项目名称不能为空", trigger: "change" },
          { max: 50, message: "项目名称长度不能超过50个字符", trigger: "blur" }
        ],
        fiveLevelStatisticsFlag: [
          { required: true, message: "五级分类资产统计标识不能为空", trigger: "change" },
          { max: 50, message: "五级分类资产统计标识长度不能超过50个字符", trigger: "blur" }
        ],
        fiveLevelClassification: [
          { required: true, message: "五级分类不能为空", trigger: "change" },
          { max: 20, message: "五级分类长度不能超过20个字符", trigger: "blur" }
        ]
      },
      // 保险资产风险五级分类状况表导入参数
      upload: {
        // 是否显示弹出层（保险资产风险五级分类状况表导入）
        open: false,
        // 弹出层标题（保险资产风险五级分类状况表导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的保险资产风险五级分类状况表数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/acm/asset/risk/five/level/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询保险资产风险五级分类状况表列表 */
    getList() {
      this.loading = true;
      listAssetRiskFiveLevel(this.queryParams).then(response => {
        this.assetRiskFiveLevelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        itemName: null,
        fiveLevelStatisticsFlag: null,
        fiveLevelClassification: null,
        bookBalance: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加保险资产风险五级分类状况表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAssetRiskFiveLevel(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改保险资产风险五级分类状况表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAssetRiskFiveLevel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssetRiskFiveLevel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除保险资产风险五级分类状况表编号为"' + ids + '"的数据项？').then(function() {
        return delAssetRiskFiveLevel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('acm/asset/risk/five/level/export', {
        ...this.queryParams
      }, `asset_risk_five_level_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "保险资产风险五级分类状况表导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('acm/asset/risk/five/level/importTemplate', {}, `asset_risk_five_level_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
