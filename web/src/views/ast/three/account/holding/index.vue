<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证券代码" prop="securityCode">
        <el-input
          v-model="queryParams.securityCode"
          placeholder="请输入证券代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证券简称" prop="securityName">
        <el-input
          v-model="queryParams.securityName"
          placeholder="请输入证券简称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ast:three:account:holding:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ast:three:account:holding:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ast:three:account:holding:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ast:three:account:holding:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['ast:three:account:holding:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="threeAccountHoldingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" width="100" />
      <el-table-column label="账户名称" align="center" prop="accountName" width="120" />
      <el-table-column label="证券代码" align="center" prop="securityCode" width="120" />
      <el-table-column label="证券简称" align="center" prop="securityName" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="维度名称2" align="center" prop="dimensionName2" :show-overflow-tooltip="true" width="120" />
      <el-table-column label="维度名称3" align="center" prop="dimensionName3" :show-overflow-tooltip="true" width="120" />
      <el-table-column label="维度名称4" align="center" prop="dimensionName4" :show-overflow-tooltip="true" width="120" />
      <el-table-column label="维度名称5" align="center" prop="dimensionName5" :show-overflow-tooltip="true" width="120" />
      <el-table-column label="维度名称6" align="center" prop="dimensionName6" :show-overflow-tooltip="true" width="120" />
      <el-table-column label="持仓数量" align="center" prop="holdingQuantity" width="120" />
      <el-table-column label="持仓面值" align="center" prop="holdingFaceValue" width="120" />
      <el-table-column label="成本" align="center" prop="cost" width="120" />
      <el-table-column label="净成本" align="center" prop="netCost" width="120" />
      <el-table-column label="净市值" align="center" prop="netMarketValue" width="120" />
      <el-table-column label="市值" align="center" prop="marketValue" width="120" />
      <el-table-column label="票面利率" align="center" prop="couponRate" width="100" />
      <el-table-column label="年付息频率" align="center" prop="annualPaymentFrequency" width="100" />
      <el-table-column label="起息日" align="center" prop="valueDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.valueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到期日" align="center" prop="maturityDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.maturityDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会计类型" align="center" prop="accountingType" width="120" />
      <el-table-column label="最新购买日" align="center" prop="latestPurchaseDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.latestPurchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主体外部评级" align="center" prop="entityRating" width="120" />
      <el-table-column label="证券外部评级" align="center" prop="securityRating" width="120" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ast:three:account:holding:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ast:three:account:holding:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改三账户持仓对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-input v-model="form.accountName" placeholder="请输入账户名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="证券代码" prop="securityCode">
              <el-input v-model="form.securityCode" placeholder="请输入证券代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证券简称" prop="securityName">
              <el-input v-model="form.securityName" placeholder="请输入证券简称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="维度名称2" prop="dimensionName2">
              <el-input v-model="form.dimensionName2" placeholder="请输入维度名称2" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度名称3" prop="dimensionName3">
              <el-input v-model="form.dimensionName3" placeholder="请输入维度名称3" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="维度名称4" prop="dimensionName4">
              <el-input v-model="form.dimensionName4" placeholder="请输入维度名称4" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度名称5" prop="dimensionName5">
              <el-input v-model="form.dimensionName5" placeholder="请输入维度名称5" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="维度名称6" prop="dimensionName6">
              <el-input v-model="form.dimensionName6" placeholder="请输入维度名称6" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="持仓数量" prop="holdingQuantity">
              <el-input v-model="form.holdingQuantity" placeholder="请输入持仓数量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="持仓面值" prop="holdingFaceValue">
              <el-input v-model="form.holdingFaceValue" placeholder="请输入持仓面值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成本" prop="cost">
              <el-input v-model="form.cost" placeholder="请输入成本" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="净价成本" prop="netCost">
              <el-input v-model="form.netCost" placeholder="请输入净价成本" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="净价市值" prop="netMarketValue">
              <el-input v-model="form.netMarketValue" placeholder="请输入净价市值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="市值" prop="marketValue">
              <el-input v-model="form.marketValue" placeholder="请输入市值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="票面利率" prop="couponRate">
              <el-input v-model="form.couponRate" placeholder="请输入票面利率" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="年付息次数" prop="annualPaymentFrequency">
              <el-input v-model="form.annualPaymentFrequency" placeholder="请输入年付息次数" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起息日" prop="valueDate">
              <el-date-picker clearable
                v-model="form.valueDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择起息日">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="到期日" prop="maturityDate">
              <el-date-picker clearable
                v-model="form.maturityDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择到期日">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会计类型" prop="accountingType">
              <el-input v-model="form.accountingType" placeholder="请输入会计类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最新买入日期" prop="latestPurchaseDate">
              <el-date-picker clearable
                v-model="form.latestPurchaseDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择最新买入日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主体外部评级" prop="entityRating">
              <el-input v-model="form.entityRating" placeholder="请输入主体外部评级" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="证券外部评级" prop="securityRating">
              <el-input v-model="form.securityRating" placeholder="请输入证券外部评级" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 三账户持仓导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="600px" append-to-body>
      <el-form ref="uploadForm" :model="upload" :rules="upload.rules" label-width="120px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="upload.accountingPeriod" placeholder="请输入账期，格式：YYYYMM" />
        </el-form-item>
        <el-form-item label="文件">
          <el-upload
            ref="upload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url + '?updateSupport=' + upload.updateSupport + '&accountingPeriod=' + upload.accountingPeriod"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
              <div class="el-upload__tip" slot="tip">
                <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的三账户持仓数据
              </div>
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listThreeAccountHolding, getThreeAccountHolding, delThreeAccountHolding, addThreeAccountHolding, updateThreeAccountHolding } from "@/api/ast/threeAccountHolding";
import { getToken } from "@/utils/auth";

export default {
  name: "ThreeAccountHolding",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 三账户持仓表格数据
      threeAccountHoldingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        accountName: null,
        securityCode: null,
        securityName: null,
        dimensionName2: null,
        dimensionName3: null,
        dimensionName4: null,
        dimensionName5: null,
        dimensionName6: null,
        accountingType: null,
        entityRating: null,
        securityRating: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "blur" }
        ],
        securityCode: [
          { required: true, message: "证券代码不能为空", trigger: "blur" }
        ]
      },
      // 三账户持仓导入参数
      upload: {
        // 是否显示弹出层（三账户持仓导入）
        open: false,
        // 弹出层标题（三账户持仓导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的三账户持仓数据
        updateSupport: 0,
        // 账期
        accountingPeriod: "",
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ast/three/account/holding/importData",
        // 表单校验
        rules: {
          accountingPeriod: [
            { required: true, message: "账期不能为空", trigger: "blur" },
            { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
          ]
        }
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询三账户持仓列表 */
    getList() {
      this.loading = true;
      listThreeAccountHolding(this.queryParams).then(response => {
        this.threeAccountHoldingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        accountName: null,
        securityCode: null,
        securityName: null,
        dimensionName2: null,
        dimensionName3: null,
        dimensionName4: null,
        dimensionName5: null,
        dimensionName6: null,
        holdingQuantity: null,
        holdingFaceValue: null,
        cost: null,
        netCost: null,
        netMarketValue: null,
        marketValue: null,
        couponRate: null,
        annualPaymentFrequency: null,
        valueDate: null,
        maturityDate: null,
        accountingType: null,
        latestPurchaseDate: null,
        entityRating: null,
        securityRating: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加三账户持仓";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getThreeAccountHolding(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改三账户持仓";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateThreeAccountHolding(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addThreeAccountHolding(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除三账户持仓编号为"' + ids + '"的数据项？').then(function() {
        return delThreeAccountHolding(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ast/three/account/holding/export', {
        ...this.queryParams
      }, `three_account_holding_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "三账户持仓导入";
      this.upload.accountingPeriod = "";
      this.upload.open = true;
      this.$nextTick(() => {
        this.$refs.uploadForm.clearValidate();
      });
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('ast/three/account/holding/importTemplate', {}, `three_account_holding_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs["uploadForm"].validate(valid => {
        if (valid) {
          this.$refs.upload.submit();
        }
      });
    }
  }
};
</script>
