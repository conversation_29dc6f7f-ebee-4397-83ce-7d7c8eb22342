<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产编号" prop="assetNumber">
        <el-input
          v-model="queryParams.assetNumber"
          placeholder="请输入资产编号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select
          v-model="queryParams.accountName"
          placeholder="请选择账户名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_account_name_mapping"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证券标识代码" prop="securityCode">
        <el-input
          v-model="queryParams.securityCode"
          placeholder="请输入证券标识代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入资产名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产小小类" prop="assetSubSubCategory">
        <el-select
          v-model="queryParams.assetSubSubCategory"
          placeholder="请选择资产小小类"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_asset_sub_sub_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ast:asset:detail:overall:add']"
        >新增</el-button>
        <el-button
          type="success"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleGenerate"
          v-hasPermi="['ast:assetDetailOverall:generate']"
          :loading="generateLoading"
        >生成宽表</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ast:asset:detail:overall:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ast:asset:detail:overall:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ast:asset:detail:overall:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['ast:asset:detail:overall:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assetDetailOverallList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" width="80" />
      <el-table-column label="资产编号" align="center" prop="assetNumber" width="120" />
      <el-table-column label="账户名称" align="center" prop="accountName" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_account_name_mapping" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="资产名称" align="center" prop="assetName" width="200" show-overflow-tooltip />
      <el-table-column label="证券代码" align="center" prop="securityCode" width="120" />
      <el-table-column label="资产小小类" align="center" prop="assetSubSubCategory" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_asset_sub_sub_category" :value="scope.row.assetSubSubCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="资产大类" align="center" prop="assetMajorCategory" width="100" show-overflow-tooltip />
      <el-table-column label="境内外" align="center" prop="domesticForeign" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_domestic_foreign" :value="scope.row.domesticForeign"/>
        </template>
      </el-table-column>
      <el-table-column label="固收资产细分类" align="center" prop="fixedIncomeSubCategory" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_fixed_income_sub_category" :value="scope.row.fixedIncomeSubCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="资产配置状况一级分类" align="center" prop="assetAllocationLevel1" width="150" show-overflow-tooltip />
      <el-table-column label="资产配置状况二级分类" align="center" prop="assetAllocationLevel2" width="150" show-overflow-tooltip />
      <el-table-column label="资产配置状况三级分类" align="center" prop="assetAllocationLevel3" width="150" show-overflow-tooltip />
      <el-table-column label="wind中主体评级" align="center" prop="windEntityRating" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.windEntityRating"/>
        </template>
      </el-table-column>
      <el-table-column label="wind中债项评级" align="center" prop="windDebtRating" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.windDebtRating"/>
        </template>
      </el-table-column>
      <el-table-column label="风控绩效系统中主体评级" align="center" prop="riskEntityRating" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.riskEntityRating"/>
        </template>
      </el-table-column>
      <el-table-column label="风控绩效系统中债项评级" align="center" prop="riskDebtRating" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.riskDebtRating"/>
        </template>
      </el-table-column>
      <el-table-column label="信用评级维护表中信用评级" align="center" prop="creditRatingMaintenance" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.creditRatingMaintenance"/>
        </template>
      </el-table-column>
      <el-table-column label="信用评级取值逻辑标识" align="center" prop="creditRatingLogicFlag" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating_logic_flag" :value="scope.row.creditRatingLogicFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="信用评级" align="center" prop="creditRating" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.creditRating"/>
        </template>
      </el-table-column>
      <el-table-column label="信用评级分类" align="center" prop="creditRatingCategory" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating_category" :value="scope.row.creditRatingCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="持仓数量" align="center" prop="holdingQuantity" width="100" />
      <el-table-column label="持仓面值" align="center" prop="holdingFaceValue" width="120" />
      <el-table-column label="成本" align="center" prop="cost" width="120" />
      <el-table-column label="净价成本" align="center" prop="netCost" width="120" />
      <el-table-column label="净价市值" align="center" prop="netMarketValue" width="120" />
      <el-table-column label="市值" align="center" prop="marketValue" width="120" />
      <el-table-column label="1年VAR值" align="center" prop="var1Year" width="120" />
      <el-table-column label="3年VAR值" align="center" prop="var3Year" width="120" />
      <el-table-column label="票面利率" align="center" prop="couponRate" width="100" />
      <el-table-column label="年付息次数" align="center" prop="annualPaymentFrequency" width="100" />
      <el-table-column label="付息方式" align="center" prop="paymentMethod" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_payment_method" :value="scope.row.paymentMethod"/>
        </template>
      </el-table-column>
      <el-table-column label="起息日" align="center" prop="valueDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.valueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="买入日期" align="center" prop="purchaseDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.purchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到期日" align="center" prop="maturityDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.maturityDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会计类型" align="center" prop="accountingType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_accounting_classification" :value="scope.row.accountingType"/>
        </template>
      </el-table-column>
      <el-table-column label="账面余额" align="center" prop="bookBalance" width="120" />
      <el-table-column label="资产减值准备" align="center" prop="assetImpairmentProvision" width="120" />
      <el-table-column label="账面价值" align="center" prop="bookValue" width="120" />
      <el-table-column label="剩余期限" align="center" prop="remainingTerm" width="100" />
      <el-table-column label="剩余期限分类" align="center" prop="remainingTermCategory" width="120" />
      <el-table-column label="剩余期限标识" align="center" prop="remainingTermFlag" width="120" />
      <el-table-column label="行业统计标识" align="center" prop="industryStatisticsFlag" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_industry_statistics_flag" :value="scope.row.industryStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="债券类型" align="center" prop="bondType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_bond_type" :value="scope.row.bondType"/>
        </template>
      </el-table-column>
      <el-table-column label="固收资产剩余期限资产分类" align="center" prop="fixedIncomeTermCategory" width="180">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_fixed_income_term_category" :value="scope.row.fixedIncomeTermCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="可计算现金流固收资产标识" align="center" prop="calculableCashflowFlag" width="180">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_calculable_cashflow_flag" :value="scope.row.calculableCashflowFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="利差久期资产统计标识" align="center" prop="spreadDurationStatisticsFlag" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_spread_duration_statistics_flag" :value="scope.row.spreadDurationStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="ALM资产名称" align="center" prop="almAssetName" width="150" show-overflow-tooltip />
      <el-table-column label="单一资产统计标识" align="center" prop="singleAssetStatisticsFlag" width="140">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_single_asset_statistics_flag" :value="scope.row.singleAssetStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="五级分类" align="center" prop="fiveLevelClassification" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_five_level_classification" :value="scope.row.fiveLevelClassification"/>
        </template>
      </el-table-column>
      <el-table-column label="五级分类资产统计标识" align="center" prop="fiveLevelStatisticsFlag" width="160">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_five_level_statistics_flag" :value="scope.row.fiveLevelStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="银行分类" align="center" prop="bankClassification" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_bank_classification" :value="scope.row.bankClassification"/>
        </template>
      </el-table-column>
      <el-table-column label="资产流动性分类" align="center" prop="assetLiquidityCategory" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_asset_liquidity_category" :value="scope.row.assetLiquidityCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="变现系数" align="center" prop="realizationCoefficient" width="100" />
      <el-table-column label="折现曲线使用评级" align="center" prop="discountCurveRating" width="140">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_credit_rating" :value="scope.row.discountCurveRating"/>
        </template>
      </el-table-column>
      <el-table-column label="折现曲线标识" align="center" prop="discountCurveFlag" width="120" />
      <el-table-column label="调整起息日" align="center" prop="adjustedValueDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedValueDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调整买入日" align="center" prop="adjustedPurchaseDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedPurchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调整到期日" align="center" prop="adjustedMaturityDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.adjustedMaturityDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ast:asset:detail:overall:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ast:asset:detail:overall:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改整体资产明细表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
        </el-form-item>
        <el-form-item label="资产编号" prop="assetNumber">
          <el-input v-model="form.assetNumber" placeholder="请输入资产编号" />
        </el-form-item>
        <el-form-item label="账户名称" prop="accountName">
          <el-select v-model="form.accountName" placeholder="请选择账户名称">
            <el-option
              v-for="dict in dict.type.ast_account_name_mapping"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证券标识代码" prop="securityCode">
          <el-input v-model="form.securityCode" placeholder="请输入证券标识代码" />
        </el-form-item>
        <el-form-item label="资产名称" prop="assetName">
          <el-input v-model="form.assetName" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="资产小小类" prop="assetSubSubCategory">
          <el-select v-model="form.assetSubSubCategory" placeholder="请选择资产小小类">
            <el-option
              v-for="dict in dict.type.ast_asset_sub_sub_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="境内外标识" prop="domesticForeign">
          <el-select v-model="form.domesticForeign" placeholder="请选择境内外标识">
            <el-option
              v-for="dict in dict.type.ast_domestic_foreign"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信用评级" prop="creditRating">
          <el-select v-model="form.creditRating" placeholder="请选择信用评级">
            <el-option
              v-for="dict in dict.type.ast_credit_rating"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="市值" prop="marketValue">
          <el-input v-model="form.marketValue" placeholder="请输入市值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 整体资产明细表导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssetDetailOverall, getAssetDetailOverall, delAssetDetailOverall, addAssetDetailOverall, updateAssetDetailOverall } from "@/api/ast/assetDetailOverall";
import { getToken } from "@/utils/auth";

export default {
  name: "AssetDetailOverall",
  dicts: [
    'ast_account_name_mapping', 'ast_asset_sub_sub_category', 'ast_domestic_foreign', 'ast_credit_rating',
    'ast_credit_rating_category', 'ast_accounting_classification', 'ast_payment_method',
    'ast_fixed_income_sub_category', 'ast_credit_rating_logic_flag', 'ast_industry_statistics_flag',
    'ast_bond_type', 'ast_fixed_income_term_category', 'ast_bank_classification', 'ast_five_level_classification',
    'ast_calculable_cashflow_flag', 'ast_spread_duration_statistics_flag', 'ast_single_asset_statistics_flag',
    'ast_five_level_statistics_flag', 'ast_asset_liquidity_category'
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 生成宽表加载状态
      generateLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 整体资产明细表表格数据
      assetDetailOverallList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        assetNumber: null,
        accountName: null,
        securityCode: null,
        assetName: null,
        assetSubSubCategory: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        assetNumber: [
          { required: true, message: "资产编号不能为空", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ]
      },
      // 整体资产明细表导入参数
      upload: {
        // 是否显示弹出层（整体资产明细表导入）
        open: false,
        // 弹出层标题（整体资产明细表导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ast/asset/detail/overall/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询整体资产明细表列表 */
    getList() {
      this.loading = true;
      listAssetDetailOverall(this.queryParams).then(response => {
        this.assetDetailOverallList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        assetNumber: null,
        accountName: null,
        securityCode: null,
        assetName: null,
        assetSubSubCategory: null,
        assetMajorCategory: null,
        domesticForeign: null,
        fixedIncomeSubCategory: null,
        assetAllocationLevel1: null,
        assetAllocationLevel2: null,
        assetAllocationLevel3: null,
        windEntityRating: null,
        windDebtRating: null,
        riskEntityRating: null,
        riskDebtRating: null,
        creditRatingMaintenance: null,
        creditRatingLogicFlag: null,
        creditRating: null,
        creditRatingCategory: null,
        holdingQuantity: null,
        marketValue: null,
        cost: null,
        couponRate: null,
        annualPaymentFrequency: null,
        paymentMethod: null,
        valueDate: null,
        purchaseDate: null,
        maturityDate: null,
        accountingType: null,
        remainingTerm: null,
        remainingTermCategory: null,
        remainingTermFlag: null,
        fixedIncomeSubCategory: null,
        assetAllocationLevel1: null,
        assetAllocationLevel2: null,
        assetAllocationLevel3: null,
        windEntityRating: null,
        windDebtRating: null,
        riskEntityRating: null,
        riskDebtRating: null,
        creditRatingMaintenance: null,
        creditRatingLogicFlag: null,
        holdingFaceValue: null,
        netCost: null,
        netMarketValue: null,
        var1Year: null,
        var3Year: null,
        bookBalance: null,
        assetImpairmentProvision: null,
        bookValue: null,
        industryStatisticsFlag: null,
        bondType: null,
        fixedIncomeTermCategory: null,
        almAssetName: null,
        bankClassification: null,
        fiveLevelClassification: null,
        calculableCashflowFlag: null,
        spreadDurationStatisticsFlag: null,
        singleAssetStatisticsFlag: null,
        fiveLevelStatisticsFlag: null,
        assetLiquidityCategory: null,
        realizationCoefficient: null,
        discountCurveRating: null,
        discountCurveFlag: null,
        adjustedValueDate: null,
        adjustedPurchaseDate: null,
        adjustedMaturityDate: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加整体资产明细表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAssetDetailOverall(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改整体资产明细表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAssetDetailOverall(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssetDetailOverall(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除整体资产明细表编号为"' + ids + '"的数据项？').then(function() {
        return delAssetDetailOverall(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ast/asset/detail/overall/export', {
        ...this.queryParams
      }, `assetDetailOverall_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "整体资产明细表导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('ast/asset/detail/overall/importTemplate', {}, `assetDetailOverall_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
