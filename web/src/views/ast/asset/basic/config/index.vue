<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配置序号" prop="sequenceNumber">
        <el-input
          v-model="queryParams.sequenceNumber"
          placeholder="请输入配置序号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产小小类" prop="assetSubSubCategory">
        <el-select
          v-model="queryParams.assetSubSubCategory"
          placeholder="请选择资产小小类"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_asset_sub_sub_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="固收资产细分类" prop="fixedIncomeSubCategory">
        <el-select
          v-model="queryParams.fixedIncomeSubCategory"
          placeholder="请选择固收资产细分类"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_fixed_income_sub_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="行业统计标识" prop="industryStatisticsFlag">
        <el-select
          v-model="queryParams.industryStatisticsFlag"
          placeholder="请选择行业统计标识"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_industry_statistics_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ast:asset:basic:config:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ast:asset:basic:config:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ast:asset:basic:config:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ast:asset:basic:config:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['ast:asset:basic:config:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assetBasicConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="配置序号" align="center" prop="sequenceNumber" />
      <el-table-column label="资产小小类" align="center" prop="assetSubSubCategory">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_asset_sub_sub_category" :value="scope.row.assetSubSubCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="固收资产细分类" align="center" prop="fixedIncomeSubCategory">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_fixed_income_sub_category" :value="scope.row.fixedIncomeSubCategory"/>
        </template>
      </el-table-column>
      <el-table-column label="可计算现金流标识" align="center" prop="calculableCashflowFlag">
        <template slot-scope="scope">
          <span>{{ scope.row.calculableCashflowFlag === '1' ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="信用评级取值逻辑标识" align="center" prop="creditRatingLogicFlag" />
      <el-table-column label="行业统计标识" align="center" prop="industryStatisticsFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_industry_statistics_flag" :value="scope.row.industryStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="单一资产统计标识" align="center" prop="singleAssetStatisticsFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_single_asset_statistics_flag" :value="scope.row.singleAssetStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="五级分类资产统计标识" align="center" prop="fiveLevelStatisticsFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_five_level_statistics_flag" :value="scope.row.fiveLevelStatisticsFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="利差久期资产统计标识" align="center" prop="spreadDurationStatisticsFlag">
        <template slot-scope="scope">
          <span>{{ scope.row.spreadDurationStatisticsFlag === '1' ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ast:asset:basic:config:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ast:asset:basic:config:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资产基础配置表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期，格式YYYYMM" />
        </el-form-item>
        <el-form-item label="配置序号" prop="sequenceNumber">
          <el-input-number v-model="form.sequenceNumber" placeholder="请输入配置序号" :min="1" />
        </el-form-item>
        <el-form-item label="资产小小类" prop="assetSubSubCategory">
          <el-select v-model="form.assetSubSubCategory" placeholder="请选择资产小小类">
            <el-option
              v-for="dict in dict.type.ast_asset_sub_sub_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="固收资产细分类" prop="fixedIncomeSubCategory">
          <el-select v-model="form.fixedIncomeSubCategory" placeholder="请选择固收资产细分类">
            <el-option
              v-for="dict in dict.type.ast_fixed_income_sub_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可计算现金流标识" prop="calculableCashflowFlag">
          <el-radio-group v-model="form.calculableCashflowFlag">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="信用评级取值逻辑标识" prop="creditRatingLogicFlag">
          <el-input v-model="form.creditRatingLogicFlag" placeholder="请输入信用评级取值逻辑标识" />
        </el-form-item>
        <el-form-item label="行业统计标识" prop="industryStatisticsFlag">
          <el-select v-model="form.industryStatisticsFlag" placeholder="请选择行业统计标识">
            <el-option
              v-for="dict in dict.type.ast_industry_statistics_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单一资产统计标识" prop="singleAssetStatisticsFlag">
          <el-select v-model="form.singleAssetStatisticsFlag" placeholder="请选择单一资产统计标识">
            <el-option
              v-for="dict in dict.type.ast_single_asset_statistics_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="五级分类资产统计标识" prop="fiveLevelStatisticsFlag">
          <el-select v-model="form.fiveLevelStatisticsFlag" placeholder="请选择五级分类资产统计标识">
            <el-option
              v-for="dict in dict.type.ast_five_level_statistics_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="利差久期资产统计标识" prop="spreadDurationStatisticsFlag">
          <el-radio-group v-model="form.spreadDurationStatisticsFlag">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            仅允许导入xls、xlsx格式文件。
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssetBasicConfig, getAssetBasicConfig, delAssetBasicConfig, addAssetBasicConfig, updateAssetBasicConfig } from "@/api/ast/asset/basic/config";

export default {
  name: "AssetBasicConfig",
  dicts: ['ast_asset_sub_sub_category', 'ast_fixed_income_sub_category', 'ast_industry_statistics_flag', 'ast_single_asset_statistics_flag', 'ast_five_level_statistics_flag'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资产基础配置表表格数据
      assetBasicConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        sequenceNumber: null,
        assetSubSubCategory: null,
        fixedIncomeSubCategory: null,
        calculableCashflowFlag: null,
        creditRatingLogicFlag: null,
        industryStatisticsFlag: null,
        singleAssetStatisticsFlag: null,
        fiveLevelStatisticsFlag: null,
        spreadDurationStatisticsFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式应为YYYYMM", trigger: "blur" }
        ],
        sequenceNumber: [
          { required: true, message: "配置序号不能为空", trigger: "blur" }
        ],
        assetSubSubCategory: [
          { required: true, message: "资产小小类不能为空", trigger: "change" }
        ],
        calculableCashflowFlag: [
          { required: true, message: "可计算现金流标识不能为空", trigger: "change" }
        ],
        creditRatingLogicFlag: [
          { required: true, message: "信用评级取值逻辑标识不能为空", trigger: "blur" }
        ],
        industryStatisticsFlag: [
          { required: true, message: "行业统计标识不能为空", trigger: "change" }
        ],
        singleAssetStatisticsFlag: [
          { required: true, message: "单一资产统计标识不能为空", trigger: "change" }
        ],
        fiveLevelStatisticsFlag: [
          { required: true, message: "五级分类资产统计标识不能为空", trigger: "change" }
        ],
        spreadDurationStatisticsFlag: [
          { required: true, message: "利差久期资产统计标识不能为空", trigger: "change" }
        ]
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ast/asset/basic/config/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询资产基础配置表列表 */
    getList() {
      this.loading = true;
      listAssetBasicConfig(this.queryParams).then(response => {
        this.assetBasicConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        sequenceNumber: null,
        assetSubSubCategory: null,
        fixedIncomeSubCategory: null,
        calculableCashflowFlag: "0",
        creditRatingLogicFlag: "0",
        industryStatisticsFlag: "01",
        singleAssetStatisticsFlag: "01",
        fiveLevelStatisticsFlag: "01",
        spreadDurationStatisticsFlag: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加资产基础配置表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAssetBasicConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改资产基础配置表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAssetBasicConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssetBasicConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除资产基础配置表编号为"' + ids + '"的数据项？').then(function() {
        return delAssetBasicConfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ast/asset/basic/config/export', {
        ...this.queryParams
      }, `asset_basic_config_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "资产基础配置表导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('ast/asset/basic/config/importTemplate', {}, `asset_basic_config_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
