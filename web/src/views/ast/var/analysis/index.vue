<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select
          v-model="queryParams.dataType"
          placeholder="请选择数据类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.ast_var_data_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账户名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="证券代码" prop="securityCode">
        <el-input
          v-model="queryParams.securityCode"
          placeholder="请输入证券代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维度名称" prop="dimensionName">
        <el-input
          v-model="queryParams.dimensionName"
          placeholder="请输入维度名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资产名称" prop="assetName">
        <el-input
          v-model="queryParams.assetName"
          placeholder="请输入资产名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ast:var:analysis:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ast:var:analysis:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ast:var:analysis:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ast:var:analysis:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleImportCommand" v-hasPermi="['ast:var:analysis:import']">
          <el-button type="info" plain icon="el-icon-upload2" size="mini">
            导入<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="bond-1year">债基1年导入</el-dropdown-item>
            <el-dropdown-item command="bond-3year">债基3年导入</el-dropdown-item>
            <el-dropdown-item command="equity-1year">权益1年导入</el-dropdown-item>
            <el-dropdown-item command="equity-3year">权益3年导入</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="varAnalysisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="数据类型" align="center" prop="dataType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_var_data_type" :value="scope.row.dataType"/>
        </template>
      </el-table-column>
      <el-table-column label="账户名称" align="center" prop="accountName" />
      <el-table-column label="证券代码" align="center" prop="securityCode" />
      <el-table-column label="维度名称" align="center" prop="dimensionName" :show-overflow-tooltip="true" />
      <el-table-column label="资产名称" align="center" prop="assetName" :show-overflow-tooltip="true" />
      <el-table-column label="周期" align="center" prop="periodDate" />
      <el-table-column label="市值金额" align="center" prop="marketValue" />
      <el-table-column label="VaR金额" align="center" prop="varAmount" />
      <el-table-column label="增量VaR金额" align="center" prop="incrementalVarAmount" />
      <el-table-column label="CTE百分比" align="center" prop="ctePercentage" />
      <el-table-column label="CTE金额" align="center" prop="cteAmount" />
      <el-table-column label="成份VaR百分比" align="center" prop="componentVarPercentage" />
      <el-table-column label="成份VaR贡献百分比" align="center" prop="componentVarContributionPercentage" />
      <el-table-column label="成分VaR金额" align="center" prop="componentVarAmount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ast:var:analysis:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ast:var:analysis:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改VaR值分析对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
        </el-form-item>
        <el-form-item label="数据类型" prop="dataType">
          <el-select v-model="form.dataType" placeholder="请选择数据类型">
            <el-option
              v-for="dict in dict.type.ast_var_data_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户名称" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入账户名称" />
        </el-form-item>
        <el-form-item label="证券代码" prop="securityCode">
          <el-input v-model="form.securityCode" placeholder="请输入证券代码" />
        </el-form-item>
        <el-form-item label="维度名称" prop="dimensionName">
          <el-input v-model="form.dimensionName" placeholder="请输入维度名称" />
        </el-form-item>
        <el-form-item label="资产名称" prop="assetName">
          <el-input v-model="form.assetName" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="周期" prop="periodDate">
          <el-input v-model="form.periodDate" placeholder="请输入周期" />
        </el-form-item>
        <el-form-item label="市值金额" prop="marketValue">
          <el-input v-model="form.marketValue" placeholder="请输入市值金额" />
        </el-form-item>
        <el-form-item label="VaR金额" prop="varAmount">
          <el-input v-model="form.varAmount" placeholder="请输入VaR金额" />
        </el-form-item>
        <el-form-item label="增量VaR金额" prop="incrementalVarAmount">
          <el-input v-model="form.incrementalVarAmount" placeholder="请输入增量VaR金额" />
        </el-form-item>
        <el-form-item label="CTE百分比" prop="ctePercentage">
          <el-input v-model="form.ctePercentage" placeholder="请输入CTE百分比" />
        </el-form-item>
        <el-form-item label="CTE金额" prop="cteAmount">
          <el-input v-model="form.cteAmount" placeholder="请输入CTE金额" />
        </el-form-item>
        <el-form-item label="成份VaR百分比" prop="componentVarPercentage">
          <el-input v-model="form.componentVarPercentage" placeholder="请输入成份VaR百分比" />
        </el-form-item>
        <el-form-item label="成份VaR贡献百分比" prop="componentVarContributionPercentage">
          <el-input v-model="form.componentVarContributionPercentage" placeholder="请输入成份VaR贡献百分比" />
        </el-form-item>
        <el-form-item label="成分VaR金额" prop="componentVarAmount">
          <el-input v-model="form.componentVarAmount" placeholder="请输入成分VaR金额" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-form ref="uploadForm" :model="upload" label-width="80px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="upload.accountingPeriod" placeholder="请输入账期，格式YYYYMM" />
        </el-form-item>
      </el-form>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?accountingPeriod=' + upload.accountingPeriod"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVarAnalysis, getVarAnalysis, delVarAnalysis, addVarAnalysis, updateVarAnalysis } from "@/api/ast/varAnalysis";
import { getToken } from "@/utils/auth";

export default {
  name: "VarAnalysis",
  dicts: ['ast_var_data_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VaR值分析表格数据
      varAnalysisList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        dataType: null,
        accountName: null,
        securityCode: null,
        dimensionName: null,
        assetName: null,
        periodDate: null,
        marketValue: null,
        varAmount: null,
        incrementalVarAmount: null,
        ctePercentage: null,
        cteAmount: null,
        componentVarPercentage: null,
        componentVarContributionPercentage: null,
        componentVarAmount: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式应为YYYYMM", trigger: "blur" }
        ],
        dataType: [
          { required: true, message: "数据类型不能为空", trigger: "change" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "blur" }
        ],
        dimensionName: [
          { required: true, message: "维度名称不能为空", trigger: "blur" }
        ],
        periodDate: [
          { required: true, message: "周期不能为空", trigger: "blur" },
          { pattern: /^\d{8}$/, message: "周期格式应为YYYYMMDD", trigger: "blur" }
        ]
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/ast/var/analysis/import/bond-1year",
        // 账期参数
        accountingPeriod: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询VaR值分析列表 */
    getList() {
      this.loading = true;
      listVarAnalysis(this.queryParams).then(response => {
        this.varAnalysisList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        dataType: null,
        accountName: null,
        securityCode: null,
        dimensionName: null,
        assetName: null,
        periodDate: null,
        marketValue: null,
        varAmount: null,
        incrementalVarAmount: null,
        ctePercentage: null,
        cteAmount: null,
        componentVarPercentage: null,
        componentVarContributionPercentage: null,
        componentVarAmount: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加VaR值分析";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getVarAnalysis(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改VaR值分析";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateVarAnalysis(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVarAnalysis(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除VaR值分析编号为"' + ids + '"的数据项？').then(function() {
        return delVarAnalysis(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ast/var/analysis/export', {
        ...this.queryParams
      }, `var_analysis_${new Date().getTime()}.xlsx`)
    },
    /** 导入下拉菜单操作 */
    handleImportCommand(command) {
      this.upload.title = this.getImportTitle(command);
      this.upload.url = process.env.VUE_APP_BASE_API + "/ast/var/analysis/import/" + command;
      this.upload.open = true;
      this.upload.accountingPeriod = "";
    },
    /** 获取导入标题 */
    getImportTitle(command) {
      const titles = {
        'bond-1year': '债基1年VAR数据导入',
        'bond-3year': '债基3年VAR数据导入',
        'equity-1year': '权益1年VAR数据导入',
        'equity-3year': '权益3年VAR数据导入'
      };
      return titles[command] || 'VAR数据导入';
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('ast/var/analysis/importTemplate', {}, `var_analysis_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      if (!this.upload.accountingPeriod) {
        this.$modal.msgError("请输入账期");
        return;
      }
      // 验证账期格式
      if (!/^\d{6}$/.test(this.upload.accountingPeriod)) {
        this.$modal.msgError("账期格式应为YYYYMM，如202406");
        return;
      }
      this.$refs.upload.submit();
    }
  }
};
</script>
