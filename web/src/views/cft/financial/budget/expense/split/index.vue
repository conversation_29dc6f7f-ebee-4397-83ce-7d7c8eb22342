<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="情景名称" prop="scenarioName">
        <el-select v-model="queryParams.scenarioName" placeholder="请选择情景名称" clearable>
          <el-option
            v-for="dict in dict.type.cft_scenario_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="财务费用类型" prop="financialExpenseType">
        <el-select v-model="queryParams.financialExpenseType" placeholder="请选择财务费用类型" clearable>
          <el-option
            v-for="dict in dict.type.cft_financial_expense_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option
            v-for="dict in dict.type.cost_business_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select v-model="queryParams.designType" placeholder="请选择设计类型" clearable>
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cft:financial:budget:expense:split:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cft:financial:budget:expense:split:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cft:financial:budget:expense:split:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cft:financial:budget:expense:split:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cft:financial:budget:expense:split:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="financialBudgetExpenseSplitList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="情景名称" align="center" prop="scenarioName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cft_scenario_name" :value="scope.row.scenarioName"/>
        </template>
      </el-table-column>
      <el-table-column label="财务费用类型" align="center" prop="financialExpenseType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cft_financial_expense_type" :value="scope.row.financialExpenseType"/>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_business_type" :value="scope.row.businessType"/>
        </template>
      </el-table-column>
      <el-table-column label="设计类型" align="center" prop="designType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="现金流值集" align="center" prop="cashFlowValueSet" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewCashFlowValueSet(scope.row)"
            v-hasPermi="['cft:financial:budget:expense:split:query']"
          >查看详情</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cft:financial:budget:expense:split:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cft:financial:budget:expense:split:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改财务预算费用拆分表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期，格式：YYYYMM" />
        </el-form-item>
        <el-form-item label="情景名称" prop="scenarioName">
          <el-select v-model="form.scenarioName" placeholder="请选择情景名称">
            <el-option
              v-for="dict in dict.type.cft_scenario_name"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="财务费用类型" prop="financialExpenseType">
          <el-select v-model="form.financialExpenseType" placeholder="请选择财务费用类型">
            <el-option
              v-for="dict in dict.type.cft_financial_expense_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="form.businessType" placeholder="请选择业务类型">
            <el-option
              v-for="dict in dict.type.cost_business_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设计类型" prop="designType">
          <el-select v-model="form.designType" placeholder="请选择设计类型">
            <el-option
              v-for="dict in dict.type.cost_design_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="现金流值集" prop="cashFlowValueSet">
          <el-input v-model="form.cashFlowValueSet" type="textarea" placeholder="请输入现金流值集（JSON格式）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 现金流值集查看对话框 -->
    <el-dialog :title="cashFlowValueSetInfo.title" :visible.sync="cashFlowValueSetInfo.open" width="800px" append-to-body>
      <el-table :data="cashFlowValueSetInfo.tableData" border style="width: 100%">
        <el-table-column prop="index" label="序号" width="80" align="center" />
        <el-table-column prop="date" label="日期" width="120" align="center" />
        <el-table-column prop="value" label="现金流金额" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.value) }}
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cashFlowValueSetInfo.open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="500px" append-to-body>
      <div style="margin-bottom: 15px;">
        <el-alert
          title="水平格式说明：账期、情景名称、财务费用类型、业务类型、设计类型 + 12个季度列（如2025Q1、2025Q2...）"
          type="info"
          :closable="false">
        </el-alert>
      </div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="upload.updateSupport" />更新已存在数据
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFinancialBudgetExpenseSplit,
  getFinancialBudgetExpenseSplit,
  addFinancialBudgetExpenseSplit,
  updateFinancialBudgetExpenseSplit,
  delFinancialBudgetExpenseSplit
} from "@/api/cft/financialBudgetExpenseSplit";
import { getToken } from "@/utils/auth";

export default {
  name: "FinancialBudgetExpenseSplit",
  dicts: [
    'cft_scenario_name',
    'cft_financial_expense_type',
    'cost_business_type',
    'cost_design_type'
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务预算费用拆分表格数据
      financialBudgetExpenseSplitList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 现金流值集查看信息
      cashFlowValueSetInfo: {
        open: false,
        title: '',
        tableData: []
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        scenarioName: null,
        financialExpenseType: null,
        businessType: null,
        designType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式为6位数字，如202412", trigger: "blur" }
        ],
        scenarioName: [
          { required: true, message: "情景名称不能为空", trigger: "change" }
        ],
        financialExpenseType: [
          { required: true, message: "财务费用类型不能为空", trigger: "change" }
        ],
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ],
        designType: [
          { required: true, message: "设计类型不能为空", trigger: "change" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "导入财务预算费用拆分数据",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已存在数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cft/financial/budget/expense/split/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询财务预算费用拆分表列表 */
    getList() {
      this.loading = true;
      listFinancialBudgetExpenseSplit(this.queryParams).then(response => {
        this.financialBudgetExpenseSplitList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        scenarioName: null,
        financialExpenseType: null,
        businessType: null,
        designType: null,
        cashFlowValueSet: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加财务预算费用拆分";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getFinancialBudgetExpenseSplit(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务预算费用拆分";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFinancialBudgetExpenseSplit(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFinancialBudgetExpenseSplit(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除财务预算费用拆分编号为"' + ids + '"的数据项？').then(function() {
        return delFinancialBudgetExpenseSplit(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看现金流值集按钮操作 */
    handleViewCashFlowValueSet(row) {
      try {
        // 清空之前的数据
        this.cashFlowValueSetInfo.tableData = [];

        // 设置标题
        this.cashFlowValueSetInfo.title = `${row.accountingPeriod} - ${this.getDictLabel('cft_scenario_name', row.scenarioName)} - ${this.getDictLabel('cft_financial_expense_type', row.financialExpenseType)}`;

        // 解析JSON数据
        if (row.cashFlowValueSet) {
          try {
            const cashFlowData = JSON.parse(row.cashFlowValueSet);
            this.processCashFlowData(cashFlowData);
          } catch (parseError) {
            console.warn("初次解析失败，尝试直接提取数据", parseError);
            // 如果解析失败，尝试其他方法
            this.extractCashFlowDataWithRegex(row.cashFlowValueSet);
          }
        } else {
          this.$message.warning("现金流值集数据为空");
        }

        // 打开对话框
        this.cashFlowValueSetInfo.open = true;
      } catch (error) {
        this.$modal.msgError("解析现金流值集数据失败：" + error.message);
        console.error("解析现金流值集数据失败", error);
      }
    },

    /**
     * 处理现金流值集数据
     */
    processCashFlowData(cashFlowData) {
      const tableData = [];

      // 遍历并格式化数据
      Object.keys(cashFlowData).forEach(key => {
        const item = cashFlowData[key];
        if (item && typeof item === 'object') {
          tableData.push({
            index: key,
            date: item.date || item.日期 || '',
            value: item.value || item.val || item.值 || 0
          });
        }
      });

      // 按序号排序
      this.cashFlowValueSetInfo.tableData = tableData.sort((a, b) => parseInt(a.index) - parseInt(b.index));
    },

    /**
     * 使用正则表达式提取现金流数据
     */
    extractCashFlowDataWithRegex(jsonString) {
      try {
        const tableData = [];

        // 尝试不同的正则表达式模式
        const patterns = [
          // 模式1: 标准格式 {"key":{"date":"value","val":value}}
          /["']?(\d+)["']?\s*:\s*{\s*["']?(?:date|日期)["']?\s*:\s*["']([^"']+)["']\s*,\s*["'](?:val|value|值)["']\s*:\s*([\d\.\-]+)/g,
          // 模式2: 可能的变体格式
          /["']?(\d+)["']?\s*:\s*{\s*["']?(?:date|日期)["']?\s*:\s*["']([^"']+)["']\s*,\s*(?:val|value|值)\s*:\s*([\d\.\-]+)/g
        ];

        for (const regex of patterns) {
          let match;
          const patternData = [];
          regex.lastIndex = 0;

          while ((match = regex.exec(jsonString)) !== null) {
            patternData.push({
              index: match[1],
              date: match[2],
              value: parseFloat(match[3])
            });
          }

          if (patternData.length > 0) {
            tableData.push(...patternData);
            break;
          }
        }

        if (tableData.length > 0) {
          this.cashFlowValueSetInfo.tableData = tableData.sort((a, b) => parseInt(a.index) - parseInt(b.index));
          this.$message.warning("现金流值集数据格式有误，已尝试提取并显示数据");
        } else {
          this.$message.error("无法解析现金流值集数据");
          this.cashFlowValueSetInfo.tableData = [{
            index: 1,
            date: '原始数据',
            value: jsonString.substring(0, 100) + (jsonString.length > 100 ? '...' : '')
          }];
        }
      } catch (error) {
        console.error("提取现金流数据失败", error);
        this.$message.error("无法解析现金流值集数据，请检查数据格式");
      }
    },

    /**
     * 格式化数字显示
     */
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      return Number(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /**
     * 获取字典标签
     */
    getDictLabel(dictType, dictValue) {
      const dictOptions = this.dict.type[dictType];
      if (dictOptions && dictOptions.length > 0) {
        const dictItem = dictOptions.find(item => item.value === dictValue);
        return dictItem ? dictItem.label : dictValue;
      }
      return dictValue;
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'cft/financial/budget/expense/split/export',
        {
          ...this.queryParams
        },
        `financial_budget_expense_split_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "财务预算费用拆分数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('cft/financial/budget/expense/split/exportTemplate', {}, `财务预算费用拆分模板_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
