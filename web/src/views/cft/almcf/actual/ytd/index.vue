<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cft:almcf:actual:ytd:add']"
        >
          新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cft:almcf:actual:ytd:edit']"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cft:almcf:actual:ytd:remove']"
        >
          删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cft:almcf:actual:ytd:import']"
        >
          导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cft:almcf:actual:ytd:export']"
        >
          导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-operation"
          size="mini"
          @click="handleCalculate"
          v-hasPermi="['cft:almcf:actual:ytd:calculate']"
        >
          计算
        </el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="almcfActualYtdList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="项目名称" align="center" prop="itemName" :show-overflow-tooltip="true" />
      <el-table-column label="传统账户" align="center" prop="traditionalAccount" />
      <el-table-column label="分红账户" align="center" prop="bonusAccount" />
      <el-table-column label="万能账户" align="center" prop="universalAccount" />
      <el-table-column label="投连账户" align="center" prop="investmentAccount" />
      <el-table-column label="普通账户" align="center" prop="ordinaryAccount" />
      <el-table-column label="公司整体" align="center" prop="totalAccount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cft:almcf:actual:ytd:edit']"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cft:almcf:actual:ytd:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改ALMCF实际发生数本年累计表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountingPeriod">
              <el-input
                v-model="form.accountingPeriod"
                placeholder="请输入账期(YYYYMM)"
                maxlength="6"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="itemName">
              <el-input
                v-model="form.itemName"
                placeholder="请输入项目名称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="传统账户" prop="traditionalAccount">
              <el-input-number
                v-model="form.traditionalAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入传统账户金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分红账户" prop="bonusAccount">
              <el-input-number
                v-model="form.bonusAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入分红账户金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="万能账户" prop="universalAccount">
              <el-input-number
                v-model="form.universalAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入万能账户金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投连账户" prop="investmentAccount">
              <el-input-number
                v-model="form.investmentAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入投连账户金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="普通账户" prop="ordinaryAccount">
              <el-input-number
                v-model="form.ordinaryAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入普通账户金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司整体" prop="totalAccount">
              <el-input-number
                v-model="form.totalAccount"
                :precision="2"
                style="width: 100%"
                placeholder="请输入公司整体金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- ALMCF实际发生数本年累计表导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的ALMCF实际发生数本年累计表数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- ALMCF计算对话框 -->
    <el-dialog
      title="ALMCF实际发生数本年累计表计算"
      :visible.sync="calculateDialog.open"
      width="400px"
      append-to-body
    >
      <el-form ref="calculateForm" :model="calculateDialog.form" :rules="calculateDialog.rules" label-width="80px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input
            v-model="calculateDialog.form.accountingPeriod"
            placeholder="请输入账期(YYYYMM)"
            maxlength="6"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCalculate" :loading="calculateDialog.loading">确 定</el-button>
        <el-button @click="calculateDialog.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAlmcfActualYtd,
  getAlmcfActualYtd,
  delAlmcfActualYtd,
  addAlmcfActualYtd,
  updateAlmcfActualYtd,
  calculateAlmcfActualYtd
} from '@/api/cft/almcfActualYtd';
import { getToken } from '@/utils/auth';

export default {
  name: 'AlmcfActualYtd',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ALMCF实际发生数本年累计表表格数据
      almcfActualYtdList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        itemName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: '账期不能为空', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '账期格式必须为YYYYMM', trigger: 'blur' }
        ],
        itemName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' },
          { max: 100, message: '项目名称长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层（ALMCF实际发生数本年累计表导入）
        open: false,
        // 弹出层标题（ALMCF实际发生数本年累计表导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的ALMCF实际发生数本年累计表数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/cft/almcf/actual/ytd/importData'
      },
      // 计算参数
      calculateDialog: {
        // 是否显示弹出层
        open: false,
        // 是否正在计算
        loading: false,
        // 表单参数
        form: {
          accountingPeriod: null
        },
        // 表单校验
        rules: {
          accountingPeriod: [
            { required: true, message: '账期不能为空', trigger: 'blur' },
            { pattern: /^\d{6}$/, message: '账期格式必须为YYYYMM', trigger: 'blur' }
          ]
        }
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询ALMCF实际发生数本年累计表列表 */
    getList() {
      this.loading = true;
      listAlmcfActualYtd(this.queryParams).then(response => {
        this.almcfActualYtdList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        itemName: null,
        traditionalAccount: null,
        bonusAccount: null,
        universalAccount: null,
        investmentAccount: null,
        ordinaryAccount: null,
        totalAccount: null,
        remark: null
      };
      this.resetForm('form');
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加ALMCF实际发生数本年累计表';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAlmcfActualYtd(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = '修改ALMCF实际发生数本年累计表';
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAlmcfActualYtd(this.form).then(response => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addAlmcfActualYtd(this.form).then(response => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除ALMCF实际发生数本年累计表编号为"' + ids + '"的数据项？').then(function() {
        return delAlmcfActualYtd(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'cft/almcf/actual/ytd/export',
        {
          ...this.queryParams
        },
        `almcf_actual_ytd_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = 'ALMCF实际发生数本年累计表导入';
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('cft/almcf/actual/ytd/importTemplate', {}, `ALMCF实际发生数本年累计表模板_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 计算按钮操作 */
    handleCalculate() {
      this.calculateDialog.form.accountingPeriod = null;
      this.calculateDialog.open = true;
    },
    /** 提交计算 */
    submitCalculate() {
      this.$refs['calculateForm'].validate(valid => {
        if (valid) {
          this.calculateDialog.loading = true;
          calculateAlmcfActualYtd(this.calculateDialog.form.accountingPeriod).then(response => {
            this.$modal.msgSuccess(response.msg);
            this.calculateDialog.open = false;
            this.calculateDialog.loading = false;
            this.getList();
          }).catch(() => {
            this.calculateDialog.loading = false;
          });
        }
      });
    }
  }
};
</script>
