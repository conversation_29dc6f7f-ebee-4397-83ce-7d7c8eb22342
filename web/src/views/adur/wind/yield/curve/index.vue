<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="曲线名称" prop="curveName">
        <el-input
          v-model="queryParams.curveName"
          placeholder="请输入折现曲线名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="曲线标识" prop="curveId">
        <el-input
          v-model="queryParams.curveId"
          placeholder="请输入折现曲线标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="日期" prop="date">
        <el-date-picker clearable
          v-model="queryParams.date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['adur:wind:yield:curve:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['adur:wind:yield:curve:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['adur:wind:yield:curve:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['adur:wind:yield:curve:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['adur:wind:yield:curve:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="windYieldCurveList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="折现曲线名称" align="center" prop="curveName" />
      <el-table-column label="折现曲线标识" align="center" prop="curveId" />
      <el-table-column label="日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="期限0" align="center" prop="term0" />
      <el-table-column label="期限1" align="center" prop="term1" />
      <el-table-column label="期限2" align="center" prop="term2" />
      <el-table-column label="期限3" align="center" prop="term3" />
      <el-table-column label="期限4" align="center" prop="term4" />
      <el-table-column label="期限5" align="center" prop="term5" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['adur:wind:yield:curve:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['adur:wind:yield:curve:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改万得收益率曲线对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="曲线名称" prop="curveName">
          <el-input v-model="form.curveName" placeholder="请输入折现曲线名称" />
        </el-form-item>
        <el-form-item label="曲线标识" prop="curveId">
          <el-input v-model="form.curveId" placeholder="请输入折现曲线标识" />
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker clearable
            v-model="form.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="期限0" prop="term0">
          <el-input v-model="form.term0" placeholder="请输入期限0" />
        </el-form-item>
        <el-form-item label="期限1" prop="term1">
          <el-input v-model="form.term1" placeholder="请输入期限1" />
        </el-form-item>
        <el-form-item label="期限2" prop="term2">
          <el-input v-model="form.term2" placeholder="请输入期限2" />
        </el-form-item>
        <el-form-item label="期限3" prop="term3">
          <el-input v-model="form.term3" placeholder="请输入期限3" />
        </el-form-item>
        <el-form-item label="期限4" prop="term4">
          <el-input v-model="form.term4" placeholder="请输入期限4" />
        </el-form-item>
        <el-form-item label="期限5" prop="term5">
          <el-input v-model="form.term5" placeholder="请输入期限5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 万得收益率曲线导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的万得收益率曲线数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWindYieldCurve, getWindYieldCurve, delWindYieldCurve, addWindYieldCurve, updateWindYieldCurve } from "@/api/adur/wind/yield/curve";

export default {
  name: "WindYieldCurve",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 万得收益率曲线表格数据
      windYieldCurveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        curveName: null,
        curveId: null,
        date: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        curveName: [
          { required: true, message: "折现曲线名称不能为空", trigger: "blur" }
        ],
        curveId: [
          { required: true, message: "折现曲线标识不能为空", trigger: "blur" }
        ],
        date: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ]
      },
      // 万得收益率曲线导入参数
      upload: {
        // 是否显示弹出层（万得收益率曲线导入）
        open: false,
        // 弹出层标题（万得收益率曲线导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的万得收益率曲线数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/adur/wind/yield/curve/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询万得收益率曲线列表 */
    getList() {
      this.loading = true;
      listWindYieldCurve(this.queryParams).then(response => {
        this.windYieldCurveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        curveName: null,
        curveId: null,
        date: null,
        term0: null,
        term1: null,
        term2: null,
        term3: null,
        term4: null,
        term5: null,
        term6: null,
        term7: null,
        term8: null,
        term9: null,
        term10: null,
        term11: null,
        term12: null,
        term13: null,
        term14: null,
        term15: null,
        term16: null,
        term17: null,
        term18: null,
        term19: null,
        term20: null,
        term21: null,
        term22: null,
        term23: null,
        term24: null,
        term25: null,
        term26: null,
        term27: null,
        term28: null,
        term29: null,
        term30: null,
        term31: null,
        term32: null,
        term33: null,
        term34: null,
        term35: null,
        term36: null,
        term37: null,
        term38: null,
        term39: null,
        term40: null,
        term41: null,
        term42: null,
        term43: null,
        term44: null,
        term45: null,
        term46: null,
        term47: null,
        term48: null,
        term49: null,
        term50: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加万得收益率曲线";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWindYieldCurve(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改万得收益率曲线";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateWindYieldCurve(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWindYieldCurve(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除万得收益率曲线编号为"' + ids + '"的数据项？').then(function() {
        return delWindYieldCurve(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('adur/wind/yield/curve/export', {
        ...this.queryParams
      }, `万得收益率曲线_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "万得收益率曲线导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('adur/wind/yield/curve/importTemplate', {
      }, `万得收益率曲线导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
