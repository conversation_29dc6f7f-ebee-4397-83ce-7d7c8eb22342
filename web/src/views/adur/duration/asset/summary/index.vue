<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountPeriod">
        <el-input
          v-model="queryParams.accountPeriod"
          placeholder="请输入账期"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select
          v-model="queryParams.accountName"
          placeholder="请选择账户名称"
          clearable
          size="small"
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_account_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['adur:duration:asset:summary:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['adur:duration:asset:summary:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['adur:duration:asset:summary:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['adur:duration:asset:summary:export']"
        >导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="durationAssetSummaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountPeriod" />
      <el-table-column label="账户名称" align="center" prop="accountName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_account_name" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="市值" align="center" prop="marketValue" />
      <el-table-column label="账面余额" align="center" prop="bookBalance" />
      <el-table-column label="账面价值" align="center" prop="bookValue" />
      <el-table-column label="评估时点到期收益率" align="center" prop="evalMaturityYield" />
      <el-table-column label="评估时点资产现值" align="center" prop="evalPresentValue" />
      <el-table-column label="资产修正久期" align="center" prop="assetModifiedDuration" />
      <el-table-column label="资产有效久期" align="center" prop="assetEffectiveDuration" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['adur:duration:asset:summary:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['adur:duration:asset:summary:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改久期资产结果汇总表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountPeriod">
              <el-input v-model="form.accountPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-select v-model="form.accountName" placeholder="请选择账户名称" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.adur_account_name"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="市值" prop="marketValue">
              <el-input v-model="form.marketValue" placeholder="请输入市值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面余额" prop="bookBalance">
              <el-input v-model="form.bookBalance" placeholder="请输入账面余额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值" prop="bookValue">
              <el-input v-model="form.bookValue" placeholder="请输入账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面价值σ0%" prop="bookValueSigma0">
              <el-input v-model="form.bookValueSigma0" placeholder="请输入账面价值σ0%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值σ9%" prop="bookValueSigma9">
              <el-input v-model="form.bookValueSigma9" placeholder="请输入账面价值σ9%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面价值σ17%" prop="bookValueSigma17">
              <el-input v-model="form.bookValueSigma17" placeholder="请输入账面价值σ17%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值σ77%" prop="bookValueSigma77">
              <el-input v-model="form.bookValueSigma77" placeholder="请输入账面价值σ77%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评估时点到期收益率" prop="evalMaturityYield">
              <el-input v-model="form.evalMaturityYield" placeholder="请输入评估时点到期收益率" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="评估时点资产现值" prop="evalPresentValue">
              <el-input v-model="form.evalPresentValue" placeholder="请输入评估时点资产现值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产修正久期" prop="assetModifiedDuration">
              <el-input v-model="form.assetModifiedDuration" placeholder="请输入资产修正久期" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资产有效久期" prop="assetEffectiveDuration">
              <el-input v-model="form.assetEffectiveDuration" placeholder="请输入资产有效久期" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  listDurationAssetSummary,
  getDurationAssetSummary,
  delDurationAssetSummary,
  addDurationAssetSummary,
  updateDurationAssetSummary
} from "@/api/adur/durationAssetSummary";

export default {
  name: "DurationAssetSummary",
  dicts: ['adur_account_name'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 久期资产结果汇总表表格数据
      durationAssetSummaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountPeriod: null,
        accountName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ]
      },

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询久期资产结果汇总表列表 */
    getList() {
      this.loading = true;
      listDurationAssetSummary(this.queryParams).then(response => {
        this.durationAssetSummaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountPeriod: null,
        accountName: null,
        marketValue: null,
        bookBalance: null,
        bookValue: null,
        bookValueSigma0: null,
        bookValueSigma9: null,
        bookValueSigma17: null,
        bookValueSigma77: null,
        evalMaturityYield: null,
        evalPresentValue: null,
        assetModifiedDuration: null,
        assetEffectiveDuration: null,
        dv100: null,
        dv1005: null,
        dv101: null,
        dv102: null,
        dv103: null,
        dv104: null,
        dv105: null,
        dv106: null,
        dv107: null,
        dv108: null,
        dv1010: null,
        dv1012: null,
        dv1015: null,
        dv1020: null,
        dv1025: null,
        dv1030: null,
        dv1035: null,
        dv1040: null,
        dv1045: null,
        dv1050: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加久期资产结果汇总表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDurationAssetSummary(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改久期资产结果汇总表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDurationAssetSummary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDurationAssetSummary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除久期资产结果汇总表编号为"' + ids + '"的数据项？').then(function() {
        return delDurationAssetSummary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'adur/duration/asset/summary/export',
        {
          ...this.queryParams
        },
        `duration_asset_summary_${new Date().getTime()}.xlsx`
      );
    },

  }
};
</script>
