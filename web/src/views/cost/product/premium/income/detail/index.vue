<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="统计期间" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入统计期间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品精算代码" prop="actuarialCode">
        <el-input
          v-model="queryParams.actuarialCode"
          placeholder="请输入产品精算代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select v-model="queryParams.designType" placeholder="请选择设计类型" clearable>
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cost:product:premium:income:detail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cost:product:premium:income:detail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cost:product:premium:income:detail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cost:product:premium:income:detail:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cost:product:premium:income:detail:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productPremiumIncomeDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false" />
      <el-table-column label="统计期间" align="center" prop="accountingPeriod" width="120" />
      <el-table-column label="产品精算代码" align="center" prop="actuarialCode" width="140" />
      <el-table-column label="产品业务代码" align="center" prop="businessCode" width="140" />
      <el-table-column label="产品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="设计类型" align="center" prop="designType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <!-- 本月字段 -->
      <el-table-column label="原保费-趸交(本月)" align="center" prop="currentSinglePremium" width="140" />
      <el-table-column label="原保费-期交(本月)" align="center" prop="currentRegularPremium" width="140" />
      <el-table-column label="原保费-续期(本月)" align="center" prop="currentRenewalPremium" width="140" />
      <el-table-column label="原保费-合计(本月)" align="center" prop="currentTotalPremium" width="140" />
      <el-table-column label="万能投连-趸交(本月)" align="center" prop="currentUlSingle" width="140" />
      <el-table-column label="万能投连-期交(本月)" align="center" prop="currentUlRegular" width="140" />
      <el-table-column label="万能投连-续期(本月)" align="center" prop="currentUlRenewal" width="140" />
      <el-table-column label="万能投连-初始费用(本月)" align="center" prop="currentUlInitialFee" width="160" />
      <el-table-column label="万能投连-合计(本月)" align="center" prop="currentUlTotal" width="140" />
      <el-table-column label="规模保费合计(本月)" align="center" prop="currentScalePremium" width="140" />
      <el-table-column label="保户储金及投资款余额(本月)" align="center" prop="currentInvestmentBalance" width="180" />
      <el-table-column label="退保金(本月)" align="center" prop="currentSurrender" width="120" />
      <el-table-column label="万能投连领取(本月)" align="center" prop="currentUlWithdraw" width="140" />
      <el-table-column label="赔款支出(本月)" align="center" prop="currentClaim" width="120" />
      <el-table-column label="死伤医疗给付(本月)" align="center" prop="currentMedical" width="140" />
      <el-table-column label="满期给付(本月)" align="center" prop="currentMaturity" width="120" />
      <el-table-column label="年金给付(本月)" align="center" prop="currentAnnuity" width="120" />
      <el-table-column label="万能投连-赔款支出(本月)" align="center" prop="currentUlClaim" width="160" />
      <el-table-column label="万能投连-死伤医疗给付(本月)" align="center" prop="currentUlMedical" width="180" />
      <el-table-column label="万能投连-满期给付(本月)" align="center" prop="currentUlMaturity" width="160" />
      <el-table-column label="万能投连-年金给付(本月)" align="center" prop="currentUlAnnuity" width="160" />
      <el-table-column label="赔付支出合计(本月)" align="center" prop="currentTotalClaim" width="140" />
      <!-- 年累计字段 -->
      <el-table-column label="原保费-趸交(年累计)" align="center" prop="ytdSinglePremium" width="140" />
      <el-table-column label="原保费-期交(年累计)" align="center" prop="ytdRegularPremium" width="140" />
      <el-table-column label="原保费-续期(年累计)" align="center" prop="ytdRenewalPremium" width="140" />
      <el-table-column label="原保费-合计(年累计)" align="center" prop="ytdTotalPremium" width="140" />
      <el-table-column label="万能投连-趸交(年累计)" align="center" prop="ytdUlSingle" width="140" />
      <el-table-column label="万能投连-期交(年累计)" align="center" prop="ytdUlRegular" width="140" />
      <el-table-column label="万能投连-续期(年累计)" align="center" prop="ytdUlRenewal" width="140" />
      <el-table-column label="万能投连-初始费用(年累计)" align="center" prop="ytdUlInitialFee" width="160" />
      <el-table-column label="万能投连-合计(年累计)" align="center" prop="ytdUlTotal" width="140" />
      <el-table-column label="规模保费合计(年累计)" align="center" prop="ytdScalePremium" width="140" />
      <el-table-column label="保户储金及投资款余额(年累计)" align="center" prop="ytdInvestmentBalance" width="180" />
      <el-table-column label="退保金(年累计)" align="center" prop="ytdSurrender" width="120" />
      <el-table-column label="万能投连领取(年累计)" align="center" prop="ytdUlWithdraw" width="140" />
      <el-table-column label="赔款支出(年累计)" align="center" prop="ytdClaim" width="120" />
      <el-table-column label="死伤医疗给付(年累计)" align="center" prop="ytdMedical" width="140" />
      <el-table-column label="满期给付(年累计)" align="center" prop="ytdMaturity" width="120" />
      <el-table-column label="年金给付(年累计)" align="center" prop="ytdAnnuity" width="120" />
      <el-table-column label="万能投连-赔款支出(年累计)" align="center" prop="ytdUlClaim" width="160" />
      <el-table-column label="万能投连-死伤医疗给付(年累计)" align="center" prop="ytdUlMedical" width="180" />
      <el-table-column label="万能投连-满期给付(年累计)" align="center" prop="ytdUlMaturity" width="160" />
      <el-table-column label="万能投连-年金给付(年累计)" align="center" prop="ytdUlAnnuity" width="160" />
      <el-table-column label="赔付支出合计(年累计)" align="center" prop="ytdTotalClaim" width="140" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cost:product:premium:income:detail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cost:product:premium:income:detail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改分产品保费收入对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="统计期间" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入统计期间" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品精算代码" prop="actuarialCode">
              <el-input v-model="form.actuarialCode" placeholder="请输入产品精算代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品业务代码" prop="businessCode">
              <el-input v-model="form.businessCode" placeholder="请输入产品业务代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计类型" prop="designType">
              <el-select v-model="form.designType" placeholder="请选择设计类型">
                <el-option
                  v-for="dict in dict.type.cost_design_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="原保费-趸交" prop="currentSinglePremium">
              <el-input-number v-model="form.currentSinglePremium" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原保费-期交" prop="currentRegularPremium">
              <el-input-number v-model="form.currentRegularPremium" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="原保费-续期" prop="currentRenewalPremium">
              <el-input-number v-model="form.currentRenewalPremium" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原保费-合计" prop="currentTotalPremium">
              <el-input-number v-model="form.currentTotalPremium" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规模保费合计" prop="currentScalePremium">
              <el-input-number v-model="form.currentScalePremium" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保户储金及投资款余额" prop="currentInvestmentBalance">
              <el-input-number v-model="form.currentInvestmentBalance" :precision="2" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="upload.updateSupport" />更新已存在数据
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProductPremiumIncomeDetail,
  getProductPremiumIncomeDetail,
  addProductPremiumIncomeDetail,
  updateProductPremiumIncomeDetail,
  delProductPremiumIncomeDetail,
  exportProductPremiumIncomeDetail,
  importTemplate
} from "@/api/cost/product/premium/income/detail";
import { getToken } from "@/utils/auth";

export default {
  name: "ProductPremiumIncomeDetail",
  dicts: ['cost_design_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分产品保费收入表格数据
      productPremiumIncomeDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        actuarialCode: null,
        productName: null,
        designType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "统计期间不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "统计期间格式为6位数字，如202401", trigger: "blur" }
        ],
        actuarialCode: [
          { required: true, message: "产品精算代码不能为空", trigger: "blur" },
          { max: 20, message: "产品精算代码长度不能超过20个字符", trigger: "blur" },
          { pattern: /^[A-Za-z].*/, message: "产品精算代码必须以字母开头", trigger: "blur" }
        ],
        businessCode: [
          { required: true, message: "产品业务代码不能为空", trigger: "blur" },
          { max: 20, message: "产品业务代码长度不能超过20个字符", trigger: "blur" }
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
          { max: 100, message: "产品名称长度不能超过100个字符", trigger: "blur" }
        ],
        designType: [
          { required: true, message: "设计类型不能为空", trigger: "change" }
        ],
        currentSinglePremium: [
          { type: 'number', min: 0, message: "原保费-趸交不能小于0", trigger: "blur" }
        ],
        currentRegularPremium: [
          { type: 'number', min: 0, message: "原保费-期交不能小于0", trigger: "blur" }
        ],
        currentRenewalPremium: [
          { type: 'number', min: 0, message: "原保费-续期不能小于0", trigger: "blur" }
        ],
        currentTotalPremium: [
          { type: 'number', min: 0, message: "原保费-合计不能小于0", trigger: "blur" }
        ],
        currentScalePremium: [
          { type: 'number', min: 0, message: "规模保费合计不能小于0", trigger: "blur" }
        ],
        currentInvestmentBalance: [
          { type: 'number', min: 0, message: "保户储金及投资款余额不能小于0", trigger: "blur" }
        ],
        remark: [
          { max: 500, message: "备注长度不能超过500个字符", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "导入分产品保费收入数据",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已存在数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cost/product/premium/income/detail/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询分产品保费收入列表 */
    getList() {
      this.loading = true;
      listProductPremiumIncomeDetail(this.queryParams).then(response => {
        this.productPremiumIncomeDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        actuarialCode: null,
        businessCode: null,
        productName: null,
        designType: null,
        currentSinglePremium: null,
        currentRegularPremium: null,
        currentRenewalPremium: null,
        currentTotalPremium: null,
        currentScalePremium: null,
        currentInvestmentBalance: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加分产品保费收入";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getProductPremiumIncomeDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改分产品保费收入";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateProductPremiumIncomeDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProductPremiumIncomeDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除分产品保费收入编号为"' + ids + '"的数据项？').then(function() {
        return delProductPremiumIncomeDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'cost/product/premium/income/detail/export',
        {
          ...this.queryParams,
        },
        `product_premium_income_detail_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "导入分产品保费收入数据";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('cost/product/premium/income/detail/importTemplate', {}, `product_premium_income_detail_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
}
</script>
