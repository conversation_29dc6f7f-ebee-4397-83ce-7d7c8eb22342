<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="统计期间" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入统计期间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="companyCode">
        <el-input
          v-model="queryParams.companyCode"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品代码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道代码" prop="channelCode">
        <el-input
          v-model="queryParams.channelCode"
          placeholder="请输入渠道代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户代码" prop="accountCode">
        <el-input
          v-model="queryParams.accountCode"
          placeholder="请输入账户代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-input
          v-model="queryParams.accountName"
          placeholder="请输入账户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cost:premium:income:detail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cost:premium:income:detail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cost:premium:income:detail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cost:premium:income:detail:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cost:premium:income:detail:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="premiumIncomeDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="统计期间" align="center" prop="accountingPeriod" width="100" />
      <el-table-column label="公司代码" align="center" prop="companyCode" width="100" />
      <el-table-column label="公司名称" align="center" prop="companyName" width="120" />
      <el-table-column label="产品代码" align="center" prop="productCode" width="100" />
      <el-table-column label="产品名称" align="center" prop="productName" width="150" />
      <el-table-column label="渠道代码" align="center" prop="channelCode" width="100" />
      <el-table-column label="渠道名称" align="center" prop="channelName" width="120" />
      <el-table-column label="账户代码" align="center" prop="accountCode" width="100" />
      <el-table-column label="账户名称" align="center" prop="accountName" width="120" />
      <el-table-column label="原保费-趸交" align="center" prop="currentSinglePremium" width="120" />
      <el-table-column label="原保费-期交" align="center" prop="currentRegularPremium" width="120" />
      <el-table-column label="原保费-续期" align="center" prop="currentRenewalPremium" width="120" />
      <el-table-column label="原保费-合计" align="center" prop="currentTotalPremium" width="120" />
      <el-table-column label="万能投连-趸交" align="center" prop="currentUlSingle" width="120" />
      <el-table-column label="万能投连-期交" align="center" prop="currentUlRegular" width="120" />
      <el-table-column label="万能投连-续期" align="center" prop="currentUlRenewal" width="120" />
      <el-table-column label="万能投连-初始费用" align="center" prop="currentUlInitialFee" width="140" />
      <el-table-column label="万能投连-合计" align="center" prop="currentUlTotal" width="120" />
      <el-table-column label="规模保费合计" align="center" prop="currentScalePremium" width="120" />
      <el-table-column label="保户储金及投资款余额" align="center" prop="currentInvestmentBalance" width="160" />
      <el-table-column label="退保金" align="center" prop="currentSurrender" width="100" />
      <el-table-column label="万能投连领取" align="center" prop="currentUlWithdraw" width="120" />
      <el-table-column label="赔款支出" align="center" prop="currentClaim" width="100" />
      <el-table-column label="死伤医疗给付" align="center" prop="currentMedical" width="120" />
      <el-table-column label="满期给付" align="center" prop="currentMaturity" width="100" />
      <el-table-column label="年金给付" align="center" prop="currentAnnuity" width="100" />
      <el-table-column label="万能投连-赔款支出" align="center" prop="currentUlClaim" width="140" />
      <el-table-column label="万能投连-死伤医疗给付" align="center" prop="currentUlMedical" width="160" />
      <el-table-column label="万能投连-满期给付" align="center" prop="currentUlMaturity" width="140" />
      <el-table-column label="万能投连-年金给付" align="center" prop="currentUlAnnuity" width="140" />
      <el-table-column label="赔付支出合计" align="center" prop="currentTotalClaim" width="120" />
      <!-- 年累计字段 -->
      <el-table-column label="原保费-趸交(年累计)" align="center" prop="ytdSinglePremium" width="140" />
      <el-table-column label="原保费-期交(年累计)" align="center" prop="ytdRegularPremium" width="140" />
      <el-table-column label="原保费-续期(年累计)" align="center" prop="ytdRenewalPremium" width="140" />
      <el-table-column label="原保费-合计(年累计)" align="center" prop="ytdTotalPremium" width="140" />
      <el-table-column label="万能投连-趸交(年累计)" align="center" prop="ytdUlSingle" width="140" />
      <el-table-column label="万能投连-期交(年累计)" align="center" prop="ytdUlRegular" width="140" />
      <el-table-column label="万能投连-续期(年累计)" align="center" prop="ytdUlRenewal" width="140" />
      <el-table-column label="万能投连-初始费用(年累计)" align="center" prop="ytdUlInitialFee" width="160" />
      <el-table-column label="万能投连-合计(年累计)" align="center" prop="ytdUlTotal" width="140" />
      <el-table-column label="规模保费合计(年累计)" align="center" prop="ytdScalePremium" width="140" />
      <el-table-column label="保户储金及投资款余额(年累计)" align="center" prop="ytdInvestmentBalance" width="180" />
      <el-table-column label="退保金(年累计)" align="center" prop="ytdSurrender" width="120" />
      <el-table-column label="万能投连领取(年累计)" align="center" prop="ytdUlWithdraw" width="140" />
      <el-table-column label="赔款支出(年累计)" align="center" prop="ytdClaim" width="120" />
      <el-table-column label="死伤医疗给付(年累计)" align="center" prop="ytdMedical" width="140" />
      <el-table-column label="满期给付(年累计)" align="center" prop="ytdMaturity" width="120" />
      <el-table-column label="年金给付(年累计)" align="center" prop="ytdAnnuity" width="120" />
      <el-table-column label="万能投连-赔款支出(年累计)" align="center" prop="ytdUlClaim" width="160" />
      <el-table-column label="万能投连-死伤医疗给付(年累计)" align="center" prop="ytdUlMedical" width="180" />
      <el-table-column label="万能投连-满期给付(年累计)" align="center" prop="ytdUlMaturity" width="160" />
      <el-table-column label="万能投连-年金给付(年累计)" align="center" prop="ytdUlAnnuity" width="160" />
      <el-table-column label="赔付支出合计(年累计)" align="center" prop="ytdTotalClaim" width="140" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cost:premium:income:detail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cost:premium:income:detail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改保费收入明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基础信息 -->
        <el-divider content-position="left">基础信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="统计期间" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入统计期间(YYYYMM)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司代码" prop="companyCode">
              <el-input v-model="form.companyCode" placeholder="请输入公司代码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产品代码" prop="productCode">
              <el-input v-model="form.productCode" placeholder="请输入产品代码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="渠道代码" prop="channelCode">
              <el-input v-model="form.channelCode" placeholder="请输入渠道代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="渠道名称" prop="channelName">
              <el-input v-model="form.channelName" placeholder="请输入渠道名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户代码" prop="accountCode">
              <el-input v-model="form.accountCode" placeholder="请输入账户代码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户名称" prop="accountName">
              <el-input v-model="form.accountName" placeholder="请输入账户名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 原保费信息 -->
        <el-divider content-position="left">原保费信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="原保费-趸交" prop="currentSinglePremium">
              <el-input-number v-model="form.currentSinglePremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-趸交" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-期交" prop="currentRegularPremium">
              <el-input-number v-model="form.currentRegularPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-期交" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-续期" prop="currentRenewalPremium">
              <el-input-number v-model="form.currentRenewalPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-续期" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-合计" prop="currentTotalPremium">
              <el-input-number v-model="form.currentTotalPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-合计" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 万能投连信息 -->
        <el-divider content-position="left">万能投连信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="万能投连-趸交" prop="currentUlSingle">
              <el-input-number v-model="form.currentUlSingle" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-趸交" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-期交" prop="currentUlRegular">
              <el-input-number v-model="form.currentUlRegular" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-期交" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-续期" prop="currentUlRenewal">
              <el-input-number v-model="form.currentUlRenewal" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-续期" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-初始费用" prop="currentUlInitialFee">
              <el-input-number v-model="form.currentUlInitialFee" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-初始费用" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="万能投连-合计" prop="currentUlTotal">
              <el-input-number v-model="form.currentUlTotal" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-合计" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规模保费合计" prop="currentScalePremium">
              <el-input-number v-model="form.currentScalePremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入规模保费合计" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保户储金及投资款余额" prop="currentInvestmentBalance">
              <el-input-number v-model="form.currentInvestmentBalance" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入保户储金及投资款余额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 支出信息 -->
        <el-divider content-position="left">支出信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="退保金" prop="currentSurrender">
              <el-input-number v-model="form.currentSurrender" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入退保金" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连领取" prop="currentUlWithdraw">
              <el-input-number v-model="form.currentUlWithdraw" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连领取" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="赔款支出" prop="currentClaim">
              <el-input-number v-model="form.currentClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入赔款支出" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="死伤医疗给付" prop="currentMedical">
              <el-input-number v-model="form.currentMedical" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入死伤医疗给付" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="满期给付" prop="currentMaturity">
              <el-input-number v-model="form.currentMaturity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入满期给付" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年金给付" prop="currentAnnuity">
              <el-input-number v-model="form.currentAnnuity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入年金给付" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-赔款支出" prop="currentUlClaim">
              <el-input-number v-model="form.currentUlClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-赔款支出" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-死伤医疗给付" prop="currentUlMedical">
              <el-input-number v-model="form.currentUlMedical" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-死伤医疗给付" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="万能投连-满期给付" prop="currentUlMaturity">
              <el-input-number v-model="form.currentUlMaturity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-满期给付" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="万能投连-年金给付" prop="currentUlAnnuity">
              <el-input-number v-model="form.currentUlAnnuity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-年金给付" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="赔付支出合计" prop="currentTotalClaim">
              <el-input-number v-model="form.currentTotalClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入赔付支出合计" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 年累计原保费信息 -->
        <el-divider content-position="left">年累计原保费信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="原保费-趸交(年累计)" prop="ytdSinglePremium">
              <el-input-number v-model="form.ytdSinglePremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-趸交(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-期交(年累计)" prop="ytdRegularPremium">
              <el-input-number v-model="form.ytdRegularPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-期交(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-续期(年累计)" prop="ytdRenewalPremium">
              <el-input-number v-model="form.ytdRenewalPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-续期(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原保费-合计(年累计)" prop="ytdTotalPremium">
              <el-input-number v-model="form.ytdTotalPremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入原保费-合计(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 年累计万能投连信息 -->
        <el-divider content-position="left">年累计万能投连信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="万能投连-趸交(年累计)" prop="ytdUlSingle">
              <el-input-number v-model="form.ytdUlSingle" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-趸交(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-期交(年累计)" prop="ytdUlRegular">
              <el-input-number v-model="form.ytdUlRegular" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-期交(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-续期(年累计)" prop="ytdUlRenewal">
              <el-input-number v-model="form.ytdUlRenewal" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-续期(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-初始费用(年累计)" prop="ytdUlInitialFee">
              <el-input-number v-model="form.ytdUlInitialFee" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-初始费用(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="万能投连-合计(年累计)" prop="ytdUlTotal">
              <el-input-number v-model="form.ytdUlTotal" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-合计(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规模保费合计(年累计)" prop="ytdScalePremium">
              <el-input-number v-model="form.ytdScalePremium" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入规模保费合计(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保户储金及投资款余额(年累计)" prop="ytdInvestmentBalance">
              <el-input-number v-model="form.ytdInvestmentBalance" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入保户储金及投资款余额(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 年累计支出信息 -->
        <el-divider content-position="left">年累计支出信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="退保金(年累计)" prop="ytdSurrender">
              <el-input-number v-model="form.ytdSurrender" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入退保金(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连领取(年累计)" prop="ytdUlWithdraw">
              <el-input-number v-model="form.ytdUlWithdraw" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连领取(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="赔款支出(年累计)" prop="ytdClaim">
              <el-input-number v-model="form.ytdClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入赔款支出(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="死伤医疗给付(年累计)" prop="ytdMedical">
              <el-input-number v-model="form.ytdMedical" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入死伤医疗给付(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="满期给付(年累计)" prop="ytdMaturity">
              <el-input-number v-model="form.ytdMaturity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入满期给付(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年金给付(年累计)" prop="ytdAnnuity">
              <el-input-number v-model="form.ytdAnnuity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入年金给付(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-赔款支出(年累计)" prop="ytdUlClaim">
              <el-input-number v-model="form.ytdUlClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-赔款支出(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="万能投连-死伤医疗给付(年累计)" prop="ytdUlMedical">
              <el-input-number v-model="form.ytdUlMedical" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-死伤医疗给付(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="万能投连-满期给付(年累计)" prop="ytdUlMaturity">
              <el-input-number v-model="form.ytdUlMaturity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-满期给付(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="万能投连-年金给付(年累计)" prop="ytdUlAnnuity">
              <el-input-number v-model="form.ytdUlAnnuity" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入万能投连-年金给付(年累计)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="赔付支出合计(年累计)" prop="ytdTotalClaim">
              <el-input-number v-model="form.ytdTotalClaim" :precision="2" :step="0.01" style="width: 100%" placeholder="请输入赔付支出合计(年累计)" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 备注信息 -->
        <el-divider content-position="left">备注信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPremiumIncomeDetail, getPremiumIncomeDetail, delPremiumIncomeDetail, addPremiumIncomeDetail, updatePremiumIncomeDetail } from "@/api/cost/premium/income/detail";

export default {
  name: "PremiumIncomeDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 保费收入明细表格数据
      premiumIncomeDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        companyCode: null,
        companyName: null,
        productCode: null,
        productName: null,
        channelCode: null,
        channelName: null,
        accountCode: null,
        accountName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "统计期间不能为空", trigger: "blur" }
        ],
        companyCode: [
          { required: true, message: "公司代码不能为空", trigger: "blur" }
        ],
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" }
        ],
        productCode: [
          { required: true, message: "产品代码不能为空", trigger: "blur" }
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" }
        ],
        channelCode: [
          { required: true, message: "渠道代码不能为空", trigger: "blur" }
        ],
        channelName: [
          { required: true, message: "渠道名称不能为空", trigger: "blur" }
        ],
        accountCode: [
          { required: true, message: "账户代码不能为空", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "blur" }
        ]
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + this.$store.getters.token },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cost/premium/income/detail/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询保费收入明细列表 */
    getList() {
      this.loading = true;
      listPremiumIncomeDetail(this.queryParams).then(response => {
        this.premiumIncomeDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        companyCode: null,
        companyName: null,
        productCode: null,
        productName: null,
        channelCode: null,
        channelName: null,
        accountCode: null,
        accountName: null,
        currentSinglePremium: null,
        currentRegularPremium: null,
        currentRenewalPremium: null,
        currentTotalPremium: null,
        currentUlSingle: null,
        currentUlRegular: null,
        currentUlRenewal: null,
        currentUlInitialFee: null,
        currentUlTotal: null,
        currentScalePremium: null,
        currentInvestmentBalance: null,
        currentSurrender: null,
        currentUlWithdraw: null,
        currentClaim: null,
        currentMedical: null,
        currentMaturity: null,
        currentAnnuity: null,
        currentUlClaim: null,
        currentUlMedical: null,
        currentUlMaturity: null,
        currentUlAnnuity: null,
        currentTotalClaim: null,
        // 年累计字段
        ytdSinglePremium: null,
        ytdRegularPremium: null,
        ytdRenewalPremium: null,
        ytdTotalPremium: null,
        ytdUlSingle: null,
        ytdUlRegular: null,
        ytdUlRenewal: null,
        ytdUlInitialFee: null,
        ytdUlTotal: null,
        ytdScalePremium: null,
        ytdInvestmentBalance: null,
        ytdSurrender: null,
        ytdUlWithdraw: null,
        ytdClaim: null,
        ytdMedical: null,
        ytdMaturity: null,
        ytdAnnuity: null,
        ytdUlClaim: null,
        ytdUlMedical: null,
        ytdUlMaturity: null,
        ytdUlAnnuity: null,
        ytdTotalClaim: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加保费收入明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPremiumIncomeDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改保费收入明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePremiumIncomeDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPremiumIncomeDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除保费收入明细编号为"' + ids + '"的数据项？').then(function() {
        return delPremiumIncomeDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('cost/premium/income/detail/export', {
        ...this.queryParams
      }, `保费收入明细_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "保费收入明细导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('cost/premium/income/detail/importTemplate', {}, `保费收入明细导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>

<style scoped>
</style>
