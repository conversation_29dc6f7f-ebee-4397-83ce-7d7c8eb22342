<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品精算代码" prop="actuarialCode">
        <el-input
          v-model="queryParams.actuarialCode"
          placeholder="请输入产品精算代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select v-model="queryParams.designType" placeholder="请选择设计类型" clearable>
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cost:short:term:product:spread:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cost:short:term:product:spread:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cost:short:term:product:spread:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cost:short:term:product:spread:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cost:short:term:product:spread:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="shortTermProductSpreadList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" v-if="false" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" width="100" />
      <el-table-column label="产品精算代码" align="center" prop="actuarialCode" width="140" />
      <el-table-column label="产品业务代码" align="center" prop="businessCode" width="140" />
      <el-table-column label="产品名称" align="center" prop="productName" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="设计类型" align="center" prop="designType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="子账户名称" align="center" prop="subAccount" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="新单规模保费" align="center" prop="newScalePremium" width="120" />
      <el-table-column label="会计准备金" align="center" prop="accountingReserve" width="120" />
      <el-table-column label="保户储金及投资款" align="center" prop="investmentBalance" width="140" />
      <el-table-column label="年化会计投资收益率" align="center" prop="accountingYieldRate" width="150" />
      <el-table-column label="年化综合投资收益率" align="center" prop="comprehensiveYieldRate" width="150" />
      <el-table-column label="负债资金成本率" align="center" prop="liabilityCostRate" width="130" />
      <el-table-column label="负债有效成本率" align="center" prop="effectiveCostRate" width="130" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cost:short:term:product:spread:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cost:short:term:product:spread:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改中短存续期产品利差对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品精算代码" prop="actuarialCode">
              <el-input v-model="form.actuarialCode" placeholder="请输入产品精算代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品业务代码" prop="businessCode">
              <el-input v-model="form.businessCode" placeholder="请输入产品业务代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设计类型" prop="designType">
              <el-select v-model="form.designType" placeholder="请选择设计类型">
                <el-option
                  v-for="dict in dict.type.cost_design_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="子账户名称" prop="subAccount">
              <el-input v-model="form.subAccount" placeholder="请输入子账户名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="新单规模保费" prop="newScalePremium">
              <el-input-number v-model="form.newScalePremium" :precision="10" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会计准备金" prop="accountingReserve">
              <el-input-number v-model="form.accountingReserve" :precision="10" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="保户储金及投资款" prop="investmentBalance">
              <el-input-number v-model="form.investmentBalance" :precision="10" :step="1000" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年化会计投资收益率" prop="accountingYieldRate">
              <el-input-number v-model="form.accountingYieldRate" :precision="10" :step="0.001" :min="-999.999999" :max="999.999999" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="年化综合投资收益率" prop="comprehensiveYieldRate">
              <el-input-number v-model="form.comprehensiveYieldRate" :precision="10" :step="0.001" :min="-999.999999" :max="999.999999" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负债资金成本率" prop="liabilityCostRate">
              <el-input-number v-model="form.liabilityCostRate" :precision="10" :step="0.001" :min="-999.999999" :max="999.999999" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负债有效成本率" prop="effectiveCostRate">
              <el-input-number v-model="form.effectiveCostRate" :precision="10" :step="0.001" :min="-999.999999" :max="999.999999" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="upload.updateSupport" />更新已存在数据
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listShortTermProductSpread,
  getShortTermProductSpread,
  addShortTermProductSpread,
  updateShortTermProductSpread,
  delShortTermProductSpread,
  exportShortTermProductSpread,
  importTemplate
} from "@/api/cost/short/term/product/spread";
import { getToken } from "@/utils/auth";

export default {
  name: "ShortTermProductSpread",
  dicts: ['cost_design_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 中短存续期产品利差表格数据
      shortTermProductSpreadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        actuarialCode: null,
        productName: null,
        designType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式为6位数字，如202401", trigger: "blur" }
        ],
        actuarialCode: [
          { required: true, message: "产品精算代码不能为空", trigger: "blur" },
          { max: 20, message: "产品精算代码长度不能超过20个字符", trigger: "blur" },
          { pattern: /^[A-Za-z].*/, message: "产品精算代码必须以字母开头", trigger: "blur" }
        ],
        businessCode: [
          { required: true, message: "产品业务代码不能为空", trigger: "blur" },
          { max: 20, message: "产品业务代码长度不能超过20个字符", trigger: "blur" }
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
          { max: 100, message: "产品名称长度不能超过100个字符", trigger: "blur" }
        ],
        designType: [
          { required: true, message: "设计类型不能为空", trigger: "change" }
        ],
        subAccount: [
          { max: 50, message: "子账户名称长度不能超过50个字符", trigger: "blur" }
        ],
        newScalePremium: [
          { type: 'number', min: 0, message: "新单规模保费不能小于0", trigger: "blur" }
        ],
        accountingReserve: [
          { type: 'number', min: 0, message: "会计准备金不能小于0", trigger: "blur" }
        ],
        investmentBalance: [
          { type: 'number', min: 0, message: "保户储金及投资款不能小于0", trigger: "blur" }
        ],
        accountingYieldRate: [
          { type: 'number', min: -999.999999, max: 999.999999, message: "年化会计投资收益率范围为-999.999999到999.999999", trigger: "blur" }
        ],
        comprehensiveYieldRate: [
          { type: 'number', min: -999.999999, max: 999.999999, message: "年化综合投资收益率范围为-999.999999到999.999999", trigger: "blur" }
        ],
        liabilityCostRate: [
          { type: 'number', min: -999.999999, max: 999.999999, message: "负债资金成本率范围为-999.999999到999.999999", trigger: "blur" }
        ],
        effectiveCostRate: [
          { type: 'number', min: -999.999999, max: 999.999999, message: "负债有效成本率范围为-999.999999到999.999999", trigger: "blur" }
        ],
        remark: [
          { max: 500, message: "备注长度不能超过500个字符", trigger: "blur" }
        ]
      },
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "导入中短存续期产品利差数据",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已存在数据
        updateSupport: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/cost/short/term/product/spread/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询中短存续期产品利差列表 */
    getList() {
      this.loading = true;
      listShortTermProductSpread(this.queryParams).then(response => {
        this.shortTermProductSpreadList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        actuarialCode: null,
        businessCode: null,
        productName: null,
        designType: null,
        subAccount: null,
        newScalePremium: null,
        accountingReserve: null,
        investmentBalance: null,
        accountingYieldRate: null,
        comprehensiveYieldRate: null,
        liabilityCostRate: null,
        effectiveCostRate: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加中短存续期产品利差";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getShortTermProductSpread(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改中短存续期产品利差";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateShortTermProductSpread(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addShortTermProductSpread(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除中短存续期产品利差编号为"' + ids + '"的数据项？').then(function() {
        return delShortTermProductSpread(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'cost/short/term/product/spread/export',
        {
          ...this.queryParams,
        },
        `short_term_product_spread_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "导入中短存续期产品利差数据";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('cost/short/term/product/spread/importTemplate', {}, `short_term_product_spread_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
}
</script>
