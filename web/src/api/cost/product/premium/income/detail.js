import request from '@/utils/request';

// 查询分产品保费收入列表
export function listProductPremiumIncomeDetail(query) {
  return request({
    url: '/cost/product/premium/income/detail/list',
    method: 'get',
    params: query
  });
}

// 查询分产品保费收入详细
export function getProductPremiumIncomeDetail(id) {
  return request({
    url: '/cost/product/premium/income/detail/' + id,
    method: 'get'
  });
}

// 新增分产品保费收入
export function addProductPremiumIncomeDetail(data) {
  return request({
    url: '/cost/product/premium/income/detail',
    method: 'post',
    data: data
  });
}

// 修改分产品保费收入
export function updateProductPremiumIncomeDetail(data) {
  return request({
    url: '/cost/product/premium/income/detail',
    method: 'put',
    data: data
  });
}

// 删除分产品保费收入
export function delProductPremiumIncomeDetail(id) {
  return request({
    url: '/cost/product/premium/income/detail/' + id,
    method: 'delete'
  });
}

// 导出分产品保费收入
export function exportProductPremiumIncomeDetail(query) {
  return request({
    url: '/cost/product/premium/income/detail/export',
    method: 'post',
    params: query
  });
}

// 获取分产品保费收入导入模板
export function importTemplate() {
  return request({
    url: '/cost/product/premium/income/detail/importTemplate',
    method: 'get',
    responseType: 'blob'
  });
}
