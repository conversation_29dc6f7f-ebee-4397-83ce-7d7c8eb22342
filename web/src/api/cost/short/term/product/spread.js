import request from '@/utils/request';

// 查询中短存续期产品利差列表
export function listShortTermProductSpread(query) {
  return request({
    url: '/cost/short/term/product/spread/list',
    method: 'get',
    params: query
  });
}

// 查询中短存续期产品利差详细
export function getShortTermProductSpread(id) {
  return request({
    url: '/cost/short/term/product/spread/' + id,
    method: 'get'
  });
}

// 新增中短存续期产品利差
export function addShortTermProductSpread(data) {
  return request({
    url: '/cost/short/term/product/spread',
    method: 'post',
    data: data
  });
}

// 修改中短存续期产品利差
export function updateShortTermProductSpread(data) {
  return request({
    url: '/cost/short/term/product/spread',
    method: 'put',
    data: data
  });
}

// 删除中短存续期产品利差
export function delShortTermProductSpread(id) {
  return request({
    url: '/cost/short/term/product/spread/' + id,
    method: 'delete'
  });
}

// 导出中短存续期产品利差
export function exportShortTermProductSpread(query) {
  return request({
    url: '/cost/short/term/product/spread/export',
    method: 'post',
    params: query
  });
}

// 获取中短存续期产品利差导入模板
export function importTemplate() {
  return request({
    url: '/cost/short/term/product/spread/importTemplate',
    method: 'get',
    responseType: 'blob'
  });
}
