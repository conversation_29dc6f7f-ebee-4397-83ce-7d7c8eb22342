import request from '@/utils/request'

// 查询保费收入明细列表
export function listPremiumIncomeDetail(query) {
  return request({
    url: '/cost/premium/income/detail/list',
    method: 'get',
    params: query
  })
}

// 查询保费收入明细详细
export function getPremiumIncomeDetail(id) {
  return request({
    url: '/cost/premium/income/detail/' + id,
    method: 'get'
  })
}

// 新增保费收入明细
export function addPremiumIncomeDetail(data) {
  return request({
    url: '/cost/premium/income/detail',
    method: 'post',
    data: data
  })
}

// 批量新增保费收入明细
export function batchAddPremiumIncomeDetail(data) {
  return request({
    url: '/cost/premium/income/detail/batchAdd',
    method: 'post',
    data: data
  })
}

// 修改保费收入明细
export function updatePremiumIncomeDetail(data) {
  return request({
    url: '/cost/premium/income/detail',
    method: 'put',
    data: data
  })
}

// 删除保费收入明细
export function delPremiumIncomeDetail(id) {
  return request({
    url: '/cost/premium/income/detail/' + id,
    method: 'delete'
  })
}

// 删除指定账期的保费收入明细
export function delPremiumIncomeDetailByPeriod(accountingPeriod) {
  return request({
    url: '/cost/premium/income/detail/period/' + accountingPeriod,
    method: 'delete'
  })
}

// 导出保费收入明细
export function exportPremiumIncomeDetail(query) {
  return request({
    url: '/cost/premium/income/detail/export',
    method: 'post',
    params: query
  })
}
