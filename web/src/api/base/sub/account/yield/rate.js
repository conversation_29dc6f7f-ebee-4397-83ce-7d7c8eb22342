import request from '@/utils/request';

// 查询子账户收益率列表
export function listSubAccountYieldRate(query) {
  return request({
    url: '/base/sub/account/yield/rate/list',
    method: 'get',
    params: query
  });
}

// 查询子账户收益率详细
export function getSubAccountYieldRate(id) {
  return request({
    url: '/base/sub/account/yield/rate/' + id,
    method: 'get'
  });
}

// 新增子账户收益率
export function addSubAccountYieldRate(data) {
  return request({
    url: '/base/sub/account/yield/rate',
    method: 'post',
    data: data
  });
}

// 修改子账户收益率
export function updateSubAccountYieldRate(data) {
  return request({
    url: '/base/sub/account/yield/rate',
    method: 'put',
    data: data
  });
}

// 删除子账户收益率
export function delSubAccountYieldRate(id) {
  return request({
    url: '/base/sub/account/yield/rate/' + id,
    method: 'delete'
  });
}

// 导出子账户收益率
export function exportSubAccountYieldRate(query) {
  return request({
    url: '/base/sub/account/yield/rate/export',
    method: 'post',
    params: query
  });
}

// 获取子账户收益率导入模板
export function importTemplate() {
  return request({
    url: '/base/sub/account/yield/rate/importTemplate',
    method: 'post',
    responseType: 'blob'
  });
}

// 导入子账户收益率数据
export function importData(data) {
  return request({
    url: '/base/sub/account/yield/rate/importData',
    method: 'post',
    data: data
  });
}
