import request from '@/utils/request'

// 查询组合持仓列表
export function listAccountHolding(query) {
  return request({
    url: '/ast/account/holding/list',
    method: 'get',
    params: query
  })
}

// 查询组合持仓详细
export function getAccountHolding(id) {
  return request({
    url: '/ast/account/holding/' + id,
    method: 'get'
  })
}

// 新增组合持仓
export function addAccountHolding(data) {
  return request({
    url: '/ast/account/holding',
    method: 'post',
    data: data
  })
}

// 修改组合持仓
export function updateAccountHolding(data) {
  return request({
    url: '/ast/account/holding',
    method: 'put',
    data: data
  })
}

// 删除组合持仓
export function delAccountHolding(id) {
  return request({
    url: '/ast/account/holding/' + id,
    method: 'delete'
  })
}
