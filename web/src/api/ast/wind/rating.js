import request from '@/utils/request'

// 查询Wind评级表列表
export function listRating(query) {
  return request({
    url: '/ast/wind/rating/list',
    method: 'get',
    params: query
  })
}

// 查询Wind评级表详细
export function getRating(id) {
  return request({
    url: '/ast/wind/rating/info/' + id,
    method: 'get'
  })
}

// 新增Wind评级表
export function addRating(data) {
  return request({
    url: '/ast/wind/rating',
    method: 'post',
    data: data
  })
}

// 修改Wind评级表
export function updateRating(data) {
  return request({
    url: '/ast/wind/rating',
    method: 'put',
    data: data
  })
}

// 删除Wind评级表
export function delRating(id) {
  return request({
    url: '/ast/wind/rating/' + id,
    method: 'delete'
  })
}

// 批量新增Wind评级表
export function batchAddRating(data) {
  return request({
    url: '/ast/wind/rating/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除Wind评级表
export function delRatingByPeriod(accountingPeriod) {
  return request({
    url: '/ast/wind/rating/period/' + accountingPeriod,
    method: 'delete'
  })
}

// 导入Wind评级表数据
export function importRating(data) {
  return request({
    url: '/ast/wind/rating/importData',
    method: 'post',
    data: data
  })
}

// 导出Wind评级表数据
export function exportRating(query) {
  return request({
    url: '/ast/wind/rating/export',
    method: 'post',
    data: query
  })
}

// 获取Wind评级表导入模板
export function importTemplate() {
  return request({
    url: '/ast/wind/rating/importTemplate',
    method: 'post'
  })
}
