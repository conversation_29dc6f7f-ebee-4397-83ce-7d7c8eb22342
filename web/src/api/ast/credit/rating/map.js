import request from '@/utils/request'

// 查询信用评级映射表列表
export function listCreditRatingMap(query) {
  return request({
    url: '/ast/credit/rating/map/list',
    method: 'get',
    params: query
  })
}

// 查询信用评级映射表详细
export function getCreditRatingMap(id) {
  return request({
    url: '/ast/credit/rating/map/info/' + id,
    method: 'get'
  })
}

// 新增信用评级映射表
export function addCreditRatingMap(data) {
  return request({
    url: '/ast/credit/rating/map',
    method: 'post',
    data: data
  })
}

// 修改信用评级映射表
export function updateCreditRatingMap(data) {
  return request({
    url: '/ast/credit/rating/map',
    method: 'put',
    data: data
  })
}

// 删除信用评级映射表
export function delCreditRatingMap(id) {
  return request({
    url: '/ast/credit/rating/map/' + id,
    method: 'delete'
  })
}

// 批量新增信用评级映射表
export function batchAddCreditRatingMap(data) {
  return request({
    url: '/ast/credit/rating/map/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除信用评级映射表
export function delCreditRatingMapByPeriod(accountingPeriod) {
  return request({
    url: '/ast/credit/rating/map/period/' + accountingPeriod,
    method: 'delete'
  })
}
