import request from '@/utils/request'

// 查询VaR值分析列表
export function listVarAnalysis(query) {
  return request({
    url: '/ast/var/analysis/list',
    method: 'get',
    params: query
  })
}

// 查询VaR值分析详细
export function getVarAnalysis(id) {
  return request({
    url: '/ast/var/analysis/' + id,
    method: 'get'
  })
}

// 新增VaR值分析
export function addVarAnalysis(data) {
  return request({
    url: '/ast/var/analysis',
    method: 'post',
    data: data
  })
}

// 修改VaR值分析
export function updateVarAnalysis(data) {
  return request({
    url: '/ast/var/analysis',
    method: 'put',
    data: data
  })
}

// 删除VaR值分析
export function delVarAnalysis(id) {
  return request({
    url: '/ast/var/analysis/' + id,
    method: 'delete'
  })
}
