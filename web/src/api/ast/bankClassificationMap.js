import request from '@/utils/request'

// 查询银行分类映射表列表
export function listBankClassificationMap(query) {
  return request({
    url: '/ast/bank/classification/map/list',
    method: 'get',
    params: query
  })
}

// 查询银行分类映射表详细
export function getBankClassificationMap(id) {
  return request({
    url: '/ast/bank/classification/map/' + id,
    method: 'get'
  })
}

// 新增银行分类映射表
export function addBankClassificationMap(data) {
  return request({
    url: '/ast/bank/classification/map',
    method: 'post',
    data: data
  })
}

// 修改银行分类映射表
export function updateBankClassificationMap(data) {
  return request({
    url: '/ast/bank/classification/map',
    method: 'put',
    data: data
  })
}

// 删除银行分类映射表
export function delBankClassificationMap(id) {
  return request({
    url: '/ast/bank/classification/map/' + id,
    method: 'delete'
  })
}
