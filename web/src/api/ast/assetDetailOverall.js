import request from '@/utils/request'

// 查询整体资产明细表列表
export function listAssetDetailOverall(query) {
  return request({
    url: '/ast/asset/detail/overall/list',
    method: 'get',
    params: query
  })
}

// 查询整体资产明细表详细
export function getAssetDetailOverall(id) {
  return request({
    url: '/ast/asset/detail/overall/info/' + id,
    method: 'get'
  })
}

// 新增整体资产明细表
export function addAssetDetailOverall(data) {
  return request({
    url: '/ast/asset/detail/overall',
    method: 'post',
    data: data
  })
}

// 修改整体资产明细表
export function updateAssetDetailOverall(data) {
  return request({
    url: '/ast/asset/detail/overall',
    method: 'put',
    data: data
  })
}

// 删除整体资产明细表
export function delAssetDetailOverall(id) {
  return request({
    url: '/ast/asset/detail/overall/' + id,
    method: 'delete'
  })
}

// 导出整体资产明细表
export function exportAssetDetailOverall(query) {
  return request({
    url: '/ast/asset/detail/overall/export',
    method: 'get',
    params: query
  })
}

// 导入整体资产明细表
export function importAssetDetailOverall(data) {
  return request({
    url: '/ast/asset/detail/overall/importData',
    method: 'post',
    data: data
  })
}

// 下载整体资产明细表导入模板
export function importTemplate() {
  return request({
    url: '/ast/asset/detail/overall/importTemplate',
    method: 'get'
  })
}
