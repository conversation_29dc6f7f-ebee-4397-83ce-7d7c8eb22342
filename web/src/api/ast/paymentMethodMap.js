import request from '@/utils/request'

// 查询付息方式映射表列表
export function listPaymentMethodMap(query) {
  return request({
    url: '/ast/payment/method/map/list',
    method: 'get',
    params: query
  })
}

// 查询付息方式映射表详细
export function getPaymentMethodMap(id) {
  return request({
    url: '/ast/payment/method/map/' + id,
    method: 'get'
  })
}

// 新增付息方式映射表
export function addPaymentMethodMap(data) {
  return request({
    url: '/ast/payment/method/map',
    method: 'post',
    data: data
  })
}

// 修改付息方式映射表
export function updatePaymentMethodMap(data) {
  return request({
    url: '/ast/payment/method/map',
    method: 'put',
    data: data
  })
}

// 删除付息方式映射表
export function delPaymentMethodMap(id) {
  return request({
    url: '/ast/payment/method/map/' + id,
    method: 'delete'
  })
}
