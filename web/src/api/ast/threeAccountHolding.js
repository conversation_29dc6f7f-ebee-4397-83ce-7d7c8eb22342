import request from '@/utils/request'

// 查询三账户持仓列表
export function listThreeAccountHolding(query) {
  return request({
    url: '/ast/three/account/holding/list',
    method: 'get',
    params: query
  })
}

// 查询三账户持仓详细
export function getThreeAccountHolding(id) {
  return request({
    url: '/ast/three/account/holding/' + id,
    method: 'get'
  })
}

// 新增三账户持仓
export function addThreeAccountHolding(data) {
  return request({
    url: '/ast/three/account/holding',
    method: 'post',
    data: data
  })
}

// 修改三账户持仓
export function updateThreeAccountHolding(data) {
  return request({
    url: '/ast/three/account/holding',
    method: 'put',
    data: data
  })
}

// 删除三账户持仓
export function delThreeAccountHolding(id) {
  return request({
    url: '/ast/three/account/holding/' + id,
    method: 'delete'
  })
}
