import request from '@/utils/request'

// 查询折现曲线配置表列表
export function listDiscountCurveConfig(query) {
  return request({
    url: '/ast/discount/curve/config/list',
    method: 'get',
    params: query
  })
}

// 查询折现曲线配置表详细
export function getDiscountCurveConfig(id) {
  return request({
    url: '/ast/discount/curve/config/' + id,
    method: 'get'
  })
}

// 新增折现曲线配置表
export function addDiscountCurveConfig(data) {
  return request({
    url: '/ast/discount/curve/config',
    method: 'post',
    data: data
  })
}

// 修改折现曲线配置表
export function updateDiscountCurveConfig(data) {
  return request({
    url: '/ast/discount/curve/config',
    method: 'put',
    data: data
  })
}

// 删除折现曲线配置表
export function delDiscountCurveConfig(id) {
  return request({
    url: '/ast/discount/curve/config/' + id,
    method: 'delete'
  })
}

// 批量新增折现曲线配置表
export function batchAddDiscountCurveConfig(data) {
  return request({
    url: '/ast/discount/curve/config/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除折现曲线配置表
export function delDiscountCurveConfigByPeriod(accountingPeriod) {
  return request({
    url: '/ast/discount/curve/config/period/' + accountingPeriod,
    method: 'delete'
  })
}
