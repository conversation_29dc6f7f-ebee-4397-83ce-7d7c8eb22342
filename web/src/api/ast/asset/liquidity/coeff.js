import request from '@/utils/request'

// 查询资产流动性分类及变现系数表列表
export function listAssetLiquidityCoeff(query) {
  return request({
    url: '/ast/asset/liquidity/coeff/list',
    method: 'get',
    params: query
  })
}

// 查询资产流动性分类及变现系数表详细
export function getAssetLiquidityCoeff(id) {
  return request({
    url: '/ast/asset/liquidity/coeff/' + id,
    method: 'get'
  })
}

// 新增资产流动性分类及变现系数表
export function addAssetLiquidityCoeff(data) {
  return request({
    url: '/ast/asset/liquidity/coeff',
    method: 'post',
    data: data
  })
}

// 修改资产流动性分类及变现系数表
export function updateAssetLiquidityCoeff(data) {
  return request({
    url: '/ast/asset/liquidity/coeff',
    method: 'put',
    data: data
  })
}

// 删除资产流动性分类及变现系数表
export function delAssetLiquidityCoeff(id) {
  return request({
    url: '/ast/asset/liquidity/coeff/' + id,
    method: 'delete'
  })
}
