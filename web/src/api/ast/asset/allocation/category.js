import request from '@/utils/request'

// 查询资产配置状况分类表列表
export function listAssetAllocationCategory(query) {
  return request({
    url: '/ast/asset/allocation/category/list',
    method: 'get',
    params: query
  })
}

// 查询资产配置状况分类表详细
export function getAssetAllocationCategory(id) {
  return request({
    url: '/ast/asset/allocation/category/' + id,
    method: 'get'
  })
}

// 新增资产配置状况分类表
export function addAssetAllocationCategory(data) {
  return request({
    url: '/ast/asset/allocation/category',
    method: 'post',
    data: data
  })
}

// 修改资产配置状况分类表
export function updateAssetAllocationCategory(data) {
  return request({
    url: '/ast/asset/allocation/category',
    method: 'put',
    data: data
  })
}

// 删除资产配置状况分类表
export function delAssetAllocationCategory(id) {
  return request({
    url: '/ast/asset/allocation/category/' + id,
    method: 'delete'
  })
}
