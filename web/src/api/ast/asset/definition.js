import request from '@/utils/request'

// 查询资产定义表列表
export function listAssetDefinition(query) {
  return request({
    url: '/ast/asset/definition/list',
    method: 'get',
    params: query
  })
}

// 查询资产定义表详细
export function getAssetDefinition(id) {
  return request({
    url: '/ast/asset/definition/info/' + id,
    method: 'get'
  })
}

// 新增资产定义表
export function addAssetDefinition(data) {
  return request({
    url: '/ast/asset/definition',
    method: 'post',
    data: data
  })
}

// 修改资产定义表
export function updateAssetDefinition(data) {
  return request({
    url: '/ast/asset/definition',
    method: 'put',
    data: data
  })
}

// 删除资产定义表
export function delAssetDefinition(id) {
  return request({
    url: '/ast/asset/definition/' + id,
    method: 'delete'
  })
}

// 批量新增资产定义表
export function batchAddAssetDefinition(data) {
  return request({
    url: '/ast/asset/definition/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除资产定义表
export function delAssetDefinitionByPeriod(accountingPeriod) {
  return request({
    url: '/ast/asset/definition/period/' + accountingPeriod,
    method: 'delete'
  })
}
