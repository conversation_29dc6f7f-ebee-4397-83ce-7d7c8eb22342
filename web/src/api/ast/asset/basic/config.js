import request from '@/utils/request'

// 查询资产基础配置表列表
export function listAssetBasicConfig(query) {
  return request({
    url: '/ast/asset/basic/config/list',
    method: 'get',
    params: query
  })
}

// 查询资产基础配置表详细
export function getAssetBasicConfig(id) {
  return request({
    url: '/ast/asset/basic/config/' + id,
    method: 'get'
  })
}

// 新增资产基础配置表
export function addAssetBasicConfig(data) {
  return request({
    url: '/ast/asset/basic/config',
    method: 'post',
    data: data
  })
}

// 修改资产基础配置表
export function updateAssetBasicConfig(data) {
  return request({
    url: '/ast/asset/basic/config',
    method: 'put',
    data: data
  })
}

// 删除资产基础配置表
export function delAssetBasicConfig(id) {
  return request({
    url: '/ast/asset/basic/config/' + id,
    method: 'delete'
  })
}
