import request from '@/utils/request';

// 查询Wind行业表列表
export function listWindIndustry(query) {
  return request({
    url: '/ast/wind/industry/list',
    method: 'get',
    params: query,
  });
}

// 查询Wind行业表详细
export function getWindIndustry(id) {
  return request({
    url: '/ast/wind/industry/info/' + id,
    method: 'get',
  });
}

// 新增Wind行业表
export function addWindIndustry(data) {
  return request({
    url: '/ast/wind/industry',
    method: 'post',
    data: data,
  });
}

// 修改Wind行业表
export function updateWindIndustry(data) {
  return request({
    url: '/ast/wind/industry',
    method: 'put',
    data: data,
  });
}

// 删除Wind行业表
export function delWindIndustry(id) {
  return request({
    url: '/ast/wind/industry/' + id,
    method: 'delete',
  });
}

// 导出Wind行业表
export function exportWindIndustry(query) {
  return request({
    url: '/ast/wind/industry/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  });
}

// 获取Wind行业表导入模板
export function importWindIndustryTemplate() {
  return request({
    url: '/ast/wind/industry/importTemplate',
    method: 'get',
    responseType: 'blob'
  });
}

// 导入Wind行业表数据
export function importWindIndustry(data) {
  return request({
    url: '/ast/wind/industry/importData',
    method: 'post',
    data: data
  });
}
