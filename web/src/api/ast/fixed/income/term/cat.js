import request from '@/utils/request'

// 查询固收资产剩余期限资产分类表列表
export function listFixedIncomeTermCat(query) {
  return request({
    url: '/ast/fixed/income/term/cat/list',
    method: 'get',
    params: query
  })
}

// 查询固收资产剩余期限资产分类表详细
export function getFixedIncomeTermCat(id) {
  return request({
    url: '/ast/fixed/income/term/cat/' + id,
    method: 'get'
  })
}

// 新增固收资产剩余期限资产分类表
export function addFixedIncomeTermCat(data) {
  return request({
    url: '/ast/fixed/income/term/cat',
    method: 'post',
    data: data
  })
}

// 修改固收资产剩余期限资产分类表
export function updateFixedIncomeTermCat(data) {
  return request({
    url: '/ast/fixed/income/term/cat',
    method: 'put',
    data: data
  })
}

// 删除固收资产剩余期限资产分类表
export function delFixedIncomeTermCat(id) {
  return request({
    url: '/ast/fixed/income/term/cat/' + id,
    method: 'delete'
  })
}

// 导出固收资产剩余期限资产分类表
export function exportFixedIncomeTermCat(query) {
  return request({
    url: '/ast/fixed/income/term/cat/export',
    method: 'post',
    data: query
  })
}

// 导入固收资产剩余期限资产分类表
export function importFixedIncomeTermCat(data) {
  return request({
    url: '/ast/fixed/income/term/cat/importData',
    method: 'post',
    data: data
  })
}

// 下载固收资产剩余期限资产分类表导入模板
export function importTemplate() {
  return request({
    url: '/ast/fixed/income/term/cat/importTemplate',
    method: 'post'
  })
}
