import request from '@/utils/request'

// 查询账户名称映射表列表
export function listAccountNameMap(query) {
  return request({
    url: '/ast/account/name/map/list',
    method: 'get',
    params: query
  })
}

// 查询账户名称映射表详细
export function getAccountNameMap(id) {
  return request({
    url: '/ast/account/name/map/' + id,
    method: 'get'
  })
}

// 新增账户名称映射表
export function addAccountNameMap(data) {
  return request({
    url: '/ast/account/name/map',
    method: 'post',
    data: data
  })
}

// 修改账户名称映射表
export function updateAccountNameMap(data) {
  return request({
    url: '/ast/account/name/map',
    method: 'put',
    data: data
  })
}

// 删除账户名称映射表
export function delAccountNameMap(id) {
  return request({
    url: '/ast/account/name/map/' + id,
    method: 'delete'
  })
}

// 导出账户名称映射表
export function exportAccountNameMap(query) {
  return request({
    url: '/ast/account/name/map/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 获取账户名称映射表导入模板
export function importAccountNameMapTemplate() {
  return request({
    url: '/ast/account/name/map/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入账户名称映射表数据
export function importAccountNameMap(data) {
  return request({
    url: '/ast/account/name/map/importData',
    method: 'post',
    data: data
  })
}
