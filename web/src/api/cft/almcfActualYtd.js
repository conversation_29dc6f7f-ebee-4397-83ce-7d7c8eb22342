import request from '@/utils/request'

// 查询ALMCF实际发生数本年累计表列表
export function listAlmcfActualYtd(query) {
  return request({
    url: '/cft/almcf/actual/ytd/list',
    method: 'get',
    params: query
  })
}

// 查询ALMCF实际发生数本年累计表详细
export function getAlmcfActualYtd(id) {
  return request({
    url: '/cft/almcf/actual/ytd/' + id,
    method: 'get'
  })
}

// 新增ALMCF实际发生数本年累计表
export function addAlmcfActualYtd(data) {
  return request({
    url: '/cft/almcf/actual/ytd',
    method: 'post',
    data: data
  })
}

// 修改ALMCF实际发生数本年累计表
export function updateAlmcfActualYtd(data) {
  return request({
    url: '/cft/almcf/actual/ytd',
    method: 'put',
    data: data
  })
}

// 删除ALMCF实际发生数本年累计表
export function delAlmcfActualYtd(id) {
  return request({
    url: '/cft/almcf/actual/ytd/' + id,
    method: 'delete'
  })
}

// 计算ALMCF实际发生数本年累计表
export function calculateAlmcfActualYtd(accountingPeriod) {
  return request({
    url: '/cft/almcf/actual/ytd/calculate',
    method: 'post',
    params: { accountingPeriod }
  })
}
