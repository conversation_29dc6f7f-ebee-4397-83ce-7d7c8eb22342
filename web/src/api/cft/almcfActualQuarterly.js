import request from '@/utils/request'

// 查询ALMCF实际发生数本季度累计表列表
export function listAlmcfActualQuarterly(query) {
  return request({
    url: '/cft/almcf/actual/quarterly/list',
    method: 'get',
    params: query
  })
}

// 查询ALMCF实际发生数本季度累计表详细
export function getAlmcfActualQuarterly(id) {
  return request({
    url: '/cft/almcf/actual/quarterly/' + id,
    method: 'get'
  })
}

// 新增ALMCF实际发生数本季度累计表
export function addAlmcfActualQuarterly(data) {
  return request({
    url: '/cft/almcf/actual/quarterly',
    method: 'post',
    data: data
  })
}

// 修改ALMCF实际发生数本季度累计表
export function updateAlmcfActualQuarterly(data) {
  return request({
    url: '/cft/almcf/actual/quarterly',
    method: 'put',
    data: data
  })
}

// 删除ALMCF实际发生数本季度累计表
export function delAlmcfActualQuarterly(id) {
  return request({
    url: '/cft/almcf/actual/quarterly/' + id,
    method: 'delete'
  })
}

// 计算ALMCF实际发生数本季度累计表
export function calculateAlmcfActualQuarterly(accountingPeriod) {
  return request({
    url: '/cft/almcf/actual/quarterly/calculate',
    method: 'post',
    params: { accountingPeriod }
  })
}
