import request from '@/utils/request'

// 查询财务预算费用列表
export function listFinancialBudgetExpense(query) {
  return request({
    url: '/cft/financial/budget/expense/list',
    method: 'get',
    params: query
  })
}

// 查询财务预算费用详细
export function getFinancialBudgetExpense(id) {
  return request({
    url: '/cft/financial/budget/expense/' + id,
    method: 'get'
  })
}

// 新增财务预算费用
export function addFinancialBudgetExpense(data) {
  return request({
    url: '/cft/financial/budget/expense',
    method: 'post',
    data: data
  })
}

// 修改财务预算费用
export function updateFinancialBudgetExpense(data) {
  return request({
    url: '/cft/financial/budget/expense',
    method: 'put',
    data: data
  })
}

// 删除财务预算费用
export function delFinancialBudgetExpense(id) {
  return request({
    url: '/cft/financial/budget/expense/' + id,
    method: 'delete'
  })
}

// 导出财务预算费用
export function exportFinancialBudgetExpense(query) {
  return request({
    url: '/cft/financial/budget/expense/export',
    method: 'post',
    data: query
  })
}

// 导入财务预算费用模板
export function importTemplate() {
  return request({
    url: '/cft/financial/budget/expense/exportTemplate',
    method: 'post'
  })
}
