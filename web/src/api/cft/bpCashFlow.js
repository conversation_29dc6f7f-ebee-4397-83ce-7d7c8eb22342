import request from '@/utils/request';

// 查询BP现金流量表列表
export function listBpCashFlow(query) {
  return request({
    url: '/cft/bp/cash/flow/list',
    method: 'get',
    params: query,
  });
}

// 查询BP现金流量表详细
export function getBpCashFlow(id) {
  return request({
    url: '/cft/bp/cash/flow/' + id,
    method: 'get',
  });
}

// 根据条件查询BP现金流量表
export function getBpCashFlowByCondition(accountingPeriod, scenarioName, businessType, actuarialCode, variableList) {
  return request({
    url: '/cft/bp/cash/flow/condition',
    method: 'get',
    params: {
      accountingPeriod,
      scenarioName,
      businessType,
      actuarialCode,
      variableList,
    },
  });
}

// 新增BP现金流量表
export function addBpCashFlow(data) {
  return request({
    url: '/cft/bp/cash/flow',
    method: 'post',
    data: data,
  });
}

// 修改BP现金流量表
export function updateBpCashFlow(data) {
  return request({
    url: '/cft/bp/cash/flow',
    method: 'put',
    data: data,
  });
}

// 删除BP现金流量表
export function delBpCashFlow(id) {
  return request({
    url: '/cft/bp/cash/flow/' + id,
    method: 'delete',
  });
}
