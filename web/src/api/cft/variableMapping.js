import request from '@/utils/request'

// 查询变量映射表列表
export function listVariableMapping(query) {
  return request({
    url: '/cft/variable/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询变量映射表详细
export function getVariableMapping(id) {
  return request({
    url: '/cft/variable/mapping/' + id,
    method: 'get'
  })
}

// 新增变量映射表
export function addVariableMapping(data) {
  return request({
    url: '/cft/variable/mapping',
    method: 'post',
    data: data
  })
}

// 修改变量映射表
export function updateVariableMapping(data) {
  return request({
    url: '/cft/variable/mapping',
    method: 'put',
    data: data
  })
}

// 删除变量映射表
export function delVariableMapping(id) {
  return request({
    url: '/cft/variable/mapping/' + id,
    method: 'delete'
  })
}
