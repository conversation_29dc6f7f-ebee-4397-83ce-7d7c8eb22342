import request from '@/utils/request'

// 查询现金流项目映射表列表
export function listCashFlowItemMapping(query) {
  return request({
    url: '/cft/cash/flow/item/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询现金流项目映射表详细
export function getCashFlowItemMapping(id) {
  return request({
    url: '/cft/cash/flow/item/mapping/' + id,
    method: 'get'
  })
}

// 新增现金流项目映射表
export function addCashFlowItemMapping(data) {
  return request({
    url: '/cft/cash/flow/item/mapping',
    method: 'post',
    data: data
  })
}

// 修改现金流项目映射表
export function updateCashFlowItemMapping(data) {
  return request({
    url: '/cft/cash/flow/item/mapping',
    method: 'put',
    data: data
  })
}

// 删除现金流项目映射表
export function delCashFlowItemMapping(id) {
  return request({
    url: '/cft/cash/flow/item/mapping/' + id,
    method: 'delete'
  })
}
