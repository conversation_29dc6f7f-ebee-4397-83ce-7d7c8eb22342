import request from '@/utils/request'

// 查询拆分比例表列表
export function listSplitRatio(query) {
  return request({
    url: '/cft/split/ratio/list',
    method: 'get',
    params: query
  })
}

// 查询拆分比例表详细
export function getSplitRatio(id) {
  return request({
    url: '/cft/split/ratio/' + id,
    method: 'get'
  })
}

// 根据条件查询拆分比例表
export function getSplitRatioByCondition(params) {
  return request({
    url: '/cft/split/ratio/condition',
    method: 'get',
    params: params
  })
}

// 新增拆分比例表
export function addSplitRatio(data) {
  return request({
    url: '/cft/split/ratio',
    method: 'post',
    data: data
  })
}

// 修改拆分比例表
export function updateSplitRatio(data) {
  return request({
    url: '/cft/split/ratio',
    method: 'put',
    data: data
  })
}

// 删除拆分比例表
export function delSplitRatio(id) {
  return request({
    url: '/cft/split/ratio/' + id,
    method: 'delete'
  })
}

// 批量新增拆分比例表
export function batchAddSplitRatio(data) {
  return request({
    url: '/cft/split/ratio/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除拆分比例表
export function delSplitRatioByPeriod(accountingPeriod) {
  return request({
    url: '/cft/split/ratio/period/' + accountingPeriod,
    method: 'delete'
  })
}

// 导出拆分比例表
export function exportSplitRatio(query) {
  return request({
    url: '/cft/split/ratio/export',
    method: 'post',
    data: query
  })
}

// 导入拆分比例表模板
export function importTemplate() {
  return request({
    url: '/cft/split/ratio/exportTemplate',
    method: 'post'
  })
}
