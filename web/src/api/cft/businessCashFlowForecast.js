import request from '@/utils/request'

// 查询业务现金流预测表列表
export function listBusinessCashFlowForecast(query) {
  return request({
    url: '/cft/business/cash/flow/forecast/list',
    method: 'get',
    params: query
  })
}

// 查询业务现金流预测表详细
export function getBusinessCashFlowForecast(id) {
  return request({
    url: '/cft/business/cash/flow/forecast/' + id,
    method: 'get'
  })
}

// 根据条件查询业务现金流预测表
export function getBusinessCashFlowForecastByCondition(params) {
  return request({
    url: '/cft/business/cash/flow/forecast/condition',
    method: 'get',
    params: params
  })
}

// 新增业务现金流预测表
export function addBusinessCashFlowForecast(data) {
  return request({
    url: '/cft/business/cash/flow/forecast',
    method: 'post',
    data: data
  })
}

// 修改业务现金流预测表
export function updateBusinessCashFlowForecast(data) {
  return request({
    url: '/cft/business/cash/flow/forecast',
    method: 'put',
    data: data
  })
}

// 删除业务现金流预测表
export function delBusinessCashFlowForecast(id) {
  return request({
    url: '/cft/business/cash/flow/forecast/' + id,
    method: 'delete'
  })
}

// 批量新增业务现金流预测表
export function batchAddBusinessCashFlowForecast(data) {
  return request({
    url: '/cft/business/cash/flow/forecast/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除业务现金流预测表
export function delBusinessCashFlowForecastByPeriod(accountingPeriod) {
  return request({
    url: '/cft/business/cash/flow/forecast/period/' + accountingPeriod,
    method: 'delete'
  })
}

// 导出业务现金流预测表
export function exportBusinessCashFlowForecast(query) {
  return request({
    url: '/cft/business/cash/flow/forecast/export',
    method: 'post',
    data: query
  })
}

// 导入业务现金流预测表模板
export function importTemplate() {
  return request({
    url: '/cft/business/cash/flow/forecast/exportTemplate',
    method: 'post'
  })
}
