import request from '@/utils/request'

// 查询精算业管费汇总表列表
export function listActuarialExpenseSummary(query) {
  return request({
    url: '/cft/actuarial/expense/summary/list',
    method: 'get',
    params: query
  })
}

// 查询精算业管费汇总表详细
export function getActuarialExpenseSummary(id) {
  return request({
    url: '/cft/actuarial/expense/summary/' + id,
    method: 'get'
  })
}

// 根据条件查询精算业管费汇总表
export function getActuarialExpenseSummaryByCondition(params) {
  return request({
    url: '/cft/actuarial/expense/summary/condition',
    method: 'get',
    params: params
  })
}

// 新增精算业管费汇总表
export function addActuarialExpenseSummary(data) {
  return request({
    url: '/cft/actuarial/expense/summary',
    method: 'post',
    data: data
  })
}

// 修改精算业管费汇总表
export function updateActuarialExpenseSummary(data) {
  return request({
    url: '/cft/actuarial/expense/summary',
    method: 'put',
    data: data
  })
}

// 删除精算业管费汇总表
export function delActuarialExpenseSummary(id) {
  return request({
    url: '/cft/actuarial/expense/summary/' + id,
    method: 'delete'
  })
}

// 批量新增精算业管费汇总表
export function batchAddActuarialExpenseSummary(data) {
  return request({
    url: '/cft/actuarial/expense/summary/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除精算业管费汇总表
export function delActuarialExpenseSummaryByPeriod(accountingPeriod) {
  return request({
    url: '/cft/actuarial/expense/summary/period/' + accountingPeriod,
    method: 'delete'
  })
}



// 导出精算业管费汇总表
export function exportActuarialExpenseSummary(query) {
  return request({
    url: '/cft/actuarial/expense/summary/export',
    method: 'post',
    data: query
  })
}

// 导入精算业管费汇总表模板
export function importTemplate() {
  return request({
    url: '/cft/actuarial/expense/summary/exportTemplate',
    method: 'post'
  })
}
