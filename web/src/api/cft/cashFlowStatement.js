import request from '@/utils/request'

// 查询现金流量表列表
export function listCashFlowStatement(query) {
  return request({
    url: '/cft/cash/flow/statement/list',
    method: 'get',
    params: query
  })
}

// 查询现金流量表详细
export function getCashFlowStatement(id) {
  return request({
    url: '/cft/cash/flow/statement/' + id,
    method: 'get'
  })
}

// 新增现金流量表
export function addCashFlowStatement(data) {
  return request({
    url: '/cft/cash/flow/statement',
    method: 'post',
    data: data
  })
}

// 修改现金流量表
export function updateCashFlowStatement(data) {
  return request({
    url: '/cft/cash/flow/statement',
    method: 'put',
    data: data
  })
}

// 删除现金流量表
export function delCashFlowStatement(id) {
  return request({
    url: '/cft/cash/flow/statement/' + id,
    method: 'delete'
  })
}
