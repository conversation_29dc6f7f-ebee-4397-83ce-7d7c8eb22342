import request from '@/utils/request'

// 查询财务预算费用拆分表列表
export function listFinancialBudgetExpenseSplit(query) {
  return request({
    url: '/cft/financial/budget/expense/split/list',
    method: 'get',
    params: query
  })
}

// 查询财务预算费用拆分表详细
export function getFinancialBudgetExpenseSplit(id) {
  return request({
    url: '/cft/financial/budget/expense/split/' + id,
    method: 'get'
  })
}

// 根据条件查询财务预算费用拆分表
export function getFinancialBudgetExpenseSplitByCondition(params) {
  return request({
    url: '/cft/financial/budget/expense/split/condition',
    method: 'get',
    params: params
  })
}

// 新增财务预算费用拆分表
export function addFinancialBudgetExpenseSplit(data) {
  return request({
    url: '/cft/financial/budget/expense/split',
    method: 'post',
    data: data
  })
}

// 修改财务预算费用拆分表
export function updateFinancialBudgetExpenseSplit(data) {
  return request({
    url: '/cft/financial/budget/expense/split',
    method: 'put',
    data: data
  })
}

// 删除财务预算费用拆分表
export function delFinancialBudgetExpenseSplit(id) {
  return request({
    url: '/cft/financial/budget/expense/split/' + id,
    method: 'delete'
  })
}

// 批量新增财务预算费用拆分表
export function batchAddFinancialBudgetExpenseSplit(data) {
  return request({
    url: '/cft/financial/budget/expense/split/batchAdd',
    method: 'post',
    data: data
  })
}

// 根据账期删除财务预算费用拆分表
export function delFinancialBudgetExpenseSplitByPeriod(accountingPeriod) {
  return request({
    url: '/cft/financial/budget/expense/split/period/' + accountingPeriod,
    method: 'delete'
  })
}

// 导出财务预算费用拆分表
export function exportFinancialBudgetExpenseSplit(query) {
  return request({
    url: '/cft/financial/budget/expense/split/export',
    method: 'post',
    data: query
  })
}

// 导入财务预算费用拆分表模板
export function importTemplate() {
  return request({
    url: '/cft/financial/budget/expense/split/exportTemplate',
    method: 'post'
  })
}
