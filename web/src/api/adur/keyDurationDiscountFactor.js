import request from '@/utils/request'

// 查询关键久期折现因子表含价差列表
export function listKeyDurationDiscountFactor(query) {
  return request({
    url: '/adur/key/duration/discount/factor/list',
    method: 'get',
    params: query
  })
}

// 查询关键久期折现因子表含价差详细
export function getKeyDurationDiscountFactor(id) {
  return request({
    url: '/adur/key/duration/discount/factor/' + id,
    method: 'get'
  })
}

// 新增关键久期折现因子表含价差
export function addKeyDurationDiscountFactor(data) {
  return request({
    url: '/adur/key/duration/discount/factor',
    method: 'post',
    data: data
  })
}

// 修改关键久期折现因子表含价差
export function updateKeyDurationDiscountFactor(data) {
  return request({
    url: '/adur/key/duration/discount/factor',
    method: 'put',
    data: data
  })
}

// 删除关键久期折现因子表含价差
export function delKeyDurationDiscountFactor(id) {
  return request({
    url: '/adur/key/duration/discount/factor/' + id,
    method: 'delete'
  })
}
