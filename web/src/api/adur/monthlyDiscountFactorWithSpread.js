import request from '@/utils/request'

// 查询ADUR月度折现因子表含价差列表
export function listMonthlyDiscountFactorWithSpread(query) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/list',
    method: 'get',
    params: query
  })
}

// 查询ADUR月度折现因子表含价差详细
export function getMonthlyDiscountFactorWithSpread(id) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/' + id,
    method: 'get'
  })
}

// 新增ADUR月度折现因子表含价差
export function addMonthlyDiscountFactorWithSpread(data) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread',
    method: 'post',
    data: data
  })
}

// 修改ADUR月度折现因子表含价差
export function updateMonthlyDiscountFactorWithSpread(data) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread',
    method: 'put',
    data: data
  })
}

// 删除ADUR月度折现因子表含价差
export function delMonthlyDiscountFactorWithSpread(id) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/' + id,
    method: 'delete'
  })
}

// 根据账期删除ADUR月度折现因子表含价差
export function delMonthlyDiscountFactorWithSpreadByPeriod(accountPeriod) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/monthly/discount/factor/with/spread/termData/' + id,
    method: 'put',
    data: data
  })
}
