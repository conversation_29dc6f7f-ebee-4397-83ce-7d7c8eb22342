import request from '@/utils/request'

// 查询月度折现曲线列表
export function listMonthlyDiscountCurve(query) {
  return request({
    url: '/adur/monthly/discount/curve/list',
    method: 'get',
    params: query
  })
}

// 查询月度折现曲线详细
export function getMonthlyDiscountCurve(id) {
  return request({
    url: '/adur/monthly/discount/curve/' + id,
    method: 'get'
  })
}

// 新增月度折现曲线
export function addMonthlyDiscountCurve(data) {
  return request({
    url: '/adur/monthly/discount/curve',
    method: 'post',
    data: data
  })
}

// 修改月度折现曲线
export function updateMonthlyDiscountCurve(data) {
  return request({
    url: '/adur/monthly/discount/curve',
    method: 'put',
    data: data
  })
}

// 删除月度折现曲线
export function delMonthlyDiscountCurve(id) {
  return request({
    url: '/adur/monthly/discount/curve/' + id,
    method: 'delete'
  })
}

// 根据账期删除月度折现曲线
export function delMonthlyDiscountCurveByPeriod(accountPeriod) {
  return request({
    url: '/adur/monthly/discount/curve/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/monthly/discount/curve/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/monthly/discount/curve/termData/' + id,
    method: 'put',
    data: data
  })
}
