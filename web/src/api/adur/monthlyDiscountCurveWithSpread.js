import request from '@/utils/request'

// 查询ADUR月度折现曲线含价差列表
export function listMonthlyDiscountCurveWithSpread(query) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/list',
    method: 'get',
    params: query
  })
}

// 查询ADUR月度折现曲线含价差详细
export function getMonthlyDiscountCurveWithSpread(id) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/' + id,
    method: 'get'
  })
}

// 新增ADUR月度折现曲线含价差
export function addMonthlyDiscountCurveWithSpread(data) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread',
    method: 'post',
    data: data
  })
}

// 修改ADUR月度折现曲线含价差
export function updateMonthlyDiscountCurveWithSpread(data) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread',
    method: 'put',
    data: data
  })
}

// 删除ADUR月度折现曲线含价差
export function delMonthlyDiscountCurveWithSpread(id) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/' + id,
    method: 'delete'
  })
}

// 根据账期删除ADUR月度折现曲线含价差
export function delMonthlyDiscountCurveWithSpreadByPeriod(accountPeriod) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/monthly/discount/curve/with/spread/termData/' + id,
    method: 'put',
    data: data
  })
}
