import request from '@/utils/request'

// 查询ADUR关键久期折现曲线表含价差列表
export function listKeyDurationCurveWithSpread(query) {
  return request({
    url: '/adur/key/duration/curve/with/spread/list',
    method: 'get',
    params: query
  })
}

// 查询ADUR关键久期折现曲线表含价差详细
export function getKeyDurationCurveWithSpread(id) {
  return request({
    url: '/adur/key/duration/curve/with/spread/' + id,
    method: 'get'
  })
}

// 新增ADUR关键久期折现曲线表含价差
export function addKeyDurationCurveWithSpread(data) {
  return request({
    url: '/adur/key/duration/curve/with/spread',
    method: 'post',
    data: data
  })
}

// 修改ADUR关键久期折现曲线表含价差
export function updateKeyDurationCurveWithSpread(data) {
  return request({
    url: '/adur/key/duration/curve/with/spread',
    method: 'put',
    data: data
  })
}

// 删除ADUR关键久期折现曲线表含价差
export function delKeyDurationCurveWithSpread(id) {
  return request({
    url: '/adur/key/duration/curve/with/spread/' + id,
    method: 'delete'
  })
}

// 根据账期删除ADUR关键久期折现曲线表含价差
export function delKeyDurationCurveWithSpreadByPeriod(accountPeriod) {
  return request({
    url: '/adur/key/duration/curve/with/spread/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/key/duration/curve/with/spread/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/key/duration/curve/with/spread/termData/' + id,
    method: 'put',
    data: data
  })
}
