import request from '@/utils/request'

// 查询关键久期折现曲线表含价差列表
export function listKeyDurationDiscountCurve(query) {
  return request({
    url: '/adur/key/duration/discount/curve/list',
    method: 'get',
    params: query
  })
}

// 查询关键久期折现曲线表含价差详细
export function getKeyDurationDiscountCurve(id) {
  return request({
    url: '/adur/key/duration/discount/curve/' + id,
    method: 'get'
  })
}

// 新增关键久期折现曲线表含价差
export function addKeyDurationDiscountCurve(data) {
  return request({
    url: '/adur/key/duration/discount/curve',
    method: 'post',
    data: data
  })
}

// 修改关键久期折现曲线表含价差
export function updateKeyDurationDiscountCurve(data) {
  return request({
    url: '/adur/key/duration/discount/curve',
    method: 'put',
    data: data
  })
}

// 删除关键久期折现曲线表含价差
export function delKeyDurationDiscountCurve(id) {
  return request({
    url: '/adur/key/duration/discount/curve/' + id,
    method: 'delete'
  })
}

// 根据账期删除关键久期折现曲线表含价差
export function delKeyDurationDiscountCurveByPeriod(accountPeriod) {
  return request({
    url: '/adur/key/duration/discount/curve/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/key/duration/discount/curve/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/key/duration/discount/curve/termData/' + id,
    method: 'put',
    data: data
  })
}
