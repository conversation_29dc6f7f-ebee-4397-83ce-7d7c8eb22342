import request from '@/utils/request'

// 查询关键久期参数列表
export function listKeyDurationParameter(query) {
  return request({
    url: '/adur/key/duration/parameter/list',
    method: 'get',
    params: query
  })
}

// 查询关键久期参数详细
export function getKeyDurationParameter(id) {
  return request({
    url: '/adur/key/duration/parameter/' + id,
    method: 'get'
  })
}

// 新增关键久期参数
export function addKeyDurationParameter(data) {
  return request({
    url: '/adur/key/duration/parameter',
    method: 'post',
    data: data
  })
}

// 修改关键久期参数
export function updateKeyDurationParameter(data) {
  return request({
    url: '/adur/key/duration/parameter',
    method: 'put',
    data: data
  })
}

// 删除关键久期参数
export function delKeyDurationParameter(id) {
  return request({
    url: '/adur/key/duration/parameter/' + id,
    method: 'delete'
  })
}

// 数据迁移
export function migrateKeyDurationParameter() {
  return request({
    url: '/adur/key/duration/parameter/migrateData',
    method: 'post'
  })
}
