import request from '@/utils/request'

// 查询月度折现因子含价差列表
export function listMonthlyDiscountFactor(query) {
  return request({
    url: '/adur/monthly/discount/factor/list',
    method: 'get',
    params: query
  })
}

// 查询月度折现因子含价差详细
export function getMonthlyDiscountFactor(id) {
  return request({
    url: '/adur/monthly/discount/factor/' + id,
    method: 'get'
  })
}

// 新增月度折现因子含价差
export function addMonthlyDiscountFactor(data) {
  return request({
    url: '/adur/monthly/discount/factor',
    method: 'post',
    data: data
  })
}

// 修改月度折现因子含价差
export function updateMonthlyDiscountFactor(data) {
  return request({
    url: '/adur/monthly/discount/factor',
    method: 'put',
    data: data
  })
}

// 删除月度折现因子含价差
export function delMonthlyDiscountFactor(id) {
  return request({
    url: '/adur/monthly/discount/factor/' + id,
    method: 'delete'
  })
}
