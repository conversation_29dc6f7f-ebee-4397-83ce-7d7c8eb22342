import request from '@/utils/request'

// 查询久期资产结果汇总表列表
export function listDurationAssetSummary(query) {
  return request({
    url: '/adur/duration/asset/summary/list',
    method: 'get',
    params: query
  })
}

// 查询久期资产结果汇总表详细
export function getDurationAssetSummary(id) {
  return request({
    url: '/adur/duration/asset/summary/' + id,
    method: 'get'
  })
}

// 新增久期资产结果汇总表
export function addDurationAssetSummary(data) {
  return request({
    url: '/adur/duration/asset/summary',
    method: 'post',
    data: data
  })
}

// 修改久期资产结果汇总表
export function updateDurationAssetSummary(data) {
  return request({
    url: '/adur/duration/asset/summary',
    method: 'put',
    data: data
  })
}

// 删除久期资产结果汇总表
export function delDurationAssetSummary(id) {
  return request({
    url: '/adur/duration/asset/summary/' + id,
    method: 'delete'
  })
}

// 导出久期资产结果汇总表
export function exportDurationAssetSummary(query) {
  return request({
    url: '/adur/duration/asset/summary/export',
    method: 'post',
    data: query
  })
}

// 下载久期资产结果汇总表导入模板
export function downloadTemplate() {
  return request({
    url: '/adur/duration/asset/summary/importTemplate',
    method: 'post'
  })
}
