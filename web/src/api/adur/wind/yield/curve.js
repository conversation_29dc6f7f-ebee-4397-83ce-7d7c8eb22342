import request from '@/utils/request'

// 查询万得收益率曲线列表
export function listWindYieldCurve(query) {
  return request({
    url: '/adur/wind/yield/curve/list',
    method: 'get',
    params: query
  })
}

// 查询万得收益率曲线详细
export function getWindYieldCurve(id) {
  return request({
    url: '/adur/wind/yield/curve/' + id,
    method: 'get'
  })
}

// 新增万得收益率曲线
export function addWindYieldCurve(data) {
  return request({
    url: '/adur/wind/yield/curve',
    method: 'post',
    data: data
  })
}

// 修改万得收益率曲线
export function updateWindYieldCurve(data) {
  return request({
    url: '/adur/wind/yield/curve',
    method: 'put',
    data: data
  })
}

// 删除万得收益率曲线
export function delWindYieldCurve(id) {
  return request({
    url: '/adur/wind/yield/curve/' + id,
    method: 'delete'
  })
}
