import request from '@/utils/request'

// 查询关键久期折现因子表含价差(第二个)列表
export function listIssueMonthlyDiscountFactor(query) {
  return request({
    url: '/adur/issue/monthly/discount/factor/list',
    method: 'get',
    params: query
  })
}

// 查询关键久期折现因子表含价差(第二个)详细
export function getIssueMonthlyDiscountFactor(id) {
  return request({
    url: '/adur/issue/monthly/discount/factor/' + id,
    method: 'get'
  })
}

// 新增关键久期折现因子表含价差(第二个)
export function addIssueMonthlyDiscountFactor(data) {
  return request({
    url: '/adur/issue/monthly/discount/factor',
    method: 'post',
    data: data
  })
}

// 修改关键久期折现因子表含价差(第二个)
export function updateIssueMonthlyDiscountFactor(data) {
  return request({
    url: '/adur/issue/monthly/discount/factor',
    method: 'put',
    data: data
  })
}

// 删除关键久期折现因子表含价差(第二个)
export function delIssueMonthlyDiscountFactor(id) {
  return request({
    url: '/adur/issue/monthly/discount/factor/' + id,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/issue/monthly/discount/factor/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/issue/monthly/discount/factor/termData/' + id,
    method: 'put',
    data: data
  })
}
