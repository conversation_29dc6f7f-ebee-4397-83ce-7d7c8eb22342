import request from '@/utils/request'

// 查询年度折现曲线列表
export function listAnnualDiscountCurve(query) {
  return request({
    url: '/adur/annual/discount/curve/list',
    method: 'get',
    params: query
  })
}

// 查询年度折现曲线详细
export function getAnnualDiscountCurve(id) {
  return request({
    url: '/adur/annual/discount/curve/' + id,
    method: 'get'
  })
}

// 新增年度折现曲线
export function addAnnualDiscountCurve(data) {
  return request({
    url: '/adur/annual/discount/curve',
    method: 'post',
    data: data
  })
}

// 修改年度折现曲线
export function updateAnnualDiscountCurve(data) {
  return request({
    url: '/adur/annual/discount/curve',
    method: 'put',
    data: data
  })
}

// 删除年度折现曲线
export function delAnnualDiscountCurve(id) {
  return request({
    url: '/adur/annual/discount/curve/' + id,
    method: 'delete'
  })
}

// 根据账期删除年度折现曲线
export function delAnnualDiscountCurveByPeriod(accountPeriod) {
  return request({
    url: '/adur/annual/discount/curve/period/' + accountPeriod,
    method: 'delete'
  })
}

// 获取期限数据
export function getTermData(id) {
  return request({
    url: '/adur/annual/discount/curve/termData/' + id,
    method: 'get'
  })
}

// 更新期限数据
export function updateTermData(id, data) {
  return request({
    url: '/adur/annual/discount/curve/termData/' + id,
    method: 'put',
    data: data
  })
}
