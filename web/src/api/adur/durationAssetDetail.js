import request from '@/utils/request'

// 查询久期资产明细列表
export function listDurationAssetDetail(query) {
  return request({
    url: '/adur/duration/asset/detail/list',
    method: 'get',
    params: query
  })
}

// 查询久期资产明细详细
export function getDurationAssetDetail(id) {
  return request({
    url: '/adur/duration/asset/detail/' + id,
    method: 'get'
  })
}

// 新增久期资产明细
export function addDurationAssetDetail(data) {
  return request({
    url: '/adur/duration/asset/detail',
    method: 'post',
    data: data
  })
}

// 修改久期资产明细
export function updateDurationAssetDetail(data) {
  return request({
    url: '/adur/duration/asset/detail',
    method: 'put',
    data: data
  })
}

// 删除久期资产明细
export function delDurationAssetDetail(id) {
  return request({
    url: '/adur/duration/asset/detail/' + id,
    method: 'delete'
  })
}
