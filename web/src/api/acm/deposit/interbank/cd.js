import request from '@/utils/request'

// 查询存款及同业存单表列表
export function listDepositInterbankCd(query) {
  return request({
    url: '/acm/deposit/interbank/cd/list',
    method: 'get',
    params: query
  })
}

// 查询存款及同业存单表详细
export function getDepositInterbankCd(id) {
  return request({
    url: '/acm/deposit/interbank/cd/' + id,
    method: 'get'
  })
}

// 根据条件查询存款及同业存单表
export function getDepositInterbankCdByCondition(accountingPeriod, assetSubSubCategory, bankClassification) {
  return request({
    url: '/acm/deposit/interbank/cd/condition',
    method: 'get',
    params: {
      accountingPeriod,
      assetSubSubCategory,
      bankClassification
    }
  })
}

// 新增存款及同业存单表
export function addDepositInterbankCd(data) {
  return request({
    url: '/acm/deposit/interbank/cd',
    method: 'post',
    data: data
  })
}

// 修改存款及同业存单表
export function updateDepositInterbankCd(data) {
  return request({
    url: '/acm/deposit/interbank/cd',
    method: 'put',
    data: data
  })
}

// 删除存款及同业存单表
export function delDepositInterbankCd(id) {
  return request({
    url: '/acm/deposit/interbank/cd/' + id,
    method: 'delete'
  })
}

// 导出存款及同业存单表
export function exportDepositInterbankCd(query) {
  return request({
    url: '/acm/deposit/interbank/cd/export',
    method: 'post',
    data: query
  })
}

// 导入存款及同业存单表
export function importDepositInterbankCd(data) {
  return request({
    url: '/acm/deposit/interbank/cd/importData',
    method: 'post',
    data: data
  })
}

// 下载存款及同业存单表导入模板
export function importTemplate() {
  return request({
    url: '/acm/deposit/interbank/cd/importTemplate',
    method: 'post'
  })
}
