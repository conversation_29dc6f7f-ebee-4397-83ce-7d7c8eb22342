import request from '@/utils/request'

// 查询保险资产风险五级分类状况表列表
export function listAssetRiskFiveLevel(query) {
  return request({
    url: '/acm/asset/risk/five/level/list',
    method: 'get',
    params: query
  })
}

// 查询保险资产风险五级分类状况表详细
export function getAssetRiskFiveLevel(id) {
  return request({
    url: '/acm/asset/risk/five/level/' + id,
    method: 'get'
  })
}

// 新增保险资产风险五级分类状况表
export function addAssetRiskFiveLevel(data) {
  return request({
    url: '/acm/asset/risk/five/level',
    method: 'post',
    data: data
  })
}

// 修改保险资产风险五级分类状况表
export function updateAssetRiskFiveLevel(data) {
  return request({
    url: '/acm/asset/risk/five/level',
    method: 'put',
    data: data
  })
}

// 删除保险资产风险五级分类状况表
export function delAssetRiskFiveLevel(id) {
  return request({
    url: '/acm/asset/risk/five/level/' + id,
    method: 'delete'
  })
}
