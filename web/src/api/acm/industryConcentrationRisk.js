import request from '@/utils/request'

// 查询行业集中度风险表列表
export function listIndustryConcentrationRisk(query) {
  return request({
    url: '/acm/industry/concentration/risk/list',
    method: 'get',
    params: query
  })
}

// 查询行业集中度风险表详细
export function getIndustryConcentrationRisk(id) {
  return request({
    url: '/acm/industry/concentration/risk/' + id,
    method: 'get'
  })
}

// 新增行业集中度风险表
export function addIndustryConcentrationRisk(data) {
  return request({
    url: '/acm/industry/concentration/risk',
    method: 'post',
    data: data
  })
}

// 修改行业集中度风险表
export function updateIndustryConcentrationRisk(data) {
  return request({
    url: '/acm/industry/concentration/risk',
    method: 'put',
    data: data
  })
}

// 删除行业集中度风险表
export function delIndustryConcentrationRisk(id) {
  return request({
    url: '/acm/industry/concentration/risk/' + id,
    method: 'delete'
  })
}
