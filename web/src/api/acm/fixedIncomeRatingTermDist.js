import request from '@/utils/request'

// 查询固定收益类投资资产外部评级剩余期限分布表列表
export function listFixedIncomeRatingTermDist(query) {
  return request({
    url: '/acm/fixed/income/rating/term/dist/list',
    method: 'get',
    params: query
  })
}

// 查询固定收益类投资资产外部评级剩余期限分布表详细
export function getFixedIncomeRatingTermDist(id) {
  return request({
    url: '/acm/fixed/income/rating/term/dist/' + id,
    method: 'get'
  })
}

// 新增固定收益类投资资产外部评级剩余期限分布表
export function addFixedIncomeRatingTermDist(data) {
  return request({
    url: '/acm/fixed/income/rating/term/dist',
    method: 'post',
    data: data
  })
}

// 修改固定收益类投资资产外部评级剩余期限分布表
export function updateFixedIncomeRatingTermDist(data) {
  return request({
    url: '/acm/fixed/income/rating/term/dist',
    method: 'put',
    data: data
  })
}

// 删除固定收益类投资资产外部评级剩余期限分布表
export function delFixedIncomeRatingTermDist(id) {
  return request({
    url: '/acm/fixed/income/rating/term/dist/' + id,
    method: 'delete'
  })
}
