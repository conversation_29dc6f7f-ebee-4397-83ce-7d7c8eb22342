import request from '@/utils/request'

// 查询固定收益类投资资产信用评级表列表
export function listFixedIncomeCreditRating(query) {
  return request({
    url: '/acm/fixed/income/credit/rating/list',
    method: 'get',
    params: query
  })
}

// 查询固定收益类投资资产信用评级表详细
export function getFixedIncomeCreditRating(id) {
  return request({
    url: '/acm/fixed/income/credit/rating/' + id,
    method: 'get'
  })
}

// 根据条件查询固定收益类投资资产信用评级表
export function getFixedIncomeCreditRatingByCondition(accountingPeriod, domesticForeign, creditRatingCategory) {
  return request({
    url: '/acm/fixed/income/credit/rating/condition',
    method: 'get',
    params: {
      accountingPeriod,
      domesticForeign,
      creditRatingCategory
    }
  })
}

// 新增固定收益类投资资产信用评级表
export function addFixedIncomeCreditRating(data) {
  return request({
    url: '/acm/fixed/income/credit/rating',
    method: 'post',
    data: data
  })
}

// 修改固定收益类投资资产信用评级表
export function updateFixedIncomeCreditRating(data) {
  return request({
    url: '/acm/fixed/income/credit/rating',
    method: 'put',
    data: data
  })
}

// 删除固定收益类投资资产信用评级表
export function delFixedIncomeCreditRating(id) {
  return request({
    url: '/acm/fixed/income/credit/rating/' + id,
    method: 'delete'
  })
}

// 导出固定收益类投资资产信用评级表
export function exportFixedIncomeCreditRating(query) {
  return request({
    url: '/acm/fixed/income/credit/rating/export',
    method: 'post',
    data: query
  })
}

// 导入固定收益类投资资产信用评级表
export function importFixedIncomeCreditRating(data) {
  return request({
    url: '/acm/fixed/income/credit/rating/importData',
    method: 'post',
    data: data
  })
}

// 下载固定收益类投资资产信用评级表导入模板
export function importTemplate() {
  return request({
    url: '/acm/fixed/income/credit/rating/importTemplate',
    method: 'post'
  })
}
