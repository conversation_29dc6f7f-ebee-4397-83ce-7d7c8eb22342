import request from '@/utils/request'

// 查询保险资产风险项目映射表列表
export function listAssetRiskItemMapping(query) {
  return request({
    url: '/acm/asset/risk/item/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询保险资产风险项目映射表详细
export function getAssetRiskItemMapping(id) {
  return request({
    url: '/acm/asset/risk/item/mapping/' + id,
    method: 'get'
  })
}

// 新增保险资产风险项目映射表
export function addAssetRiskItemMapping(data) {
  return request({
    url: '/acm/asset/risk/item/mapping',
    method: 'post',
    data: data
  })
}

// 修改保险资产风险项目映射表
export function updateAssetRiskItemMapping(data) {
  return request({
    url: '/acm/asset/risk/item/mapping',
    method: 'put',
    data: data
  })
}

// 删除保险资产风险项目映射表
export function delAssetRiskItemMapping(id) {
  return request({
    url: '/acm/asset/risk/item/mapping/' + id,
    method: 'delete'
  })
}
