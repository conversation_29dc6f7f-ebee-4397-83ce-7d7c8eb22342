import request from '@/utils/request'

// 查询缴费年期映射列表
export function listPaymentPeriodMapping(query) {
  return request({
    url: '/liab/payment/period/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询缴费年期映射详细
export function getPaymentPeriodMapping(id) {
  return request({
    url: '/liab/payment/period/mapping/' + id,
    method: 'get'
  })
}

// 新增缴费年期映射
export function addPaymentPeriodMapping(data) {
  return request({
    url: '/liab/payment/period/mapping',
    method: 'post',
    data: data
  })
}

// 修改缴费年期映射
export function updatePaymentPeriodMapping(data) {
  return request({
    url: '/liab/payment/period/mapping',
    method: 'put',
    data: data
  })
}

// 删除缴费年期映射
export function delPaymentPeriodMapping(id) {
  return request({
    url: '/liab/payment/period/mapping/' + id,
    method: 'delete'
  })
}
