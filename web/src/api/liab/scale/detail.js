import request from '@/utils/request'

// 查询负债规模明细表列表
export function listLiabScaleDetail(query) {
  return request({
    url: '/liab/scale/detail/list',
    method: 'get',
    params: query
  })
}

// 查询负债规模明细表详细
export function getLiabScaleDetail(id) {
  return request({
    url: '/liab/scale/detail/' + id,
    method: 'get'
  })
}

// 新增负债规模明细表
export function addLiabScaleDetail(data) {
  return request({
    url: '/liab/scale/detail',
    method: 'post',
    data: data
  })
}

// 修改负债规模明细表
export function updateLiabScaleDetail(data) {
  return request({
    url: '/liab/scale/detail',
    method: 'put',
    data: data
  })
}

// 删除负债规模明细表
export function delLiabScaleDetail(id) {
  return request({
    url: '/liab/scale/detail/' + id,
    method: 'delete'
  })
}

// 导出负债规模明细表
export function exportLiabScaleDetail(query) {
  return request({
    url: '/liab/scale/detail/export',
    method: 'post',
    data: query
  })
}

// 获取负债规模明细表导入模板
export function getImportTemplate() {
  return request({
    url: '/liab/scale/detail/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入负债规模明细表
export function importLiabScaleDetail(data) {
  return request({
    url: '/liab/scale/detail/importData',
    method: 'post',
    data: data
  })
}
