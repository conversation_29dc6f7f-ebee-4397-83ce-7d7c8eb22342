import request from '@/utils/request'

// 查询负债规模汇总表列表
export function listLiabScaleSummary(query) {
  return request({
    url: '/liab/scale/summary/list',
    method: 'get',
    params: query
  })
}

// 查询负债规模汇总表详细
export function getLiabScaleSummary(id) {
  return request({
    url: '/liab/scale/summary/' + id,
    method: 'get'
  })
}

// 新增负债规模汇总表
export function addLiabScaleSummary(data) {
  return request({
    url: '/liab/scale/summary',
    method: 'post',
    data: data
  })
}

// 修改负债规模汇总表
export function updateLiabScaleSummary(data) {
  return request({
    url: '/liab/scale/summary',
    method: 'put',
    data: data
  })
}

// 删除负债规模汇总表
export function delLiabScaleSummary(id) {
  return request({
    url: '/liab/scale/summary/' + id,
    method: 'delete'
  })
}

// 导出负债规模汇总表
export function exportLiabScaleSummary(query) {
  return request({
    url: '/liab/scale/summary/export',
    method: 'post',
    data: query
  })
}

// 获取负债规模汇总表导入模板
export function getImportTemplate() {
  return request({
    url: '/liab/scale/summary/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入负债规模汇总表
export function importLiabScaleSummary(data) {
  return request({
    url: '/liab/scale/summary/importData',
    method: 'post',
    data: data
  })
}
