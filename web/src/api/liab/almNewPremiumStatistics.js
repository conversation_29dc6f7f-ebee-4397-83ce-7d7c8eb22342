import request from '@/utils/request'

// 查询ALM新单保费统计表列表
export function listAlmNewPremiumStatistics(query) {
  return request({
    url: '/liab/alm/new/premium/statistics/list',
    method: 'get',
    params: query
  })
}

// 查询ALM新单保费统计表详细
export function getAlmNewPremiumStatistics(id) {
  return request({
    url: '/liab/alm/new/premium/statistics/' + id,
    method: 'get'
  })
}

// 新增ALM新单保费统计表
export function addAlmNewPremiumStatistics(data) {
  return request({
    url: '/liab/alm/new/premium/statistics',
    method: 'post',
    data: data
  })
}

// 修改ALM新单保费统计表
export function updateAlmNewPremiumStatistics(data) {
  return request({
    url: '/liab/alm/new/premium/statistics',
    method: 'put',
    data: data
  })
}

// 删除ALM新单保费统计表
export function delAlmNewPremiumStatistics(id) {
  return request({
    url: '/liab/alm/new/premium/statistics/' + id,
    method: 'delete'
  })
}
