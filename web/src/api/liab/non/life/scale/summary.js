import request from '@/utils/request'

// 查询非寿险负债规模汇总表列表
export function listLiabNonLifeScaleSummary(query) {
  return request({
    url: '/liab/non/life/scale/summary/list',
    method: 'get',
    params: query
  })
}

// 查询非寿险负债规模汇总表详细
export function getLiabNonLifeScaleSummary(id) {
  return request({
    url: '/liab/non/life/scale/summary/' + id,
    method: 'get'
  })
}

// 新增非寿险负债规模汇总表
export function addLiabNonLifeScaleSummary(data) {
  return request({
    url: '/liab/non/life/scale/summary',
    method: 'post',
    data: data
  })
}

// 修改非寿险负债规模汇总表
export function updateLiabNonLifeScaleSummary(data) {
  return request({
    url: '/liab/non/life/scale/summary',
    method: 'put',
    data: data
  })
}

// 删除非寿险负债规模汇总表
export function delLiabNonLifeScaleSummary(id) {
  return request({
    url: '/liab/non/life/scale/summary/' + id,
    method: 'delete'
  })
}

// 导出非寿险负债规模汇总表
export function exportLiabNonLifeScaleSummary(query) {
  return request({
    url: '/liab/non/life/scale/summary/export',
    method: 'post',
    data: query
  })
}

// 获取非寿险负债规模汇总表导入模板
export function getImportTemplate() {
  return request({
    url: '/liab/non/life/scale/summary/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入非寿险负债规模汇总表
export function importLiabNonLifeScaleSummary(data) {
  return request({
    url: '/liab/non/life/scale/summary/importData',
    method: 'post',
    data: data
  })
}
