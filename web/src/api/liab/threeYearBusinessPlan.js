import request from '@/utils/request'

// 查询三年新业务规划列表
export function listThreeYearBusinessPlan(query) {
  return request({
    url: '/liab/three/year/business/plan/list',
    method: 'get',
    params: query
  })
}

// 查询三年新业务规划详细
export function getThreeYearBusinessPlan(id) {
  return request({
    url: '/liab/three/year/business/plan/' + id,
    method: 'get'
  })
}

// 新增三年新业务规划
export function addThreeYearBusinessPlan(data) {
  return request({
    url: '/liab/three/year/business/plan',
    method: 'post',
    data: data
  })
}

// 修改三年新业务规划
export function updateThreeYearBusinessPlan(data) {
  return request({
    url: '/liab/three/year/business/plan',
    method: 'put',
    data: data
  })
}

// 删除三年新业务规划
export function delThreeYearBusinessPlan(id) {
  return request({
    url: '/liab/three/year/business/plan/' + id,
    method: 'delete'
  })
}

// 根据账期删除三年新业务规划
export function delThreeYearBusinessPlanByPeriod(accountingPeriod) {
  return request({
    url: '/liab/three/year/business/plan/period/' + accountingPeriod,
    method: 'delete'
  })
}
