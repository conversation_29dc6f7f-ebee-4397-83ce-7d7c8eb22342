import request from '@/utils/request'

// 查询新单保费汇总列表
export function listNewPremiumSummary(query) {
  return request({
    url: '/liab/new/premium/summary/list',
    method: 'get',
    params: query
  })
}

// 查询新单保费汇总详细
export function getNewPremiumSummary(id) {
  return request({
    url: '/liab/new/premium/summary/' + id,
    method: 'get'
  })
}

// 新增新单保费汇总
export function addNewPremiumSummary(data) {
  return request({
    url: '/liab/new/premium/summary',
    method: 'post',
    data: data
  })
}

// 修改新单保费汇总
export function updateNewPremiumSummary(data) {
  return request({
    url: '/liab/new/premium/summary',
    method: 'put',
    data: data
  })
}

// 删除新单保费汇总
export function delNewPremiumSummary(id) {
  return request({
    url: '/liab/new/premium/summary/' + id,
    method: 'delete'
  })
}
