import request from '@/utils/request'

// 查询资产负债表列表
export function listBalanceSheet(query) {
  return request({
    url: '/liab/balance/sheet/list',
    method: 'get',
    params: query
  })
}

// 查询资产负债表详细
export function getBalanceSheet(id) {
  return request({
    url: '/liab/balance/sheet/' + id,
    method: 'get'
  })
}

// 新增资产负债表
export function addBalanceSheet(data) {
  return request({
    url: '/liab/balance/sheet',
    method: 'post',
    data: data
  })
}

// 修改资产负债表
export function updateBalanceSheet(data) {
  return request({
    url: '/liab/balance/sheet',
    method: 'put',
    data: data
  })
}

// 删除资产负债表
export function delBalanceSheet(id) {
  return request({
    url: '/liab/balance/sheet/' + id,
    method: 'delete'
  })
}

// 导出资产负债表
export function exportBalanceSheet(query) {
  return request({
    url: '/liab/balance/sheet/export',
    method: 'post',
    data: query
  })
}

// 获取资产负债表导入模板
export function getImportTemplate() {
  return request({
    url: '/liab/balance/sheet/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入资产负债表
export function importBalanceSheet(data) {
  return request({
    url: '/liab/balance/sheet/importData',
    method: 'post',
    data: data
  })
}
