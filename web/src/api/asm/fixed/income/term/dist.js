import request from '@/utils/request'

// 查询固定收益类投资资产剩余期限分布表列表
export function listFixedIncomeTermDist(query) {
  return request({
    url: '/asm/fixed/income/term/dist/list',
    method: 'get',
    params: query
  })
}

// 查询固定收益类投资资产剩余期限分布表详细
export function getFixedIncomeTermDist(id) {
  return request({
    url: '/asm/fixed/income/term/dist/' + id,
    method: 'get'
  })
}

// 根据条件查询固定收益类投资资产剩余期限分布表
export function getFixedIncomeTermDistByCondition(accountingPeriod, domesticForeign, fixedIncomeTermCategory, remainingTermFlag) {
  return request({
    url: '/asm/fixed/income/term/dist/condition',
    method: 'get',
    params: {
      accountingPeriod,
      domesticForeign,
      fixedIncomeTermCategory,
      remainingTermFlag
    }
  })
}

// 新增固定收益类投资资产剩余期限分布表
export function addFixedIncomeTermDist(data) {
  return request({
    url: '/asm/fixed/income/term/dist',
    method: 'post',
    data: data
  })
}

// 修改固定收益类投资资产剩余期限分布表
export function updateFixedIncomeTermDist(data) {
  return request({
    url: '/asm/fixed/income/term/dist',
    method: 'put',
    data: data
  })
}

// 删除固定收益类投资资产剩余期限分布表
export function delFixedIncomeTermDist(id) {
  return request({
    url: '/asm/fixed/income/term/dist/' + id,
    method: 'delete'
  })
}

// 导出固定收益类投资资产剩余期限分布表
export function exportFixedIncomeTermDist(query) {
  return request({
    url: '/asm/fixed/income/term/dist/export',
    method: 'post',
    data: query
  })
}

// 导入固定收益类投资资产剩余期限分布表
export function importFixedIncomeTermDist(data) {
  return request({
    url: '/asm/fixed/income/term/dist/importData',
    method: 'post',
    data: data
  })
}

// 下载固定收益类投资资产剩余期限分布表导入模板
export function importTemplate() {
  return request({
    url: '/asm/fixed/income/term/dist/importTemplate',
    method: 'post'
  })
}
