import request from '@/utils/request'

// 查询偿付能力状况表列表
export function listSolvencyStatus(query) {
  return request({
    url: '/asm/solvency/status/list',
    method: 'get',
    params: query
  })
}

// 查询偿付能力状况表详细
export function getSolvencyStatus(id) {
  return request({
    url: '/asm/solvency/status/' + id,
    method: 'get'
  })
}

// 根据条件查询偿付能力状况表
export function getSolvencyStatusByCondition(accountingPeriod, itemName) {
  return request({
    url: '/asm/solvency/status/condition',
    method: 'get',
    params: {
      accountingPeriod,
      itemName
    }
  })
}

// 新增偿付能力状况表
export function addSolvencyStatus(data) {
  return request({
    url: '/asm/solvency/status',
    method: 'post',
    data: data
  })
}

// 修改偿付能力状况表
export function updateSolvencyStatus(data) {
  return request({
    url: '/asm/solvency/status',
    method: 'put',
    data: data
  })
}

// 删除偿付能力状况表
export function delSolvencyStatus(id) {
  return request({
    url: '/asm/solvency/status/' + id,
    method: 'delete'
  })
}
