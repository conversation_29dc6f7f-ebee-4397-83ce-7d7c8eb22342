import request from '@/utils/request'

// 查询资产规模与偿付能力表列表
export function listAssetScaleSolvency(query) {
  return request({
    url: '/asm/asset/scale/solvency/list',
    method: 'get',
    params: query
  })
}

// 查询资产规模与偿付能力表详细
export function getAssetScaleSolvency(id) {
  return request({
    url: '/asm/asset/scale/solvency/' + id,
    method: 'get'
  })
}

// 根据条件查询资产规模与偿付能力表
export function getAssetScaleSolvencyByCondition(accountingPeriod, itemName) {
  return request({
    url: '/asm/asset/scale/solvency/condition',
    method: 'get',
    params: {
      accountingPeriod,
      itemName
    }
  })
}

// 新增资产规模与偿付能力表
export function addAssetScaleSolvency(data) {
  return request({
    url: '/asm/asset/scale/solvency',
    method: 'post',
    data: data
  })
}

// 修改资产规模与偿付能力表
export function updateAssetScaleSolvency(data) {
  return request({
    url: '/asm/asset/scale/solvency',
    method: 'put',
    data: data
  })
}

// 删除资产规模与偿付能力表
export function delAssetScaleSolvency(id) {
  return request({
    url: '/asm/asset/scale/solvency/' + id,
    method: 'delete'
  })
}

// 计算资产规模与偿付能力表
export function calculateAssetScaleSolvency(data) {
  return request({
    url: '/asm/asset/scale/solvency/calculate',
    method: 'post',
    params: data
  })
}
