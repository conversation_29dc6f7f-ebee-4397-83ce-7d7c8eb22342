import request from '@/utils/request'

// 查询资金运用规模表列表
export function listFundUtilizationScale(query) {
  return request({
    url: '/asm/fund/utilization/scale/list',
    method: 'get',
    params: query
  })
}

// 查询资金运用规模表详细
export function getFundUtilizationScale(id) {
  return request({
    url: '/asm/fund/utilization/scale/' + id,
    method: 'get'
  })
}

// 根据条件查询资金运用规模表
export function getFundUtilizationScaleByCondition(accountingPeriod, itemName, itemClassificationLevel, dataType) {
  return request({
    url: '/asm/fund/utilization/scale/condition',
    method: 'get',
    params: {
      accountingPeriod,
      itemName,
      itemClassificationLevel,
      dataType
    }
  })
}

// 新增资金运用规模表
export function addFundUtilizationScale(data) {
  return request({
    url: '/asm/fund/utilization/scale',
    method: 'post',
    data: data
  })
}

// 修改资金运用规模表
export function updateFundUtilizationScale(data) {
  return request({
    url: '/asm/fund/utilization/scale',
    method: 'put',
    data: data
  })
}

// 删除资金运用规模表
export function delFundUtilizationScale(id) {
  return request({
    url: '/asm/fund/utilization/scale/' + id,
    method: 'delete'
  })
}
