import request from '@/utils/request'

// 查询资金运用比例监管表列表
export function listFundUtilizationRatio(query) {
  return request({
    url: '/asm/fund/utilization/ratio/list',
    method: 'get',
    params: query
  })
}

// 查询资金运用比例监管表详细
export function getFundUtilizationRatio(id) {
  return request({
    url: '/asm/fund/utilization/ratio/' + id,
    method: 'get'
  })
}

// 根据条件查询资金运用比例监管表
export function getFundUtilizationRatioByCondition(accountingPeriod, assetSubSubCategory, accountName) {
  return request({
    url: '/asm/fund/utilization/ratio/condition',
    method: 'get',
    params: {
      accountingPeriod,
      assetSubSubCategory,
      accountName
    }
  })
}

// 新增资金运用比例监管表
export function addFundUtilizationRatio(data) {
  return request({
    url: '/asm/fund/utilization/ratio',
    method: 'post',
    data: data
  })
}

// 修改资金运用比例监管表
export function updateFundUtilizationRatio(data) {
  return request({
    url: '/asm/fund/utilization/ratio',
    method: 'put',
    data: data
  })
}

// 删除资金运用比例监管表
export function delFundUtilizationRatio(id) {
  return request({
    url: '/asm/fund/utilization/ratio/' + id,
    method: 'delete'
  })
}

// 导出资金运用比例监管表
export function exportFundUtilizationRatio(query) {
  return request({
    url: '/asm/fund/utilization/ratio/export',
    method: 'post',
    data: query
  })
}

// 导入资金运用比例监管表
export function importFundUtilizationRatio(data) {
  return request({
    url: '/asm/fund/utilization/ratio/importData',
    method: 'post',
    data: data
  })
}

// 下载资金运用比例监管表导入模板
export function importTemplate() {
  return request({
    url: '/asm/fund/utilization/ratio/importTemplate',
    method: 'post'
  })
}
