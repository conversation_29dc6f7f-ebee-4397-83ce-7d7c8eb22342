import request from '@/utils/request'

// 查询融资杠杆比例表列表
export function listFinancingLeverageRatio(query) {
  return request({
    url: '/asm/financing/leverage/ratio/list',
    method: 'get',
    params: query
  })
}

// 查询融资杠杆比例表详细
export function getFinancingLeverageRatio(id) {
  return request({
    url: '/asm/financing/leverage/ratio/' + id,
    method: 'get'
  })
}

// 根据条件查询融资杠杆比例表
export function getFinancingLeverageRatioByCondition(accountingPeriod, itemName) {
  return request({
    url: '/asm/financing/leverage/ratio/condition',
    method: 'get',
    params: {
      accountingPeriod,
      itemName
    }
  })
}

// 新增融资杠杆比例表
export function addFinancingLeverageRatio(data) {
  return request({
    url: '/asm/financing/leverage/ratio',
    method: 'post',
    data: data
  })
}

// 修改融资杠杆比例表
export function updateFinancingLeverageRatio(data) {
  return request({
    url: '/asm/financing/leverage/ratio',
    method: 'put',
    data: data
  })
}

// 删除融资杠杆比例表
export function delFinancingLeverageRatio(id) {
  return request({
    url: '/asm/financing/leverage/ratio/' + id,
    method: 'delete'
  })
}

// 导出融资杠杆比例表
export function exportFinancingLeverageRatio(query) {
  return request({
    url: '/asm/financing/leverage/ratio/export',
    method: 'post',
    data: query
  })
}

// 导入融资杠杆比例表
export function importFinancingLeverageRatio(data) {
  return request({
    url: '/asm/financing/leverage/ratio/importData',
    method: 'post',
    data: data
  })
}

// 下载融资杠杆比例表导入模板
export function importTemplate() {
  return request({
    url: '/asm/financing/leverage/ratio/importTemplate',
    method: 'post'
  })
}
