import request from '@/utils/request'

// 查询风险10日VaR值表列表
export function listRisk10DayVar(query) {
  return request({
    url: '/asm/risk/10day/var/list',
    method: 'get',
    params: query
  })
}

// 查询风险10日VaR值表详细
export function getRisk10DayVar(id) {
  return request({
    url: '/asm/risk/10day/var/' + id,
    method: 'get'
  })
}

// 根据条件查询风险10日VaR值表
export function getRisk10DayVarByCondition(accountingPeriod, domesticForeign, itemCategory, samplePeriod) {
  return request({
    url: '/asm/risk/10day/var/condition',
    method: 'get',
    params: {
      accountingPeriod,
      domesticForeign,
      itemCategory,
      samplePeriod
    }
  })
}

// 新增风险10日VaR值表
export function addRisk10DayVar(data) {
  return request({
    url: '/asm/risk/10day/var',
    method: 'post',
    data: data
  })
}

// 修改风险10日VaR值表
export function updateRisk10DayVar(data) {
  return request({
    url: '/asm/risk/10day/var',
    method: 'put',
    data: data
  })
}

// 删除风险10日VaR值表
export function delRisk10DayVar(id) {
  return request({
    url: '/asm/risk/10day/var/' + id,
    method: 'delete'
  })
}

// 导出风险10日VaR值表
export function exportRisk10DayVar(query) {
  return request({
    url: '/asm/risk/10day/var/export',
    method: 'post',
    data: query
  })
}

// 导入风险10日VaR值表
export function importRisk10DayVar(data) {
  return request({
    url: '/asm/risk/10day/var/importData',
    method: 'post',
    data: data
  })
}

// 下载风险10日VaR值表导入模板
export function importTemplate() {
  return request({
    url: '/asm/risk/10day/var/importTemplate',
    method: 'post'
  })
}
