/**
 * 前端文件名解析测试
 * 测试从HTTP响应头Content-Disposition中解析文件名的逻辑
 */

// 模拟Content-Disposition解析逻辑
function parseFilenameFromContentDisposition(contentDisposition) {
  if (!contentDisposition) {
    return null;
  }
  
  // 尝试解析新格式：filename*=UTF-8''xxx
  const filenameMatch = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
  if (filenameMatch) {
    return decodeURIComponent(filenameMatch[1]);
  }
  
  // 尝试解析旧格式：filename="xxx" 或 filename=xxx
  const simpleMatch = contentDisposition.match(/filename="?([^";]+)"?/);
  if (simpleMatch) {
    return decodeURIComponent(simpleMatch[1]);
  }
  
  return null;
}

// 测试用例
const testCases = [
  {
    name: "新格式 - TB0006月度折现曲线表含价差",
    contentDisposition: "attachment;filename*=UTF-8''%E6%9C%88%E5%BA%A6%E6%8A%98%E7%8E%B0%E6%9B%B2%E7%BA%BF%E8%A1%A8%E5%90%AB%E4%BB%B7%E5%B7%AE_1753837847067.xlsx",
    expected: "月度折现曲线表含价差_1753837847067.xlsx"
  },
  {
    name: "新格式 - TB0005月度折现曲线表不含价差",
    contentDisposition: "attachment;filename*=UTF-8''%E6%9C%88%E5%BA%A6%E6%8A%98%E7%8E%B0%E6%9B%B2%E7%BA%BF%E8%A1%A8%E4%B8%8D%E5%90%AB%E4%BB%B7%E5%B7%AE_1753837847067.xlsx",
    expected: "月度折现曲线表不含价差_1753837847067.xlsx"
  },
  {
    name: "新格式 - TB0003久期资产明细表",
    contentDisposition: "attachment;filename*=UTF-8''%E4%B9%85%E6%9C%9F%E8%B5%84%E4%BA%A7%E6%98%8E%E7%BB%86%E8%A1%A8_1753837847067.xlsx",
    expected: "久期资产明细表_1753837847067.xlsx"
  },
  {
    name: "旧格式 - 带引号",
    contentDisposition: 'attachment;filename="test_file.xlsx"',
    expected: "test_file.xlsx"
  },
  {
    name: "旧格式 - 不带引号",
    contentDisposition: "attachment;filename=test_file.xlsx",
    expected: "test_file.xlsx"
  },
  {
    name: "空Content-Disposition",
    contentDisposition: "",
    expected: null
  },
  {
    name: "null Content-Disposition",
    contentDisposition: null,
    expected: null
  },
  {
    name: "无效格式",
    contentDisposition: "attachment;invalid=test",
    expected: null
  }
];

// 运行测试
console.log("=== 前端文件名解析测试 ===");
let passCount = 0;
let totalCount = testCases.length;

testCases.forEach((testCase, index) => {
  const result = parseFilenameFromContentDisposition(testCase.contentDisposition);
  const passed = result === testCase.expected;
  
  console.log(`\n测试 ${index + 1}: ${testCase.name}`);
  console.log(`Content-Disposition: ${testCase.contentDisposition}`);
  console.log(`期望结果: ${testCase.expected}`);
  console.log(`实际结果: ${result}`);
  console.log(`测试结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
  
  if (passed) {
    passCount++;
  }
});

console.log(`\n=== 测试总结 ===`);
console.log(`总测试数: ${totalCount}`);
console.log(`通过数: ${passCount}`);
console.log(`失败数: ${totalCount - passCount}`);
console.log(`通过率: ${((passCount / totalCount) * 100).toFixed(2)}%`);

// 额外测试：验证URL编码解码
console.log(`\n=== URL编码解码测试 ===`);
const chineseTexts = [
  "月度折现曲线表含价差_1753837847067.xlsx",
  "月度折现曲线表不含价差_1753837847067.xlsx", 
  "久期资产明细表_1753837847067.xlsx"
];

chineseTexts.forEach((text, index) => {
  const encoded = encodeURIComponent(text);
  const decoded = decodeURIComponent(encoded);
  const matches = text === decoded;
  
  console.log(`\n编码测试 ${index + 1}:`);
  console.log(`原始文本: ${text}`);
  console.log(`编码后: ${encoded}`);
  console.log(`解码后: ${decoded}`);
  console.log(`编解码一致: ${matches ? '✅ 是' : '❌ 否'}`);
});

// 模拟实际的Content-Disposition头生成
console.log(`\n=== 模拟后端Content-Disposition头生成 ===`);
chineseTexts.forEach((filename, index) => {
  // 模拟后端ValueSetExcelExporter的逻辑
  const encodedFilename = encodeURIComponent(filename).replace(/\+/g, '%20');
  const contentDisposition = `attachment;filename*=UTF-8''${encodedFilename}`;
  
  console.log(`\n文件 ${index + 1}:`);
  console.log(`原始文件名: ${filename}`);
  console.log(`生成的Content-Disposition: ${contentDisposition}`);
  
  // 验证前端能否正确解析
  const parsedFilename = parseFilenameFromContentDisposition(contentDisposition);
  const parseSuccess = parsedFilename === filename;
  
  console.log(`前端解析结果: ${parsedFilename}`);
  console.log(`解析成功: ${parseSuccess ? '✅ 是' : '❌ 否'}`);
});

console.log(`\n=== 测试完成 ===`);
