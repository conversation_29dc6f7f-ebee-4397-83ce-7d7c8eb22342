-- 检查ADUR模块字典数据是否正确配置
-- 请在数据库中执行以下查询来验证字典数据

-- 1. 检查字典类型是否存在
SELECT dict_name, dict_type, status 
FROM sys_dict_type 
WHERE dict_type IN (
    'adur_duration_type',
    'adur_basis_point_type', 
    'adur_date_type',
    'adur_spread_type',
    'adur_account_name'
)
ORDER BY dict_type;

-- 2. 检查久期类型字典数据
SELECT dict_sort, dict_label, dict_value, status
FROM sys_dict_data 
WHERE dict_type = 'adur_duration_type'
ORDER BY dict_sort;

-- 3. 检查基点类型字典数据
SELECT dict_sort, dict_label, dict_value, status
FROM sys_dict_data 
WHERE dict_type = 'adur_basis_point_type'
ORDER BY dict_sort;

-- 4. 检查日期类型字典数据
SELECT dict_sort, dict_label, dict_value, status
FROM sys_dict_data 
WHERE dict_type = 'adur_date_type'
ORDER BY dict_sort;

-- 5. 检查价差类型字典数据
SELECT dict_sort, dict_label, dict_value, status
FROM sys_dict_data 
WHERE dict_type = 'adur_spread_type'
ORDER BY dict_sort;

-- 6. 检查账户名称字典数据
SELECT dict_sort, dict_label, dict_value, status
FROM sys_dict_data 
WHERE dict_type = 'adur_account_name'
ORDER BY dict_sort;

-- 7. 检查月度折现因子表中的实际数据值
SELECT DISTINCT 
    duration_type,
    basis_point_type,
    date_type,
    spread_type,
    account_name
FROM t_adur_monthly_discount_factor
LIMIT 10;

-- 如果字典数据不存在，请执行以下插入语句：
-- 注意：请先确认数据库中没有这些数据再执行

/*
-- 插入字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,remark) VALUES
('久期类型','adur_duration_type','0','admin',NOW(),'资产久期管理久期类型'),
('基点类型','adur_basis_point_type','0','admin',NOW(),'资产久期管理基点类型'),
('日期类型','adur_date_type','0','admin',NOW(),'资产久期管理日期类型'),
('价差类型','adur_spread_type','0','admin',NOW(),'资产久期管理价差类型'),
('账户名称','adur_account_name','0','admin',NOW(),'资产久期管理账户名称');

-- 插入久期类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,status,create_by,create_time) VALUES
(1,'修正久期','01','adur_duration_type','0','admin',NOW()),
(2,'有效久期','02','adur_duration_type','0','admin',NOW()),
(3,'利差久期','03','adur_duration_type','0','admin',NOW()),
(4,'关键久期','04','adur_duration_type','0','admin',NOW());

-- 插入基点类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,status,create_by,create_time) VALUES
(1,'0bp','01','adur_basis_point_type','0','admin',NOW()),
(2,'+50bp','02','adur_basis_point_type','0','admin',NOW()),
(3,'-50bp','03','adur_basis_point_type','0','admin',NOW());

-- 插入日期类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,status,create_by,create_time) VALUES
(1,'发行时点','01','adur_date_type','0','admin',NOW()),
(2,'评估时点','02','adur_date_type','0','admin',NOW());

-- 插入价差类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,status,create_by,create_time) VALUES
(1,'发行时点价差','01','adur_spread_type','0','admin',NOW()),
(2,'评估时点价差','02','adur_spread_type','0','admin',NOW());

-- 插入账户名称字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,status,create_by,create_time) VALUES
(1,'传统账户','01','adur_account_name','0','admin',NOW()),
(2,'分红账户','02','adur_account_name','0','admin',NOW()),
(3,'万能账户','03','adur_account_name','0','admin',NOW()),
(4,'普通账户','04','adur_account_name','0','admin',NOW());
*/
