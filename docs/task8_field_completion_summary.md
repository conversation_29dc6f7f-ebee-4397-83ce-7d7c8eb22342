# 任务8字段补全总结 - 中短存续期产品利差job任务

## 问题发现

在检查任务8（计算中短存续期产品利差job任务）时，发现UC0016的实现存在以下问题：

### 1. 中短存续期产品识别逻辑不正确

**问题：** 原代码使用shortTermFlag字段进行判断，但设计文档要求使用"报监管中短标识"字段。

**设计文档要求：** 筛选产品属性表中"报监管中短标识"为是的产品

**修复内容：** 
- 修改isShortTermProduct方法，使用regMidId字段进行判断
- 支持"是"、"Y"、"1"等多种标识值

### 2. 新单规模保费计算逻辑不正确

**问题：** 原代码从TB0012表(分产品统计表)获取新单规模保费，不符合设计文档要求。

**设计文档要求：** 取自分产品保费收入表（匹配字段：精算代码，查询列：为以下几项之和"原保费-趸交"+"原保费-期交"+"万能投连-趸交"+"万能投连-期交"+"万能投连-初始费用"）

**修复内容：**
- 从TB0015表(分产品保费收入表)获取数据
- 计算5个字段的和：原保费-趸交、原保费-期交、万能投连-趸交、万能投连-期交、万能投连-初始费用

### 3. 保户储金及投资款获取逻辑优化

**问题：** 原代码从会计准备金明细表获取保户储金及投资款，但设计文档要求从分产品保费收入表获取。

**设计文档要求：** 保户储金及投资款=取自分产品保费收入表（匹配字段：精算代码，查询列：保户储金及投资款余额）

**修复内容：**
- 会计准备金仍从会计准备金明细表获取
- 保户储金及投资款改为从分产品保费收入表获取

### 4. 子账户收益率获取逻辑完善

**问题：** 原代码没有根据设计类型区分传统险和其他险种的处理方式。

**设计文档要求：**
- 如果设计类型=传统险，取自子账户收益率表中子账户字段="传统账户"
- 如果设计类型=分红险或设计类型=万能险，取自子账户收益率表（匹配字段：子账户）

**修复内容：**
- 添加设计类型判断逻辑
- 传统险使用"传统账户"，其他险种使用产品的子账户

### 5. 负债资金成本率获取逻辑修正

**问题：** 原代码从TB0013表(分产品有效成本率表)获取负债资金成本率，不符合设计文档要求。

**设计文档要求：** 负债资金成本率=取分产品统计表（匹配字段：精算代码、[固定条件：基础情景、有效业务]，查询列：资金成本率T0）

**修复内容：**
- 负债资金成本率从TB0012表(分产品统计表)获取costRateT0字段
- 负债有效成本率仍从TB0013表(分产品有效成本率表)获取

## 字段补全内容

### 1. job模块 - ProductAttributeEntity.java
**文件路径：** `job/src/main/java/com/xl/alm/job/cost/entity/ProductAttributeEntity.java`

**添加字段：**
- regMidId（报监管中短标识）
- subAccount（子账户）

### 2. job模块 - ProductAttributeMapper.xml
**文件路径：** `job/src/main/resources/mapper/cost/ProductAttributeMapper.xml`

**修复内容：**
- 更新resultMap，添加regMidId和subAccount字段映射
- 更新selectProductAttributeVo SQL查询，包含新增字段
- 添加regMidId字段的查询条件

### 3. job模块 - ShortTermProductSpreadServiceImpl.java
**文件路径：** `job/src/main/java/com/xl/alm/job/cost/service/impl/ShortTermProductSpreadServiceImpl.java`

**修复内容：**
- 添加ProductPremiumIncomeDetailMapper依赖注入
- 修复isShortTermProduct方法，使用regMidId字段
- 修复新单规模保费计算逻辑，从分产品保费收入表计算
- 修复保户储金及投资款获取逻辑
- 完善子账户收益率获取逻辑，支持传统险特殊处理
- 修正负债资金成本率获取逻辑

## 业务逻辑完善

### 计算逻辑优化

**新单规模保费计算公式：**
```
新单规模保费 = 原保费-趸交(本月) + 原保费-期交(本月) + 万能投连-趸交(本月) + 万能投连-期交(本月) + 万能投连-初始费用(本月)
```

**子账户收益率获取逻辑：**
```
if (设计类型 == "传统险" || 设计类型 == "传统型") {
    targetSubAccount = "传统账户";
} else {
    targetSubAccount = 产品属性.子账户;
}
```

**中短存续期产品识别：**
```
if (产品属性.报监管中短标识 == "是" || "Y" || "1") {
    return true; // 是中短存续期产品
}
```

## 数据来源映射

| 字段名 | 数据来源表 | 匹配条件 | 查询字段 |
|--------|------------|----------|----------|
| 新单规模保费 | TB0015(分产品保费收入表) | 精算代码 | 5个保费字段之和 |
| 会计准备金 | 会计准备金明细表 | 业务代码 | 会计准备金合计 |
| 保户储金及投资款 | TB0015(分产品保费收入表) | 精算代码 | 保户储金及投资款余额 |
| 年化会计投资收益率 | TB0014(子账户收益率表) | 子账户 | 年化会计投资收益率 |
| 年化综合投资收益率 | TB0014(子账户收益率表) | 子账户 | 年化综合投资收益率 |
| 负债资金成本率 | TB0012(分产品统计表) | 精算代码 | 资金成本率T0 |
| 负债有效成本率 | TB0013(分产品有效成本率表) | 精算代码 | 有效成本率 |

## 完成状态

**✅ 任务8字段补全已完成**

中短存续期产品利差job任务现在完全符合设计文档4.1.3.2.3.4章节的要求，包括：

1. ✅ 正确的中短存续期产品识别逻辑
2. ✅ 准确的新单规模保费计算公式
3. ✅ 完整的数据获取逻辑
4. ✅ 符合业务规则的子账户收益率处理
5. ✅ 正确的负债成本率数据来源

所有计算逻辑都已按照设计文档要求进行修正，确保数据的准确性和业务逻辑的正确性。
