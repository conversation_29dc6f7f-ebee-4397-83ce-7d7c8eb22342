# TB0013保费收入明细表Excel中文表头配置

## 修改说明

已为 `PremiumIncomeDetailDTO` 类添加了完整的Excel导出中文表头注解，支持导出模板和数据时显示中文表头。

## 添加的注解

### 1. 导入的注解类
```java
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
```

### 2. 字段注解配置

#### 基础信息字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| id | - | @ExcelIgnore (不导出) |
| accountingPeriod | 统计期间 | @Excel(name = "统计期间") @ExcelProperty("统计期间") |
| companyCode | 公司代码 | @Excel(name = "公司代码") @ExcelProperty("公司代码") |
| companyName | 公司名称 | @Excel(name = "公司名称") @ExcelProperty("公司名称") |
| productCode | 产品代码 | @Excel(name = "产品代码") @ExcelProperty("产品代码") |
| productName | 产品名称 | @Excel(name = "产品名称") @ExcelProperty("产品名称") |
| channelCode | 渠道代码 | @Excel(name = "渠道代码") @ExcelProperty("渠道代码") |
| channelName | 渠道名称 | @Excel(name = "渠道名称") @ExcelProperty("渠道名称") |
| accountCode | 账户代码 | @Excel(name = "账户代码") @ExcelProperty("账户代码") |
| accountName | 账户名称 | @Excel(name = "账户名称") @ExcelProperty("账户名称") |

#### 原保费字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentSinglePremium | 原保费-趸交 | @Excel(name = "原保费-趸交", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentRegularPremium | 原保费-期交 | @Excel(name = "原保费-期交", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentRenewalPremium | 原保费-续期 | @Excel(name = "原保费-续期", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentTotalPremium | 原保费-合计 | @Excel(name = "原保费-合计", cellType = Excel.ColumnType.NUMERIC, scale = 2) |

#### 万能投连字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentUlSingle | 万能投连-趸交 | @Excel(name = "万能投连-趸交", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlRegular | 万能投连-期交 | @Excel(name = "万能投连-期交", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlRenewal | 万能投连-续期 | @Excel(name = "万能投连-续期", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlInitialFee | 万能投连-初始费用 | @Excel(name = "万能投连-初始费用", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlTotal | 万能投连-合计 | @Excel(name = "万能投连-合计", cellType = Excel.ColumnType.NUMERIC, scale = 2) |

#### 其他收入字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentScalePremium | 规模保费合计 | @Excel(name = "规模保费合计", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentInvestmentBalance | 保户储金及投资款余额 | @Excel(name = "保户储金及投资款余额", cellType = Excel.ColumnType.NUMERIC, scale = 2) |

#### 支出字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentSurrender | 退保金 | @Excel(name = "退保金", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlWithdraw | 万能投连领取 | @Excel(name = "万能投连领取", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentClaim | 赔款支出 | @Excel(name = "赔款支出", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentMedical | 死伤医疗给付 | @Excel(name = "死伤医疗给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentMaturity | 满期给付 | @Excel(name = "满期给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentAnnuity | 年金给付 | @Excel(name = "年金给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |

#### 万能投连支出字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentUlClaim | 万能投连-赔款支出 | @Excel(name = "万能投连-赔款支出", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlMedical | 万能投连-死伤医疗给付 | @Excel(name = "万能投连-死伤医疗给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlMaturity | 万能投连-满期给付 | @Excel(name = "万能投连-满期给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| currentUlAnnuity | 万能投连-年金给付 | @Excel(name = "万能投连-年金给付", cellType = Excel.ColumnType.NUMERIC, scale = 2) |

#### 汇总字段
| 字段名 | 中文表头 | 注解配置 |
|--------|----------|----------|
| currentTotalClaim | 赔付支出合计 | @Excel(name = "赔付支出合计", cellType = Excel.ColumnType.NUMERIC, scale = 2) |
| remark | 备注 | @Excel(name = "备注") @ExcelProperty("备注") |
| isDel | - | @ExcelIgnore (不导出) |

## 注解说明

### @Excel 注解
- `name`: 设置Excel表头的中文名称
- `cellType = Excel.ColumnType.NUMERIC`: 指定数值类型
- `scale = 2`: 设置小数位数为2位

### @ExcelProperty 注解
- 用于EasyExcel框架的表头设置
- 与@Excel注解配合使用，确保兼容性

### @ExcelIgnore 注解
- 标记不需要导出到Excel的字段
- 如：id、isDel等内部字段

## 效果

配置完成后，Excel导出模板和数据将显示中文表头：
- 统计期间、公司代码、公司名称、产品代码、产品名称
- 渠道代码、渠道名称、账户代码、账户名称
- 原保费-趸交、原保费-期交、原保费-续期、原保费-合计
- 万能投连-趸交、万能投连-期交、万能投连-续期、万能投连-初始费用、万能投连-合计
- 规模保费合计、保户储金及投资款余额
- 退保金、万能投连领取、赔款支出、死伤医疗给付、满期给付、年金给付
- 万能投连-赔款支出、万能投连-死伤医疗给付、万能投连-满期给付、万能投连-年金给付
- 赔付支出合计、备注

所有金额字段都设置为数值类型，保留2位小数，便于Excel中进行计算和格式化显示。
