# TB0003久期资产明细表现金流导出功能增强

## 概述

本文档描述了TB0003久期资产明细表Excel导出功能的增强，实现了现金流值集JSON字段的平铺展开导出。

## 功能特性

### 1. 现金流字段展开
- **发行时点现金流值集** (`issueCashflowSet`) - JSON字段展开为多列
- **评估时点现金流值集** (`evalCashflowSet`) - JSON字段展开为多列

### 2. 表头格式
- **第一行表头**: 字段名-序号 (如: "发行时点现金流值集-0", "发行时点现金流值集-1")
- **第二行表头**: 对应的日期 (如: "2024-07-31", "2024-08-31")

### 3. 数据格式
- **值**: 从JSON中的"值"字段提取的现金流金额
- **排序**: 按期限序号自然排序 (0, 1, 2, ..., 600)

## JSON数据格式

### 现金流JSON结构
```json
{
  "0": {"日期": "2024-07-31", "值": "0"},
  "1": {"日期": "2024-08-31", "值": "0"},
  "6": {"日期": "2025-01-31", "值": "17500"},
  "12": {"日期": "2025-07-31", "值": "35000"},
  "24": {"日期": "2026-07-31", "值": "1035000"},
  "600": {"日期": "2074-07-31", "值": "0"}
}
```

### 字段说明
- **外层键**: 期限序号 (0-600)
- **日期**: 现金流发生日期
- **值**: 现金流金额

## 实现细节

### 1. 控制器修改
文件: `app/src/main/java/com/xl/alm/app/controller/DurationAssetDetailController.java`

```java
@PostMapping("/export")
public void export(HttpServletResponse response, DurationAssetDetailQuery durationAssetDetailQuery) {
    List<DurationAssetDetailDTO> list = durationAssetDetailService.selectDurationAssetDetailDtoList(durationAssetDetailQuery);

    // 转换字典值为中文标签用于导出
    convertDictValueToLabel(list);

    // 使用ValueSetExcelExporter导出，处理现金流值集字段
    // 将发行时点现金流值集和评估时点现金流值集JSON字段展开为多列，key和date作为表头，value作为值
    ValueSetExcelExporter.exportExcel(list, "久期资产明细数据", response, "issueCashflowSet", "evalCashflowSet");
}
```

### 2. ValueSetExcelExporter增强
文件: `app/src/main/java/com/xl/alm/app/util/ValueSetExcelExporter.java`

#### 主要增强点:
1. **多字段名支持**: 支持同时处理多个JSON值集字段
2. **中文字段名支持**: 支持解析"日期"和"值"字段名
3. **自然排序**: 按期限序号自然排序，适合现金流数据

#### 关键方法修改:
```java
// 支持多种可能的日期字段名：date 和 日期
if (itemData.containsKey("date")) {
    date = itemData.getString("date");
} else if (itemData.containsKey("日期")) {
    date = itemData.getString("日期");
}

// 支持多种可能的值字段名：value、值、val
if (itemData.containsKey("value")) {
    value = itemData.getBigDecimal("value");
} else if (itemData.containsKey("值")) {
    value = itemData.getBigDecimal("值");
} else if (itemData.containsKey("val")) {
    value = itemData.getBigDecimal("val");
}
```

## 导出效果

### Excel表头示例
```
| 账期 | 资产编号 | 账户名称 | ... | 发行时点现金流值集-0 | 发行时点现金流值集-1 | ... | 评估时点现金流值集-0 | 评估时点现金流值集-1 | ... |
|------|----------|----------|-----|---------------------|---------------------|-----|---------------------|---------------------|-----|
|      |          |          |     | 2024-01-31          | 2024-02-29          | ... | 2024-07-31          | 2024-08-31          | ... |
| 202407| BOND001 | 账户A    | ... | 0                   | 0                   | ... | 0                   | 0                   | ... |
```

### 特点
1. **双行表头**: 第一行显示字段名和序号，第二行显示对应日期
2. **数据展开**: JSON中的每个期限都成为独立的列
3. **字典转换**: 普通字段的字典值仍然转换为中文标签
4. **完整性**: 包含所有普通字段和展开的现金流字段

## 测试验证

### 测试文件
`app/src/test/java/com/xl/alm/app/controller/DurationAssetDetailControllerExportTest.java`

### 测试用例
1. **现金流值集导出功能测试**: 验证两个现金流字段同时导出
2. **现金流JSON格式解析测试**: 验证JSON格式正确性
3. **空数据导出测试**: 验证空数据处理

### 运行测试
```bash
cd app
mvn test -Dtest=DurationAssetDetailControllerExportTest
```

## 使用方法

### 1. 前端调用
访问TB0003页面，点击导出按钮

### 2. API调用
```
POST /adur/duration/asset/detail/export
```

### 3. 参数
- 支持所有现有的查询参数
- 导出文件名: "久期资产明细数据.xlsx"

## 注意事项

1. **性能考虑**: 现金流数据展开后列数较多，大量数据导出时需要注意性能
2. **Excel限制**: Excel单个工作表最多支持16384列，需要注意现金流期限数量
3. **内存使用**: 大量数据展开时会占用较多内存
4. **字典数据**: 确保相关字典数据已正确配置

## 相关文件

### 修改的文件
1. `app/src/main/java/com/xl/alm/app/controller/DurationAssetDetailController.java`
2. `app/src/main/java/com/xl/alm/app/util/ValueSetExcelExporter.java`

### 新增的文件
1. `app/src/test/java/com/xl/alm/app/controller/DurationAssetDetailControllerExportTest.java`
2. `docs/tb0003_cashflow_export_enhancement.md`

### 依赖的文件
1. `app/src/main/java/com/xl/alm/app/dto/DurationAssetDetailDTO.java`
2. `app/src/main/java/com/xl/alm/app/service/DurationAssetDetailService.java`
3. `app/src/main/java/com/xl/alm/app/util/DictConvertUtil.java`

## 完成状态

✅ **已完成** - TB0003久期资产明细表现金流导出功能增强已实现，支持发行时点现金流值集和评估时点现金流值集的JSON平铺展开导出。
