# TB0013保费收入明细表字段补全总结

## 修改说明

已为TB0013保费收入明细表补全了所有业务字段，包括查询页面和新增/编辑对话框。

## 完成的修改

### 1. 查询表单字段补全

**原有字段**：
- 统计期间、公司代码、公司名称、产品代码、产品名称

**新增字段**：
- 渠道代码、渠道名称、账户代码、账户名称

**总计**：9个查询字段，覆盖所有基础维度信息

### 2. 表格列字段补全

**原有字段**：
- ID、统计期间、公司信息、产品信息、部分保费字段

**新增字段**：
- 渠道代码、渠道名称、账户代码、账户名称
- 万能投连-趸交、万能投连-期交、万能投连-续期、万能投连-初始费用、万能投连-合计
- 保户储金及投资款余额
- 退保金、万能投连领取、赔款支出、死伤医疗给付、满期给付、年金给付
- 万能投连-赔款支出、万能投连-死伤医疗给付、万能投连-满期给付、万能投连-年金给付
- 赔付支出合计

**总计**：32个表格列，包含所有业务字段

### 3. 新增/编辑对话框字段补全

#### 对话框布局优化
- **宽度调整**：从500px扩展到1200px，适应更多字段
- **分组显示**：使用el-divider分组，提升用户体验
- **响应式布局**：使用el-row和el-col实现响应式布局

#### 字段分组

**基础信息组**（9个字段）：
- 统计期间、公司代码、公司名称
- 产品代码、产品名称、渠道代码
- 渠道名称、账户代码、账户名称

**原保费信息组**（4个字段）：
- 原保费-趸交、原保费-期交、原保费-续期、原保费-合计

**万能投连信息组**（8个字段）：
- 万能投连-趸交、万能投连-期交、万能投连-续期、万能投连-初始费用
- 万能投连-合计、规模保费合计、保户储金及投资款余额

**支出信息组**（11个字段）：
- 退保金、万能投连领取、赔款支出、死伤医疗给付
- 满期给付、年金给付、万能投连-赔款支出、万能投连-死伤医疗给付
- 万能投连-满期给付、万能投连-年金给付、赔付支出合计

**备注信息组**（1个字段）：
- 备注

**总计**：33个业务字段（包括备注）

### 4. 字段类型优化

#### 金额字段
- 使用`el-input-number`组件
- 设置`precision="2"`保留2位小数
- 设置`step="0.01"`步长
- 设置`style="width: 100%"`全宽显示

#### 文本字段
- 基础信息使用`el-input`组件
- 备注使用`el-input type="textarea"`多行文本

#### 表格列宽
- 为所有表格列设置了合适的宽度
- 金额字段：100-160px
- 文本字段：80-150px
- 确保表格显示美观

### 5. 数据结构完整性

#### queryParams对象
包含所有查询字段：
```javascript
queryParams: {
  pageNum: 1,
  pageSize: 10,
  accountingPeriod: null,
  companyCode: null,
  companyName: null,
  productCode: null,
  productName: null,
  channelCode: null,
  channelName: null,
  accountCode: null,
  accountName: null,
}
```

#### form对象
包含所有业务字段：
```javascript
form: {
  id: null,
  // 基础信息
  accountingPeriod: null,
  companyCode: null,
  companyName: null,
  productCode: null,
  productName: null,
  channelCode: null,
  channelName: null,
  accountCode: null,
  accountName: null,
  // 原保费
  currentSinglePremium: null,
  currentRegularPremium: null,
  currentRenewalPremium: null,
  currentTotalPremium: null,
  // 万能投连
  currentUlSingle: null,
  currentUlRegular: null,
  currentUlRenewal: null,
  currentUlInitialFee: null,
  currentUlTotal: null,
  currentScalePremium: null,
  currentInvestmentBalance: null,
  // 支出
  currentSurrender: null,
  currentUlWithdraw: null,
  currentClaim: null,
  currentMedical: null,
  currentMaturity: null,
  currentAnnuity: null,
  currentUlClaim: null,
  currentUlMedical: null,
  currentUlMaturity: null,
  currentUlAnnuity: null,
  currentTotalClaim: null,
  // 备注
  remark: null
}
```

### 6. 验证规则
保持原有的必填字段验证：
- 统计期间、公司代码、公司名称
- 产品代码、产品名称
- 渠道代码、渠道名称
- 账户代码、账户名称

## 业务字段统计

### TB0013表完整字段清单（32个业务字段）

#### 基础维度字段（9个）
1. accounting_period - 统计期间
2. company_code - 公司代码
3. company_name - 公司名称
4. product_code - 产品代码
5. product_name - 产品名称
6. channel_code - 渠道代码
7. channel_name - 渠道名称
8. account_code - 账户代码
9. account_name - 账户名称

#### 原保费字段（4个）
10. current_single_premium - 原保费-趸交
11. current_regular_premium - 原保费-期交
12. current_renewal_premium - 原保费-续期
13. current_total_premium - 原保费-合计

#### 万能投连字段（5个）
14. current_ul_single - 万能投连-趸交
15. current_ul_regular - 万能投连-期交
16. current_ul_renewal - 万能投连-续期
17. current_ul_initial_fee - 万能投连-初始费用
18. current_ul_total - 万能投连-合计

#### 其他收入字段（2个）
19. current_scale_premium - 规模保费合计
20. current_investment_balance - 保户储金及投资款余额

#### 支出字段（11个）
21. current_surrender - 退保金
22. current_ul_withdraw - 万能投连领取
23. current_claim - 赔款支出
24. current_medical - 死伤医疗给付
25. current_maturity - 满期给付
26. current_annuity - 年金给付
27. current_ul_claim - 万能投连-赔款支出
28. current_ul_medical - 万能投连-死伤医疗给付
29. current_ul_maturity - 万能投连-满期给付
30. current_ul_annuity - 万能投连-年金给付
31. current_total_claim - 赔付支出合计

#### 备注字段（1个）
32. remark - 备注

## 完成状态

✅ **查询页面**：已补全所有基础维度字段的查询条件  
✅ **表格显示**：已补全所有32个业务字段的表格列  
✅ **新增对话框**：已补全所有32个业务字段的输入表单  
✅ **编辑对话框**：已补全所有32个业务字段的编辑表单  
✅ **数据结构**：queryParams和form对象包含所有字段  
✅ **用户体验**：分组显示、响应式布局、合适的组件类型  

**字段补全工作已完成**！TB0013保费收入明细表现在支持所有32个业务字段的完整CRUD操作。
