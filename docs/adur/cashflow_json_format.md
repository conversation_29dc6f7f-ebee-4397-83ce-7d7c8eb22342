# ADUR模块现金流JSON格式规范

## 概述

ADUR模块中的 `issue_cashflow_set`（发行时点现金流值集）和 `eval_cashflow_set`（评估时点现金流值集）字段使用JSON格式存储现金流数据。

## JSON格式规范

### 基本格式
```json
{
  "0": {"日期": "2023/06/30", "值": "0"},
  "1": {"日期": "2023/07/31", "值": "0"},
  "2": {"日期": "2023/08/31", "值": "0"},
  "12": {"日期": "2024/06/30", "值": "5000"},
  "24": {"日期": "2025/06/30", "值": "105000"},
  "600": {"日期": "2073/06/30", "值": "0"}
}
```

**重要特性**：JSON中的键按期限数字顺序排序（0, 1, 2, ..., 600），确保数据库存储的一致性和可读性。

### 字段说明

#### 外层键（期限索引）
- **格式**: 字符串类型的数字，从 "0" 到 "600"
- **含义**: 表示从基准日开始的月份数
- **范围**: 0-600，总共601个期限
- **示例**: "0"表示基准日当月，"12"表示12个月后，"600"表示50年后

#### 内层对象
每个期限包含两个字段：

##### 日期字段
- **键名**: "日期"
- **格式**: "YYYY/MM/DD" 或 "YYYY-MM-DD"
- **含义**: 该期限对应的具体日期
- **示例**: "2023/06/30", "2024-06-30"

##### 值字段
- **键名**: "值"
- **格式**: 字符串类型的数字
- **含义**: 该期限的现金流金额
- **单位**: 通常为元（人民币）
- **示例**: "0", "5000", "105000"

## 期限说明

### 期限0
- **含义**: 基准日当天
- **现金流**: 通常为0
- **用途**: 作为计算起点

### 期限1-11
- **含义**: 基准日后1-11个月
- **现金流**: 根据付息方式决定，通常为0

### 期限12
- **含义**: 基准日后12个月（1年）
- **现金流**: 按年付息的债券在此期限有利息现金流

### 期限24
- **含义**: 基准日后24个月（2年）
- **现金流**: 2年期债券的到期现金流

### 期限600
- **含义**: 基准日后600个月（50年）
- **现金流**: 超长期债券的最远期限

## JSON排序规则

### 排序方式
- **外层键排序**: 按期限数字大小排序（0, 1, 2, ..., 600）
- **内层字段排序**: 固定顺序为"日期"在前，"值"在后

### 排序示例
```json
{
  "0": {"日期": "2023/06/30", "值": "0"},
  "1": {"日期": "2023/07/31", "值": "0"},
  "2": {"日期": "2023/08/31", "值": "0"},
  "10": {"日期": "2024/04/30", "值": "0"},
  "12": {"日期": "2024/06/30", "值": "5000"},
  "100": {"日期": "2031/10/30", "值": "0"},
  "600": {"日期": "2073/06/30", "值": "0"}
}
```

### 排序的重要性
1. **数据库存储一致性**: 确保相同数据生成的JSON字符串完全一致
2. **便于调试和查看**: 按期限顺序排列便于人工检查
3. **减少存储差异**: 避免因键顺序不同导致的不必要的数据更新
4. **提高查询效率**: 有序的JSON在某些场景下查询更高效

## 使用场景

### 发行时点现金流值集 (issue_cashflow_set)
- **基准日**: 债券的调整起息日
- **用途**: 计算发行时点价差
- **计算**: 基于债券发行时的参数计算未来现金流

### 评估时点现金流值集 (eval_cashflow_set)
- **基准日**: 当前评估时点（通常为账期月末）
- **用途**: 计算评估时点价差、久期指标
- **计算**: 基于当前时点计算剩余现金流

## 代码示例

### 解析现金流JSON
```java
// 解析现金流金额
Map<Integer, BigDecimal> amounts = CashFlowUtil.parseCashflowAmounts(jsonString);

// 解析现金流日期
Map<Integer, String> dates = CashFlowUtil.parseCashflowDates(jsonString);

// 解析完整现金流信息
Map<Integer, CashFlowUtil.CashFlowItem> details = CashFlowUtil.parseCashflowDetails(jsonString);
```

### 生成现金流JSON
```java
// 准备现金流数据
Map<Integer, CashFlowUtil.CashFlowItem> cashflowData = new HashMap<>();
cashflowData.put(0, new CashFlowUtil.CashFlowItem("2023/06/30", BigDecimal.ZERO));
cashflowData.put(12, new CashFlowUtil.CashFlowItem("2024/06/30", new BigDecimal("5000")));

// 生成JSON
String json = CashFlowUtil.createCashflowJson(cashflowData);
```

### 债券现金流计算
```java
// 计算债券现金流
BondCashFlowCalculator.CashFlowResult result = BondCashFlowCalculator.calculateCashFlow(
    assetNumber, holdingFaceValue, couponRate, paymentMethodCode,
    adjustedValueDate, adjustedMaturityDate, calculationBaseDate
);

// 转换为JSON格式
String json = BondCashFlowCalculator.toJson(result);
```

## 注意事项

1. **期限完整性**: 必须包含0-600所有期限，即使现金流为0
2. **期限排序**: JSON中的键按期限数字顺序排序（0, 1, 2, ..., 600），确保数据库存储一致性
3. **日期格式**: 建议使用统一的日期格式
4. **数值精度**: 现金流金额建议保持适当的小数位数
5. **JSON有效性**: 确保生成的JSON格式正确，可被标准JSON解析器解析
6. **性能考虑**: 601个期限的JSON数据量较大，注意存储和传输性能

## 相关工具类

- `CashFlowUtil`: 现金流JSON解析和生成工具
- `BondCashFlowCalculator`: 债券现金流计算工具
- `TermDataUtil`: 期限数据处理工具（用于其他期限相关JSON）

## 测试验证

项目中提供了完整的测试用例：
- `CashFlowUtilTest`: 测试现金流工具类
- `BondCashFlowCalculatorTest`: 测试债券现金流计算

运行测试确保JSON格式的正确性和完整性。
