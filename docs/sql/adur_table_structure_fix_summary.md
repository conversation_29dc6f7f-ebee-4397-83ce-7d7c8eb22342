# ADUR表结构修复总结

## 问题描述

用户报告了一个SQL错误：
```
Unknown column 'key_term' in 'field list'
```

错误发生在 `AdurKeyDurationParameterMapper.xml` 中的查询语句，提示 `key_term` 字段不存在。

## 根本原因

经过分析发现，`t_adur_key_duration_parameter` 表存在两套不同的表结构定义：

### 错误的表结构（job模块使用）
```sql
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `key_term` varchar(10) NOT NULL COMMENT '关键期限',
  `parameter_val_set` mediumtext NOT NULL COMMENT '参数值集',
  -- 其他字段...
);
```

### 正确的表结构（app模块使用）
```sql
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `key_duration` varchar(20) NOT NULL COMMENT '关键久期',
  `parameter_val_set` mediumtext COMMENT 'JSON格式参数值集',
  -- 其他字段...
);
```

## 修复内容

### 1. Job模块实体类修复

**文件**: `job/src/main/java/com/xl/alm/job/adur/entity/AdurKeyDurationParameterEntity.java`

**修改**:
```java
// 修改前
@TableField("key_term")
private String keyTerm;

// 修改后
@TableField("key_duration")
private String keyDuration;
```

### 2. Job模块Mapper XML修复

**文件**: `job/src/main/resources/mapper/adur/AdurKeyDurationParameterMapper.xml`

**修改**:
- ResultMap中的字段映射：`key_term` → `key_duration`
- SQL查询语句中的字段名：`key_term` → `key_duration`
- ORDER BY子句中的字段名：`key_term` → `key_duration`

### 3. Job模块Mapper接口修复

**文件**: `job/src/main/java/com/xl/alm/job/adur/mapper/AdurKeyDurationParameterMapper.java`

**修改**:
```java
// 修改前
AdurKeyDurationParameterEntity selectByAccountPeriodAndKeyTerm(
    @Param("accountPeriod") String accountPeriod,
    @Param("keyTerm") String keyTerm);

// 修改后
AdurKeyDurationParameterEntity selectByAccountPeriodAndKeyDuration(
    @Param("accountPeriod") String accountPeriod,
    @Param("keyDuration") String keyDuration);
```

### 4. 服务实现类修复

**文件**: `job/src/main/java/com/xl/alm/job/adur/service/impl/AdurKeyDurationDiscountCurveWithSpreadServiceImpl.java`

**修改**:
- `entity.getKeyTerm()` → `entity.getKeyDuration()`
- 方法参数名：`keyTerm` → `keyDuration`
- 变量名：`keyTerm` → `keyDuration`

### 5. 设计文档修复

**文件**: `docs/sql/adur_program_design.sql`

**修改**: 更新TB0002表结构定义，使其与实际表结构一致：
- 添加 `account_period` 字段
- `key_term` → `key_duration`
- 更新唯一索引定义

## 表结构统一

修复后，所有模块都使用统一的表结构：

```sql
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `key_duration` varchar(20) NOT NULL COMMENT '关键久期',
  `parameter_val_set` mediumtext COMMENT 'JSON格式参数值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_key_duration` (`account_period`, `key_duration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期参数表';
```

## 字段说明

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| id | bigint(20) | 主键ID |
| account_period | varchar(6) | 账期，格式YYYYMM |
| key_duration | varchar(20) | 关键久期，如：0、0.5、1、2等 |
| parameter_val_set | mediumtext | JSON格式参数值集，存储0-600期的参数值 |

## 验证方法

1. **编译验证**: 确保job模块能够正常编译
2. **SQL验证**: 确保Mapper XML中的SQL语句能够正常执行
3. **功能验证**: 测试ADUR相关功能是否正常工作
4. **数据一致性验证**: 确保app模块和job模块使用相同的表结构

## 影响范围

此次修复主要影响：
- **Job模块**: AdurKeyDurationParameterEntity、AdurKeyDurationParameterMapper及相关服务类
- **设计文档**: adur_program_design.sql中的表结构定义
- **ADUR功能**: UC0008关键久期折现曲线含价差计算等相关功能

## 注意事项

1. 确保数据库中的实际表结构与修复后的代码一致
2. 如果数据库表结构不正确，需要执行DDL语句进行修复
3. 相关的测试用例可能需要相应更新
4. 其他可能引用此表的代码也需要检查和更新

## 后续建议

1. 建立统一的表结构管理机制，避免app模块和job模块使用不同的表结构定义
2. 加强代码审查，确保实体类、Mapper文件与实际表结构保持一致
3. 完善集成测试，及早发现此类表结构不一致问题
