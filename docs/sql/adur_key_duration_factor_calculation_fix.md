# ADUR关键久期折现因子含价差计算逻辑修复

## 问题发现

用户要求检查关键久期折现因子表含价差的JSON计算逻辑是否符合要求：

**用户提供的正确计算公式**：
```
等于1/（1+关键久期折现曲线表含价差.期限0）^(月份/12)
```

**关键点**：所有期限的折现因子都应该使用 **期限0** 的收益率进行计算。

## 原始代码问题

### 错误的实现
```java
// 原始错误代码
for (int termIndex = 0; termIndex <= 600; termIndex++) {
    BigDecimal yieldRate = curveTermValues.get(termIndex); // ❌ 使用当前期限的收益率
    
    // 计算公式：期限X的折现因子 = 1/(1+期限X的收益率)^(X/12)  ❌ 错误
    BigDecimal discountFactor = calculateDiscountFactor(yieldRate, termIndex);
}
```

**问题**：代码使用的是每个期限自己的收益率，而不是期限0的收益率。

## 修复后的实现

### 正确的实现
```java
// 获取期限0的收益率（用于所有期限的折现因子计算）
BigDecimal term0YieldRate = curveTermValues.get(0);

if (term0YieldRate == null) {
    log.warn("期限0收益率为空，无法计算折现因子");
    isNormal = false;
} else {
    // 计算0-600期的折现因子（都使用期限0的收益率）
    for (int termIndex = 0; termIndex <= 600; termIndex++) {
        // 计算公式：期限X的折现因子 = 1/(1+关键久期折现曲线表含价差.期限0)^(X/12) ✅ 正确
        BigDecimal discountFactor = calculateDiscountFactor(term0YieldRate, termIndex);
        factorTermValues.put(termIndex, discountFactor.setScale(6, RoundingMode.HALF_UP));
    }
}
```

### 计算方法更新
```java
/**
 * 计算单个期限的折现因子
 *
 * @param term0YieldRate 期限0的收益率（来自关键久期折现曲线表含价差）
 * @param termIndex 期限索引(月份数)
 * @return 折现因子
 */
private BigDecimal calculateDiscountFactor(BigDecimal term0YieldRate, int termIndex) {
    // 计算公式：期限X的折现因子 = 1/(1+关键久期折现曲线表含价差.期限0)^(X/12)
    
    // 如果期限为0，折现因子为1
    if (termIndex == 0) {
        return BigDecimal.ONE;
    }
    
    // 计算 (1 + 期限0收益率)
    BigDecimal onePlusRate = BigDecimal.ONE.add(term0YieldRate);
    
    // 计算指数 X/12
    double exponent = (double) termIndex / 12.0;
    
    // 计算 (1 + 期限0收益率)^(X/12)
    double powerResult = Math.pow(onePlusRate.doubleValue(), exponent);
    
    // 计算 1 / (1 + 期限0收益率)^(X/12)
    BigDecimal discountFactor = BigDecimal.ONE.divide(
            BigDecimal.valueOf(powerResult), 
            10, 
            RoundingMode.HALF_UP
    );
    
    return discountFactor.setScale(6, RoundingMode.HALF_UP);
}
```

## 计算示例验证

### 输入数据
假设关键久期折现曲线含价差的期限数据为：
```json
{
  "0": 0.05,    // 期限0：5%（关键数据）
  "1": 0.06,    // 期限1：6%（不会被使用）
  "12": 0.07,   // 期限12：7%（不会被使用）
  ...
  "600": 0.08   // 期限600：8%（不会被使用）
}
```

### 计算过程
所有期限的折现因子都使用期限0的收益率（5%）进行计算：

#### 期限0的折现因子
```
期限0 = 1/(1+0.05)^(0/12) = 1/(1.05)^0 = 1/1 = 1.000000
```

#### 期限12的折现因子（1年）
```
期限12 = 1/(1+0.05)^(12/12) = 1/(1.05)^1 = 1/1.05 ≈ 0.952381
```

#### 期限24的折现因子（2年）
```
期限24 = 1/(1+0.05)^(24/12) = 1/(1.05)^2 = 1/1.1025 ≈ 0.907029
```

#### 期限600的折现因子（50年）
```
期限600 = 1/(1+0.05)^(600/12) = 1/(1.05)^50 ≈ 0.087204
```

### 预期输出JSON
```json
{
  "0": 1.000000,
  "1": 0.995951,
  "12": 0.952381,
  "24": 0.907029,
  ...
  "600": 0.087204
}
```

## 测试验证

### 测试用例设计
创建了完整的测试用例 `AdurKeyDurationDiscountFactorWithSpreadTaskTest`：

1. **准备测试数据**：
   - 关键久期折现曲线含价差数据
   - 期限0设置为5%，其他期限设置为6%（验证只使用期限0的值）

2. **执行计算**：运行关键久期折现因子含价差计算任务

3. **验证结果**：
   - 期限0的折现因子 = 1.000000
   - 期限12的折现因子 = 0.952381
   - 期限24的折现因子 = 0.907029

### 验证公式
测试用例中包含了手工计算的期望值，确保计算结果的准确性。

## 关键改进点

### 1. 数据源修正
- **修复前**：使用每个期限自己的收益率
- **修复后**：统一使用期限0的收益率

### 2. 计算逻辑修正
- **修复前**：`1/(1+期限X收益率)^(X/12)`
- **修复后**：`1/(1+期限0收益率)^(X/12)`

### 3. 错误处理改进
- 增加了期限0收益率为空的检查
- 更新了日志信息，明确显示使用的是期限0收益率

### 4. 代码可读性提升
- 方法参数名从 `yieldRate` 改为 `term0YieldRate`
- 注释更加明确地说明计算公式

## 业务意义

### 1. 符合监管要求
修复后的计算逻辑符合用户提供的业务规则，确保监管合规性。

### 2. 计算一致性
所有期限使用相同的基准利率（期限0），保证了折现因子的一致性。

### 3. 风险管理准确性
正确的折现因子计算对于久期风险管理和资产负债匹配至关重要。

## 影响范围

此次修复影响：
- **计算结果**：所有期限的折现因子值都会发生变化
- **下游任务**：可能影响依赖此数据的其他计算
- **风险指标**：可能影响基于折现因子的风险指标计算

## 验证建议

1. **运行测试用例**：执行 `AdurKeyDurationDiscountFactorWithSpreadTaskTest`
2. **手工验证**：选择几个关键期限进行手工计算验证
3. **数据对比**：对比修复前后的计算结果差异
4. **业务验证**：与业务专家确认计算结果的合理性

## 总结

修复后的计算逻辑现在完全符合用户提供的业务要求：
- ✅ 使用期限0的收益率进行所有期限的计算
- ✅ 计算公式：`1/(1+期限0收益率)^(月份/12)`
- ✅ 包含完整的错误处理和数据验证
- ✅ 提供了详细的测试用例验证

这确保了关键久期折现因子含价差的计算准确性和业务合规性。
