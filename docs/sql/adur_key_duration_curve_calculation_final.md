# ADUR关键久期折现曲线含价差计算逻辑最终确认

## 业务逻辑确认

经过与用户的沟通确认，关键久期折现曲线含价差的正确计算逻辑如下：

### 压力方向处理
- **上升和下降都需要计算**：每个(资产+关键期限)组合需要生成上升和下降两条记录
- **记录数量计算公式**：`曲线细分类=2的月度折现曲线含价差记录数 × 关键期限数量 × 压力方向数(2)`

### 计算公式
```
如果压力方向=上升：
期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.参数值集.期限X

如果压力方向=下降：
期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.参数值集.期限X
```

### 匹配条件
**月度折现曲线表含价差匹配条件**：
- `月度折现曲线表含价差.曲线细分类 = 2`
- `月度折现曲线表含价差.账户名称 = 关键久期折现曲线表含价差.账户名称`
- `月度折现曲线表含价差.证券代码 = 关键久期折现曲线表含价差.证券代码`

**关键久期参数表匹配条件**：
- `关键久期参数表.关键期限 = 关键久期折现曲线表含价差.关键期限`

## 最终实现逻辑

### 核心计算代码
```java
// 筛选曲线细分类=2的月度折现曲线含价差数据
List<AdurMonthlyDiscountCurveWithSpreadEntity> category2CurveList = monthlyDiscountCurveWithSpreadList.stream()
        .filter(curve -> "2".equals(curve.getCurveSubCategory()))
        .collect(java.util.stream.Collectors.toList());

// 为每个关键期限生成数据
for (String keyDuration : KEY_TERMS) {
    AdurKeyDurationParameterEntity parameterEntity = keyDurationParameterMap.get(keyDuration);
    
    // 为每个曲线细分类=2的月度折现曲线含价差记录生成关键久期数据
    for (AdurMonthlyDiscountCurveWithSpreadEntity curveEntity : category2CurveList) {
        // 生成上升和下降两种压力方向的数据
        resultList.add(createKeyDurationCurveEntity(accountPeriod, curveEntity, parameterEntity, keyDuration, STRESS_DIRECTION_UP));
        resultList.add(createKeyDurationCurveEntity(accountPeriod, curveEntity, parameterEntity, keyDuration, STRESS_DIRECTION_DOWN));
    }
}
```

### JSON生成逻辑
```java
// 计算0-600期的关键久期折现曲线值
for (int termIndex = 0; termIndex <= 600; termIndex++) {
    BigDecimal baseRate = baseRateMap.get(termIndex);
    BigDecimal parameterValue = parameterValueMap.get(String.valueOf(termIndex));

    if (baseRate != null && parameterValue != null) {
        BigDecimal adjustedRate;

        if (AdurConstant.STRESS_DIRECTION_UP.equals(stressDirection)) {
            // 上升：期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.参数值集[X]
            adjustedRate = baseRate.add(parameterValue);
        } else {
            // 下降：期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.参数值集[X]
            adjustedRate = baseRate.subtract(parameterValue);
        }

        // 保留6位小数并存储结果
        resultTermValues.put(termIndex, adjustedRate.setScale(6, RoundingMode.HALF_UP));
    } else {
        // 如果基础利率或参数值为空，设置为0（确保JSON包含所有期限）
        resultTermValues.put(termIndex, BigDecimal.ZERO);
    }
}

// 将计算结果转换为完整的JSON格式并设置到实体（确保包含0-600所有期限）
String resultJson = TermDataUtil.createCompleteTermJson(resultTermValues);
entity.setKeyDurationDiscountCurveWithSpreadSet(resultJson);
```

## 数据示例

### 输入数据示例

#### 月度折现曲线含价差（曲线细分类=2）
```json
{
  "0": 0.04,
  "1": 0.04,
  ...
  "600": 0.04
}
```

#### 关键久期参数（关键期限=1）
```json
{
  "0": {"val": 0.001},
  "1": {"val": 0.001},
  ...
  "600": {"val": 0.001}
}
```

### 输出数据示例

#### 上升压力方向记录
```json
{
  "0": 0.041000,
  "1": 0.041000,
  ...
  "600": 0.041000
}
```

#### 下降压力方向记录
```json
{
  "0": 0.039000,
  "1": 0.039000,
  ...
  "600": 0.039000
}
```

## 关键改进点

### 1. 数据筛选优化
- 先筛选曲线细分类=2的数据，再进行计算
- 避免处理不符合条件的基础数据

### 2. 完整JSON生成
- 使用 `TermDataUtil.createCompleteTermJson()` 确保包含0-600所有期限
- 缺失数据时设置为0，而不是跳过

### 3. 压力方向处理
- 明确为每个资产生成上升和下降两条记录
- 根据压力方向进行相应的加法或减法计算

### 4. 错误处理改进
- 当找不到曲线细分类=2的数据时，记录警告日志
- 异常情况下仍然生成完整的JSON结构

## 测试验证

### 测试用例覆盖
1. **正常计算测试**：验证上升和下降两种压力方向的计算结果
2. **缺失数据测试**：验证基础数据缺失时的处理逻辑
3. **匹配条件测试**：验证曲线细分类筛选逻辑

### 预期结果
- **记录数量**：每个资产的每个关键期限生成2条记录（上升+下降）
- **JSON格式**：每条记录包含0-600所有期限的完整数据
- **计算准确性**：上升压力=基础值+参数值，下降压力=基础值-参数值

## 业务价值

### 1. 风险管理
- 通过上升和下降两种压力情景，全面评估利率风险
- 为风险管理决策提供完整的数据支持

### 2. 监管合规
- 满足监管要求的压力测试需求
- 提供完整的久期风险敞口数据

### 3. 数据完整性
- 确保每条记录包含完整的期限结构数据
- 支持后续的折现因子计算和风险分析

## 后续任务依赖

此表的数据将被以下任务使用：
- **UC0009**：关键久期折现因子含价差计算
- **UC0010**：久期资产结果汇总

确保数据格式和内容的正确性对后续任务的成功执行至关重要。

## 总结

经过修复，关键久期折现曲线含价差计算逻辑现在能够：
1. 正确筛选曲线细分类=2的基础数据
2. 为每个资产的每个关键期限生成上升和下降两条记录
3. 生成包含0-600所有期限的完整JSON数据
4. 按照正确的业务公式进行计算

这样应该能够解决之前值集为空的问题，并提供完整准确的计算结果。
