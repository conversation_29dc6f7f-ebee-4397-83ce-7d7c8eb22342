-- =============================================
-- ADUR关键久期折现曲线表含价差DDL
-- 表名：t_adur_key_duration_curve_with_spread
-- 对应表：TB0008 关键久期折现曲线表含价差
-- 基于最新字段列表要求生成
-- =============================================

DROP TABLE IF EXISTS `t_adur_key_duration_curve_with_spread`;

CREATE TABLE `t_adur_key_duration_curve_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) DEFAULT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(10) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `key_duration_curve_with_spread_set` text COMMENT '关键久期折现曲线表含价差值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_key_stress` (`account_period`,`asset_number`,`key_term`,`stress_direction`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_key_term` (`key_term`),
  KEY `idx_stress_direction` (`stress_direction`),
  KEY `idx_duration_type` (`duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键久期折现曲线表含价差';

-- =============================================
-- 字段说明
-- =============================================

/*
字段取值规范：

1. 久期类型 (duration_type)：
   - 关键久期（固定值）

2. 基点类型 (basis_point_type)：
   - 0bp（固定值）

3. 关键期限 (key_term)：
   - 0, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 15, 20, 25, 30, 35, 40, 45, 50

4. 压力方向 (stress_direction)：
   - 上升
   - 下降

5. 日期类型 (date_type)：
   - 评估时点（固定值）

6. 价差类型 (spread_type)：
   - 发行时点价差（固定值）

7. 曲线细分类 (curve_sub_category)：
   - 2（固定值）

关键久期折现曲线表含价差值集 (key_duration_curve_with_spread_set) 字段说明：

1. 数据格式：JSON格式
   示例：{"0":0.0275,"1":0.0285,"2":0.0295,...,"600":0.0485}

2. 计算逻辑：
   根据压力方向确定计算方式：
   
   - 压力方向=上升：
     月度折现曲线表含价差.期限X + 关键久期参数表.关键参数值集.key
   
   - 压力方向=下降：
     月度折现曲线表含价差.期限X - 关键久期参数表.关键参数值集.key

3. 匹配条件：
   
   月度折现曲线表含价差匹配条件：
   - 曲线细分类=2
   - 账户名称相同
   - 证券代码相同
   
   关键久期参数表匹配条件：
   - 关键期限相同

4. 数据范围：
   - 期限范围：0-600（共601个期限点）
   - 数值类型：decimal(10,6)对应的数值
   - 缺省值：0

5. 索引设计：
   - 主键：id
   - 唯一索引：account_period + asset_number + key_term + stress_direction
   - 普通索引：account_period, asset_number, key_term, stress_direction, duration_type
*/

-- =============================================
-- 使用示例
-- =============================================

/*
1. 插入数据示例：
INSERT INTO t_adur_key_duration_curve_with_spread (
  account_period, duration_type, basis_point_type, key_term, stress_direction,
  date_type, date, spread_type, spread, curve_sub_category, 
  asset_number, account_name, asset_name, security_code, curve_id, 
  key_duration_curve_with_spread_set
) VALUES (
  '202501', '关键久期', '0bp', '1', '上升',
  '评估时点', '2025-01-31', '发行时点价差', 0.001500, '2',
  'ASSET001', '传统账户', '国债001', 'T001', '1',
  '{"0":0.0275,"1":0.0285,"2":0.0295,"3":0.0305,"4":0.0315,"5":0.0325}'
);

2. 查询特定期限关键久期折现曲线：
SELECT 
  asset_number,
  key_term,
  stress_direction,
  JSON_EXTRACT(key_duration_curve_with_spread_set, '$.12') as curve_12_month,
  JSON_EXTRACT(key_duration_curve_with_spread_set, '$.24') as curve_24_month
FROM t_adur_key_duration_curve_with_spread 
WHERE account_period = '202501' AND key_term = '1';

3. 更新特定期限关键久期折现曲线：
UPDATE t_adur_key_duration_curve_with_spread 
SET key_duration_curve_with_spread_set = JSON_SET(
  key_duration_curve_with_spread_set, 
  '$.12', 0.0320,
  '$.24', 0.0340
)
WHERE account_period = '202501' AND asset_number = 'ASSET001' AND key_term = '1';

4. 根据压力方向查询：
SELECT 
  asset_number,
  key_term,
  stress_direction,
  JSON_EXTRACT(key_duration_curve_with_spread_set, '$.0') as base_curve
FROM t_adur_key_duration_curve_with_spread 
WHERE stress_direction = '上升' AND key_term = '1';
*/

-- =============================================
-- 数据验证查询
-- =============================================

-- 验证JSON格式是否正确
SELECT 
  id,
  account_period,
  asset_number,
  key_term,
  stress_direction,
  CASE 
    WHEN JSON_VALID(key_duration_curve_with_spread_set) = 1 THEN 'Valid JSON'
    ELSE 'Invalid JSON'
  END as json_status,
  JSON_LENGTH(key_duration_curve_with_spread_set) as json_length
FROM `t_adur_key_duration_curve_with_spread`
WHERE key_duration_curve_with_spread_set IS NOT NULL
LIMIT 10;

-- 验证关键期限和压力方向分布
SELECT 
  key_term,
  stress_direction,
  COUNT(*) as record_count
FROM `t_adur_key_duration_curve_with_spread`
GROUP BY key_term, stress_direction
ORDER BY key_term, stress_direction;

-- 验证压力测试计算逻辑
SELECT 
  asset_number,
  key_term,
  stress_direction,
  JSON_EXTRACT(key_duration_curve_with_spread_set, '$.0') as curve_0,
  JSON_EXTRACT(key_duration_curve_with_spread_set, '$.12') as curve_12
FROM `t_adur_key_duration_curve_with_spread`
WHERE key_term IN ('1', '2', '3')
ORDER BY asset_number, key_term, stress_direction
LIMIT 10;

-- 统计数据完整性
SELECT 
  COUNT(*) as total_records,
  COUNT(key_duration_curve_with_spread_set) as with_curve_set,
  COUNT(*) - COUNT(key_duration_curve_with_spread_set) as missing_curve_set
FROM `t_adur_key_duration_curve_with_spread`;

-- =============================================
-- 性能优化建议
-- =============================================

/*
1. 对于频繁查询的关键期限，可以考虑创建函数索引：
   CREATE INDEX idx_curve_1y ON t_adur_key_duration_curve_with_spread 
   ((CAST(JSON_EXTRACT(key_duration_curve_with_spread_set, '$.12') AS DECIMAL(10,6))));

2. 对于大量数据的JSON操作，建议：
   - 使用批量操作减少单条记录的JSON解析开销
   - 考虑将常用期限的折现曲线提取为独立字段
   - 定期维护和优化JSON数据格式

3. 关键久期计算优化：
   - 预计算常用关键期限的压力测试结果
   - 使用缓存机制存储计算结果
   - 考虑并行计算提高处理效率
*/
