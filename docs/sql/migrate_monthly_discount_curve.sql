-- =============================================
-- 月度折现曲线表完整DDL - 直接执行版本
-- 表名：t_adur_monthly_discount_curve
-- 对应表：TB0005 月度折现曲线表不含价差
-- =============================================

-- 删除旧表（如果存在）
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;

-- 创建新表结构
CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_set` text COMMENT '月度折现曲线利率值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_date_type` (`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现曲线表不含价差';

-- 步骤4：验证数据迁移结果
SELECT 
  id,
  account_period,
  asset_number,
  JSON_EXTRACT(monthly_discount_rate_set, '$.0') as term_0_check,
  JSON_EXTRACT(monthly_discount_rate_set, '$.12') as term_12_check,
  JSON_EXTRACT(monthly_discount_rate_set, '$.600') as term_600_check
FROM `t_adur_monthly_discount_curve` 
LIMIT 10;

-- 步骤5：删除旧的term字段（谨慎操作，建议先测试）
/*
ALTER TABLE `t_adur_monthly_discount_curve` 
DROP COLUMN term_0,
DROP COLUMN term_1,
DROP COLUMN term_2,
-- ... 继续删除所有term字段
DROP COLUMN term_600;
*/

-- =============================================
-- 完整的数据迁移程序示例（伪代码）
-- =============================================

/*
建议使用程序进行数据迁移，确保JSON格式正确：

DELIMITER $$

CREATE PROCEDURE MigrateMonthlyDiscountCurve()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id BIGINT;
    DECLARE v_json_data TEXT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM t_adur_monthly_discount_curve;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 构建JSON字符串
        SET v_json_data = '{';
        
        -- 循环添加所有term字段
        SET @i = 0;
        WHILE @i <= 600 DO
            SET @sql = CONCAT(
                'SELECT IFNULL(term_', @i, ', 0) INTO @term_value ',
                'FROM t_adur_monthly_discount_curve WHERE id = ', v_id
            );
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            IF @i > 0 THEN
                SET v_json_data = CONCAT(v_json_data, ',');
            END IF;
            
            SET v_json_data = CONCAT(v_json_data, '"', @i, '":', @term_value);
            SET @i = @i + 1;
        END WHILE;
        
        SET v_json_data = CONCAT(v_json_data, '}');
        
        -- 更新记录
        UPDATE t_adur_monthly_discount_curve 
        SET monthly_discount_rate_set = v_json_data 
        WHERE id = v_id;
        
    END LOOP;
    
    CLOSE cur;
END$$

DELIMITER ;

-- 执行迁移
CALL MigrateMonthlyDiscountCurve();

-- 清理存储过程
DROP PROCEDURE MigrateMonthlyDiscountCurve;
*/

-- =============================================
-- 数据验证查询
-- =============================================

-- 验证JSON格式是否正确
SELECT 
  id,
  account_period,
  asset_number,
  CASE 
    WHEN JSON_VALID(monthly_discount_rate_set) = 1 THEN 'Valid JSON'
    ELSE 'Invalid JSON'
  END as json_status,
  JSON_LENGTH(monthly_discount_rate_set) as json_length
FROM `t_adur_monthly_discount_curve`
WHERE monthly_discount_rate_set IS NOT NULL
LIMIT 10;

-- 验证特定期限的数据
SELECT 
  id,
  account_period,
  asset_number,
  JSON_EXTRACT(monthly_discount_rate_set, '$.0') as rate_0_month,
  JSON_EXTRACT(monthly_discount_rate_set, '$.12') as rate_12_month,
  JSON_EXTRACT(monthly_discount_rate_set, '$.24') as rate_24_month,
  JSON_EXTRACT(monthly_discount_rate_set, '$.36') as rate_36_month,
  JSON_EXTRACT(monthly_discount_rate_set, '$.600') as rate_600_month
FROM `t_adur_monthly_discount_curve`
WHERE monthly_discount_rate_set IS NOT NULL
LIMIT 5;

-- 统计迁移结果
SELECT 
  COUNT(*) as total_records,
  COUNT(monthly_discount_rate_set) as migrated_records,
  COUNT(*) - COUNT(monthly_discount_rate_set) as pending_records
FROM `t_adur_monthly_discount_curve`;

-- =============================================
-- 回滚脚本（如果迁移失败）
-- =============================================

/*
-- 从备份表恢复数据
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;
CREATE TABLE `t_adur_monthly_discount_curve` AS 
SELECT * FROM `t_adur_monthly_discount_curve_backup`;

-- 重建索引
ALTER TABLE `t_adur_monthly_discount_curve` 
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`),
ADD KEY `idx_account_period` (`account_period`),
ADD KEY `idx_asset_number` (`asset_number`),
ADD KEY `idx_date_type` (`date_type`);
*/
