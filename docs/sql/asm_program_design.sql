-- =============================================
-- 资产规模与偿付能力管理模块 DDL
-- 基于 ass_program_design.md 文档 2.3 章节生成
-- 数据库: MySQL 8.0
-- 字符集: utf8
-- =============================================

-- =============================================
-- 表名: t_liab_balance_sheet
-- 表中文名: 资产负债表
-- 表编号: TB0017
-- 表描述: 存储资产负债表数据，包括资产、负债、所有者权益等项目的期末余额和年初余额
-- =============================================
CREATE TABLE IF NOT EXISTS `t_liab_balance_sheet` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
    `category` varchar(20) NOT NULL COMMENT '类别，值域：资产、负债、所有者权益',
    `item_name` varchar(100) NOT NULL COMMENT '项目名称，资产负债表项目名称',
    `ending_balance` decimal(28,10) DEFAULT 0.********** COMMENT '期末余额金额',
    `beginning_balance` decimal(28,10) DEFAULT 0.********** COMMENT '年初余额金额',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_category_item` (`accounting_period`, `category`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产负债表';

-- =============================================
-- 表名: t_base_solvency_status
-- 表中文名: 偿付能力状况表
-- 表编号: TB0018
-- 表描述: 存储S01-偿付能力状况表数据，包括各项偿付能力指标
-- =============================================
CREATE TABLE IF NOT EXISTS `t_base_solvency_status` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202503）',
    `row_number` int(11) NOT NULL COMMENT '行次，表中行的序号',
    `item_name` varchar(100) NOT NULL COMMENT '项目，偿付能力指标项目名称',
    `ending_balance` decimal(28,10) DEFAULT 0.********** COMMENT '期末数，期末余额',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_row_number` (`row_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='偿付能力状况表';

-- =============================================
-- 表名: t_asm_asset_scale_solvency
-- 表中文名: 资产规模与偿付能力表
-- 表编号: TB0019
-- 表描述: 存储资产规模与偿付能力计算结果，通过资产负债表和偿付能力状况表数据计算生成
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_asset_scale_solvency` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202503）',
    `item_name` varchar(100) NOT NULL COMMENT '项目，指标项目名称，项目内容固定',
    `last_year_end` decimal(28,10) DEFAULT 0.********** COMMENT '上年末数据，计算字段',
    `last_quarter_end` decimal(28,10) DEFAULT 0.********** COMMENT '上季末数据，计算字段',
    `current_quarter_end` decimal(28,10) DEFAULT 0.********** COMMENT '本季末数据，计算字段',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产规模与偿付能力表';
