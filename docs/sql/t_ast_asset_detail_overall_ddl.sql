-- =============================================
-- 整体资产明细表DDL
-- 表名：t_ast_asset_detail_overall
-- 功能：存储整体资产明细数据，用于ADUR模块UC0003生成久期资产明细数据
-- 说明：此表作为基础数据源，从其他系统导入或手工维护
-- =============================================

DROP TABLE IF EXISTS `t_ast_asset_detail_overall`;
CREATE TABLE `t_ast_asset_detail_overall` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `asset_sub_category` varchar(50) NOT NULL COMMENT '资产小小类',
  `holding_face_value` decimal(28,10) DEFAULT '0.**********' COMMENT '持仓面值',
  `market_value` decimal(28,10) DEFAULT '0.**********' COMMENT '市值',
  `book_balance` decimal(28,10) DEFAULT '0.**********' COMMENT '账面余额',
  `book_value` decimal(28,10) DEFAULT '0.**********' COMMENT '账面价值',
  `coupon_rate` decimal(10,6) DEFAULT '0.000000' COMMENT '票面利率',
  `payment_method` varchar(20) NOT NULL COMMENT '付息方式',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `adjusted_value_date` date NOT NULL COMMENT '调整起息日',
  `adjusted_purchase_date` date NOT NULL COMMENT '调整买入日',
  `adjusted_maturity_date` date NOT NULL COMMENT '调整到期日',
  `issue_spread_calc_flag` char(1) NOT NULL COMMENT '发行时点价差计算标识',
  `spread_duration_stat_flag` char(1) DEFAULT '0' COMMENT '利差久期资产统计标识',
  `cashflow_calc_flag` char(1) NOT NULL COMMENT '可计算现金流固收资产标识',
  `issue_cashflow_set` mediumtext COMMENT '发行时点现金流值集',
  `eval_cashflow_set` mediumtext COMMENT '评估时点现金流值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset` (`account_period`,`asset_number`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_cashflow_calc_flag` (`cashflow_calc_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='整体资产明细表';

-- =============================================
-- 表结构说明
-- =============================================
/*
表名：t_ast_asset_detail_overall
用途：存储整体资产明细数据，作为ADUR模块久期资产明细数据生成的基础数据源

关键字段说明：
1. account_period: 账期，格式YYYYMM，如202501
2. asset_number: 资产编号，唯一标识一个资产
3. cashflow_calc_flag: 可计算现金流固收资产标识，'1'表示可计算现金流的固收资产
4. issue_spread_calc_flag: 发行时点价差计算标识
5. spread_duration_stat_flag: 利差久期资产统计标识
6. issue_cashflow_set: 发行时点现金流值集，JSON格式存储
7. eval_cashflow_set: 评估时点现金流值集，JSON格式存储

索引说明：
1. uk_account_asset: 账期+资产编号唯一约束
2. idx_account_period: 账期索引，用于按账期查询
3. idx_cashflow_calc_flag: 现金流计算标识索引，用于筛选可计算现金流的资产

数据来源：
- 此表数据通常从其他系统导入或手工维护
- 用于ADUR模块UC0003生成久期资产明细数据的基础数据源
- 筛选条件：cashflow_calc_flag = '1' 且 is_del = 0

使用场景：
- AdurDurationAssetDetailGenerationServiceImpl.loadCashflowCalcAssets()方法
- 根据账期查询可计算现金流固收资产数据
*/

-- =============================================
-- DDL创建完成
-- =============================================
