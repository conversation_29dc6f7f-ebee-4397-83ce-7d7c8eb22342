-- 久期管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('久期管理', 0, 10, 'dur', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', SYSDATE(), '', NULL, '久期管理菜单');

-- 获取久期管理菜单ID
SET @durMenuId = (SELECT id FROM sys_menu WHERE menu_name = '久期管理' AND parent_id = 0 AND path = 'dur');

-- 资产久期管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产久期管理', 0, 11, 'adur', NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', SYSDATE(), '', NULL, '资产久期管理菜单');

-- 获取资产久期管理菜单ID
SET @adurMenuId = (SELECT id FROM sys_menu WHERE menu_name = '资产久期管理' AND parent_id = 0 AND path = 'adur');

-- TB0008: ADUR关键久期折现曲线表含价差菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ADUR关键久期折现曲线含价差', @adurMenuId, 8, 'adurKeyDurationDiscountCurve', 'adur/key/duration/discount/curve/index', '', 1, 0, 'C', '0', '0', 'adur:key:duration:discount:curve:list', 'chart', 'admin', SYSDATE(), '', NULL, 'ADUR关键久期折现曲线表含价差菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ADUR关键久期折现曲线含价差查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现曲线含价差新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现曲线含价差修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现曲线含价差删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现曲线含价差导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现曲线含价差导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 1. 负债现金流表菜单 SQL (TB0001)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债现金流', @durMenuId, 1, 'liabilityCashFlow', 'dur/liability/cash/flow/index', '', 1, 0, 'C', '0', '0', 'dur:liability:cash:flow:list', 'table', 'admin', SYSDATE(), '', NULL, '负债现金流菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债现金流查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 2. 折现曲线表菜单 SQL (TB0002)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('折现曲线', @durMenuId, 2, 'discountCurve', 'dur/discount/curve/index', '', 1, 0, 'C', '0', '0', 'dur:discount:curve:list', 'chart', 'admin', SYSDATE(), '', NULL, '折现曲线菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('折现曲线查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:curve:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 3. 折现因子表菜单 SQL (TB0003 - program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('折现因子', @durMenuId, 3, 'discountFactor', 'dur/discount/factor/index', '', 1, 0, 'C', '0', '0', 'dur:discount:factor:list', 'table', 'admin', SYSDATE(), '', NULL, '折现因子菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('折现因子查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现因子新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现因子修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现因子删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现因子导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现因子导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:discount:factor:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 4. 关键久期参数表菜单 SQL (TB0003 - key_duration_program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期参数管理', @durMenuId, 4, 'keyDurationParameter', 'dur/key/duration/parameter/index', '', 1, 0, 'C', '0', '0', 'dur:key:duration:parameter:list', 'table', 'admin', SYSDATE(), '', NULL, '关键久期参数菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期参数查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期参数新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期参数修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期参数删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期参数导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期参数导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:parameter:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 5. 关键久期折现曲线表菜单 SQL (TB0004)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期折现曲线', @durMenuId, 5, 'keyDurationDiscountCurve', 'dur/key/duration/discount/curve/index', '', 1, 0, 'C', '0', '0', 'dur:key:duration:discount:curve:list', 'chart', 'admin', SYSDATE(), '', NULL, '关键久期折现曲线菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期折现曲线查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现曲线新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现曲线修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现曲线删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现曲线导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现曲线导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:curve:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 6. 关键久期折现因子表菜单 SQL (TB0005 - key_duration_program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期折现因子', @durMenuId, 6, 'keyDurationDiscountFactors', 'dur/key/duration/discount/factors/index', '', 1, 0, 'C', '0', '0', 'dur:key:duration:discount:factors:list', 'table', 'admin', SYSDATE(), '', NULL, '关键久期折现因子菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('关键久期折现因子查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现因子新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现因子修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现因子删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现因子导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('关键久期折现因子导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:key:duration:discount:factors:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 7. 负债现金流汇总表菜单 SQL (TB0005 - program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债现金流汇总', @durMenuId, 7, 'liabilityCashFlowSummary', 'dur/liability/cash/flow/summary/index', '', 1, 0, 'C', '0', '0', 'dur:liability:cash:flow:summary:list', 'table', 'admin', SYSDATE(), '', NULL, '负债现金流汇总菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债现金流汇总查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流汇总新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流汇总修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流汇总删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流汇总导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债现金流汇总导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:cash:flow:summary:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 8. 分中短负债基点价值DV10表菜单 SQL (TB0006)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分中短负债基点价值DV10', @durMenuId, 8, 'liabilityDv10ByDuration', 'dur/liability/dv10/by/duration/index', '', 1, 0, 'C', '0', '0', 'dur:liability:dv10:by:duration:list', 'table', 'admin', SYSDATE(), '', NULL, '分中短负债基点价值DV10菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分中短负债基点价值DV10查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分中短负债基点价值DV10新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分中短负债基点价值DV10修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分中短负债基点价值DV10删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分中短负债基点价值DV10导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分中短负债基点价值DV10导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:dv10:by:duration:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 9. 分账户负债基点价值DV10表菜单 SQL (TB0007 - key_duration_program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债基点价值DV10', @durMenuId, 9, 'accountLiabilityDv10', 'dur/account/liability/dv10/index', '', 1, 0, 'C', '0', '0', 'dur:account:liability:dv10:list', 'table', 'admin', SYSDATE(), '', NULL, '分账户负债基点价值DV10菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债基点价值DV10查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债基点价值DV10新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债基点价值DV10修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债基点价值DV10删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债基点价值DV10导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债基点价值DV10导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:account:liability:dv10:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 10. 负债久期汇总表菜单 SQL (TB0007 - program_design.md)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债久期汇总', @durMenuId, 10, 'liabilityDurationSummary', 'dur/liability/duration/summary/index', '', 1, 0, 'C', '0', '0', 'dur:liability:duration:summary:list', 'table', 'admin', SYSDATE(), '', NULL, '负债久期汇总菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('负债久期汇总查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债久期汇总新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债久期汇总修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债久期汇总删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债久期汇总导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('负债久期汇总导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:liability:duration:summary:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 11. 分账户负债现金流现值汇总表菜单 SQL (TB0008)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债现金流现值汇总', @durMenuId, 11, 'subAccountLiabilityPresentValue', 'dur/sub/account/liability/present/value/index', '', 1, 0, 'C', '0', '0', 'dur:sub:account:liability:present:value:list', 'table', 'admin', SYSDATE(), '', NULL, '分账户负债现金流现值汇总菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债现金流现值汇总查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债现金流现值汇总新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债现金流现值汇总修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债现金流现值汇总删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债现金流现值汇总导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债现金流现值汇总导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:present:value:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 12. 分账户负债久期汇总表菜单 SQL (TB0009)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债久期汇总', @durMenuId, 12, 'subAccountLiabilityDuration', 'dur/sub/account/liability/duration/index', '', 1, 0, 'C', '0', '0', 'dur:sub:account:liability:duration:list', 'table', 'admin', SYSDATE(), '', NULL, '分账户负债久期汇总菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分账户负债久期汇总查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债久期汇总新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债久期汇总修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债久期汇总删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债久期汇总导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分账户负债久期汇总导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'dur:sub:account:liability:duration:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 最低资本管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('最低资本管理', 0, 20, 'minc', NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', SYSDATE(), '', NULL, '最低资本管理菜单');

-- 获取最低资本管理菜单ID
SET @mincMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '最低资本管理' AND parent_id = 0 AND path = 'minc');

-- 分部门最低资本明细表菜单 SQL (TB0001)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分部门最低资本明细管理', @mincMenuId, 1, 'deptMincapDetail', 'minc/dept/mincap/detail/index', '', 1, 0, 'C', '0', '0', 'minc:dept:mincap:detail:list', 'table', 'admin', SYSDATE(), '', NULL, '分部门最低资本明细菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分部门最低资本明细查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分部门最低资本明细新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分部门最低资本明细修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分部门最低资本明细删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分部门最低资本明细导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分部门最低资本明细导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'minc:dept:mincap:detail:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额管理', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '最低资本模块' AND parent_id = 0) t), 2, 'amount', 'minc/risk/item/amount/index', 1, 0, 'C', '0', '0', 'minc:risk:item:amount:list', 'table', 'admin', SYSDATE(), '', NULL, '风险项目金额菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:export', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('风险项目金额导入', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'minc:risk:item:amount:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 项目定义表菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义管理', @mincMenuId, 3, 'itemDefinition', 'minc/item/definition/index', 1, 0, 'C', '0', '0', 'minc:item:definition:list', 'dict', 'admin', SYSDATE(), '', NULL, '项目定义管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:export', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('项目定义导入', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'minc:item:definition:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 相关系数表菜单 SQL (TB0004)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数管理', @mincMenuId, 4, 'correlationCoef', 'minc/correlation/coef/index', 1, 0, 'C', '0', '0', 'minc:correlation:coef:list', 'table', 'admin', SYSDATE(), '', NULL, '相关系数管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:export', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('相关系数导入', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'minc:correlation:coef:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 边际最低资本贡献率表菜单 SQL (TB0006)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际最低资本管理', @mincMenuId, 5, 'marginalCapital', 'minc/marginal/capital/index', 1, 0, 'C', '0', '0', 'minc:marginal:capital:list', 'money', 'admin', SYSDATE(), '', NULL, '边际最低资本管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际资本查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:marginal:capital:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际资本新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:marginal:capital:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际资本修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:marginal:capital:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际资本删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:marginal:capital:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('边际资本导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:marginal:capital:export', '#', 'admin', SYSDATE(), '', NULL, '');

-- 最低资本明细汇总表菜单 SQL (TB0007)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('最低资本明细汇总', @mincMenuId, 6, 'minCapitalSummary', 'minc/min/capital/summary/index', 1, 0, 'C', '0', '0', 'minc:min:capital:summary:list', 'table', 'admin', SYSDATE(), '', NULL, '最低资本明细汇总菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('明细汇总查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:summary:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('明细汇总新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:summary:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('明细汇总修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:summary:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('明细汇总删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:summary:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('明细汇总导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:summary:export', '#', 'admin', SYSDATE(), '', NULL, '');

-- 市场及信用最低资本表菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本管理', @mincMenuId, '7', 'minCapitalByAccount', 'minc/min/capital/by/account/index', 1, 0, 'C', '0', '0', 'minc:min:capital:by:account:list', '#', 'admin', sysdate(), '', null, '市场及信用最低资本表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本表查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:by:account:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本表新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:by:account:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本表修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:by:account:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本表删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:by:account:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('市场及信用最低资本表导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'minc:min:capital:by:account:export',       '#', 'admin', sysdate(), '', null, '');

-- 利率风险对冲率表菜单 SQL (TB0009)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率管理', @mincMenuId, 8, 'irHedgeRatio', 'minc/ir/hedge/ratio/index', 1, 0, 'C', '0', '0', 'minc:irHedgeRatio:list', 'chart', 'admin', SYSDATE(), '', NULL, '利率风险对冲率管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'minc:irHedgeRatio:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'minc:irHedgeRatio:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'minc:irHedgeRatio:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'minc:irHedgeRatio:remove', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('利率风险对冲率导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'minc:irHedgeRatio:export', '#', 'admin', SYSDATE(), '', NULL, '');

-- 基础数据管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('基础数据管理', 0, 30, 'base', NULL, '', 1, 0, 'M', '0', '0', '', 'database', 'admin', SYSDATE(), '', NULL, '基础数据管理菜单');

-- 获取基础数据管理菜单ID
SET @baseMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '基础数据管理' AND parent_id = 0 AND path = 'base');

-- 子账户收益率表菜单 SQL (TB0014)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('子账户收益率管理', @baseMenuId, 1, 'subAccountYieldRate', 'base/sub/account/yield/rate/index', '', 1, 0, 'C', '0', '0', 'base:sub:account:yield:rate:list', 'chart', 'admin', SYSDATE(), '', NULL, '子账户收益率管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('子账户收益率查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('子账户收益率新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('子账户收益率修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('子账户收益率删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('子账户收益率导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('子账户收益率导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'base:sub:account:yield:rate:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 成本管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('成本管理', 0, 40, 'cost', NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', SYSDATE(), '', NULL, '成本管理菜单');

-- 获取成本管理菜单ID
SET @costMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '成本管理' AND parent_id = 0 AND path = 'cost');

-- 分产品保费收入表菜单 SQL (TB0015)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分产品保费收入管理', @costMenuId, 1, 'productPremiumIncomeDetail', 'cost/product/premium/income/detail/index', '', 1, 0, 'C', '0', '0', 'cost:product:premium:income:detail:list', 'money', 'admin', SYSDATE(), '', NULL, '分产品保费收入管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分产品保费收入查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('分产品保费收入新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('分产品保费收入修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('分产品保费收入删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('分产品保费收入导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('分产品保费收入导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cost:product:premium:income:detail:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 中短存续期产品利差表菜单 SQL (TB0016)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('中短存续期产品利差管理', @costMenuId, 2, 'shortTermProductSpread', 'cost/short/term/product/spread/index', '', 1, 0, 'C', '0', '0', 'cost:short:term:product:spread:list', 'chart', 'admin', SYSDATE(), '', NULL, '中短存续期产品利差管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('中短存续期产品利差查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('中短存续期产品利差新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('中短存续期产品利差修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('中短存续期产品利差删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('中短存续期产品利差导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('中短存续期产品利差导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cost:short:term:product:spread:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 现金流测试管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流测试管理', 0, 50, 'cft', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', SYSDATE(), '', NULL, '现金流测试管理菜单');

-- 获取现金流测试管理菜单ID
SET @cftMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '现金流测试管理' AND parent_id = 0 AND path = 'cft');

-- BP现金流量表菜单 SQL (TB0001)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('BP现金流量管理', @cftMenuId, 1, 'bpCashFlow', 'cft/bp/cash/flow/index', '', 1, 0, 'C', '0', '0', 'cft:bp:cash:flow:list', 'table', 'admin', SYSDATE(), '', NULL, 'BP现金流量管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('BP现金流量查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('BP现金流量新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('BP现金流量修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('BP现金流量删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('BP现金流量导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('BP现金流量导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:bp:cash:flow:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 菜单 SQL
-- 获取现金流测试模块的菜单ID
SELECT @parent_id := menu_id FROM sys_menu WHERE menu_name = '现金流测试' AND parent_id = 0;

-- 插入变量映射表菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表', @parent_id, 2, 'variable-mapping', 'cft/variable/mapping/index', '', 1, 0, 'C', '0', '0', 'cft:variable:mapping:list', 'table', 'admin', sysdate(), '', null, '变量映射表菜单');

-- 获取刚插入的变量映射表菜单ID
SELECT @variable_mapping_menu_id := LAST_INSERT_ID();

-- 按钮父菜单ID
-- 变量映射表查询按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表查询', @variable_mapping_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:query', '#', 'admin', sysdate(), '', null, '');

-- 变量映射表新增按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表新增', @variable_mapping_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:add', '#', 'admin', sysdate(), '', null, '');

-- 变量映射表修改按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表修改', @variable_mapping_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:edit', '#', 'admin', sysdate(), '', null, '');

-- 变量映射表删除按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表删除', @variable_mapping_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:remove', '#', 'admin', sysdate(), '', null, '');

-- 变量映射表导出按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表导出', @variable_mapping_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:export', '#', 'admin', sysdate(), '', null, '');

-- 变量映射表导入按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('变量映射表导入', @variable_mapping_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'cft:variable:mapping:import', '#', 'admin', sysdate(), '', null, '');

-- 财务预算费用表菜单 SQL (TB0003)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('财务预算费用管理', 2467, 3, 'financialBudgetExpense', 'cft/financial/budget/expense/index', '', 1, 0, 'C', '0', '0', 'cft:financial:budget:expense:list', 'money', 'admin', SYSDATE(), '', NULL, '财务预算费用管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('财务预算费用查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 精算业管费汇总表菜单 SQL (TB0004)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('精算业管费汇总管理', 2467, 4, 'actuarialExpenseSummary', 'cft/actuarial/expense/summary/index', '', 1, 0, 'C', '0', '0', 'cft:actuarial:expense:summary:list', 'chart', 'admin', SYSDATE(), '', NULL, '精算业管费汇总管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('精算业管费汇总查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('精算业管费汇总新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('精算业管费汇总修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('精算业管费汇总删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('精算业管费汇总导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('精算业管费汇总导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:actuarial:expense:summary:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 拆分比例表菜单 SQL (TB0005)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('拆分比例管理', 2467, 5, 'splitRatio', 'cft/split/ratio/index', '', 1, 0, 'C', '0', '0', 'cft:split:ratio:list', 'percentage', 'admin', SYSDATE(), '', NULL, '拆分比例管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('拆分比例查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('拆分比例新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('拆分比例修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('拆分比例删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('拆分比例导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('拆分比例导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:split:ratio:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 财务预算费用拆分表菜单 SQL (TB0006)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('财务预算费用拆分管理', 2467, 6, 'financialBudgetExpenseSplit', 'cft/financial/budget/expense/split/index', '', 1, 0, 'C', '0', '0', 'cft:financial:budget:expense:split:list', 'money', 'admin', SYSDATE(), '', NULL, '财务预算费用拆分管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('财务预算费用拆分查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用拆分新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用拆分修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用拆分删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用拆分导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('财务预算费用拆分导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:financial:budget:expense:split:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 业务现金流预测表菜单 SQL (TB0007)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('业务现金流预测管理', 2467, 7, 'businessCashFlowForecast', 'cft/business/cash/flow/forecast/index', '', 1, 0, 'C', '0', '0', 'cft:business:cash:flow:forecast:list', 'chart', 'admin', SYSDATE(), '', NULL, '业务现金流预测管理菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('业务现金流预测查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('业务现金流预测新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('业务现金流预测修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('业务现金流预测删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('业务现金流预测导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('业务现金流预测导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:business:cash:flow:forecast:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- TB0003: 久期资产明细表菜单 SQL (ADUR模块)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('久期资产明细', @adurMenuId, 1, 'durationAssetDetail', 'adur/duration/asset/detail/index', '', 1, 0, 'C', '0', '0', 'adur:duration:asset:detail:list', 'table', 'admin', SYSDATE(), '', NULL, '久期资产明细菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('久期资产明细查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产明细新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产明细修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产明细删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产明细导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产明细导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- TB0011: 久期资产结果汇总表菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('久期资产结果汇总表', @adurMenuId, 11, 'durationAssetSummary', 'adur/duration/asset/summary/index', '', 1, 0, 'C', '0', '0', 'adur:duration:asset:summary:list', 'table', 'admin', SYSDATE(), '', NULL, '久期资产结果汇总表菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('久期资产结果汇总表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产结果汇总表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产结果汇总表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产结果汇总表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产结果汇总表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('久期资产结果汇总表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:summary:import', '#', 'admin', SYSDATE(), '', NULL, '');
