-- =====================================================
-- 成本管理模块字典数据
-- 基于文档：docs/sql/cost_program_design.sql
-- 生成时间：2025-01-27
-- 模块编号：MD0001
-- =====================================================

-- 字典类型定义
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('长短期标识','cost_term_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理长短期标识'),
('设计类型','cost_design_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理产品设计类型'),
('中短期标识','cost_short_term_flag','0','admin','2025-01-27 10:58:14','',NULL,'成本管理是否中短期产品'),
('监管中短标识','cost_reg_mid_id','0','admin','2025-01-27 10:58:14','',NULL,'成本管理报监管中短标识'),
('新业务标识','cost_new_business_flag','0','admin','2025-01-27 10:58:14','',NULL,'成本管理新业务标识'),
('情景名称','cost_scenario_name','0','admin','2025-01-27 10:58:14','',NULL,'成本管理情景名称'),
('业务类型','cost_business_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理业务类型'),
('现金流类型','cost_cash_flow_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理现金流类型'),
('基点类型','cost_basis_point_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理基点类型'),
('久期类型','cost_duration_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理久期类型'),
('删除标识','cost_is_del','0','admin','2025-01-27 10:58:14','',NULL,'成本管理删除标识'),
('险种主类','cost_insurance_main_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理险种主类'),
('险种细类','cost_insurance_sub_type','0','admin','2025-01-27 10:58:14','',NULL,'成本管理险种细类');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(120, 1, '长期', 'L', 'cost_term_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:02', '', NULL, '长期保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(121, 2, '短期', 'S', 'cost_term_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:02', '', NULL, '短期保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(122, 1, '是', 'Y', 'cost_short_term_flag', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:02', '', NULL, '中短期产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(123, 2, '否', 'N', 'cost_short_term_flag', '', 'success', 'Y', '0', 'admin', '2025-05-09 16:37:02', '', NULL, '非中短期产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(124, 1, '传统险', '01', 'cost_design_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:33:21', '传统型保险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(125, 2, '分红险', '02', 'cost_design_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:33:27', '分红型保险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(126, 3, '万能险', '03', 'cost_design_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:33:32', '万能型保险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(127, 4, '投连险', '04', 'cost_design_type', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:33:37', '投资连结保险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(128, 5, '普通账户', '05', 'cost_design_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:33:42', '普通账户汇总');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(129, 1, '长期寿险', '01', 'cost_insurance_main_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:22', '长期寿险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(130, 2, '长期健康险', '02', 'cost_insurance_main_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:28', '长期健康险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(131, 3, '长期意外险', '03', 'cost_insurance_main_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:33', '长期意外险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(132, 4, '短期寿险', '04', 'cost_insurance_main_type', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:38', '短期寿险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(133, 5, '短期健康险', '05', 'cost_insurance_main_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:43', '短期健康险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(134, 6, '短期意外险', '06', 'cost_insurance_main_type', '', 'default', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:32:48', '短期意外险产品');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(135, 1, '年金险', '01', 'cost_insurance_sub_type', '', 'primary', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:38:17', '年金保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(136, 2, '两全险', '02', 'cost_insurance_sub_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:38:22', '两全保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(137, 3, '附加两全险', '03', 'cost_insurance_sub_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:38:27', '附加两全保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(138, 4, '终身寿险', '04', 'cost_insurance_sub_type', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:02', 'admin', '2025-05-13 20:38:32', '终身寿险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(139, 5, '定期寿险', '05', 'cost_insurance_sub_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:38:37', '定期寿险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(140, 6, '长期医疗险', '06', 'cost_insurance_sub_type', '', 'primary', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:38:41', '长期医疗保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(141, 7, '长期重疾险', '07', 'cost_insurance_sub_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:38:46', '长期重疾保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(142, 8, '长期防癌险', '08', 'cost_insurance_sub_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:38:51', '长期防癌保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(143, 9, '长期护理险', '09', 'cost_insurance_sub_type', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:38:56', '长期护理保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(144, 10, '长期意外伤害险', '10', 'cost_insurance_sub_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-13 20:39:01', '长期意外伤害保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(145, 11, '长期意外医疗险', '11', 'cost_insurance_sub_type', '', 'default', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '长期意外医疗保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(146, 12, '短期定期寿险', '12', 'cost_insurance_sub_type', '', 'primary', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期定期寿险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(147, 13, '短期医疗险', '13', 'cost_insurance_sub_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期医疗保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(148, 14, '短期重疾险', '14', 'cost_insurance_sub_type', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期重疾保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(149, 15, '短期防癌险', '15', 'cost_insurance_sub_type', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期防癌保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(150, 16, '短期意外伤害险', '16', 'cost_insurance_sub_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期意外伤害保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(151, 17, '短期意外医疗险', '17', 'cost_insurance_sub_type', '', 'default', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '短期意外医疗保险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(152, 1, '有效业务', '01', 'cost_business_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-15 16:22:00', '有效业务数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(153, 2, '新业务', '02', 'cost_business_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-15 16:22:05', '新业务数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(154, 1, '基本情景', '01', 'cost_scenario_name', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-22 14:35:19', '基本情景数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(155, 2, '压力情景一', '02', 'cost_scenario_name', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-22 14:34:56', '压力情景一数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(156, 3, '压力情景二', '03', 'cost_scenario_name', '', 'info', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-22 14:35:03', '压力情景二数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(157, 4, '压力情景三', '04', 'cost_scenario_name', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:03', 'admin', '2025-05-22 14:35:25', '压力情景三数据');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(158, 1, '流入', '01', 'cost_cash_flow_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '现金流入');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(159, 2, '流出', '02', 'cost_cash_flow_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '现金流出');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(160, 1, '基准情景', '0bp', 'cost_basis_point_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '基准情景(0bp)');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(161, 2, '上升50bp', '+50bp', 'cost_basis_point_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '利率上升50bp');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(162, 3, '下降50bp', '-50bp', 'cost_basis_point_type', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '利率下降50bp');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(163, 1, '修正久期', '01', 'cost_duration_type', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '修正久期计算');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(164, 2, '有效久期', '02', 'cost_duration_type', '', 'success', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '有效久期计算');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(165, 1, '是', 'Y', 'cost_new_business_flag', '', 'primary', 'Y', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '新业务');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(166, 2, '否', 'N', 'cost_new_business_flag', '', 'danger', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '非新业务');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(167, 1, '是', 'Y', 'cost_reg_mid_id', '', 'warning', 'N', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '报监管中短');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(168, 2, '否', 'N', 'cost_reg_mid_id', '', 'success', 'Y', '0', 'admin', '2025-05-09 16:37:03', '', NULL, '非报监管中短');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(170, 0, '附加年金险', '11', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:32:23', '', NULL, '附加年金险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(171, 0, '短期寿险', '12', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:32:56', '', NULL, '短期寿险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(172, 0, '短期健康险', '13', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:34:05', '', NULL, '短期健康险
');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(173, 0, '短期意外险', '14', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:34:47', '', NULL, '短期意外险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(174, 0, '附加定期寿险', '15', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:35:16', '', NULL, '附加定期寿险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(175, 0, '附加疾病险', '16', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:35:35', '', NULL, '附加疾病险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(176, 0, '附加重疾险', '17', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:36:13', '', NULL, '附加重疾险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(177, 0, '疾病险', '18', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:36:38', '', NULL, '疾病险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(178, 0, '养老年金险', '19', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:36:59', '', NULL, '养老年金险');
INSERT INTO alm.sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(179, 0, '重疾险', '20', 'cost_insurance_sub_type', NULL, 'default', 'N', '0', 'admin', '2025-05-14 10:37:19', '', NULL, '重疾险');
