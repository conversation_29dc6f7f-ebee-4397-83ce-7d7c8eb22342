-- =============================================
-- ACM模块主菜单SQL
-- 模块: acm (资产信用状况)
-- =============================================

-- 资产信用状况主菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资产信用状况', 0, 5, 'acm', null, 1, 0, 'M', '0', '0', '', 'money', 'admin', sysdate(), '', null, '资产信用状况目录');

-- 获取资产信用状况主菜单ID
SELECT @acmParentId := LAST_INSERT_ID();

-- =============================================
-- 资金运用规模表菜单SQL
-- 表编号: TB0002
-- 表名: t_asm_fund_utilization_scale
-- 模块: asm (资产配置状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表', 2423, 3, 'fundUtilizationScale', 'asm/fund/utilization/scale/index', 1, 0, 'C', '0', '0', 'asm:fund:utilization:scale:list', '#', 'admin', sysdate(), '', null, '资金运用规模表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用规模表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:scale:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 资金运用比例监管表菜单SQL
-- 表编号: TB0003
-- 表名: t_asm_fund_utilization_ratio
-- 模块: asm (资产配置状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表', 2423, 4, 'fundUtilizationRatio', 'asm/fund/utilization/ratio/index', 1, 0, 'C', '0', '0', 'asm:fund:utilization:ratio:list', '#', 'admin', sysdate(), '', null, '资金运用比例监管表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('资金运用比例监管表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'asm:fund:utilization:ratio:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 固定收益类投资资产剩余期限分布表菜单SQL
-- 表编号: TB0004
-- 表名: t_asm_fixed_income_term_dist
-- 模块: asm (资产配置状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表', 2423, 5, 'fixedIncomeTermDist', 'asm/fixed/income/term/dist/index', 1, 0, 'C', '0', '0', 'asm:fixed:income:term:dist:list', '#', 'admin', sysdate(), '', null, '固定收益类投资资产剩余期限分布表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产剩余期限分布表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'asm:fixed:income:term:dist:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 风险10日VaR值表菜单SQL
-- 表编号: TB0005
-- 表名: t_asm_risk_10day_var
-- 模块: asm (资产配置状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表', 2423, 6, 'risk10DayVar', 'asm/risk/10day/var/index', 1, 0, 'C', '0', '0', 'asm:risk:10day:var:list', '#', 'admin', sysdate(), '', null, '风险10日VaR值表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('风险10日VaR值表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'asm:risk:10day:var:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 融资杠杆比例表菜单SQL
-- 表编号: TB0007
-- 表名: t_asm_financing_leverage_ratio
-- 模块: asm (资产配置状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表', 2423, 7, 'financingLeverageRatio', 'asm/financing/leverage/ratio/index', 1, 0, 'C', '0', '0', 'asm:financing:leverage:ratio:list', '#', 'admin', sysdate(), '', null, '融资杠杆比例表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('融资杠杆比例表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'asm:financing:leverage:ratio:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 固定收益类投资资产信用评级表菜单SQL
-- 表编号: TB0008
-- 表名: t_acm_fixed_income_credit_rating
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表', @acmParentId, 1, 'fixedIncomeCreditRating', 'acm/fixed/income/credit/rating/index', 1, 0, 'C', '0', '0', 'acm:fixed:income:credit:rating:list', '#', 'admin', sysdate(), '', null, '固定收益类投资资产信用评级表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产信用评级表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:credit:rating:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 存款及同业存单表菜单SQL
-- 表编号: TB0009
-- 表名: t_acm_deposit_interbank_cd
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表', @acmParentId, 2, 'depositInterbankCd', 'acm/deposit/interbank/cd/index', 1, 0, 'C', '0', '0', 'acm:deposit:interbank:cd:list', '#', 'admin', sysdate(), '', null, '存款及同业存单表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('存款及同业存单表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:deposit:interbank:cd:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 固定收益类投资资产外部评级剩余期限分布表菜单SQL
-- 表编号: TB0010
-- 表名: t_acm_fixed_income_rating_term_dist
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表', 2795, 3, 'fixedIncomeRatingTermDist', 'acm/fixed/income/rating/term/dist/index', 1, 0, 'C', '0', '0', 'acm:fixed:income:rating:term:dist:list', '#', 'admin', sysdate(), '', null, '固定收益类投资资产外部评级剩余期限分布表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('固定收益类投资资产外部评级剩余期限分布表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:fixed:income:rating:term:dist:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 保险资产风险五级分类状况表菜单SQL
-- 表编号: TB0011
-- 表名: t_acm_asset_risk_five_level
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表', 2795, 4, 'assetRiskFiveLevel', 'acm/asset/risk/five/level/index', 1, 0, 'C', '0', '0', 'acm:asset:risk:five:level:list', '#', 'admin', sysdate(), '', null, '保险资产风险五级分类状况表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险五级分类状况表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:five:level:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 保险资产风险项目映射表菜单SQL
-- 表编号: TB0012
-- 表名: t_acm_asset_risk_item_mapping
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表', 2795, 5, 'assetRiskItemMapping', 'acm/asset/risk/item/mapping/index', 1, 0, 'C', '0', '0', 'acm:asset:risk:item:mapping:list', '#', 'admin', sysdate(), '', null, '保险资产风险项目映射表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('保险资产风险项目映射表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:asset:risk:item:mapping:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 行业集中度风险表菜单SQL
-- 表编号: TB0013
-- 表名: t_acm_industry_concentration_risk
-- 模块: acm (资产信用状况)
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表', 2795, 6, 'industryConcentrationRisk', 'acm/industry/concentration/risk/index', 1, 0, 'C', '0', '0', 'acm:industry:concentration:risk:list', '#', 'admin', sysdate(), '', null, '行业集中度风险表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表查询', @parentId, 1,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表新增', @parentId, 2,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:add',          '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表修改', @parentId, 3,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:edit',         '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表删除', @parentId, 4,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:remove',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表导出', @parentId, 5,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:export',       '#', 'admin', sysdate(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('行业集中度风险表导入', @parentId, 6,  '#', '', 1, 0, 'F', '0', '0', 'acm:industry:concentration:risk:import',       '#', 'admin', sysdate(), '', null, '');
