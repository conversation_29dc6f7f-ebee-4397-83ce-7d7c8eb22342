package com.ruoyi.adur.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 月度折现曲线利率值集工具类
 * 用于处理JSON格式的月度折现曲线数据
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public class MonthlyDiscountRateUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(MonthlyDiscountRateUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param rateMap 期限-利率映射
     * @return JSON字符串
     */
    public static String mapToJson(Map<Integer, BigDecimal> rateMap) {
        try {
            ObjectNode jsonNode = objectMapper.createObjectNode();
            for (Map.Entry<Integer, BigDecimal> entry : rateMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("转换Map到JSON失败", e);
            return "{}";
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param jsonStr JSON字符串
     * @return 期限-利率映射
     */
    public static Map<Integer, BigDecimal> jsonToMap(String jsonStr) {
        Map<Integer, BigDecimal> rateMap = new HashMap<>();
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return rateMap;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                try {
                    Integer term = Integer.parseInt(fieldName);
                    BigDecimal rate = new BigDecimal(jsonNode.get(fieldName).asText());
                    rateMap.put(term, rate);
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或利率失败: {}", fieldName);
                }
            });
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return rateMap;
    }
    
    /**
     * 获取指定期限的利率
     * 
     * @param jsonStr JSON字符串
     * @param term 期限（月份）
     * @return 利率值，如果不存在返回null
     */
    public static BigDecimal getRate(String jsonStr, Integer term) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode rateNode = jsonNode.get(term.toString());
            if (rateNode != null) {
                return new BigDecimal(rateNode.asText());
            }
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return null;
    }
    
    /**
     * 设置指定期限的利率
     * 
     * @param jsonStr 原JSON字符串
     * @param term 期限（月份）
     * @param rate 利率值
     * @return 更新后的JSON字符串
     */
    public static String setRate(String jsonStr, Integer term, BigDecimal rate) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            jsonNode.put(term.toString(), rate);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("设置利率失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 批量设置利率
     * 
     * @param jsonStr 原JSON字符串
     * @param rateMap 期限-利率映射
     * @return 更新后的JSON字符串
     */
    public static String setRates(String jsonStr, Map<Integer, BigDecimal> rateMap) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            for (Map.Entry<Integer, BigDecimal> entry : rateMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("批量设置利率失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 创建完整的利率值集（0-600期）
     * 
     * @param defaultRate 默认利率值
     * @return JSON字符串
     */
    public static String createFullRateSet(BigDecimal defaultRate) {
        Map<Integer, BigDecimal> rateMap = new HashMap<>();
        for (int i = 0; i <= 600; i++) {
            rateMap.put(i, defaultRate);
        }
        return mapToJson(rateMap);
    }
    
    /**
     * 验证JSON格式是否正确
     * 
     * @param jsonStr JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            objectMapper.readTree(jsonStr);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 获取JSON中包含的期限数量
     * 
     * @param jsonStr JSON字符串
     * @return 期限数量
     */
    public static int getTermCount(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return 0;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.size();
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return 0;
        }
    }
    
    /**
     * 线性插值计算
     * 用于计算非整数年期限的利率
     * 
     * @param jsonStr JSON字符串
     * @param targetTerm 目标期限（月份）
     * @return 插值后的利率
     */
    public static BigDecimal linearInterpolation(String jsonStr, double targetTerm) {
        Map<Integer, BigDecimal> rateMap = jsonToMap(jsonStr);
        
        // 转换为年份
        double targetYear = targetTerm / 12.0;
        
        // 找到临近的两个整数年
        int lowerYear = (int) Math.floor(targetYear);
        int upperYear = (int) Math.ceil(targetYear);
        
        int lowerTerm = lowerYear * 12;
        int upperTerm = upperYear * 12;
        
        BigDecimal lowerRate = rateMap.get(lowerTerm);
        BigDecimal upperRate = rateMap.get(upperTerm);
        
        if (lowerRate == null || upperRate == null) {
            logger.warn("缺少插值所需的利率数据: lowerTerm={}, upperTerm={}", lowerTerm, upperTerm);
            return BigDecimal.ZERO;
        }
        
        if (lowerYear == upperYear) {
            return lowerRate;
        }
        
        // 线性插值公式: rate = lowerRate + (upperRate - lowerRate) * (targetYear - lowerYear) / (upperYear - lowerYear)
        double weight = (targetYear - lowerYear) / (upperYear - lowerYear);
        BigDecimal rateDiff = upperRate.subtract(lowerRate);
        BigDecimal interpolatedRate = lowerRate.add(rateDiff.multiply(BigDecimal.valueOf(weight)));
        
        return interpolatedRate;
    }
}
