-- 现金流测试模块字典数据
-- 基于 docs/sql/cash_flow_test_program_design.sql 中的DDL生成

-- =============================================
-- 字典类型数据
-- =============================================

-- 情景名称字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('情景名称','cft_scenario_name','0','admin','2025-06-16 10:00:00','',NULL,'现金流测试情景名称，用于区分不同的测试场景');

-- 财务费用类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('财务费用类型','cft_financial_expense_type','0','admin','2025-06-16 10:00:00','',NULL,'财务预算费用分类类型');

-- 拆分比例类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('拆分比例类型','cft_split_ratio_type','0','admin','2025-06-16 10:00:00','',NULL,'费用拆分比例分类，用于区分不同的拆分计算方式');

-- =============================================
-- 字典数据
-- =============================================

-- 情景名称字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'基本情景','01','cft_scenario_name',NULL,'primary','Y','0','admin','2025-06-16 10:00:00','',NULL,'正常市场环境下的现金流测试场景'),
(2,'压力情景','02','cft_scenario_name',NULL,'warning','N','0','admin','2025-06-16 10:00:00','',NULL,'压力测试环境下的现金流测试场景');

-- 财务费用类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'财务业管费','01','cft_financial_expense_type',NULL,'success','Y','0','admin','2025-06-16 10:00:00','',NULL,'业务及管理费用'),
(2,'财务税金及附加','02','cft_financial_expense_type',NULL,'info','N','0','admin','2025-06-16 10:00:00','',NULL,'税金及附加费用');

-- 拆分比例类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'拆分比例1','01','cft_split_ratio_type',NULL,'primary','Y','0','admin','2025-06-16 10:00:00','',NULL,'按季度汇总计算的拆分比例'),
(2,'拆分比例2','02','cft_split_ratio_type',NULL,'info','N','0','admin','2025-06-16 10:00:00','',NULL,'按月份计算的拆分比例');
