-- =============================================
-- 资产信用状况模块 DDL
-- 基于 acm_program_design.md 文档生成
-- 数据库: MySQL 8.0
-- 字符集: utf8
-- =============================================

-- =============================================
-- 表名: t_asm_fund_utilization_scale
-- 表中文名: 资金运用规模表
-- 表编号: TB0002
-- 表描述: 存储资金运用规模统计数据，通过汇总资产整体明细表数据生成
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_fund_utilization_scale` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `item_name` varchar(100) NOT NULL COMMENT '项目名称',
    `item_classification_level` varchar(5) NOT NULL COMMENT '项目分级标识',
    `data_type` varchar(20) NOT NULL COMMENT '数据类型，1:账面余额,2:账面价值',
    `general_account` decimal(18,2) DEFAULT 0.00 COMMENT '普通账户金额',
    `traditional_account` decimal(18,2) DEFAULT 0.00 COMMENT '传统账户金额',
    `capital_supplement_bond_account` decimal(18,2) DEFAULT 0.00 COMMENT '资本补充债账户金额',
    `bonus_account` decimal(18,2) DEFAULT 0.00 COMMENT '分红账户金额',
    `universal_account` decimal(18,2) DEFAULT 0.00 COMMENT '万能账户金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item_level_type` (`accounting_period`, `item_name`, `item_classification_level`, `data_type`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_data_type` (`data_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0002 - 资金运用规模表';

-- =============================================
-- 表名: t_asm_fund_utilization_ratio
-- 表中文名: 资金运用比例监管表
-- 表编号: TB0003
-- 表描述: 存储资金运用比例监管数据，用于监控特定资产类型在各账户中的使用情况
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_fund_utilization_ratio` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类，引用字典ast_asset_sub_sub_category',
    `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
    `book_balance` decimal(18,2) DEFAULT 0.00 COMMENT '账面余额金额',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_asset_account` (`accounting_period`, `asset_sub_sub_category`, `account_name`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_asset_sub_sub_category` (`asset_sub_sub_category`),
    KEY `idx_account_name` (`account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0003 - 资金运用比例监管表';

-- =============================================
-- 表名: t_asm_fixed_income_term_dist
-- 表中文名: 固定收益类投资资产剩余期限分布表
-- 表编号: TB0004
-- 表描述: 存储固定收益类投资资产的剩余期限分布情况，用于分析不同类型固收资产的期限结构
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_fixed_income_term_dist` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识，引用字典ast_domestic_foreign',
    `fixed_income_term_category` varchar(50) NOT NULL COMMENT '固收资产剩余期限资产分类，引用字典ast_fixed_income_term_category',
    `remaining_term_flag` varchar(50) NOT NULL COMMENT '剩余期限标识描述，01:1年及以内,02:1-3年（含3年）,03:3-5年(含5年),04:5-7年（含7年）,05:7-10年（含10年）,06:10-15年（含15年）,07:15年以上,08:无明确期限',
    `book_balance` decimal(28,10) DEFAULT 0.00 COMMENT '账面余额金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_domestic_category_term` (`accounting_period`, `domestic_foreign`, `fixed_income_term_category`, `remaining_term_flag`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_domestic_foreign` (`domestic_foreign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0004 - 固定收益类投资资产剩余期限分布表';

-- =============================================
-- 表名: t_asm_risk_10day_var
-- 表中文名: 风险10日VaR值表
-- 表编号: TB0005
-- 表描述: 存储风险10日VaR值统计数据，用于分析不同资产类型的风险价值分布
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_risk_10day_var` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识，引用字典ast_domestic_foreign',
    `item_category` varchar(50) NOT NULL COMMENT '项目分类，引用字典ast_asset_sub_sub_category（债券型基金、上市普通股票、证券投资基金等）',
    `sample_period` varchar(10) NOT NULL COMMENT '样本期限，01:1年,02:3年',
    `var_value` decimal(28,10) DEFAULT 0.00 COMMENT 'VAR值',
    `book_value` decimal(28,10) DEFAULT 0.00 COMMENT '账面价值金额',
    `var_book_value_ratio` decimal(10,4) DEFAULT 0.0000 COMMENT 'VAR值与账面价值比率',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_domestic_category_sample` (`accounting_period`, `domestic_foreign`, `item_category`, `sample_period`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_domestic_foreign` (`domestic_foreign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0005 - 风险10日VaR值表';

-- =============================================
-- 表名: t_asm_financing_leverage_ratio
-- 表中文名: 融资杠杆比例表
-- 表编号: TB0007
-- 表描述: 存储融资杠杆比例统计数据，用于监控融资杠杆风险
-- =============================================
CREATE TABLE IF NOT EXISTS `t_asm_financing_leverage_ratio` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `item_name` varchar(100) NOT NULL COMMENT '项目名称',
    `book_value` decimal(28,10) DEFAULT 0.00 COMMENT '账面价值金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0007 - 融资杠杆比例表';

-- =============================================
-- 表名: t_acm_fixed_income_credit_rating
-- 表中文名: 固定收益类投资资产信用评级表
-- 表编号: TB0008
-- 表描述: 存储固定收益类投资资产的信用评级分布情况，用于分析不同信用等级资产的分布
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_fixed_income_credit_rating` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识，引用字典ast_domestic_foreign',
    `fixed_income_term_category` varchar(50) NOT NULL COMMENT '固收资产剩余期限资产分类，引用字典ast_fixed_income_term_category',
    `credit_rating_category` varchar(20) NOT NULL COMMENT '信用评级分类，引用字典ast_credit_rating',
    `book_balance` decimal(28,10) DEFAULT 0.00 COMMENT '账面余额金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_domestic_category_rating` (`accounting_period`, `domestic_foreign`, `fixed_income_term_category`, `credit_rating_category`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_domestic_foreign` (`domestic_foreign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0008 - 固定收益类投资资产信用评级表';

-- =============================================
-- 表名: t_acm_deposit_interbank_cd
-- 表中文名: 存款及同业存单表
-- 表编号: TB0009
-- 表描述: 存储存款及同业存单的银行分类分布情况，用于分析不同银行类型的资产配置
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_deposit_interbank_cd` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类，引用字典ast_asset_sub_sub_category',
    `bank_classification` varchar(100) NOT NULL COMMENT '银行分类，引用字典ast_bank_classification',
    `book_value` decimal(28,10) DEFAULT 0.00 COMMENT '账面价值金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_asset_bank` (`accounting_period`, `asset_sub_sub_category`, `bank_classification`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_asset_sub_sub_category` (`asset_sub_sub_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0009 - 存款及同业存单表';

-- =============================================
-- 表名: t_acm_fixed_income_rating_term_dist
-- 表中文名: 固定收益类投资资产外部评级剩余期限分布表
-- 表编号: TB0010
-- 表描述: 存储固定收益类投资资产的外部评级与剩余期限交叉分布情况，用于分析不同信用等级资产的期限结构
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_fixed_income_rating_term_dist` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `domestic_foreign` varchar(20) NOT NULL COMMENT '境内外标识，引用字典ast_domestic_foreign',
    `credit_rating_category` varchar(20) NOT NULL COMMENT '信用评级分类，引用字典ast_credit_rating',
    `fixed_income_term_category` varchar(50) NOT NULL COMMENT '固收资产剩余期限资产分类，引用字典ast_fixed_income_term_category',
    `book_balance` decimal(28,10) DEFAULT 0.00 COMMENT '账面余额金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_domestic_rating_term` (`accounting_period`, `domestic_foreign`, `credit_rating_category`, `fixed_income_term_category`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_domestic_foreign` (`domestic_foreign`),
    KEY `idx_credit_rating_category` (`credit_rating_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0010 - 固定收益类投资资产外部评级剩余期限分布表';

-- =============================================
-- 表名: t_acm_asset_risk_five_level
-- 表中文名: 保险资产风险五级分类状况表
-- 表编号: TB0011
-- 表描述: 存储保险资产的风险五级分类状况，用于分析不同类型资产的风险分布
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_asset_risk_five_level` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `item_name` varchar(50) NOT NULL COMMENT '项目名称，01:权益类,02:固定收益类,03:不动产类,04:其他投资资产',
    `five_level_statistics_flag` varchar(50) NOT NULL COMMENT '五级分类资产统计标识，引用字典ast_five_level_statistics_flag',
    `five_level_classification` varchar(20) NOT NULL COMMENT '五级分类，引用字典ast_five_level_classification',
    `book_balance` decimal(28,10) DEFAULT 0.00 COMMENT '账面余额金额',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item_flag_classification` (`accounting_period`, `item_name`, `five_level_statistics_flag`, `five_level_classification`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_item_name` (`item_name`),
    KEY `idx_five_level_statistics_flag` (`five_level_statistics_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0011 - 保险资产风险五级分类状况表';

-- =============================================
-- 表名: t_acm_asset_risk_item_mapping
-- 表中文名: 保险资产风险项目映射表
-- 表编号: TB0012
-- 表描述: 存储保险资产风险项目与五级分类统计标识的映射关系，用于TB0011表的数据生成
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_asset_risk_item_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `item_name` varchar(50) NOT NULL COMMENT '项目名称，01:权益类,02:固定收益类,03:不动产类,04:其他投资资产',
    `five_level_statistics_flag` varchar(50) NOT NULL COMMENT '五级分类资产统计标识，引用字典ast_five_level_statistics_flag',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_item_flag` (`accounting_period`, `item_name`, `five_level_statistics_flag`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_item_name` (`item_name`),
    KEY `idx_five_level_statistics_flag` (`five_level_statistics_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0012 - 保险资产风险项目映射表';

-- =============================================
-- 表名: t_acm_industry_concentration_risk
-- 表中文名: 行业集中度风险表
-- 表编号: TB0013
-- 表描述: 存储行业集中度风险统计数据，用于分析不同行业的资产集中度风险
-- =============================================
CREATE TABLE IF NOT EXISTS `t_acm_industry_concentration_risk` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202406）',
    `industry_name` varchar(50) NOT NULL COMMENT '行业名称',
    `industry_statistics_flag` varchar(50) NOT NULL COMMENT '行业统计标识，引用字典acm_industry_statistics',
    `book_value` decimal(28,10) DEFAULT 0.00 COMMENT '账面价值金额',
    `weight_percentage` decimal(10,2) DEFAULT 0.00 COMMENT '权重百分比',
    `industry_concentration` decimal(10,2) DEFAULT 0.00 COMMENT '行业集中度',
    `counterparty_ranking` varchar(20) DEFAULT NULL COMMENT '交易对手排序',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_industry_name` (`accounting_period`, `industry_name`),
    KEY `idx_accounting_period` (`accounting_period`),
    KEY `idx_industry_name` (`industry_name`),
    KEY `idx_industry_statistics_flag` (`industry_statistics_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0013 - 行业集中度风险表';
