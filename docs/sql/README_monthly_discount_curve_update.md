# 月度折现曲线表更新说明

## 更新概述

根据最新的字段列表要求，对TB0005月度折现曲线表不含价差进行了重大更新，主要变化是将原来的601个独立期限字段（term_0到term_600）合并为一个JSON格式的字段。

## 主要变化

### 1. 表结构简化

**旧版本结构**：
- 基础字段：9个
- 期限字段：601个（term_0到term_600）
- 审计字段：5个
- **总计**：615个字段

**新版本结构**：
- 基础字段：9个
- JSON字段：1个（monthly_discount_rate_set）
- 审计字段：5个
- **总计**：15个字段

### 2. 数据存储方式

**旧版本**：
```sql
term_0 decimal(10,6) DEFAULT 0,
term_1 decimal(10,6) DEFAULT 0,
...
term_600 decimal(10,6) DEFAULT 0
```

**新版本**：
```sql
monthly_discount_rate_set text COMMENT '月度折现曲线利率值集'
```

**JSON格式示例**：
```json
{
  "0": 0.025000,
  "1": 0.025100,
  "2": 0.025200,
  ...
  "600": 0.045000
}
```

## 文件更新列表

### 1. 设计文档更新
- **文件**：`docs/design/adur_program_design.md`
- **更新内容**：
  - 添加TB0005月度折现曲线表不含价差的完整字段定义
  - 更新表编号顺序
  - 添加计算逻辑说明
  - 新增数据库脚本文件引用部分

### 2. 新增SQL文件
- **主DDL文件**：`docs/sql/t_adur_monthly_discount_curve_new_ddl.sql`
  - 新版本表结构定义
  - 详细的字段说明和计算逻辑
  - 使用示例和最佳实践

- **数据迁移脚本**：`docs/sql/migrate_monthly_discount_curve.sql`
  - 完整的数据迁移方案
  - 数据验证查询
  - 回滚脚本

### 3. Java工具类
- **工具类文件**：`docs/sql/MonthlyDiscountRateUtil.java`
- **主要功能**：
  - JSON数据的读写操作
  - 期限利率的获取和设置
  - 线性插值计算
  - 数据格式验证

## 计算逻辑说明

### 月度折现曲线利率值集计算规则

1. **折现曲线标识=0 AND 日期类型=发行时点**
   - 取值："-"（在JSON中用特殊值表示）

2. **折现曲线标识=0 AND 日期类型=评估时点**
   - 取值：久期资产明细表.到期收益率

3. **折现曲线标识≠0**
   - **月份为12的整数倍**（0/12/24/36/48/……/600）：
     - 计算对应年份数：月份/12
     - 在年度折现曲线表中查找期限为（月份/12）年的收益率
   - **月份不为12的整数倍**：
     - 取临近两个整数年的收益率进行线性插值

## 优势分析

### 1. 存储效率
- **字段数量**：从615个字段减少到15个字段
- **表结构**：更加简洁，易于维护
- **索引优化**：减少了索引复杂度

### 2. 灵活性
- **动态字段**：JSON格式支持灵活的期限配置
- **扩展性**：易于添加新的期限点
- **数据完整性**：单个字段存储完整的利率曲线

### 3. 开发效率
- **工具支持**：提供专用的Java工具类
- **操作简化**：统一的JSON操作接口
- **维护便利**：减少了字段管理复杂度

## 注意事项

### 1. 性能考虑
- JSON查询可能比直接字段查询稍慢
- 建议对频繁查询的期限建立函数索引
- 大量数据操作时注意内存使用

### 2. 兼容性
- 需要MySQL 5.7+版本支持JSON函数
- 现有代码需要适配新的数据结构
- 建议逐步迁移，确保系统稳定

### 3. 数据迁移
- 迁移前务必备份原始数据
- 建议在测试环境先验证迁移脚本
- 迁移后进行充分的数据验证

## 使用建议

### 1. 新项目
- 直接使用新版本DDL
- 采用提供的Java工具类
- 遵循JSON数据操作最佳实践

### 2. 现有项目
- 评估迁移成本和收益
- 制定详细的迁移计划
- 考虑分阶段迁移策略

### 3. 开发规范
- 使用工具类进行JSON操作
- 避免直接拼接JSON字符串
- 注意数据类型转换和精度处理

## 技术支持

如有疑问或需要技术支持，请参考：
1. 设计文档：`docs/design/adur_program_design.md`
2. DDL文件：`docs/sql/t_adur_monthly_discount_curve_new_ddl.sql`
3. 工具类：`docs/sql/MonthlyDiscountRateUtil.java`
4. 迁移脚本：`docs/sql/migrate_monthly_discount_curve.sql`
