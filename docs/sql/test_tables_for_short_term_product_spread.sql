-- 中短存续期产品利差计算任务测试所需的表结构
-- 包含所有相关的基础表和计算表

-- 1. 产品属性表 (TB0001)
CREATE TABLE IF NOT EXISTS `t_base_product_attribute` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `insurance_main_type` varchar(50) NOT NULL COMMENT '险种主类，如：长期寿险',
  `insurance_sub_type` varchar(50) NOT NULL COMMENT '险种细类，如：年金险、两全险、附加两全险等',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，如：传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `reg_mid_id` char(1) NOT NULL DEFAULT 'N' COMMENT '报监管中短标识，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,6) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户名称',
  `new_business_flag` char(1) DEFAULT 'Y' COMMENT '新业务标识，Y-是，N-否',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品属性表';

-- 2. 会计准备金明细表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_base_accounting_reserve_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `valid_policy_count` int(11) DEFAULT 0 COMMENT '有效保单件数',
  `accumulated_premium` decimal(28,10) DEFAULT 0 COMMENT '存量累计规模保费',
  `account_value` decimal(28,10) DEFAULT 0 COMMENT '账户价值',
  `dividend_provision` decimal(28,10) DEFAULT 0 COMMENT '红利预提',
  `best_estimate` decimal(28,10) DEFAULT 0 COMMENT '最优估计',
  `risk_margin` decimal(28,10) DEFAULT 0 COMMENT '风险边际',
  `residual_margin` decimal(28,10) DEFAULT 0 COMMENT '剩余边际',
  `unmodeled_reserve` decimal(28,10) DEFAULT 0 COMMENT '未建模准备金',
  `waiver_reserve` decimal(28,10) DEFAULT 0 COMMENT '豁免准备金',
  `persistence_bonus_reserve` decimal(28,10) DEFAULT 0 COMMENT '持续奖准备金',
  `long_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '长期险未到期',
  `short_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '短险未到期',
  `unearned_premium_reserve` decimal(28,10) DEFAULT 0 COMMENT '未到期责任准备金',
  `reported_unpaid` decimal(28,10) DEFAULT 0 COMMENT '已报未决',
  `incurred_unreported` decimal(28,10) DEFAULT 0 COMMENT '未报未决',
  `claim_expense_reserve` decimal(28,10) DEFAULT 0 COMMENT '理赔费用准备金',
  `outstanding_claim_reserve` decimal(28,10) DEFAULT 0 COMMENT '未决赔款准备金',
  `total_accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金合计',
  `reinsurance_unearned` decimal(28,10) DEFAULT 0 COMMENT '应收分保未到期责任准备金',
  `reinsurance_reported` decimal(28,10) DEFAULT 0 COMMENT '应收分保已报未决',
  `reinsurance_unreported` decimal(28,10) DEFAULT 0 COMMENT '应收分保未报未决',
  `reinsurance_claim_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保未决合计',
  `reinsurance_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保合计',
  `lapsed_policy_value` decimal(28,10) DEFAULT 0 COMMENT '失效保单现价',
  `fractional_month_dividend` decimal(28,10) DEFAULT 0 COMMENT '零头月红利',
  `unpaid_dividend` decimal(28,10) DEFAULT 0 COMMENT '应付未付红利',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会计准备金明细表';

-- 3. 分产品统计表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_cost_product_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点法定准备金',
  `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `business_type`, `accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品统计表';

-- 4. 分产品有效成本率表 (TB0010)
CREATE TABLE IF NOT EXISTS `t_cost_product_effective_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '产品有效成本率',
  `cash_flow_set` text COMMENT 'JSON格式存储现金流数据',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`, `design_type`, `short_term_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品有效成本率表';

-- 5. 子账户收益率表 (TB0014)
CREATE TABLE IF NOT EXISTS `t_base_sub_account_yield_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(20) NOT NULL COMMENT '统计期间，格式：YYYYMM',
  `account_name` varchar(50) NOT NULL COMMENT '子账户名称',
  `accounting_yield_rate` decimal(10,6) DEFAULT 0 COMMENT '年化会计投资收益率，以百分比表示',
  `comprehensive_yield_rate` decimal(10,6) DEFAULT 0 COMMENT '年化综合投资收益率，以百分比表示',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='子账户收益率表';

-- 6. 分产品保费收入明细表 (TB0015)
CREATE TABLE IF NOT EXISTS `t_base_product_premium_income_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `current_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(本月)',
  `current_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(本月)',
  `current_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(本月)',
  `current_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(本月)',
  `current_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(本月)',
  `current_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品保费收入明细表';

-- 7. 中短存续期产品利差表 (TB0016)
CREATE TABLE IF NOT EXISTS `t_cost_short_term_product_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(20) NOT NULL COMMENT '设计类型，如：传统型',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户名称',
  `new_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '新单规模保费',
  `accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金',
  `investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款',
  `accounting_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化会计投资收益率',
  `comprehensive_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化综合投资收益率',
  `liability_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债资金成本率',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债有效成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='中短存续期产品利差表';
