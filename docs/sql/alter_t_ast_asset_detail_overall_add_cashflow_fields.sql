-- =============================================
-- 整体资产明细表字段添加脚本
-- 表名：t_ast_asset_detail_overall
-- 功能：添加现金流相关字段
-- 说明：为支持ADUR模块计算，添加发行时点和评估时点现金流值集字段
-- =============================================

-- 检查表是否存在
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 't_ast_asset_detail_overall';

-- 检查字段是否已存在
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 't_ast_asset_detail_overall' 
AND column_name = 'issue_cashflow_set';

SELECT COUNT(*) FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 't_ast_asset_detail_overall' 
AND column_name = 'eval_cashflow_set';

-- 添加发行时点现金流值集字段（如果不存在）
ALTER TABLE `t_ast_asset_detail_overall` 
ADD COLUMN IF NOT EXISTS `issue_cashflow_set` mediumtext COMMENT '发行时点现金流值集，JSON格式存储';

-- 添加评估时点现金流值集字段（如果不存在）
ALTER TABLE `t_ast_asset_detail_overall` 
ADD COLUMN IF NOT EXISTS `eval_cashflow_set` mediumtext COMMENT '评估时点现金流值集，JSON格式存储';

-- 验证字段添加结果
DESCRIBE `t_ast_asset_detail_overall`;

-- 显示表结构
SHOW CREATE TABLE `t_ast_asset_detail_overall`;

-- =============================================
-- 字段说明
-- =============================================
/*
新增字段说明：

1. issue_cashflow_set (mediumtext)
   - 发行时点现金流值集
   - JSON格式存储，如：{"0":0,"1":5000,"2":5000,"12":1005000}
   - 用于ADUR模块UC0010计算发行时点价差

2. eval_cashflow_set (mediumtext)
   - 评估时点现金流值集
   - JSON格式存储，如：{"0":0,"1":5000,"2":5000,"12":1005000}
   - 用于ADUR模块UC0010计算评估时点价差、利差久期等指标

数据来源：
- 这些字段的数据通常从现金流计算系统导入
- 或者通过ADUR模块的现金流计算功能生成
- 在久期资产明细数据生成过程中会被读取和使用

使用场景：
- UC0003：生成久期资产明细数据时作为基础数据源
- UC0010：计算久期指标时读取现金流数据进行计算
- 价差计算：使用goseek方法计算发行时点价差和评估时点价差
- 利差久期计算：复杂的向量计算需要现金流数据
*/
