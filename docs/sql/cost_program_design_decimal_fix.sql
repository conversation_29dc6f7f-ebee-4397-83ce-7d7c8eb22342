-- =====================================================
-- 成本管理模块金额字段精度修正DDL语句
-- 基于文档：docs/design/cost_program_design.md 2.3章节
-- 生成时间：2025-01-27
-- 模块编号：MD0001
-- 说明：将金额字段统一修改为decimal(28,10)类型
-- =====================================================

-- 修正产品属性表 (TB0001) 金额字段
ALTER TABLE `t_base_product_attribute` 
MODIFY COLUMN `guaranteed_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%';

-- 修正万能平均结算利率表 (TB0002) 金额字段
ALTER TABLE `t_base_universal_avg_settlement_rate` 
MODIFY COLUMN `guaranteed_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
MODIFY COLUMN `avg_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点平均结息利率',
MODIFY COLUMN `avg_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末平均结息利率',
MODIFY COLUMN `avg_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末平均结息利率',
MODIFY COLUMN `avg_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末平均结息利率';

-- 修正分红方案表 (TB0003) 金额字段
ALTER TABLE `t_base_dividend_fund_cost_rate` 
MODIFY COLUMN `guaranteed_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
MODIFY COLUMN `investment_return_rate` decimal(28,10) DEFAULT 0 COMMENT '投资收益率假设',
MODIFY COLUMN `dividend_ratio` decimal(28,10) DEFAULT 0 COMMENT '分红比例',
MODIFY COLUMN `fund_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点资金成本率',
MODIFY COLUMN `fund_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末资金成本率';

-- 修正法定准备金明细表 (TB0004) 金额字段
ALTER TABLE `t_base_statutory_reserve_detail` 
MODIFY COLUMN `accumulated_premium` decimal(28,10) DEFAULT 0 COMMENT '存量累计规模保费',
MODIFY COLUMN `account_value` decimal(28,10) DEFAULT 0 COMMENT '账户价值',
MODIFY COLUMN `statutory_reserve` decimal(28,10) DEFAULT 0 COMMENT '法定/非单位准备金',
MODIFY COLUMN `guaranteed_rate_reserve` decimal(28,10) DEFAULT 0 COMMENT '保证利率准备金',
MODIFY COLUMN `lapsed_policy_value` decimal(28,10) DEFAULT 0 COMMENT '失效单现价',
MODIFY COLUMN `waiver_reserve` decimal(28,10) DEFAULT 0 COMMENT '豁免责任准备金',
MODIFY COLUMN `unmodeled_reserve` decimal(28,10) DEFAULT 0 COMMENT '未建模准备金',
MODIFY COLUMN `persistence_bonus_reserve` decimal(28,10) DEFAULT 0 COMMENT '持续奖准备金',
MODIFY COLUMN `long_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '长期未到期准备金',
MODIFY COLUMN `short_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '短险未到期准备金',
MODIFY COLUMN `unearned_premium_reserve` decimal(28,10) DEFAULT 0 COMMENT '未到期责任准备金',
MODIFY COLUMN `reported_unpaid` decimal(28,10) DEFAULT 0 COMMENT '已报未决赔款',
MODIFY COLUMN `incurred_unreported` decimal(28,10) DEFAULT 0 COMMENT '未报未决赔款',
MODIFY COLUMN `claim_expense_reserve` decimal(28,10) DEFAULT 0 COMMENT '理赔费用准备金',
MODIFY COLUMN `outstanding_claim_reserve` decimal(28,10) DEFAULT 0 COMMENT '未决赔款准备金',
MODIFY COLUMN `total_statutory_reserve` decimal(28,10) DEFAULT 0 COMMENT '法定准备金合计';

-- 修正会计准备金明细表 (TB0005) 金额字段
ALTER TABLE `t_base_accounting_reserve_detail` 
MODIFY COLUMN `accumulated_premium` decimal(28,10) DEFAULT 0 COMMENT '存量累计规模保费',
MODIFY COLUMN `account_value` decimal(28,10) DEFAULT 0 COMMENT '账户价值',
MODIFY COLUMN `dividend_provision` decimal(28,10) DEFAULT 0 COMMENT '红利预提',
MODIFY COLUMN `best_estimate` decimal(28,10) DEFAULT 0 COMMENT '最优估计',
MODIFY COLUMN `risk_margin` decimal(28,10) DEFAULT 0 COMMENT '风险边际',
MODIFY COLUMN `residual_margin` decimal(28,10) DEFAULT 0 COMMENT '剩余边际',
MODIFY COLUMN `unmodeled_reserve` decimal(28,10) DEFAULT 0 COMMENT '未建模准备金',
MODIFY COLUMN `waiver_reserve` decimal(28,10) DEFAULT 0 COMMENT '豁免准备金',
MODIFY COLUMN `persistence_bonus_reserve` decimal(28,10) DEFAULT 0 COMMENT '持续奖准备金',
MODIFY COLUMN `long_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '长期险未到期准备金',
MODIFY COLUMN `short_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '短险未到期准备金',
MODIFY COLUMN `unearned_premium_reserve` decimal(28,10) DEFAULT 0 COMMENT '未到期责任准备金',
MODIFY COLUMN `reported_unpaid` decimal(28,10) DEFAULT 0 COMMENT '已报未决赔款',
MODIFY COLUMN `incurred_unreported` decimal(28,10) DEFAULT 0 COMMENT '未报未决赔款',
MODIFY COLUMN `claim_expense_reserve` decimal(28,10) DEFAULT 0 COMMENT '理赔费用准备金',
MODIFY COLUMN `outstanding_claim_reserve` decimal(28,10) DEFAULT 0 COMMENT '未决赔款准备金',
MODIFY COLUMN `total_accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金合计',
MODIFY COLUMN `reinsurance_unearned` decimal(28,10) DEFAULT 0 COMMENT '应收分保未到期责任准备金',
MODIFY COLUMN `reinsurance_reported` decimal(28,10) DEFAULT 0 COMMENT '应收分保已报未决',
MODIFY COLUMN `reinsurance_unreported` decimal(28,10) DEFAULT 0 COMMENT '应收分保未报未决',
MODIFY COLUMN `reinsurance_claim_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保未决合计',
MODIFY COLUMN `reinsurance_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保合计',
MODIFY COLUMN `lapsed_policy_value` decimal(28,10) DEFAULT 0 COMMENT '失效保单现价',
MODIFY COLUMN `fractional_month_dividend` decimal(28,10) DEFAULT 0 COMMENT '零头月红利',
MODIFY COLUMN `unpaid_dividend` decimal(28,10) DEFAULT 0 COMMENT '应付未付红利';

-- 修正法定准备金预测表 (TB0006) 金额字段
ALTER TABLE `t_base_statutory_reserve_forecast` 
MODIFY COLUMN `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
MODIFY COLUMN `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
MODIFY COLUMN `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金';

-- 修正分产品统计表 (TB0007) 金额字段
ALTER TABLE `t_cost_product_statistics` 
MODIFY COLUMN `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '当前法定准备金',
MODIFY COLUMN `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
MODIFY COLUMN `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
MODIFY COLUMN `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
MODIFY COLUMN `fund_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '当前资金成本率',
MODIFY COLUMN `fund_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
MODIFY COLUMN `guaranteed_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '当前保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末保证成本率';

-- 修正汇总统计表 (TB0008) 金额字段
ALTER TABLE `t_cost_summary_statistics` 
MODIFY COLUMN `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '当前法定准备金',
MODIFY COLUMN `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
MODIFY COLUMN `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
MODIFY COLUMN `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
MODIFY COLUMN `fund_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '当前资金成本率',
MODIFY COLUMN `fund_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
MODIFY COLUMN `guaranteed_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '当前保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末保证成本率';

-- 修正分账户汇总表 (TB0009) 金额字段
ALTER TABLE `t_cost_account_summary`
MODIFY COLUMN `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点法定准备金',
MODIFY COLUMN `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
MODIFY COLUMN `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
MODIFY COLUMN `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
MODIFY COLUMN `fund_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点资金成本率',
MODIFY COLUMN `fund_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
MODIFY COLUMN `fund_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
MODIFY COLUMN `guaranteed_cost_rate_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
MODIFY COLUMN `guaranteed_cost_rate_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末保证成本率';

-- 修正分产品有效成本率表 (TB0010) 金额字段
ALTER TABLE `t_cost_product_effective_rate`
MODIFY COLUMN `effective_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '产品有效成本率';

-- 修正分账户有效成本率表 (TB0011) 金额字段
ALTER TABLE `t_cost_account_effective_rate`
MODIFY COLUMN `effective_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '账户有效成本率';

-- 修正保费收入明细表 (TB0013) 金额字段
ALTER TABLE `t_base_premium_income_detail`
MODIFY COLUMN `current_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(本月)',
MODIFY COLUMN `current_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(本月)',
MODIFY COLUMN `current_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(本月)',
MODIFY COLUMN `current_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(本月)',
MODIFY COLUMN `current_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(本月)',
MODIFY COLUMN `current_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(本月)',
MODIFY COLUMN `current_ul_renewal` decimal(28,10) DEFAULT 0 COMMENT '万能投连-续期(本月)',
MODIFY COLUMN `current_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(本月)',
MODIFY COLUMN `current_ul_total` decimal(28,10) DEFAULT 0 COMMENT '万能投连-合计(本月)',
MODIFY COLUMN `current_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(本月)',
MODIFY COLUMN `current_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(本月)',
MODIFY COLUMN `current_surrender` decimal(28,10) DEFAULT 0 COMMENT '退保金(本月)',
MODIFY COLUMN `current_ul_withdraw` decimal(28,10) DEFAULT 0 COMMENT '万能投连领取(本月)',
MODIFY COLUMN `current_claim` decimal(28,10) DEFAULT 0 COMMENT '赔款支出(本月)',
MODIFY COLUMN `current_medical` decimal(28,10) DEFAULT 0 COMMENT '死伤医疗给付(本月)',
MODIFY COLUMN `current_maturity` decimal(28,10) DEFAULT 0 COMMENT '满期给付(本月)',
MODIFY COLUMN `current_annuity` decimal(28,10) DEFAULT 0 COMMENT '年金给付(本月)',
MODIFY COLUMN `current_ul_claim` decimal(28,10) DEFAULT 0 COMMENT '万能投连-赔款支出(本月)',
MODIFY COLUMN `current_ul_medical` decimal(28,10) DEFAULT 0 COMMENT '万能投连-死伤医疗给付(本月)',
MODIFY COLUMN `current_ul_maturity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-满期给付(本月)',
MODIFY COLUMN `current_ul_annuity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-年金给付(本月)',
MODIFY COLUMN `current_total_claim` decimal(28,10) DEFAULT 0 COMMENT '赔付支出合计(本月)';

-- 修正子账户收益率表 (TB0014) 金额字段
ALTER TABLE `t_base_sub_account_yield_rate`
MODIFY COLUMN `accounting_yield_rate` decimal(28,10) DEFAULT 0 COMMENT '年化会计投资收益率，以百分比表示',
MODIFY COLUMN `comprehensive_yield_rate` decimal(28,10) DEFAULT 0 COMMENT '年化综合投资收益率，以百分比表示';

-- 修正分产品保费收入表 (TB0015) 金额字段
ALTER TABLE `t_cost_product_premium_income_detail`
MODIFY COLUMN `current_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(本月)',
MODIFY COLUMN `current_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(本月)',
MODIFY COLUMN `current_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(本月)',
MODIFY COLUMN `current_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(本月)',
MODIFY COLUMN `current_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(本月)',
MODIFY COLUMN `current_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(本月)';

-- 修正中短存续期产品利差表 (TB0016) 金额字段
ALTER TABLE `t_cost_short_term_product_spread`
MODIFY COLUMN `new_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '新单规模保费',
MODIFY COLUMN `accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金',
MODIFY COLUMN `investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款',
MODIFY COLUMN `accounting_yield_rate` decimal(28,10) DEFAULT 0 COMMENT '年化会计投资收益率',
MODIFY COLUMN `comprehensive_yield_rate` decimal(28,10) DEFAULT 0 COMMENT '年化综合投资收益率',
MODIFY COLUMN `liability_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '负债资金成本率',
MODIFY COLUMN `effective_cost_rate` decimal(28,10) DEFAULT 0 COMMENT '负债有效成本率';
