-- =============================================
-- ADUR月度折现曲线表不含价差 - 新版本DDL
-- 表名：t_adur_monthly_discount_curve
-- 对应表：TB0005 月度折现曲线表不含价差
-- 基于最新字段列表要求生成
-- =============================================

DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;

CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_set` text COMMENT '月度折现曲线利率值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_date_type` (`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现曲线表不含价差';

-- =============================================
-- 字段说明
-- =============================================

/*
月度折现曲线利率值集 (monthly_discount_rate_set) 字段说明：

1. 数据格式：JSON格式
   示例：{"0":0.25,"1":0.35,"2":0.28,...,"600":0.15}

2. 计算逻辑：
   (1) 如果折现曲线标识=0 AND 日期类型=发行时点
       等于"-"（在JSON中可用特殊值如null或"-"表示）
   
   (2) 如果折现曲线标识=0 AND 日期类型=评估时点
       等于久期资产明细表.到期收益率
   
   (3) 如果折现曲线标识<>0
       - 月份为12的整数倍时（0/12/24/36/48/……/600）：
         A：计算对应年份数：月份/12
         B：在年度折现曲线表中查找期限为（月份/12）年的收益率
       - 月份不为12的整数倍时：
         取临近两个整数年的收益率，根据所在月度进行线性插值

3. 数据范围：
   - 期限范围：0-600（共601个期限点）
   - 数值类型：decimal(10,6)对应的数值
   - 缺省值：0

4. 索引设计：
   - 主键：id
   - 唯一索引：account_period + asset_number + date_type
   - 普通索引：account_period, asset_number, date_type
*/

-- =============================================
-- 数据迁移说明
-- =============================================

/*
如果需要从旧版本（包含term_0到term_600字段）迁移到新版本：

1. 数据转换SQL示例：
UPDATE t_adur_monthly_discount_curve 
SET monthly_discount_rate_set = CONCAT(
  '{"0":', IFNULL(term_0, 0),
  ',"1":', IFNULL(term_1, 0),
  ',"2":', IFNULL(term_2, 0),
  -- ... 继续到term_600
  ',"600":', IFNULL(term_600, 0),
  '}'
);

2. 建议使用程序进行数据迁移，确保JSON格式正确性

3. 迁移完成后删除旧的term_*字段：
ALTER TABLE t_adur_monthly_discount_curve 
DROP COLUMN term_0, DROP COLUMN term_1, ..., DROP COLUMN term_600;
*/

-- =============================================
-- 使用示例
-- =============================================

/*
1. 插入数据示例：
INSERT INTO t_adur_monthly_discount_curve (
  account_period, date_type, date, asset_number, account_name,
  asset_name, security_code, curve_id, monthly_discount_rate_set
) VALUES (
  '202501', '02', '2025-01-31', 'ASSET001', '01',
  '国债001', 'T001', '1',
  '{"0":0.025,"1":0.026,"2":0.027,"3":0.028,"4":0.029,"5":0.030}'
);

2. 查询特定期限利率：
SELECT 
  asset_number,
  JSON_EXTRACT(monthly_discount_rate_set, '$.12') as rate_12_month,
  JSON_EXTRACT(monthly_discount_rate_set, '$.24') as rate_24_month
FROM t_adur_monthly_discount_curve 
WHERE account_period = '202501';

3. 更新特定期限利率：
UPDATE t_adur_monthly_discount_curve 
SET monthly_discount_rate_set = JSON_SET(
  monthly_discount_rate_set, 
  '$.12', 0.035,
  '$.24', 0.040
)
WHERE account_period = '202501' AND asset_number = 'ASSET001';
*/
