-- =============================================
-- ADUR久期资产明细表菜单SQL
-- 表名：t_adur_duration_asset_detail
-- 功能：ADUR久期资产明细管理
-- 对应表：TB0003 久期资产明细表
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR久期资产明细', '2548', '3', 'adurdurationassetdetail', 'adur/duration/asset/detail/index', 1, 0, 'C', '0', '0', 'adur:duration:asset:detail:list', '#', 'admin', sysdate(), '', null, 'ADUR久期资产明细菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('久期资产明细导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:duration:asset:detail:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:duration:asset:detail:list    - ADUR久期资产明细列表查询
-- adur:duration:asset:detail:query   - ADUR久期资产明细详情查询
-- adur:duration:asset:detail:add     - ADUR久期资产明细新增
-- adur:duration:asset:detail:edit    - ADUR久期资产明细修改
-- adur:duration:asset:detail:remove  - ADUR久期资产明细删除
-- adur:duration:asset:detail:export  - ADUR久期资产明细导出
-- adur:duration:asset:detail:import  - ADUR久期资产明细导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为3，表示在ADUR模块中的第3个子菜单
-- 3. path 为 'adurdurationassetdetail'，对应前端路由
-- 4. component 为 'adur/duration/asset/detail/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:duration:asset:detail:*' 格式
-- =============================================
