-- =============================================
-- ADUR关键久期参数表DDL
-- 表名：t_adur_key_duration_parameter
-- 功能：存储从老表迁移的关键久期参数数据（截取1-600标识）
-- =============================================

CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `key_term` varchar(50) NOT NULL COMMENT '关键期限',
  `parameter_val_set` mediumtext NOT NULL COMMENT '参数值集（从老表JSON中截取1-600标识的数据）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_key_term` (`key_term`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='ADUR关键久期参数表';

-- =============================================
-- 表结构说明：
-- 1. key_term: 关键期限，对应老表的key_duration字段
-- 2. parameter_val_set: 参数值集，从老表JSON中截取1-600标识的数据
-- 3. 唯一约束：key_term字段唯一
-- 4. 数据来源：t_dur_key_duration_parameter表
-- =============================================

-- 创建索引
CREATE INDEX `idx_key_term` ON `t_adur_key_duration_parameter` (`key_term`);
CREATE INDEX `idx_create_time` ON `t_adur_key_duration_parameter` (`create_time`);
CREATE INDEX `idx_is_del` ON `t_adur_key_duration_parameter` (`is_del`);
