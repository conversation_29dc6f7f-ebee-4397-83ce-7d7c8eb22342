-- =============================================
-- 万得收益率曲线表菜单SQL
-- 表名：t_base_wind_yield_curve
-- 功能：万得收益率曲线管理
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线', '2000', '1', 'windyieldcurve', 'adur/wind/yield/curve/index', 1, 0, 'C', '0', '0', 'adur:wind:yield:curve:list', '#', 'admin', sysdate(), '', null, '万得收益率曲线菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('万得收益率曲线导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:wind:yield:curve:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:wind:yield:curve:list   - 万得收益率曲线列表查询
-- adur:wind:yield:curve:query  - 万得收益率曲线详情查询
-- adur:wind:yield:curve:add    - 万得收益率曲线新增
-- adur:wind:yield:curve:edit   - 万得收益率曲线修改
-- adur:wind:yield:curve:remove - 万得收益率曲线删除
-- adur:wind:yield:curve:export - 万得收益率曲线导出
-- adur:wind:yield:curve:import - 万得收益率曲线导入
-- =============================================

-- 注意事项：
-- 1. parent_id 需要根据实际的父菜单ID进行调整（这里使用2000作为示例）
-- 2. order_num 需要根据实际的菜单顺序进行调整
-- 3. path 和 component 需要根据前端路由配置进行调整
-- 4. 执行前请确认sys_menu表结构与SQL语句匹配
-- =============================================
