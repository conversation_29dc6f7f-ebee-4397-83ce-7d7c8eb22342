-- 现金流量表管理模块DDL
-- 数据库：MySQL 8.0
-- 字符集：utf8

-- =============================================
-- 表名：t_base_cash_flow_statement
-- 表中文名：现金流量表
-- 表描述：存储现金流量表数据，包括公司整体、分红账户、万能账户、投连账户等不同账户类型的现金流数据
-- =============================================
CREATE TABLE IF NOT EXISTS `t_base_cash_flow_statement` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
    `level_type` varchar(20) NOT NULL COMMENT '级别类型，项目级别分类',
    `item_name` varchar(100) NOT NULL COMMENT '项目，现金流项目名称',
    `company_overall` decimal(28,10) DEFAULT 0 COMMENT '公司整体现金流数据',
    `dividend_account` decimal(28,10) DEFAULT 0 COMMENT '分红账户现金流数据',
    `universal_account` decimal(28,10) DEFAULT 0 COMMENT '万能账户现金流数据',
    `investment_linked_account` decimal(28,10) DEFAULT 0 COMMENT '投连账户现金流数据',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_accounting_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='现金流量表';

-- =============================================
-- 表名：t_base_cash_flow_item_mapping
-- 表中文名：现金流项目映射表
-- 表描述：存储现金流量表项目与资负现金流测试项目的映射关系
-- =============================================
CREATE TABLE IF NOT EXISTS `t_base_cash_flow_item_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
    `cash_flow_item` varchar(100) NOT NULL COMMENT '现金流量表项目，现金流量表对应项目',
    `test_item` varchar(100) NOT NULL COMMENT '资负现金流测试项目，资负现金流测试对应项目',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_accounting_period` (`accounting_period`),
    KEY `idx_cash_flow_item` (`cash_flow_item`),
    KEY `idx_test_item` (`test_item`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='现金流项目映射表';

-- =============================================
-- 表名：t_cft_almcf_actual_ytd
-- 表中文名：ALMCF实际发生数本年累计表
-- 表描述：存储ALMCF实际发生数本年累计数据，通过现金流量表和映射表计算生成
-- =============================================
CREATE TABLE IF NOT EXISTS `t_cft_almcf_actual_ytd` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增长',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
    `item_name` varchar(100) NOT NULL COMMENT '项目名称，项目中文名称',
    `traditional_account` decimal(28,10) DEFAULT 0 COMMENT '传统账户实际发生金额，等于公司整体-分红账户-万能账户-投连账户',
    `bonus_account` decimal(28,10) DEFAULT 0 COMMENT '分红账户实际发生金额，通过映射表计算',
    `universal_account` decimal(28,10) DEFAULT 0 COMMENT '万能账户实际发生金额，通过映射表计算',
    `investment_account` decimal(28,10) DEFAULT 0 COMMENT '投连账户实际发生金额，通过映射表计算',
    `ordinary_account` decimal(28,10) DEFAULT 0 COMMENT '普通账户实际发生金额，等于公司整体-投连账户',
    `total_account` decimal(28,10) DEFAULT 0 COMMENT '公司整体，各账户金额合计，通过映射表计算',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_accounting_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ALMCF实际发生数本年累计表';

-- =============================================
-- 表名：t_cft_almcf_actual_quarterly_cumulative
-- 表中文名：ALMCF实际发生数本季度累计表
-- 表描述：存储ALMCF实际发生数本季度累计数据，通过ALMCF实际发生数本年累计表计算生成
-- =============================================
CREATE TABLE IF NOT EXISTS `t_cft_almcf_actual_quarterly_cumulative` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增长',
    `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
    `item_name` varchar(100) NOT NULL COMMENT '现金流项目名称，如：保费收入、赔付支出等',
    `total_account` decimal(18,2) DEFAULT 0 COMMENT '公司整体金额',
    `general_account` decimal(18,2) DEFAULT 0 COMMENT '普通账户金额',
    `traditional_account` decimal(18,2) DEFAULT 0 COMMENT '传统账户金额',
    `dividend_account` decimal(18,2) DEFAULT 0 COMMENT '分红账户金额',
    `universal_account` decimal(18,2) DEFAULT 0 COMMENT '万能账户金额',
    `investment_account` decimal(18,2) DEFAULT 0 COMMENT '投连账户金额',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_accounting_period_item` (`accounting_period`, `item_name`),
    KEY `idx_accounting_period` (`accounting_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ALMCF实际发生数本季度累计表';
