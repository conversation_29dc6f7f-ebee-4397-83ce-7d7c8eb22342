-- =============================================
-- ADUR模块完整表结构DDL
-- 根据最新设计文档生成
-- 生成时间：2025-01-07
-- =============================================

-- =============================================
-- TB0001: 万得收益率曲线表
-- =============================================
DROP TABLE IF EXISTS `t_base_wind_yield_curve`;
CREATE TABLE `t_base_wind_yield_curve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `curve_name` varchar(50) NOT NULL COMMENT '折现曲线名称',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `date` date NOT NULL COMMENT '日期',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  `term_6` decimal(10,6) DEFAULT 0 COMMENT '期限6',
  `term_7` decimal(10,6) DEFAULT 0 COMMENT '期限7',
  `term_8` decimal(10,6) DEFAULT 0 COMMENT '期限8',
  `term_9` decimal(10,6) DEFAULT 0 COMMENT '期限9',
  `term_10` decimal(10,6) DEFAULT 0 COMMENT '期限10',
  -- 注意：实际包含更多期限字段，此处仅显示部分
  `term_50` decimal(10,6) DEFAULT 0 COMMENT '期限50',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`curve_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='万得收益率曲线表';

-- =============================================
-- TB0002: 关键久期参数表
-- =============================================
DROP TABLE IF EXISTS `t_adur_key_duration_parameter`;
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `key_duration` varchar(20) NOT NULL COMMENT '关键久期',
  `parameter_val_set` mediumtext COMMENT 'JSON格式参数值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_param` (`account_period`,`key_duration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='关键久期参数表';

-- =============================================
-- TB0003: 久期资产明细表
-- =============================================
DROP TABLE IF EXISTS `t_adur_duration_asset_detail`;
CREATE TABLE `t_adur_duration_asset_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `asset_category` varchar(20) NOT NULL COMMENT '资产类别',
  `book_value` decimal(18,2) DEFAULT 0 COMMENT '账面价值',
  `market_value` decimal(18,2) DEFAULT 0 COMMENT '市场价值',
  `modified_duration` decimal(10,6) DEFAULT 0 COMMENT '修正久期',
  `effective_duration` decimal(10,6) DEFAULT 0 COMMENT '有效久期',
  `convexity` decimal(10,6) DEFAULT 0 COMMENT '凸性',
  `yield_to_maturity` decimal(10,6) DEFAULT 0 COMMENT '到期收益率',
  `maturity_date` date DEFAULT NULL COMMENT '到期日期',
  -- DV10风险指标字段（20个关键期限的上升和下降情景）
  `dv10_1_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期0年上升情景现值',
  `dv10_2_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期0.5年上升情景现值',
  `dv10_3_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期1年上升情景现值',
  `dv10_4_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期2年上升情景现值',
  `dv10_5_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期3年上升情景现值',
  `dv10_6_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期4年上升情景现值',
  `dv10_7_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期5年上升情景现值',
  `dv10_8_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期6年上升情景现值',
  `dv10_9_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期7年上升情景现值',
  `dv10_10_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期8年上升情景现值',
  `dv10_11_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期10年上升情景现值',
  `dv10_12_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期12年上升情景现值',
  `dv10_13_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期15年上升情景现值',
  `dv10_14_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期20年上升情景现值',
  `dv10_15_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期25年上升情景现值',
  `dv10_16_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期30年上升情景现值',
  `dv10_17_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期35年上升情景现值',
  `dv10_18_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期40年上升情景现值',
  `dv10_19_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期45年上升情景现值',
  `dv10_20_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期50年上升情景现值',
  `dv10_1_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期0年下降情景现值',
  `dv10_2_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期0.5年下降情景现值',
  `dv10_3_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期1年下降情景现值',
  `dv10_4_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期2年下降情景现值',
  `dv10_5_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期3年下降情景现值',
  `dv10_6_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期4年下降情景现值',
  `dv10_7_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期5年下降情景现值',
  `dv10_8_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期6年下降情景现值',
  `dv10_9_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期7年下降情景现值',
  `dv10_10_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期8年下降情景现值',
  `dv10_11_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期10年下降情景现值',
  `dv10_12_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期12年下降情景现值',
  `dv10_13_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期15年下降情景现值',
  `dv10_14_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期20年下降情景现值',
  `dv10_15_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期25年下降情景现值',
  `dv10_16_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期30年下降情景现值',
  `dv10_17_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期35年下降情景现值',
  `dv10_18_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期40年下降情景现值',
  `dv10_19_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期45年下降情景现值',
  `dv10_20_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期50年下降情景现值',
  `issue_cashflow_set` mediumtext COMMENT 'JSON格式发行时点现金流数据',
  `eval_cashflow_set` mediumtext COMMENT 'JSON格式评估时点现金流数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_asset` (`account_period`,`asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='久期资产明细表';

-- =============================================
-- TB0004: 年度折现曲线表
-- =============================================
DROP TABLE IF EXISTS `t_adur_annual_discount_curve`;
CREATE TABLE `t_adur_annual_discount_curve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `curve_type` varchar(20) NOT NULL COMMENT '曲线类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `curve_val_set` mediumtext COMMENT 'JSON格式曲线值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`account_period`,`curve_type`,`basis_point_type`,`duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='年度折现曲线表';

-- =============================================
-- TB0005: 月度折现曲线表不含价差
-- =============================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;
CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  -- 注意：实际包含term_0到term_600共601个期限字段
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`account_period`,`asset_number`,`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='月度折现曲线表不含价差';

-- =============================================
-- TB0006: 月度折现曲线表含价差
-- =============================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve_with_spread`;
CREATE TABLE `t_adur_monthly_discount_curve_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  -- 注意：实际包含term_0到term_600共601个期限字段
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`account_period`,`asset_number`,`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='月度折现曲线表含价差';

-- =============================================
-- TB0007: 月度折现因子表含价差
-- =============================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_factor_with_spread`;
CREATE TABLE `t_adur_monthly_discount_factor_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  -- 注意：实际包含term_0到term_600共601个期限字段
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_factor` (`account_period`,`asset_number`,`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='月度折现因子表含价差';

-- =============================================
-- TB0008: 关键久期折现曲线表含价差
-- =============================================
DROP TABLE IF EXISTS `t_adur_key_duration_curve_with_spread`;
CREATE TABLE `t_adur_key_duration_curve_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) DEFAULT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(10) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  -- 注意：实际包含term_0到term_600共601个期限字段
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`account_period`,`asset_number`,`key_term`,`stress_direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='关键久期折现曲线表含价差';

-- =============================================
-- TB0009: 关键久期折现因子表含价差
-- =============================================
DROP TABLE IF EXISTS `t_adur_key_duration_factor_with_spread`;
CREATE TABLE `t_adur_key_duration_factor_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(10) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `issue_spread` decimal(10,6) DEFAULT 0 COMMENT '发行时点价差',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  -- 注意：实际包含term_0到term_600共601个期限字段
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_factor` (`account_period`,`asset_number`,`key_term`,`stress_direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='关键久期折现因子表含价差';

-- =============================================
-- TB0010: 久期资产结果汇总表
-- =============================================
DROP TABLE IF EXISTS `t_adur_duration_asset_summary`;
CREATE TABLE `t_adur_duration_asset_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_category` varchar(20) NOT NULL COMMENT '资产类别',
  `total_book_value` decimal(18,2) DEFAULT 0 COMMENT '总账面价值',
  `total_market_value` decimal(18,2) DEFAULT 0 COMMENT '总市场价值',
  `weighted_modified_duration` decimal(10,6) DEFAULT 0 COMMENT '加权修正久期',
  `weighted_effective_duration` decimal(10,6) DEFAULT 0 COMMENT '加权有效久期',
  `weighted_convexity` decimal(10,6) DEFAULT 0 COMMENT '加权凸性',
  `weighted_yield_to_maturity` decimal(10,6) DEFAULT 0 COMMENT '加权到期收益率',
  `asset_count` int DEFAULT 0 COMMENT '资产数量',
  -- DV10风险指标汇总（20个关键期限的上升和下降情景）
  `total_dv10_1_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期0年上升情景总现值',
  `total_dv10_2_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期0.5年上升情景总现值',
  `total_dv10_3_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期1年上升情景总现值',
  `total_dv10_4_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期2年上升情景总现值',
  `total_dv10_5_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期3年上升情景总现值',
  `total_dv10_6_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期4年上升情景总现值',
  `total_dv10_7_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期5年上升情景总现值',
  `total_dv10_8_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期6年上升情景总现值',
  `total_dv10_9_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期7年上升情景总现值',
  `total_dv10_10_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期8年上升情景总现值',
  `total_dv10_11_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期10年上升情景总现值',
  `total_dv10_12_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期12年上升情景总现值',
  `total_dv10_13_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期15年上升情景总现值',
  `total_dv10_14_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期20年上升情景总现值',
  `total_dv10_15_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期25年上升情景总现值',
  `total_dv10_16_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期30年上升情景总现值',
  `total_dv10_17_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期35年上升情景总现值',
  `total_dv10_18_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期40年上升情景总现值',
  `total_dv10_19_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期45年上升情景总现值',
  `total_dv10_20_up` decimal(18,2) DEFAULT 0 COMMENT '关键久期50年上升情景总现值',
  `total_dv10_1_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期0年下降情景总现值',
  `total_dv10_2_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期0.5年下降情景总现值',
  `total_dv10_3_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期1年下降情景总现值',
  `total_dv10_4_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期2年下降情景总现值',
  `total_dv10_5_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期3年下降情景总现值',
  `total_dv10_6_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期4年下降情景总现值',
  `total_dv10_7_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期5年下降情景总现值',
  `total_dv10_8_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期6年下降情景总现值',
  `total_dv10_9_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期7年下降情景总现值',
  `total_dv10_10_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期8年下降情景总现值',
  `total_dv10_11_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期10年下降情景总现值',
  `total_dv10_12_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期12年下降情景总现值',
  `total_dv10_13_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期15年下降情景总现值',
  `total_dv10_14_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期20年下降情景总现值',
  `total_dv10_15_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期25年下降情景总现值',
  `total_dv10_16_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期30年下降情景总现值',
  `total_dv10_17_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期35年下降情景总现值',
  `total_dv10_18_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期40年下降情景总现值',
  `total_dv10_19_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期45年下降情景总现值',
  `total_dv10_20_down` decimal(18,2) DEFAULT 0 COMMENT '关键久期50年下降情景总现值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_summary` (`account_period`,`account_name`,`asset_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='久期资产结果汇总表';

-- =============================================
-- 索引说明总结
-- =============================================
/*
表名映射关系：
TB0001: t_base_wind_yield_curve                    - 万得收益率曲线表
TB0002: t_adur_key_duration_parameter              - 关键久期参数表
TB0003: t_adur_duration_asset_detail               - 久期资产明细表
TB0004: t_adur_annual_discount_curve               - 年度折现曲线表
TB0005: t_adur_monthly_discount_curve              - 月度折现曲线表不含价差
TB0006: t_adur_monthly_discount_curve_with_spread  - 月度折现曲线表含价差
TB0007: t_adur_monthly_discount_factor_with_spread - 月度折现因子表含价差
TB0008: t_adur_key_duration_curve_with_spread      - 关键久期折现曲线表含价差
TB0009: t_adur_key_duration_factor_with_spread     - 关键久期折现因子表含价差
TB0010: t_adur_duration_asset_summary              - 久期资产结果汇总表

关键特性：
1. 所有表都包含标准的审计字段：create_time, create_by, update_time, update_by, is_del
2. 所有表都使用bigint类型的自增主键id
3. 账期字段统一使用varchar(6)格式，如：202501
4. 期限字段表（TB0005-TB0009）包含term_0到term_600共601个期限字段
5. DV10风险指标表（TB0003, TB0010）包含20个关键期限的上升和下降情景字段
6. 价差相关表（TB0006-TB0009）包含价差类型和价差值字段
7. 所有表都设置了合适的唯一索引以确保数据完整性

注意事项：
- 期限字段表的完整字段列表需要包含term_0到term_600共601个字段
- DV10字段对应20个关键期限：0, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 15, 20, 25, 30, 35, 40, 45, 50年
- JSON字段用于存储复杂的参数值集和现金流数据
- 所有decimal字段都设置了合适的精度和标度
*/

-- =============================================
-- DDL生成完成
-- =============================================
