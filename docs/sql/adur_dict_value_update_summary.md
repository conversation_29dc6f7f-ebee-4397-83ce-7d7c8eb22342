# ADUR模块字典值更新总结

## 概述

根据用户要求，将TB0006（t_adur_monthly_discount_curve_with_spread）表的以下字段从存储中文值改为存储字典值：

- `account_period` varchar(6) NOT NULL COMMENT '账期'
- `duration_type` varchar(20) NOT NULL COMMENT '久期类型'  
- `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型'
- `account_name` varchar(50) NOT NULL COMMENT '账户名称'

## 字典值映射关系

### 1. 久期类型 (duration_type)
- **01**: 修正久期
- **02**: 有效久期  
- **03**: 利差久期
- **04**: 关键久期

### 2. 基点类型 (basis_point_type)
- **01**: 0bp
- **02**: +50bp
- **03**: -50bp

### 3. 账户名称 (account_name)
- **01**: 传统账户
- **02**: 分红账户
- **03**: 万能账户
- **04**: 普通账户

### 4. 价差类型 (spread_type)
- **01**: 发行时点价差
- **02**: 评估时点价差

### 5. 日期类型 (date_type)
- **01**: 发行时点
- **02**: 评估时点

## 已修改的文件

### 1. DDL文件更新

#### docs/sql/t_adur_monthly_discount_curve_with_spread_new_ddl.sql
- 更新字段取值规范说明，将中文值改为字典值
- 更新示例数据，使用字典值而非中文值
- 更新查询示例，使用字典值进行查询

#### docs/sql/t_adur_monthly_discount_factor_with_spread_ddl.sql
- 更新示例数据，使用字典值而非中文值

#### docs/sql/t_adur_monthly_discount_curve_new_ddl.sql
- 更新示例数据，使用字典值而非中文值

### 2. 工具类更新

#### docs/sql/MonthlyDiscountCurveWithSpreadUtil.java
- 添加字典值常量定义
- 更新 `determineCurveId` 方法，使用字典值进行判断
- 将硬编码的中文值改为字典值常量

### 3. Job模块代码更新

#### job/src/main/java/com/xl/alm/job/adur/service/impl/AdurMonthlyDiscountCurveCalculationServiceImpl.java
- 修复硬编码的中文值："发行时点" -> `AdurConstant.DATE_TYPE_ISSUE`
- 修复硬编码的中文值："评估时点" -> `AdurConstant.DATE_TYPE_EVALUATION`
- 添加 `AdurConstant` 导入语句

## 现有支持

### 1. 字典数据
- `docs/sql/adur_program_dict.sql` 文件已包含完整的字典类型和字典数据定义
- 所有相关字典类型都已正确配置

### 2. 常量类支持
- `job/src/main/java/com/xl/alm/job/adur/constant/AdurConstant.java` 提供了完整的字典值常量
- 包含中文到字典值的转换方法
- Job模块服务类已正确使用常量类进行字典值判断

### 3. 前端支持
- 前端页面已正确使用字典标签显示字段值
- 表单组件使用字典下拉选择，确保数据一致性

### 4. 实体类支持
- 所有相关实体类字段类型为String，支持字典值存储
- MyBatis映射文件正确配置

### 5. Job模块支持
- 服务实现类已使用 `AdurConstant` 常量进行字典值判断
- 测试代码已正确使用字典值进行数据准备
- 大部分业务逻辑已适配字典值格式

## 数据迁移建议

如果数据库中已存在使用中文值的数据，建议执行以下迁移SQL：

```sql
-- 更新久期类型字段
UPDATE t_adur_monthly_discount_curve_with_spread 
SET duration_type = CASE duration_type
    WHEN '修正久期' THEN '01'
    WHEN '有效久期' THEN '02'
    WHEN '利差久期' THEN '03'
    WHEN '关键久期' THEN '04'
    ELSE duration_type
END;

-- 更新基点类型字段
UPDATE t_adur_monthly_discount_curve_with_spread 
SET basis_point_type = CASE basis_point_type
    WHEN '0bp' THEN '01'
    WHEN '+50bp' THEN '02'
    WHEN '-50bp' THEN '03'
    ELSE basis_point_type
END;

-- 更新账户名称字段
UPDATE t_adur_monthly_discount_curve_with_spread 
SET account_name = CASE account_name
    WHEN '传统账户' THEN '01'
    WHEN '分红账户' THEN '02'
    WHEN '万能账户' THEN '03'
    WHEN '普通账户' THEN '04'
    ELSE account_name
END;

-- 更新价差类型字段
UPDATE t_adur_monthly_discount_curve_with_spread 
SET spread_type = CASE spread_type
    WHEN '发行时点价差' THEN '01'
    WHEN '评估时点价差' THEN '02'
    ELSE spread_type
END;

-- 更新日期类型字段
UPDATE t_adur_monthly_discount_curve_with_spread 
SET date_type = CASE date_type
    WHEN '发行时点' THEN '01'
    WHEN '评估时点' THEN '02'
    ELSE date_type
END;
```

## 验证方法

1. **数据库验证**: 检查表中字段值是否为字典值格式（01、02等）
2. **前端验证**: 确认页面显示正确的中文标签
3. **功能验证**: 测试增删改查功能是否正常工作
4. **字典验证**: 确认字典数据已正确导入系统

## 注意事项

1. 所有新增数据都应使用字典值存储
2. 前端表单提交时会自动使用字典值
3. 显示时通过字典标签组件自动转换为中文显示
4. 工具类和服务类应使用常量类中定义的字典值常量
5. 避免在代码中硬编码中文值

## 影响范围

此次修改主要影响：
- **数据库表**: TB0006 月度折现曲线表含价差、TB0007 月度折现因子表含价差、TB0005 月度折现曲线表不含价差
- **工具类**: MonthlyDiscountCurveWithSpreadUtil.java
- **Job模块**: AdurMonthlyDiscountCurveCalculationServiceImpl.java 等服务实现类
- **前端页面**: 所有ADUR相关的Vue组件
- **实体类**: 所有ADUR相关的Entity类

## 验证清单

### ✅ 已完成
1. DDL文件中的示例数据已更新为字典值
2. 工具类中的硬编码中文值已修复
3. Job模块服务类中的硬编码中文值已修复
4. 常量类提供完整的字典值支持
5. 前端页面使用字典标签正确显示

### 🔍 需要验证
1. 数据库中现有数据是否需要迁移
2. 其他ADUR模块表是否存在类似问题
3. 集成测试是否通过
4. 前端功能是否正常工作

其他ADUR模块表如果也存在类似问题，建议按照相同方式进行修改。
