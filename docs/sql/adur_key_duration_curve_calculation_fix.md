# ADUR关键久期折现曲线含价差计算逻辑修复

## 问题描述

用户指出关键久期折现曲线含价差的计算逻辑有问题，需要按照正确的业务逻辑进行修复。

## 正确的计算逻辑

### JSON格式
```json
{
  "0": 0.25,
  "1": 0.35,
  ...
  "600": 0.15
}
```

### 计算公式

#### 1. 上升压力方向
```
期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.关键参数值集.期限X
```

#### 2. 下降压力方向
```
期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.关键参数值集.期限X
```

### 匹配条件

#### 月度折现曲线表含价差匹配条件
- `月度折现曲线表含价差.曲线细分类 = 2`
- `月度折现曲线表含价差.账户名称 = 关键久期折现曲线表含价差.账户名称`
- `月度折现曲线表含价差.证券代码 = 关键久期折现曲线表含价差.证券代码`

#### 关键久期参数表匹配条件
- `关键久期参数表.关键期限 = 关键久期折现曲线表含价差.关键期限`

## 修复内容

### 1. 数据筛选逻辑修复

**修复前**：
```java
// 遍历每个月度折现曲线含价差记录
for (AdurMonthlyDiscountCurveWithSpreadEntity curveEntity : monthlyDiscountCurveWithSpreadList) {
    // 为每个关键期限生成数据
    for (String keyDuration : KEY_TERMS) {
        // ...
    }
}
```

**修复后**：
```java
// 筛选曲线细分类=2的月度折现曲线含价差数据
List<AdurMonthlyDiscountCurveWithSpreadEntity> category2CurveList = monthlyDiscountCurveWithSpreadList.stream()
        .filter(curve -> "2".equals(curve.getCurveSubCategory()))
        .collect(java.util.stream.Collectors.toList());

if (category2CurveList.isEmpty()) {
    log.warn("未找到曲线细分类=2的月度折现曲线含价差数据，账期：{}", accountPeriod);
    return resultList;
}

// 为每个关键期限生成数据
for (String keyDuration : KEY_TERMS) {
    // 为每个曲线细分类=2的月度折现曲线含价差记录生成关键久期数据
    for (AdurMonthlyDiscountCurveWithSpreadEntity curveEntity : category2CurveList) {
        // ...
    }
}
```

### 2. JSON生成逻辑修复

**修复前**：
```java
if (baseRate != null && parameterValue != null) {
    // 计算逻辑
    resultTermValues.put(termIndex, adjustedRate.setScale(6, RoundingMode.HALF_UP));
}
// 如果数据为null，不添加到结果中

String resultJson = TermDataUtil.createTermJson(resultTermValues);
```

**修复后**：
```java
if (baseRate != null && parameterValue != null) {
    // 计算逻辑
    resultTermValues.put(termIndex, adjustedRate.setScale(6, RoundingMode.HALF_UP));
} else {
    // 如果基础利率或参数值为空，设置为0（确保JSON包含所有期限）
    resultTermValues.put(termIndex, BigDecimal.ZERO);
}

// 使用完整的JSON生成方法，确保包含0-600所有期限
String resultJson = TermDataUtil.createCompleteTermJson(resultTermValues);
```

### 3. 计算逻辑验证

计算逻辑本身是正确的，保持不变：

```java
if (AdurConstant.STRESS_DIRECTION_UP.equals(stressDirection)) {
    // 上升：期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.参数值集[X]
    adjustedRate = baseRate.add(parameterValue);
} else {
    // 下降：期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.参数值集[X]
    adjustedRate = baseRate.subtract(parameterValue);
}
```

## 测试验证

### 测试用例设计

创建了 `AdurKeyDurationDiscountCurveWithSpreadCalculationTest` 测试类，包含以下测试场景：

#### 1. 正常计算逻辑测试
- **基础数据**：月度折现曲线含价差（基础利率4%），关键久期参数（参数值0.001）
- **期望结果**：
  - 上升压力：0.04 + 0.001 = 0.041000
  - 下降压力：0.04 - 0.001 = 0.039000
- **验证点**：JSON包含0-600所有期限，计算结果正确

#### 2. 缺失数据测试
- **场景**：只有关键久期参数数据，没有月度折现曲线含价差数据
- **期望结果**：任务成功完成，但结果为空

#### 3. 匹配条件测试
- **场景**：月度折现曲线含价差的曲线细分类不是2
- **期望结果**：任务成功完成，但结果为空（因为不满足匹配条件）

### 测试数据示例

#### 关键久期参数数据
```json
{
  "0": {"val": 0.001},
  "1": {"val": 0.001},
  ...
  "600": {"val": 0.001}
}
```

#### 月度折现曲线含价差数据
```json
{
  "0": 0.04,
  "1": 0.04,
  ...
  "600": 0.04
}
```

#### 期望的计算结果（上升压力）
```json
{
  "0": 0.041000,
  "1": 0.041000,
  ...
  "600": 0.041000
}
```

## 关键改进点

### 1. 匹配条件严格执行
- 确保只使用曲线细分类=2的月度折现曲线含价差数据
- 账户名称和证券代码的匹配通过数据结构自然实现

### 2. 完整JSON生成
- 使用 `TermDataUtil.createCompleteTermJson()` 确保包含0-600所有期限
- 缺失数据时设置为0，而不是跳过

### 3. 错误处理改进
- 当找不到曲线细分类=2的数据时，记录警告日志
- 异常情况下仍然生成完整的JSON结构

### 4. 数据流程优化
- 先筛选符合条件的基础数据，再进行计算
- 减少不必要的循环和计算

## 验证方法

1. **运行测试用例**：执行 `AdurKeyDurationDiscountCurveWithSpreadCalculationTest`
2. **检查日志**：确认曲线细分类=2的数据加载情况
3. **验证结果**：确认JSON包含完整的0-600期限数据
4. **计算验证**：手工验证几个关键期限的计算结果

## 预期效果

修复后，关键久期折现曲线含价差值集应该：

1. **格式正确**：包含0-600所有期限的完整JSON
2. **计算正确**：严格按照业务公式进行计算
3. **匹配准确**：只使用符合条件的基础数据
4. **处理完善**：异常情况下也能生成合理的结果

通过这些修复，应该能够解决关键久期折现曲线含价差值集为空或计算不正确的问题。
