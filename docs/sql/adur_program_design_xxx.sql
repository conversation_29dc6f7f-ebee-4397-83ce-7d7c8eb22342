-- =====================================================
-- ADUR资产久期管理模块数据表DDL
-- 基于设计文档：docs/design/adur_program_design.md 第2.3章节
-- 生成时间：2025-01-16
-- 模块编号：MD0001
-- =====================================================

-- =====================================================
-- TB0001: 万得收益率曲线表
-- =====================================================
DROP TABLE IF EXISTS `t_base_wind_yield_curve`;
CREATE TABLE `t_base_wind_yield_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `curve_name` varchar(50) NOT NULL COMMENT '折现曲线名称',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `date` date NOT NULL COMMENT '日期',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  `term_6` decimal(10,6) DEFAULT 0 COMMENT '期限6',
  `term_7` decimal(10,6) DEFAULT 0 COMMENT '期限7',
  `term_8` decimal(10,6) DEFAULT 0 COMMENT '期限8',
  `term_9` decimal(10,6) DEFAULT 0 COMMENT '期限9',
  `term_10` decimal(10,6) DEFAULT 0 COMMENT '期限10',
  `term_11` decimal(10,6) DEFAULT 0 COMMENT '期限11',
  `term_12` decimal(10,6) DEFAULT 0 COMMENT '期限12',
  `term_13` decimal(10,6) DEFAULT 0 COMMENT '期限13',
  `term_14` decimal(10,6) DEFAULT 0 COMMENT '期限14',
  `term_15` decimal(10,6) DEFAULT 0 COMMENT '期限15',
  `term_16` decimal(10,6) DEFAULT 0 COMMENT '期限16',
  `term_17` decimal(10,6) DEFAULT 0 COMMENT '期限17',
  `term_18` decimal(10,6) DEFAULT 0 COMMENT '期限18',
  `term_19` decimal(10,6) DEFAULT 0 COMMENT '期限19',
  `term_20` decimal(10,6) DEFAULT 0 COMMENT '期限20',
  `term_21` decimal(10,6) DEFAULT 0 COMMENT '期限21',
  `term_22` decimal(10,6) DEFAULT 0 COMMENT '期限22',
  `term_23` decimal(10,6) DEFAULT 0 COMMENT '期限23',
  `term_24` decimal(10,6) DEFAULT 0 COMMENT '期限24',
  `term_25` decimal(10,6) DEFAULT 0 COMMENT '期限25',
  `term_26` decimal(10,6) DEFAULT 0 COMMENT '期限26',
  `term_27` decimal(10,6) DEFAULT 0 COMMENT '期限27',
  `term_28` decimal(10,6) DEFAULT 0 COMMENT '期限28',
  `term_29` decimal(10,6) DEFAULT 0 COMMENT '期限29',
  `term_30` decimal(10,6) DEFAULT 0 COMMENT '期限30',
  `term_31` decimal(10,6) DEFAULT 0 COMMENT '期限31',
  `term_32` decimal(10,6) DEFAULT 0 COMMENT '期限32',
  `term_33` decimal(10,6) DEFAULT 0 COMMENT '期限33',
  `term_34` decimal(10,6) DEFAULT 0 COMMENT '期限34',
  `term_35` decimal(10,6) DEFAULT 0 COMMENT '期限35',
  `term_36` decimal(10,6) DEFAULT 0 COMMENT '期限36',
  `term_37` decimal(10,6) DEFAULT 0 COMMENT '期限37',
  `term_38` decimal(10,6) DEFAULT 0 COMMENT '期限38',
  `term_39` decimal(10,6) DEFAULT 0 COMMENT '期限39',
  `term_40` decimal(10,6) DEFAULT 0 COMMENT '期限40',
  `term_41` decimal(10,6) DEFAULT 0 COMMENT '期限41',
  `term_42` decimal(10,6) DEFAULT 0 COMMENT '期限42',
  `term_43` decimal(10,6) DEFAULT 0 COMMENT '期限43',
  `term_44` decimal(10,6) DEFAULT 0 COMMENT '期限44',
  `term_45` decimal(10,6) DEFAULT 0 COMMENT '期限45',
  `term_46` decimal(10,6) DEFAULT 0 COMMENT '期限46',
  `term_47` decimal(10,6) DEFAULT 0 COMMENT '期限47',
  `term_48` decimal(10,6) DEFAULT 0 COMMENT '期限48',
  `term_49` decimal(10,6) DEFAULT 0 COMMENT '期限49',
  `term_50` decimal(10,6) DEFAULT 0 COMMENT '期限50',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_curve_name_curve_id_date` (`curve_name`, `curve_id`, `date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='万得收益率曲线表';

-- =====================================================
-- TB0002: 关键久期参数表
-- =====================================================
DROP TABLE IF EXISTS `t_adur_key_duration_parameter`;
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `key_duration` varchar(20) NOT NULL COMMENT '关键久期',
  `parameter_val_set` mediumtext COMMENT 'JSON格式参数值集',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_key_duration` (`account_period`, `key_duration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期参数表';

-- =====================================================
-- TB0003: 久期资产明细表
-- =====================================================
DROP TABLE IF EXISTS `t_adur_duration_asset_detail`;
CREATE TABLE `t_adur_duration_asset_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `asset_sub_category` varchar(50) NOT NULL COMMENT '资产小小类',
  `holding_face_value` decimal(18,2) DEFAULT 0 COMMENT '持仓面值',
  `market_value` decimal(18,2) DEFAULT 0 COMMENT '市值',
  `book_balance` decimal(18,2) DEFAULT 0 COMMENT '账面余额',
  `book_value` decimal(18,2) DEFAULT 0 COMMENT '账面价值',
  `coupon_rate` decimal(10,6) DEFAULT 0 COMMENT '票面利率',
  `payment_method` varchar(20) NOT NULL COMMENT '付息方式',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `adjusted_value_date` date NOT NULL COMMENT '调整起息日',
  `adjusted_purchase_date` date NOT NULL COMMENT '调整买入日',
  `adjusted_maturity_date` date NOT NULL COMMENT '调整到期日',
  `issue_spread_calc_flag` char(1) NOT NULL COMMENT '发行时点价差计算标识',
  `spread_duration_stat_flag` char(1) DEFAULT '0' COMMENT '利差久期资产统计标识',
  `eval_spread` decimal(10,6) DEFAULT 0 COMMENT '评估时点价差',
  `spread_duration` decimal(10,6) DEFAULT 0 COMMENT '利差久期',
  `book_value_sigma_9` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ9%',
  `book_value_sigma_17` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ17%',
  `book_value_sigma_77` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ77%',
  `issue_present_value` decimal(18,2) DEFAULT 0 COMMENT '发行时点资产现值',
  `issue_spread` decimal(10,6) DEFAULT 0 COMMENT '发行时点价差',
  `eval_present_value` decimal(18,2) DEFAULT 0 COMMENT '评估时点资产现值',
  `eval_maturity_yield` decimal(10,6) DEFAULT 0 COMMENT '评估时点到期收益率',
  `asset_modified_duration` decimal(10,6) DEFAULT 0 COMMENT '资产修正久期',
  `eval_present_value_plus_50bp` decimal(18,2) DEFAULT 0 COMMENT '评估时点资产现值+50bp',
  `eval_present_value_minus_50bp` decimal(18,2) DEFAULT 0 COMMENT '评估时点资产现值-50bp',
  `asset_effective_duration` decimal(10,6) DEFAULT 0 COMMENT '资产有效久期',
  -- DV10风险指标字段（关键久期0-50年）
  `dv10_1` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0',
  `dv10_2` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0.5',
  `dv10_3` decimal(18,2) DEFAULT 0 COMMENT 'DV10_1',
  `dv10_4` decimal(18,2) DEFAULT 0 COMMENT 'DV10_2',
  `dv10_5` decimal(18,2) DEFAULT 0 COMMENT 'DV10_3',
  `dv10_6` decimal(18,2) DEFAULT 0 COMMENT 'DV10_4',
  `dv10_7` decimal(18,2) DEFAULT 0 COMMENT 'DV10_5',
  `dv10_8` decimal(18,2) DEFAULT 0 COMMENT 'DV10_6',
  `dv10_9` decimal(18,2) DEFAULT 0 COMMENT 'DV10_7',
  `dv10_10` decimal(18,2) DEFAULT 0 COMMENT 'DV10_8',
  `dv10_11` decimal(18,2) DEFAULT 0 COMMENT 'DV10_10',
  `dv10_12` decimal(18,2) DEFAULT 0 COMMENT 'DV10_12',
  `dv10_13` decimal(18,2) DEFAULT 0 COMMENT 'DV10_15',
  `dv10_14` decimal(18,2) DEFAULT 0 COMMENT 'DV10_20',
  `dv10_15` decimal(18,2) DEFAULT 0 COMMENT 'DV10_25',
  `dv10_16` decimal(18,2) DEFAULT 0 COMMENT 'DV10_30',
  `dv10_17` decimal(18,2) DEFAULT 0 COMMENT 'DV10_35',
  `dv10_18` decimal(18,2) DEFAULT 0 COMMENT 'DV10_40',
  `dv10_19` decimal(18,2) DEFAULT 0 COMMENT 'DV10_45',
  `dv10_20` decimal(18,2) DEFAULT 0 COMMENT 'DV10_50',
  -- DV10上升情景现值字段
  `dv10_1_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0_上升',
  `dv10_2_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0.5_上升',
  `dv10_3_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_1_上升',
  `dv10_4_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_2_上升',
  `dv10_5_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_3_上升',
  `dv10_6_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_4_上升',
  `dv10_7_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_5_上升',
  `dv10_8_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_6_上升',
  `dv10_9_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_7_上升',
  `dv10_10_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_8_上升',
  `dv10_11_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_10_上升',
  `dv10_12_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_12_上升',
  `dv10_13_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_15_上升',
  `dv10_14_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_20_上升',
  `dv10_15_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_25_上升',
  `dv10_16_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_30_上升',
  `dv10_17_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_35_上升',
  `dv10_18_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_40_上升',
  `dv10_19_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_45_上升',
  `dv10_20_up` decimal(18,2) DEFAULT 0 COMMENT 'DV10_50_上升',
  -- DV10下降情景现值字段
  `dv10_1_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0_下降',
  `dv10_2_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_0.5_下降',
  `dv10_3_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_1_下降',
  `dv10_4_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_2_下降',
  `dv10_5_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_3_下降',
  `dv10_6_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_4_下降',
  `dv10_7_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_5_下降',
  `dv10_8_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_6_下降',
  `dv10_9_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_7_下降',
  `dv10_10_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_8_下降',
  `dv10_11_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_10_下降',
  `dv10_12_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_12_下降',
  `dv10_13_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_15_下降',
  `dv10_14_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_20_下降',
  `dv10_15_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_25_下降',
  `dv10_16_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_30_下降',
  `dv10_17_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_35_下降',
  `dv10_18_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_40_下降',
  `dv10_19_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_45_下降',
  `dv10_20_down` decimal(18,2) DEFAULT 0 COMMENT 'DV10_50_下降',
  -- 现金流数据字段
  `issue_cashflow_set` mediumtext COMMENT '发行时点现金流值集',
  `eval_cashflow_set` mediumtext COMMENT '评估时点现金流值集',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_asset_number` (`account_period`, `asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='久期资产明细表';

-- =====================================================
-- TB0004: 年度折现曲线表（根据业务逻辑推断结构）
-- =====================================================
DROP TABLE IF EXISTS `t_adur_annual_discount_curve`;
CREATE TABLE `t_adur_annual_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `annual_discount_rate_set` text COMMENT 'JSON格式年度折现利率值集',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_asset_number_date_type` (`account_period`, `asset_number`, `date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='年度折现曲线表';

-- =====================================================
-- TB0005: 月度折现曲线表不含价差
-- =====================================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;
CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_set` text COMMENT 'JSON格式存储0-600期利率值',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_asset_number_date_type` (`account_period`, `asset_number`, `date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='月度折现曲线表不含价差';

-- =====================================================
-- TB0006: 月度折现曲线表含价差
-- =====================================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve_with_spread`;
CREATE TABLE `t_adur_monthly_discount_curve_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_with_spread_set` text COMMENT 'JSON格式存储0-600期含价差利率值',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_duration_basis_asset` (`account_period`, `duration_type`, `basis_point_type`, `asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='月度折现曲线表含价差';

-- =====================================================
-- TB0007: 月度折现因子表含价差
-- =====================================================
DROP TABLE IF EXISTS `t_adur_monthly_discount_factor_with_spread`;
CREATE TABLE `t_adur_monthly_discount_factor_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_factor_set` text COMMENT 'JSON格式存储0-600期折现因子值',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_duration_basis_asset` (`account_period`, `duration_type`, `basis_point_type`, `asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='月度折现因子表含价差';

-- =====================================================
-- TB0008: 关键久期折现曲线表含价差
-- =====================================================
DROP TABLE IF EXISTS `t_adur_key_duration_curve_with_spread`;
CREATE TABLE `t_adur_key_duration_curve_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(20) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `key_duration_discount_curve_with_spread_set` text COMMENT 'JSON格式存储0-600期关键久期折现曲线含价差值',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_key_term_stress_asset` (`account_period`, `key_term`, `stress_direction`, `asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期折现曲线表含价差';

-- =====================================================
-- TB0009: 关键久期折现因子表含价差
-- =====================================================
DROP TABLE IF EXISTS `t_adur_key_duration_factor_with_spread`;
CREATE TABLE `t_adur_key_duration_factor_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(20) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `key_duration_factor_with_spread_set` text COMMENT 'JSON格式存储0-600期关键久期折现因子含价差值',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_key_term_stress_asset` (`account_period`, `key_term`, `stress_direction`, `asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期折现因子表含价差';

-- =====================================================
-- TB0010: 久期资产结果汇总表
-- =====================================================
DROP TABLE IF EXISTS `t_adur_duration_asset_summary`;
CREATE TABLE `t_adur_duration_asset_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `market_value` decimal(18,2) DEFAULT 0 COMMENT '市值',
  `book_balance` decimal(18,2) DEFAULT 0 COMMENT '账面余额',
  `book_value` decimal(18,2) DEFAULT 0 COMMENT '账面价值',
  `book_value_sigma_0` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ0%',
  `book_value_sigma_9` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ9%',
  `book_value_sigma_17` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ17%',
  `book_value_sigma_77` decimal(18,2) DEFAULT 0 COMMENT '账面价值σ77%',
  `eval_maturity_yield` decimal(10,6) DEFAULT 0 COMMENT '评估时点到期收益率',
  `eval_present_value` decimal(18,2) DEFAULT 0 COMMENT '评估时点资产现值',
  `asset_modified_duration` decimal(10,6) DEFAULT 0 COMMENT '资产修正久期',
  `asset_effective_duration` decimal(10,6) DEFAULT 0 COMMENT '资产有效久期',
  -- DV10风险指标字段（关键久期0-50年）
  `dv10_0` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0',
  `dv10_0_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5',
  `dv10_1` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1',
  `dv10_2` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2',
  `dv10_3` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3',
  `dv10_4` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4',
  `dv10_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5',
  `dv10_6` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6',
  `dv10_7` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7',
  `dv10_8` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8',
  `dv10_10` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10',
  `dv10_12` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12',
  `dv10_15` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15',
  `dv10_20` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20',
  `dv10_25` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25',
  `dv10_30` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30',
  `dv10_35` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35',
  `dv10_40` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40',
  `dv10_45` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45',
  `dv10_50` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_account_name` (`account_period`, `account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='久期资产结果汇总表';

-- =====================================================
-- 索引创建
-- =====================================================

-- 万得收益率曲线表索引
CREATE INDEX `idx_wind_yield_curve_date` ON `t_base_wind_yield_curve` (`date`);
CREATE INDEX `idx_wind_yield_curve_id` ON `t_base_wind_yield_curve` (`curve_id`);

-- 关键久期参数表索引
CREATE INDEX `idx_key_duration_param_period` ON `t_adur_key_duration_parameter` (`account_period`);

-- 久期资产明细表索引
CREATE INDEX `idx_duration_asset_detail_period` ON `t_adur_duration_asset_detail` (`account_period`);
CREATE INDEX `idx_duration_asset_detail_account` ON `t_adur_duration_asset_detail` (`account_name`);
CREATE INDEX `idx_duration_asset_detail_security` ON `t_adur_duration_asset_detail` (`security_code`);

-- 年度折现曲线表索引
CREATE INDEX `idx_annual_discount_curve_period` ON `t_adur_annual_discount_curve` (`account_period`);
CREATE INDEX `idx_annual_discount_curve_date_type` ON `t_adur_annual_discount_curve` (`date_type`);

-- 月度折现曲线表不含价差索引
CREATE INDEX `idx_monthly_discount_curve_period` ON `t_adur_monthly_discount_curve` (`account_period`);
CREATE INDEX `idx_monthly_discount_curve_date_type` ON `t_adur_monthly_discount_curve` (`date_type`);

-- 月度折现曲线表含价差索引
CREATE INDEX `idx_monthly_discount_curve_spread_period` ON `t_adur_monthly_discount_curve_with_spread` (`account_period`);
CREATE INDEX `idx_monthly_discount_curve_spread_duration` ON `t_adur_monthly_discount_curve_with_spread` (`duration_type`);
CREATE INDEX `idx_monthly_discount_curve_spread_category` ON `t_adur_monthly_discount_curve_with_spread` (`curve_sub_category`);

-- 月度折现因子表含价差索引
CREATE INDEX `idx_monthly_discount_factor_period` ON `t_adur_monthly_discount_factor_with_spread` (`account_period`);
CREATE INDEX `idx_monthly_discount_factor_duration` ON `t_adur_monthly_discount_factor_with_spread` (`duration_type`);

-- 关键久期折现曲线表含价差索引
CREATE INDEX `idx_key_duration_curve_period` ON `t_adur_key_duration_curve_with_spread` (`account_period`);
CREATE INDEX `idx_key_duration_curve_key_term` ON `t_adur_key_duration_curve_with_spread` (`key_term`);
CREATE INDEX `idx_key_duration_curve_stress` ON `t_adur_key_duration_curve_with_spread` (`stress_direction`);

-- 关键久期折现因子表含价差索引
CREATE INDEX `idx_key_duration_factor_period` ON `t_adur_key_duration_factor_with_spread` (`account_period`);
CREATE INDEX `idx_key_duration_factor_key_term` ON `t_adur_key_duration_factor_with_spread` (`key_term`);
CREATE INDEX `idx_key_duration_factor_stress` ON `t_adur_key_duration_factor_with_spread` (`stress_direction`);

-- 久期资产结果汇总表索引
CREATE INDEX `idx_duration_asset_summary_period` ON `t_adur_duration_asset_summary` (`account_period`);
CREATE INDEX `idx_duration_asset_summary_account` ON `t_adur_duration_asset_summary` (`account_name`);

-- =====================================================
-- DDL生成完成
-- 基于设计文档：docs/design/adur_program_design.md 第2.3章节
-- 包含表：TB0001-TB0010（共10个表）
-- 生成时间：2025-01-16
-- =====================================================
