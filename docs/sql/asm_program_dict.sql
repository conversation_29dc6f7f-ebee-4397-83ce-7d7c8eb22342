-- =============================================
-- 资产规模与偿付能力管理模块字典数据
-- 基于 asm_program_design.sql DDL生成
-- 数据库: MySQL 8.0
-- 字符集: utf8
-- =============================================

-- =============================================
-- 字典类型数据
-- =============================================

-- 资产负债表类别字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('资产负债表类别','asm_balance_sheet_category','0','admin',NOW(),'',NULL,'资产负债表类别：资产、负债、所有者权益');

-- 是否删除字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('是否删除','asm_is_del','0','admin',NOW(),'',NULL,'是否删除标识：0否，1是');

-- =============================================
-- 字典数据
-- =============================================

-- 资产负债表类别字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'资产','01','asm_balance_sheet_category',NULL,'primary','Y','0','admin',NOW(),'',NULL,'资产类科目'),
(2,'负债','02','asm_balance_sheet_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'负债类科目'),
(3,'所有者权益','03','asm_balance_sheet_category',NULL,'success','N','0','admin',NOW(),'',NULL,'所有者权益类科目');

-- 是否删除字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'否','0','asm_is_del',NULL,'success','Y','0','admin',NOW(),'',NULL,'未删除'),
(2,'是','1','asm_is_del',NULL,'danger','N','0','admin',NOW(),'',NULL,'已删除');
