-- =============================================
-- ADUR月度折现因子表含价差菜单SQL
-- 表名：t_adur_monthly_discount_factor_with_spread
-- 功能：ADUR月度折现因子管理（含价差）
-- 对应表：TB0007 月度折现因子表含价差
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR月度折现因子含价差', '2548', '7', 'adurmonthlydiscountfactor', 'adur/monthly/discount/factor/index', 1, 0, 'C', '0', '0', 'adur:monthly:discount:factor:list', '#', 'admin', sysdate(), '', null, 'ADUR月度折现因子含价差菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现因子含价差导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:factor:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:monthly:discount:factor:list    - ADUR月度折现因子含价差列表查询
-- adur:monthly:discount:factor:query   - ADUR月度折现因子含价差详情查询
-- adur:monthly:discount:factor:add     - ADUR月度折现因子含价差新增
-- adur:monthly:discount:factor:edit    - ADUR月度折现因子含价差修改
-- adur:monthly:discount:factor:remove  - ADUR月度折现因子含价差删除
-- adur:monthly:discount:factor:export  - ADUR月度折现因子含价差导出
-- adur:monthly:discount:factor:import  - ADUR月度折现因子含价差导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为7，表示在ADUR模块中的第7个子菜单
-- 3. path 为 'adurmonthlydiscountfactor'，对应前端路由
-- 4. component 为 'adur/monthly/discount/factor/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:monthly:discount:factor:*' 格式
-- 6. 该表包含601个期限字段（term_0到term_600）和价差相关字段
-- 7. 与TB0006（月度折现曲线表含价差）相比，主要区别在于存储的是折现因子而非折现曲线
-- 8. 折现因子是基于折现曲线计算得出的，用于实际的现金流折现计算
-- =============================================
