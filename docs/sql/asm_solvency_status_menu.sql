-- =============================================
-- 偿付能力状况表菜单SQL
-- 表编号: TB0018
-- 表名: t_base_solvency_status
-- 模块: asm (资产规模与偿付能力管理)
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表', '3', '1', 'solvencyStatus', 'asm/solvency/status/index', 1, 0, 'C', '0', '0', 'asm:solvency:status:list', '#', 'admin', sysdate(), '', null, '偿付能力状况表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('偿付能力状况表导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'asm:solvency:status:import',       '#', 'admin', sysdate(), '', null, '');
