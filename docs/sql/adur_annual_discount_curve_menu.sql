-- =============================================
-- ADUR年度折现曲线表菜单SQL
-- 表名：t_adur_annual_discount_curve
-- 功能：ADUR年度折现曲线管理
-- 对应表：TB0004 年度折现曲线表
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR年度折现曲线', '2548', '4', 'adurannualdiscountcurve', 'adur/annual/discount/curve/index', 1, 0, 'C', '0', '0', 'adur:annual:discount:curve:list', '#', 'admin', sysdate(), '', null, 'ADUR年度折现曲线菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('年度折现曲线导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:annual:discount:curve:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:annual:discount:curve:list    - ADUR年度折现曲线列表查询
-- adur:annual:discount:curve:query   - ADUR年度折现曲线详情查询
-- adur:annual:discount:curve:add     - ADUR年度折现曲线新增
-- adur:annual:discount:curve:edit    - ADUR年度折现曲线修改
-- adur:annual:discount:curve:remove  - ADUR年度折现曲线删除
-- adur:annual:discount:curve:export  - ADUR年度折现曲线导出
-- adur:annual:discount:curve:import  - ADUR年度折现曲线导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为4，表示在ADUR模块中的第4个子菜单
-- 3. path 为 'adurannualdiscountcurve'，对应前端路由
-- 4. component 为 'adur/annual/discount/curve/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:annual:discount:curve:*' 格式
-- =============================================
