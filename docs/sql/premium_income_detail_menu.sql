-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细', '2000', '1', 'premiumIncomeDetail', 'cost/premium/income/detail/index', 1, 0, 'C', '0', '0', 'cost:premium:income:detail:list', 'money', 'admin', sysdate(), '', null, '保费收入明细菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('保费收入明细导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'cost:premium:income:detail:import',       '#', 'admin', sysdate(), '', null, '');
