-- =============================================
-- ADUR关键久期参数表菜单SQL
-- 表名：t_adur_key_duration_parameter
-- 功能：ADUR关键久期参数管理（从老表JSON中截取0-600标识的数据）
-- 数据来源：t_dur_key_duration_parameter表的parameter_val_set字段
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR关键久期参数', '2000', '2', 'adurkeydurationparameter', 'adur/key/duration/parameter/index', 1, 0, 'C', '0', '0', 'adur:key:duration:parameter:list', '#', 'admin', sysdate(), '', null, 'ADUR关键久期参数菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:import',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期参数数据迁移', @parentId, '7',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:parameter:migrate',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:key:duration:parameter:list    - ADUR关键久期参数列表查询
-- adur:key:duration:parameter:query   - ADUR关键久期参数详情查询
-- adur:key:duration:parameter:add     - ADUR关键久期参数新增
-- adur:key:duration:parameter:edit    - ADUR关键久期参数修改
-- adur:key:duration:parameter:remove  - ADUR关键久期参数删除
-- adur:key:duration:parameter:export  - ADUR关键久期参数导出
-- adur:key:duration:parameter:import  - ADUR关键久期参数导入
-- adur:key:duration:parameter:migrate - ADUR关键久期参数数据迁移
-- =============================================

-- 注意事项：
-- 1. parent_id 需要根据实际的父菜单ID进行调整（这里使用2000作为示例）
-- 2. order_num 需要根据实际的菜单顺序进行调整
-- 3. path 和 component 需要根据前端路由配置进行调整
-- 4. 执行前请确认sys_menu表结构与SQL语句匹配
-- =============================================
