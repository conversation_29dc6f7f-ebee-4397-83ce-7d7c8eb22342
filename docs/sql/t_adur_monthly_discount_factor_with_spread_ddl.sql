-- =============================================
-- ADUR月度折现因子表含价差DDL
-- 表名：t_adur_monthly_discount_factor_with_spread
-- 对应表：TB0007 月度折现因子表含价差
-- 基于最新字段列表要求生成
-- =============================================

DROP TABLE IF EXISTS `t_adur_monthly_discount_factor_with_spread`;

CREATE TABLE `t_adur_monthly_discount_factor_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_factor_set` text COMMENT '月度折现因子表含价差值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_duration` (`account_period`,`asset_number`,`duration_type`,`basis_point_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_duration_type` (`duration_type`),
  KEY `idx_basis_point_type` (`basis_point_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现因子表含价差';

-- =============================================
-- 字段说明
-- =============================================

/*
月度折现因子表含价差值集 (monthly_discount_factor_set) 字段说明：

1. 数据格式：JSON格式
   示例：{"0":0.9950,"1":0.9925,"2":0.9900,...,"600":0.8500}

2. 计算逻辑：
   等于 1/(1+月度折现曲线表含价差.期限X)^(月份/12)
   
   其中：
   - X 为期限编号（0-600）
   - 月份为期限编号对应的月份数
   - 月度折现曲线表含价差.期限X 为对应期限的折现曲线利率值

3. 计算示例：
   假设月度折现曲线表含价差.期限12 = 0.03（3%年利率）
   则月度折现因子表含价差值集中期限12的值 = 1/(1+0.03)^(12/12) = 1/1.03 ≈ 0.9709

4. 数据范围：
   - 期限范围：0-600（共601个期限点）
   - 数值类型：decimal(10,6)对应的数值
   - 缺省值：0

5. 索引设计：
   - 主键：id
   - 唯一索引：account_period + asset_number + duration_type + basis_point_type
   - 普通索引：account_period, asset_number, duration_type, basis_point_type
*/

-- =============================================
-- 使用示例
-- =============================================

/*
1. 插入数据示例：
INSERT INTO t_adur_monthly_discount_factor_with_spread (
  account_period, duration_type, basis_point_type, date_type, date,
  asset_number, account_name, asset_name, security_code, curve_id,
  monthly_discount_factor_set
) VALUES (
  '202501', '01', '01', '02', '2025-01-31',
  'ASSET001', '01', '国债001', 'T001', '1',
  '{"0":1.0000,"1":0.9975,"2":0.9950,"3":0.9925,"4":0.9900,"5":0.9875}'
);

2. 查询特定期限折现因子：
SELECT 
  asset_number,
  JSON_EXTRACT(monthly_discount_factor_set, '$.12') as factor_12_month,
  JSON_EXTRACT(monthly_discount_factor_set, '$.24') as factor_24_month
FROM t_adur_monthly_discount_factor_with_spread 
WHERE account_period = '202501';

3. 更新特定期限折现因子：
UPDATE t_adur_monthly_discount_factor_with_spread 
SET monthly_discount_factor_set = JSON_SET(
  monthly_discount_factor_set, 
  '$.12', 0.9709,
  '$.24', 0.9426
)
WHERE account_period = '202501' AND asset_number = 'ASSET001';

4. 计算折现因子示例（基于折现曲线利率）：
-- 假设从月度折现曲线表含价差获取利率数据
SELECT 
  asset_number,
  JSON_EXTRACT(monthly_discount_curve_set, '$.12') as rate_12_month,
  1/POWER(1 + JSON_EXTRACT(monthly_discount_curve_set, '$.12'), 12/12) as factor_12_month
FROM t_adur_monthly_discount_curve_with_spread 
WHERE account_period = '202501';
*/
