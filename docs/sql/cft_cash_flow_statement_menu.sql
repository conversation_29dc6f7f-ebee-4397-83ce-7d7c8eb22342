-- =============================================
-- 现金流量表菜单SQL
-- 表编号: TB0020
-- 表名: t_base_cash_flow_statement
-- 模块: cft (现金流量表管理)
-- =============================================

-- 现金流量表管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流量表管理', 0, 50, 'cft', NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', SYSDATE(), '', NULL, '现金流量表管理菜单');

-- 获取现金流量表管理菜单ID
SET @cftMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '现金流量表管理' AND parent_id = 0 AND path = 'cft');

-- 1. 现金流量表菜单 SQL (TB0020)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流量表', @cftMenuId, 1, 'cashFlowStatement', 'cft/cash/flow/statement/index', '', 1, 0, 'C', '0', '0', 'cft:cash:flow:statement:list', 'table', 'admin', SYSDATE(), '', NULL, '现金流量表菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流量表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流量表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流量表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流量表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流量表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流量表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:statement:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 2. 现金流项目映射表菜单 SQL (TB0021)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流项目映射', @cftMenuId, 2, 'cashFlowItemMapping', 'cft/cash/flow/item/mapping/index', '', 1, 0, 'C', '0', '0', 'cft:cash:flow:item:mapping:list', 'dict', 'admin', SYSDATE(), '', NULL, '现金流项目映射菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('现金流项目映射查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流项目映射新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流项目映射修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流项目映射删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流项目映射导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('现金流项目映射导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:cash:flow:item:mapping:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 3. ALMCF实际发生数本年累计表菜单 SQL (TB0022)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ALMCF实际发生数本年累计', @cftMenuId, 3, 'almcfActualYtd', 'cft/almcf/actual/ytd/index', '', 1, 0, 'C', '0', '0', 'cft:almcf:actual:ytd:list', 'chart', 'admin', SYSDATE(), '', NULL, 'ALMCF实际发生数本年累计菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ALMCF实际发生数查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:import', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF实际发生数计算', @parentId, 7, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:ytd:calculate', '#', 'admin', SYSDATE(), '', NULL, '');

-- 4. ALMCF实际发生数本季度累计表菜单 SQL (TB0023)
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ALMCF实际发生数本季度累计', @cftMenuId, 4, 'almcfActualQuarterly', 'cft/almcf/actual/quarterly/index', '', 1, 0, 'C', '0', '0', 'cft:almcf:actual:quarterly:list', 'chart', 'admin', SYSDATE(), '', NULL, 'ALMCF实际发生数本季度累计菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ALMCF季度数据查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:import', '#', 'admin', SYSDATE(), '', NULL, ''),
('ALMCF季度数据计算', @parentId, 7, '#', '', '', 1, 0, 'F', '0', '0', 'cft:almcf:actual:quarterly:calculate', '#', 'admin', SYSDATE(), '', NULL, '');
