-- =============================================
-- ADUR月度折现曲线表菜单SQL
-- 表名：t_adur_monthly_discount_curve
-- 功能：ADUR月度折现曲线管理（不含价差）
-- 对应表：TB0005 月度折现曲线表不含价差
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR月度折现曲线', '2548', '5', 'adurmonthlydiscountcurve', 'adur/monthly/discount/curve/index', 1, 0, 'C', '0', '0', 'adur:monthly:discount:curve:list', '#', 'admin', sysdate(), '', null, 'ADUR月度折现曲线菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:monthly:discount:curve:list    - ADUR月度折现曲线列表查询
-- adur:monthly:discount:curve:query   - ADUR月度折现曲线详情查询
-- adur:monthly:discount:curve:add     - ADUR月度折现曲线新增
-- adur:monthly:discount:curve:edit    - ADUR月度折现曲线修改
-- adur:monthly:discount:curve:remove  - ADUR月度折现曲线删除
-- adur:monthly:discount:curve:export  - ADUR月度折现曲线导出
-- adur:monthly:discount:curve:import  - ADUR月度折现曲线导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为5，表示在ADUR模块中的第5个子菜单
-- 3. path 为 'adurmonthlydiscountcurve'，对应前端路由
-- 4. component 为 'adur/monthly/discount/curve/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:monthly:discount:curve:*' 格式
-- 6. 该表包含601个期限字段（term_0到term_600），是数据量最大的表
-- =============================================
