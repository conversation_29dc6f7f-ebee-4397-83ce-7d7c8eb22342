-- =============================================
-- 资产规模与偿付能力表菜单SQL
-- 表编号: TB0019
-- 表名: t_asm_asset_scale_solvency
-- 模块: asm (资产规模与偿付能力管理)
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表', '3', '2', 'assetScaleSolvency', 'asm/asset/scale/solvency/index', 1, 0, 'C', '0', '0', 'asm:asset:scale:solvency:list', '#', 'admin', sysdate(), '', null, '资产规模与偿付能力表菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:import',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('资产规模与偿付能力表计算', @parentId, '7',  '#', '', 1, 0, 'F', '0', '0', 'asm:asset:scale:solvency:calculate',    '#', 'admin', sysdate(), '', null, '');
