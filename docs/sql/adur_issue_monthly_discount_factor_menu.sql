-- =============================================
-- ADUR关键久期折现因子表含价差(第二个)菜单SQL
-- 表名：t_adur_key_duration_factor_with_spread
-- 功能：ADUR关键久期折现因子管理（含价差，第二个）
-- 对应表：TB0010 关键久期折现因子表含价差(第二个)
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR关键久期折现因子含价差(第二个)', '2548', '10', 'adurissuemonthlydiscountfactor', 'adur/issue/monthly/discount/factor/index', 1, 0, 'C', '0', '0', 'adur:issue:monthly:discount:factor:list', '#', 'admin', sysdate(), '', null, 'ADUR关键久期折现因子含价差(第二个)菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现因子含价差(第二个)导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:issue:monthly:discount:factor:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:issue:monthly:discount:factor:list    - ADUR关键久期折现因子含价差(第二个)列表查询
-- adur:issue:monthly:discount:factor:query   - ADUR关键久期折现因子含价差(第二个)详情查询
-- adur:issue:monthly:discount:factor:add     - ADUR关键久期折现因子含价差(第二个)新增
-- adur:issue:monthly:discount:factor:edit    - ADUR关键久期折现因子含价差(第二个)修改
-- adur:issue:monthly:discount:factor:remove  - ADUR关键久期折现因子含价差(第二个)删除
-- adur:issue:monthly:discount:factor:export  - ADUR关键久期折现因子含价差(第二个)导出
-- adur:issue:monthly:discount:factor:import  - ADUR关键久期折现因子含价差(第二个)导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为10，表示在ADUR模块中的第10个子菜单
-- 3. path 为 'adurissuemonthlydiscountfactor'，对应前端路由
-- 4. component 为 'adur/issue/monthly/discount/factor/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:issue:monthly:discount:factor:*' 格式
-- 6. 该表包含601个期限字段（term_0到term_600）和价差相关字段
-- 7. 关键久期折现因子是基于关键久期折现曲线计算得出的
-- 8. 包含久期类型、基点类型、关键期限、压力方向等关键久期特有字段
-- 9. 支持资产级别的关键久期风险管理和计算
-- 10. 唯一约束：account_period + asset_number + key_term + stress_direction
-- 11. 相比TB0008（关键久期折现曲线），TB0010增加了issue_spread（发行时点价差）字段
-- 12. 折现因子是基于折现曲线计算得出的，用于实际的现金流折现计算
-- 13. TB0010是TB0009的第二个版本，具有相同的表结构但可能用于不同的业务场景
-- 14. 该表与TB0009（t_adur_key_duration_discount_factor）结构相同，但作为独立的数据存储
-- 15. 基点类型字段在TB0010中为必填字段，与TB0009有所区别
-- =============================================
