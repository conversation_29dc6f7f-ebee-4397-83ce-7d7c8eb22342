-- =============================================
-- ADUR月度折现曲线表含价差菜单SQL
-- 表名：t_adur_monthly_discount_curve_with_spread
-- 功能：ADUR月度折现曲线管理（含价差）
-- 对应表：TB0006 月度折现曲线表含价差
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR月度折现曲线含价差', '2548', '6', 'adurmonthlydiscountcurvewithspread', 'adur/monthly/discount/curve/with/spread/index', 1, 0, 'C', '0', '0', 'adur:monthly:discount:curve:with:spread:list', '#', 'admin', sysdate(), '', null, 'ADUR月度折现曲线含价差菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('月度折现曲线含价差导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:monthly:discount:curve:with:spread:list    - ADUR月度折现曲线含价差列表查询
-- adur:monthly:discount:curve:with:spread:query   - ADUR月度折现曲线含价差详情查询
-- adur:monthly:discount:curve:with:spread:add     - ADUR月度折现曲线含价差新增
-- adur:monthly:discount:curve:with:spread:edit    - ADUR月度折现曲线含价差修改
-- adur:monthly:discount:curve:with:spread:remove  - ADUR月度折现曲线含价差删除
-- adur:monthly:discount:curve:with:spread:export  - ADUR月度折现曲线含价差导出
-- adur:monthly:discount:curve:with:spread:import  - ADUR月度折现曲线含价差导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为6，表示在ADUR模块中的第6个子菜单
-- 3. path 为 'adurmonthlydiscountcurvewithspread'，对应前端路由
-- 4. component 为 'adur/monthly/discount/curve/with/spread/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:monthly:discount:curve:with:spread:*' 格式
-- 6. 该表包含JSON格式的月度折现曲线利率值集字段
-- 7. 与TB0005（月度折现曲线表不含价差）相比，增加了久期类型、基点类型、价差类型、价差等字段
-- 8. 表名已从 t_adur_issue_monthly_discount_curve 更改为 t_adur_monthly_discount_curve_with_spread
-- =============================================
