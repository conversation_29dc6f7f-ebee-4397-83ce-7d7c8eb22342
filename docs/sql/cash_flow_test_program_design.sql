-- 现金流测试模块表结构DDL
-- 基于 docs/design/cash_flow_program_design.md 中的 TB0001 设计

-- =============================================
-- TB0001: BP现金流量表 (t_cft_bp_cash_flow)
-- =============================================
DROP TABLE IF EXISTS `t_cft_bp_cash_flow`;
CREATE TABLE `t_cft_bp_cash_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景等，对应cft_scenario_name字典',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，值域：有效业务、新业务，对应cost_business_type字典',
  `actuarial_code` varchar(20) NOT NULL COMMENT '精算代码，产品精算代码，以字母开头，关联成本管理模块产品属性表(t_base_product_attribute)',
  `business_code` varchar(20) NOT NULL COMMENT '业务代码，产品业务代码，取自成本管理模块产品属性表',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称，产品全称，取自成本管理模块产品属性表',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险，取自成本管理模块产品属性表，对应cost_design_type字典',
  `variable_list` varchar(50) NOT NULL COMMENT '变量列表，变量代码标识，现金流变量分类',
  `variable_name` varchar(100) NOT NULL COMMENT '变量名称，变量中文名称，取自变量映射表，匹配字段：variable_list',
  `cash_flow_value_set` mediumtext COMMENT '现金流值集，JSON格式存储现金流数据，现金流数据集合',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bp_cash_flow` (`accounting_period`,`scenario_name`,`business_type`,`actuarial_code`,`variable_list`) COMMENT '唯一索引：账期+情景名称+业务类型+精算代码+变量列表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0001: BP现金流量表，用于存储BP现金流量数据，支持按情景、业务类型、产品和变量维度进行现金流数据管理';

-- =============================================
-- TB0002: 变量映射表 (t_cft_variable_mapping)
-- =============================================
DROP TABLE IF EXISTS `t_cft_variable_mapping`;
CREATE TABLE `t_cft_variable_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `variable_list` varchar(50) NOT NULL COMMENT '变量列表，变量代码标识，现金流变量分类',
  `variable_name` varchar(100) NOT NULL COMMENT '变量名称，变量中文名称，变量描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_variable_mapping` (`accounting_period`,`variable_list`) COMMENT '唯一索引：账期+变量列表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0002: 变量映射表，用于存储现金流变量的映射关系，建立变量代码与变量名称的对应关系';

-- =============================================
-- TB0003: 财务预算费用表 (t_cft_financial_budget_expense)
-- =============================================
DROP TABLE IF EXISTS `t_cft_financial_budget_expense`;
CREATE TABLE `t_cft_financial_budget_expense` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景等，对应cft_scenario_name字典',
  `financial_expense_type` varchar(50) NOT NULL COMMENT '财务费用类型，财务费用分类，对应cft_financial_expense_type字典',
  `date` varchar(10) NOT NULL COMMENT '日期，格式：YYYYQX（如2025Q1），季度标识',
  `amount` decimal(28,10) DEFAULT '0' COMMENT '金额，费用金额，财务费用数值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_financial_budget_expense` (`accounting_period`,`scenario_name`,`financial_expense_type`,`date`) COMMENT '唯一索引：账期+情景名称+财务费用类型+日期'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0003: 财务预算费用表，用于存储财务预算费用数据，支持按情景、费用类型和季度维度进行费用管理';

-- =============================================
-- TB0004: 精算业管费汇总表 (t_cft_actuarial_expense_summary)
-- =============================================
DROP TABLE IF EXISTS `t_cft_actuarial_expense_summary`;
CREATE TABLE `t_cft_actuarial_expense_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，汇总维度，来源于TB0001表，对应cft_scenario_name字典',
  `actuarial_expense_type` varchar(100) NOT NULL COMMENT '精算费用类型，汇总维度，来源于TB0001表的变量名称字段',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，汇总维度，来源于TB0001表，对应cost_business_type字典',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，汇总维度，来源于TB0001表，对应cost_design_type字典',
  `cash_flow_value_set` mediumtext COMMENT '现金流值集，存储现金流数据，格式：{"0": {"日期": "2025/01/31", "值": "2100"}}，汇总现金流数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_actuarial_expense_summary` (`accounting_period`,`scenario_name`,`actuarial_expense_type`,`business_type`,`design_type`) COMMENT '唯一索引：账期+情景名称+精算费用类型+业务类型+设计类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0004: 精算业管费汇总表，用于存储精算业管费汇总数据，通过加工计算得出';

-- =============================================
-- TB0005: 拆分比例表 (t_cft_split_ratio)
-- =============================================
DROP TABLE IF EXISTS `t_cft_split_ratio`;
CREATE TABLE `t_cft_split_ratio` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，汇总维度，来源于TB0004表，对应cft_scenario_name字典',
  `business_type` varchar(20) DEFAULT NULL COMMENT '业务类型，汇总维度，来源于TB0004表，拆分比例1时为空，对应cost_business_type字典',
  `design_type` varchar(50) DEFAULT NULL COMMENT '设计类型，汇总维度，来源于TB0004表，拆分比例1时为空，对应cost_design_type字典',
  `split_ratio_type` varchar(20) NOT NULL COMMENT '拆分比例类型，拆分比例分类：拆分比例1/拆分比例2，对应cft_split_ratio_type字典',
  `split_ratio_value_set` mediumtext COMMENT '拆分比例值集，存储拆分比例数据，JSON格式，计算得出的拆分比例',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_split_ratio` (`accounting_period`,`scenario_name`,`business_type`,`design_type`,`split_ratio_type`) COMMENT '唯一索引：账期+情景名称+业务类型+设计类型+拆分比例类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0005: 拆分比例表，用于存储拆分比例数据，通过加工计算得出';

-- =============================================
-- TB0006: 财务预算费用拆分表 (t_cft_financial_budget_expense_split)
-- =============================================
DROP TABLE IF EXISTS `t_cft_financial_budget_expense_split`;
CREATE TABLE `t_cft_financial_budget_expense_split` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，汇总维度，来源于TB0003、TB0005表，对应cft_scenario_name字典',
  `financial_expense_type` varchar(50) NOT NULL COMMENT '财务费用类型，汇总维度，来源于TB0003表，对应cft_financial_expense_type字典',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，汇总维度，来源于TB0005表，对应cost_business_type字典',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，汇总维度，来源于TB0005表，对应cost_design_type字典',
  `cash_flow_value_set` mediumtext COMMENT '现金流值集，拆分后的费用数据，JSON格式，拆分后的费用数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_financial_budget_expense_split` (`accounting_period`,`scenario_name`,`financial_expense_type`,`business_type`,`design_type`) COMMENT '唯一索引：账期+情景名称+财务费用类型+业务类型+设计类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0006: 财务预算费用拆分表，用于存储财务预算费用拆分数据，通过加工计算得出';

-- =============================================
-- TB0007: 业务现金流预测表 (t_cft_business_cash_flow_forecast)
-- =============================================
DROP TABLE IF EXISTS `t_cft_business_cash_flow_forecast`;
CREATE TABLE `t_cft_business_cash_flow_forecast` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202412）',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景等，对应cft_scenario_name字典',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险，对应cost_design_type字典',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务、新业务，对应cost_business_type字典',
  `item` varchar(50) NOT NULL COMMENT '项目，现金流项目名称，现金流分类',
  `future_first_quarter` decimal(28,10) DEFAULT '0' COMMENT '未来第一季度，未来第一季度现金流金额，预测数据',
  `future_second_quarter` decimal(28,10) DEFAULT '0' COMMENT '未来第二季度，未来第二季度现金流金额，预测数据',
  `future_third_quarter` decimal(28,10) DEFAULT '0' COMMENT '未来第三季度，未来第三季度现金流金额，预测数据',
  `future_fourth_quarter` decimal(28,10) DEFAULT '0' COMMENT '未来第四季度，未来第四季度现金流金额，预测数据',
  `future_second_year_remaining_quarters` decimal(28,10) DEFAULT '0' COMMENT '未来第二年剩余季度，未来第二年剩余季度现金流金额，预测数据',
  `future_third_year` decimal(28,10) DEFAULT '0' COMMENT '未来第三年，未来第三年现金流金额，预测数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_cash_flow_forecast` (`accounting_period`,`scenario_name`,`design_type`,`business_type`,`item`) COMMENT '唯一索引：账期+情景名称+设计类型+业务类型+项目'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='TB0007: 业务现金流预测表，用于存储业务现金流预测数据，通过加工计算得出';
