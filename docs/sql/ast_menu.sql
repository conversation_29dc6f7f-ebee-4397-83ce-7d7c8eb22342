-- 资产宽表模块菜单SQL
-- 包含资产宽表模块下所有功能的菜单配置

-- 资产宽表管理主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产宽表', 0, 60, 'ast', NULL, '', 1, 0, 'M', '0', '0', '', 'table', 'admin', SYSDATE(), '', NULL, '资产宽表管理菜单');

-- 获取资产宽表管理菜单ID
SELECT @astMenuId := LAST_INSERT_ID();

-- TB0001 - 组合持仓表管理菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('组合持仓管理', 2563, 1, 'account/holding', 'ast/account/holding/index', '', 1, 0, 'C', '0', '0', 'ast:account:holding:list', 'table', 'admin', SYSDATE(), '', NULL, '组合持仓管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('组合持仓查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('组合持仓新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('组合持仓修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('组合持仓删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('组合持仓导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('组合持仓导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:holding:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- TB0002 - 三账户持仓表管理菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('三账户持仓管理', 2563, 2, 'three/account/holding', 'ast/three/account/holding/index', '', 1, 0, 'C', '0', '0', 'ast:three:account:holding:list', 'table', 'admin', SYSDATE(), '', NULL, '三账户持仓管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('三账户持仓查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('三账户持仓新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('三账户持仓修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('三账户持仓删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('三账户持仓导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('三账户持仓导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:three:account:holding:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- TB0003 - VaR值分析表管理菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('VaR值分析管理', 2563, 3, 'var/analysis', 'ast/var/analysis/index', '', 1, 0, 'C', '0', '0', 'ast:var:analysis:list', 'chart', 'admin', SYSDATE(), '', NULL, 'VaR值分析管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('VaR值分析查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('VaR值分析新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('VaR值分析修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('VaR值分析删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('VaR值分析导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('VaR值分析导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:var:analysis:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- Wind行业表管理菜单 SQL (TB0004)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('Wind行业表管理', @astMenuId, 1, 'wind/industry', 'ast/wind/industry/index', '', 1, 0, 'C', '0', '0', 'ast:wind:industry:list', 'table', 'admin', SYSDATE(), '', NULL, 'Wind行业表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('Wind行业表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind行业表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind行业表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind行业表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind行业表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind行业表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:industry:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- Wind评级表管理菜单 SQL (TB0005)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('Wind评级表管理', @astMenuId, 4, 'wind/rating', 'ast/wind/rating/index', '', 1, 0, 'C', '0', '0', 'ast:wind:rating:list', 'star', 'admin', SYSDATE(), '', NULL, 'Wind评级表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('Wind评级表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind评级表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind评级表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind评级表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind评级表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('Wind评级表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:wind:rating:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 资产定义表管理菜单 SQL (TB0006)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资产定义表管理', @astMenuId, 5, 'asset/definition', 'ast/asset/definition/index', '', 1, 0, 'C', '0', '0', 'ast:asset:definition:list', 'list', 'admin', SYSDATE(), '', NULL, '资产定义表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产定义表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产定义表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产定义表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产定义表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产定义表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产定义表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:definition:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 账户名称映射表管理菜单 SQL (TB0007)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('账户名称映射管理', @astMenuId, 6, 'account/name/map', 'ast/account/name/map/index', '', 1, 0, 'C', '0', '0', 'ast:account:name:map:list', 'table', 'admin', SYSDATE(), '', NULL, '账户名称映射管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('账户名称映射查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('账户名称映射新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('账户名称映射修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('账户名称映射删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('账户名称映射导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('账户名称映射导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:account:name:map:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 资产基础配置表管理菜单 SQL (TB0008)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资产基础配置管理', @astMenuId, 7, 'asset/basic/config', 'ast/asset/basic/config/index', '', 1, 0, 'C', '0', '0', 'ast:asset:basic:config:list', 'build', 'admin', SYSDATE(), '', NULL, '资产基础配置管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产基础配置查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产基础配置新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产基础配置修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产基础配置删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产基础配置导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产基础配置导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:basic:config:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 资产配置状况分类表管理菜单 SQL (TB0009)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资产配置状况分类管理', @astMenuId, 8, 'asset/allocation/category', 'ast/asset/allocation/category/index', '', 1, 0, 'C', '0', '0', 'ast:asset:allocation:category:list', 'tree-table', 'admin', SYSDATE(), '', NULL, '资产配置状况分类管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产配置状况分类查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产配置状况分类新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产配置状况分类修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产配置状况分类删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产配置状况分类导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产配置状况分类导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:allocation:category:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 资产流动性分类及变现系数表管理菜单 SQL (TB0010)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资产流动性分类及变现系数表管理', @astMenuId, 9, 'asset/liquidity/coeff', 'ast/asset/liquidity/coeff/index', '', 1, 0, 'C', '0', '0', 'ast:asset:liquidity:coeff:list', 'money', 'admin', SYSDATE(), '', NULL, '资产流动性分类及变现系数表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('资产流动性分类及变现系数表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产流动性分类及变现系数表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产流动性分类及变现系数表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产流动性分类及变现系数表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产流动性分类及变现系数表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('资产流动性分类及变现系数表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:liquidity:coeff:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 信用评级映射表管理菜单 SQL (TB0011)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('信用评级映射表管理', @astMenuId, 10, 'credit/rating/map', 'ast/credit/rating/map/index', '', 1, 0, 'C', '0', '0', 'ast:credit:rating:map:list', 'star', 'admin', SYSDATE(), '', NULL, '信用评级映射表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('信用评级映射表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('信用评级映射表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('信用评级映射表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('信用评级映射表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('信用评级映射表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('信用评级映射表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:credit:rating:map:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 银行分类映射表管理菜单 SQL (TB0012)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('银行分类映射表管理', @astMenuId, 11, 'bank/classification/map', 'ast/bank/classification/map/index', '', 1, 0, 'C', '0', '0', 'ast:bank:classification:map:list', 'money', 'admin', SYSDATE(), '', NULL, '银行分类映射表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('银行分类映射表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('银行分类映射表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('银行分类映射表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('银行分类映射表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('银行分类映射表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('银行分类映射表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:bank:classification:map:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 付息方式映射表管理菜单 SQL (TB0013)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('付息方式映射表管理', @astMenuId, 12, 'payment/method/map', 'ast/payment/method/map/index', '', 1, 0, 'C', '0', '0', 'ast:payment:method:map:list', 'money', 'admin', SYSDATE(), '', NULL, '付息方式映射表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('付息方式映射表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('付息方式映射表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('付息方式映射表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('付息方式映射表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('付息方式映射表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('付息方式映射表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:payment:method:map:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 折现曲线配置表管理菜单 SQL (TB0014)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('折现曲线配置表管理', @astMenuId, 13, 'discount/curve/config', 'ast/discount/curve/config/index', '', 1, 0, 'C', '0', '0', 'ast:discount:curve:config:list', 'chart', 'admin', SYSDATE(), '', NULL, '折现曲线配置表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('折现曲线配置表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线配置表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线配置表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线配置表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线配置表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('折现曲线配置表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:discount:curve:config:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 固收资产剩余期限资产分类表管理菜单 SQL (TB0015)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('固收资产剩余期限资产分类表管理', @astMenuId, 14, 'fixed/income/term/cat', 'ast/fixed/income/term/cat/index', '', 1, 0, 'C', '0', '0', 'ast:fixed:income:term:cat:list', 'table', 'admin', SYSDATE(), '', NULL, '固收资产剩余期限资产分类表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('固收资产剩余期限资产分类表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('固收资产剩余期限资产分类表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('固收资产剩余期限资产分类表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('固收资产剩余期限资产分类表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('固收资产剩余期限资产分类表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('固收资产剩余期限资产分类表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:fixed:income:term:cat:import', '#', 'admin', SYSDATE(), '', NULL, '');

-- 整体资产明细表管理菜单 SQL (TB0016)
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('整体资产明细表管理', 2563, 16, 'asset/detail/overall', 'ast/asset/detail/overall/index', '', 1, 0, 'C', '0', '0', 'ast:asset:detail:overall:list', 'table', 'admin', SYSDATE(), '', NULL, '整体资产明细表管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('整体资产明细表查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('整体资产明细表新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('整体资产明细表修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('整体资产明细表删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('整体资产明细表导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('整体资产明细表导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'ast:asset:detail:overall:import', '#', 'admin', SYSDATE(), '', NULL, '');

