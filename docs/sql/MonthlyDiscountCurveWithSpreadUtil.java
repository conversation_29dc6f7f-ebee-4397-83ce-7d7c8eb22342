package com.ruoyi.adur.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 月度折现曲线含价差工具类
 * 用于处理JSON格式的月度折现曲线含价差数据和相关计算
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public class MonthlyDiscountCurveWithSpreadUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(MonthlyDiscountCurveWithSpreadUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final MathContext MATH_CONTEXT = new MathContext(10, RoundingMode.HALF_UP);
    
    // 基点调整值
    private static final BigDecimal BP_50 = new BigDecimal("0.005"); // 50bp = 0.5%
    
    /**
     * 根据曲线细分类计算含价差的月度折现曲线
     * 
     * @param baseRateJsonStr 基础利率JSON字符串（来自月度折现曲线表不含价差）
     * @param spread 价差值
     * @param curveSubCategory 曲线细分类
     * @return 含价差的月度折现曲线JSON字符串
     */
    public static String calculateRateWithSpread(String baseRateJsonStr, BigDecimal spread, String curveSubCategory) {
        if (baseRateJsonStr == null || baseRateJsonStr.trim().isEmpty()) {
            logger.warn("输入的基础利率JSON字符串为空");
            return "{}";
        }
        
        if (spread == null) {
            spread = BigDecimal.ZERO;
        }
        
        try {
            JsonNode baseRateNode = objectMapper.readTree(baseRateJsonStr);
            ObjectNode resultNode = objectMapper.createObjectNode();
            
            baseRateNode.fieldNames().forEachRemaining(termStr -> {
                try {
                    BigDecimal baseRate = new BigDecimal(baseRateNode.get(termStr).asText());
                    BigDecimal adjustedRate = calculateAdjustedRate(baseRate, spread, curveSubCategory);
                    resultNode.put(termStr, adjustedRate);
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或利率失败: {}", termStr);
                }
            });
            
            return objectMapper.writeValueAsString(resultNode);
            
        } catch (JsonProcessingException e) {
            logger.error("计算含价差利率失败: {}", baseRateJsonStr, e);
            return "{}";
        }
    }
    
    /**
     * 根据曲线细分类计算调整后的利率
     * 
     * @param baseRate 基础利率
     * @param spread 价差
     * @param curveSubCategory 曲线细分类
     * @return 调整后的利率
     */
    public static BigDecimal calculateAdjustedRate(BigDecimal baseRate, BigDecimal spread, String curveSubCategory) {
        if (baseRate == null) {
            baseRate = BigDecimal.ZERO;
        }
        if (spread == null) {
            spread = BigDecimal.ZERO;
        }
        
        BigDecimal adjustedRate = baseRate.add(spread, MATH_CONTEXT);
        
        // 根据曲线细分类进行额外调整
        switch (curveSubCategory) {
            case "1": // 修正久期 + 0bp + 发行时点 + 发行时点价差
            case "2": // 修正久期 + 0bp + 评估时点 + 发行时点价差
            case "5": // 利差久期 + 0bp + 评估时点 + 评估时点价差
                // 只加价差，无额外调整
                break;
            case "3": // 有效久期 + +50bp + 评估时点 + 发行时点价差
                adjustedRate = adjustedRate.add(BP_50, MATH_CONTEXT);
                break;
            case "4": // 有效久期 + -50bp + 评估时点 + 发行时点价差
                adjustedRate = adjustedRate.subtract(BP_50, MATH_CONTEXT);
                break;
            default:
                logger.warn("未知的曲线细分类: {}", curveSubCategory);
                break;
        }
        
        return adjustedRate;
    }
    
    /**
     * 确定曲线细分类
     * 
     * @param durationType 久期类型
     * @param basisPointType 基点类型
     * @param dateType 日期类型
     * @param spreadType 价差类型
     * @return 曲线细分类
     */
    public static String determineCurveSubCategory(String durationType, String basisPointType, 
                                                   String dateType, String spreadType) {
        if ("修正久期".equals(durationType) && "0bp".equals(basisPointType) && 
            "发行时点".equals(dateType) && "发行时点价差".equals(spreadType)) {
            return "1";
        } else if ("修正久期".equals(durationType) && "0bp".equals(basisPointType) && 
                   "评估时点".equals(dateType) && "发行时点价差".equals(spreadType)) {
            return "2";
        } else if ("有效久期".equals(durationType) && "+50bp".equals(basisPointType) && 
                   "评估时点".equals(dateType) && "发行时点价差".equals(spreadType)) {
            return "3";
        } else if ("有效久期".equals(durationType) && "-50bp".equals(basisPointType) && 
                   "评估时点".equals(dateType) && "发行时点价差".equals(spreadType)) {
            return "4";
        } else if ("利差久期".equals(durationType) && "0bp".equals(basisPointType) && 
                   "评估时点".equals(dateType) && "评估时点价差".equals(spreadType)) {
            return "5";
        }
        
        logger.warn("无法确定曲线细分类: durationType={}, basisPointType={}, dateType={}, spreadType={}", 
                   durationType, basisPointType, dateType, spreadType);
        return "0"; // 默认值
    }
    
    // 字典值常量
    private static final String DURATION_TYPE_MODIFIED = "01";    // 修正久期
    private static final String DURATION_TYPE_EFFECTIVE = "02";   // 有效久期
    private static final String DURATION_TYPE_SPREAD = "03";      // 利差久期

    /**
     * 确定折现曲线标识
     *
     * @param durationType 久期类型（字典值：01-修正久期，02-有效久期，03-利差久期）
     * @param originalCurveId 原始折现曲线标识（来自年度折现曲线表）
     * @return 折现曲线标识
     */
    public static String determineCurveId(String durationType, String originalCurveId) {
        if (DURATION_TYPE_SPREAD.equals(durationType)) { // 利差久期
            return "1";
        } else if (DURATION_TYPE_MODIFIED.equals(durationType) || DURATION_TYPE_EFFECTIVE.equals(durationType)) { // 修正久期或有效久期
            return originalCurveId != null ? originalCurveId : "1";
        }

        return "1"; // 默认值
    }
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param rateMap 期限-利率映射
     * @return JSON字符串
     */
    public static String mapToJson(Map<Integer, BigDecimal> rateMap) {
        try {
            ObjectNode jsonNode = objectMapper.createObjectNode();
            for (Map.Entry<Integer, BigDecimal> entry : rateMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("转换Map到JSON失败", e);
            return "{}";
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param jsonStr JSON字符串
     * @return 期限-利率映射
     */
    public static Map<Integer, BigDecimal> jsonToMap(String jsonStr) {
        Map<Integer, BigDecimal> rateMap = new HashMap<>();
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return rateMap;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                try {
                    Integer term = Integer.parseInt(fieldName);
                    BigDecimal rate = new BigDecimal(jsonNode.get(fieldName).asText());
                    rateMap.put(term, rate);
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或利率失败: {}", fieldName);
                }
            });
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return rateMap;
    }
    
    /**
     * 获取指定期限的含价差利率
     * 
     * @param jsonStr JSON字符串
     * @param term 期限（月份）
     * @return 利率值，如果不存在返回null
     */
    public static BigDecimal getRate(String jsonStr, Integer term) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode rateNode = jsonNode.get(term.toString());
            if (rateNode != null) {
                return new BigDecimal(rateNode.asText());
            }
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return null;
    }
    
    /**
     * 设置指定期限的含价差利率
     * 
     * @param jsonStr 原JSON字符串
     * @param term 期限（月份）
     * @param rate 利率值
     * @return 更新后的JSON字符串
     */
    public static String setRate(String jsonStr, Integer term, BigDecimal rate) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            jsonNode.put(term.toString(), rate);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("设置含价差利率失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 验证JSON格式是否正确
     * 
     * @param jsonStr JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            objectMapper.readTree(jsonStr);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 获取JSON中包含的期限数量
     * 
     * @param jsonStr JSON字符串
     * @return 期限数量
     */
    public static int getTermCount(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return 0;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.size();
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return 0;
        }
    }
    
    /**
     * 批量计算含价差利率
     * 
     * @param baseRateMap 基础利率映射
     * @param spread 价差
     * @param curveSubCategory 曲线细分类
     * @return 含价差利率映射
     */
    public static Map<Integer, BigDecimal> calculateBatchRateWithSpread(Map<Integer, BigDecimal> baseRateMap, 
                                                                        BigDecimal spread, String curveSubCategory) {
        Map<Integer, BigDecimal> resultMap = new HashMap<>();
        
        for (Map.Entry<Integer, BigDecimal> entry : baseRateMap.entrySet()) {
            Integer term = entry.getKey();
            BigDecimal baseRate = entry.getValue();
            BigDecimal adjustedRate = calculateAdjustedRate(baseRate, spread, curveSubCategory);
            resultMap.put(term, adjustedRate);
        }
        
        return resultMap;
    }
}
