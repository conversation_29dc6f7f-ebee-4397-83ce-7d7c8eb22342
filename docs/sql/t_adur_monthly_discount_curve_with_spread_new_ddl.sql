-- =============================================
-- ADUR月度折现曲线表含价差DDL
-- 表名：t_adur_monthly_discount_curve_with_spread
-- 对应表：TB0006 月度折现曲线表含价差
-- 基于最新字段列表要求生成
-- =============================================

DROP TABLE IF EXISTS `t_adur_monthly_discount_curve_with_spread`;

CREATE TABLE `t_adur_monthly_discount_curve_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_with_spread_set` text COMMENT '月度折现曲线利率含价差值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_duration` (`account_period`,`asset_number`,`duration_type`,`basis_point_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_duration_type` (`duration_type`),
  KEY `idx_basis_point_type` (`basis_point_type`),
  KEY `idx_curve_sub_category` (`curve_sub_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现曲线表含价差';

-- =============================================
-- 字段说明
-- =============================================

/*
字段取值规范：

1. 久期类型 (duration_type)：
   - 01：修正久期
   - 02：有效久期
   - 03：利差久期

2. 基点类型 (basis_point_type)：
   - 01：0bp
   - 02：+50bp
   - 03：-50bp

3. 价差类型 (spread_type)：
   - 01：发行时点价差
   - 02：评估时点价差

4. 账户名称 (account_name)：
   - 01：传统账户
   - 02：分红账户
   - 03：万能账户
   - 04：普通账户

5. 曲线细分类 (curve_sub_category)：
   - 1：修正久期 + 0bp + 发行时点 + 发行时点价差
   - 2：修正久期 + 0bp + 评估时点 + 发行时点价差
   - 3：有效久期 + +50bp + 评估时点 + 发行时点价差
   - 4：有效久期 + -50bp + 评估时点 + 发行时点价差
   - 5：利差久期 + 0bp + 评估时点 + 评估时点价差

5. 折现曲线标识 (curve_id)：
   - 修正久期/有效久期：同年度折现曲线表
   - 利差久期：固定为1

月度折现曲线利率含价差值集 (monthly_discount_rate_with_spread_set) 字段说明：

1. 数据格式：JSON格式
   示例：{"0":0.0275,"1":0.0285,"2":0.0295,...,"600":0.0485}

2. 计算逻辑：
   根据曲线细分类确定计算方式：
   
   - 曲线细分类=1：月度折现曲线表不含价差.期限X + 价差
     匹配条件：日期类型=发行时点 AND 账户名称相同 AND 证券代码相同
   
   - 曲线细分类=2：月度折现曲线表不含价差.期限X + 价差
     匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
   
   - 曲线细分类=3：月度折现曲线表不含价差.期限X + 价差 + 0.005
     匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
   
   - 曲线细分类=4：月度折现曲线表不含价差.期限X + 价差 - 0.005
     匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
   
   - 曲线细分类=5：月度折现曲线表不含价差.期限X + 价差
     匹配条件：日期类型=评估时点 AND 折现曲线标识=1

3. 数据范围：
   - 期限范围：0-600（共601个期限点）
   - 数值类型：decimal(10,6)对应的数值
   - 缺省值：0

4. 索引设计：
   - 主键：id
   - 唯一索引：account_period + asset_number + duration_type + basis_point_type
   - 普通索引：account_period, asset_number, duration_type, basis_point_type, curve_sub_category
*/

-- =============================================
-- 使用示例
-- =============================================

/*
1. 插入数据示例：
INSERT INTO t_adur_monthly_discount_curve_with_spread (
  account_period, duration_type, basis_point_type, date_type, date,
  spread_type, spread, curve_sub_category, asset_number, account_name,
  asset_name, security_code, curve_id, monthly_discount_rate_with_spread_set
) VALUES (
  '202501', '01', '01', '02', '2025-01-31',
  '01', 0.001500, '2', 'ASSET001', '01',
  '国债001', 'T001', '1',
  '{"0":0.0265,"1":0.0275,"2":0.0285,"3":0.0295,"4":0.0305,"5":0.0315}'
);

2. 查询特定期限含价差利率：
SELECT 
  asset_number,
  curve_sub_category,
  JSON_EXTRACT(monthly_discount_rate_with_spread_set, '$.12') as rate_12_month,
  JSON_EXTRACT(monthly_discount_rate_with_spread_set, '$.24') as rate_24_month
FROM t_adur_monthly_discount_curve_with_spread 
WHERE account_period = '202501' AND duration_type = '01';

3. 更新特定期限含价差利率：
UPDATE t_adur_monthly_discount_curve_with_spread 
SET monthly_discount_rate_with_spread_set = JSON_SET(
  monthly_discount_rate_with_spread_set, 
  '$.12', 0.0320,
  '$.24', 0.0340
)
WHERE account_period = '202501' AND asset_number = 'ASSET001';

4. 根据曲线细分类查询：
SELECT 
  asset_number,
  duration_type,
  basis_point_type,
  spread,
  JSON_EXTRACT(monthly_discount_rate_with_spread_set, '$.0') as base_rate
FROM t_adur_monthly_discount_curve_with_spread 
WHERE curve_sub_category = '3';  -- 有效久期+50bp情景
*/

-- =============================================
-- 数据验证查询
-- =============================================

-- 验证JSON格式是否正确
SELECT 
  id,
  account_period,
  asset_number,
  curve_sub_category,
  CASE 
    WHEN JSON_VALID(monthly_discount_rate_with_spread_set) = 1 THEN 'Valid JSON'
    ELSE 'Invalid JSON'
  END as json_status,
  JSON_LENGTH(monthly_discount_rate_with_spread_set) as json_length
FROM `t_adur_monthly_discount_curve_with_spread`
WHERE monthly_discount_rate_with_spread_set IS NOT NULL
LIMIT 10;

-- 验证曲线细分类分布
SELECT 
  curve_sub_category,
  duration_type,
  basis_point_type,
  date_type,
  spread_type,
  COUNT(*) as record_count
FROM `t_adur_monthly_discount_curve_with_spread`
GROUP BY curve_sub_category, duration_type, basis_point_type, date_type, spread_type
ORDER BY curve_sub_category;

-- 验证价差计算逻辑
SELECT 
  asset_number,
  duration_type,
  spread_type,
  spread,
  JSON_EXTRACT(monthly_discount_rate_with_spread_set, '$.0') as rate_with_spread_0,
  JSON_EXTRACT(monthly_discount_rate_with_spread_set, '$.12') as rate_with_spread_12
FROM `t_adur_monthly_discount_curve_with_spread`
WHERE curve_sub_category IN ('1', '2', '5')
LIMIT 5;
