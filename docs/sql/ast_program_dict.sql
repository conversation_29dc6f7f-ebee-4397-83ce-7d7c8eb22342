-- =============================================
-- 资产宽表模块字典数据DML
-- 数据库：MySQL 8.0
-- 字符集：utf8
-- =============================================

-- TB0001 - 组合持仓表
-- 该表中没有需要生成字典数据的枚举类型字段
-- 所有字段都是基础数据类型：accounting_period、account_name、security_code、asset_name、market_value

-- TB0002 - 三账户持仓表
-- 该表中没有需要生成字典数据的枚举类型字段
-- 会计类型字段(accounting_type)保持原始导入数据，不使用字典映射

-- TB0003 - VaR值分析表字典数据

-- VaR数据类型字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('VaR数据类型','ast_var_data_type','0','admin',NOW(),'',NULL,'VaR值分析表的数据类型分类，用于区分四种不同的VaR分析类型');

-- VaR数据类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'债基1年','债基1年','ast_var_data_type',NULL,'primary','N','0','admin',NOW(),'',NULL,'债基1年VAR值表数据'),
(2,'债基3年','债基3年','ast_var_data_type',NULL,'success','N','0','admin',NOW(),'',NULL,'债基3年VAR值表数据'),
(3,'权益1年','权益1年','ast_var_data_type',NULL,'warning','N','0','admin',NOW(),'',NULL,'权益1年VAR值表数据'),
(4,'权益3年','权益3年','ast_var_data_type',NULL,'danger','N','0','admin',NOW(),'',NULL,'权益3年VAR值表数据');

-- TB0004 - Wind行业表
-- 该表中没有需要生成字典数据的枚举类型字段
-- industry字段为Wind行业分类，属于外部系统提供的动态数据，不需要预定义字典数据

-- TB0005 - Wind评级表
-- entity_rating和bond_rating字段使用信用评级字典数据

-- TB0011 - 信用评级映射表
-- credit_rating、credit_rating_table_used、discount_curve_rating字段使用信用评级字典数据

-- 信用评级字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('信用评级','ast_credit_rating','0','admin',NOW(),'',NULL,'资产信用评级分类，用于Wind评级表、资产定义表等');

-- 信用评级字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'AA','01','ast_credit_rating',NULL,'warning','N','0','admin',NOW(),'',NULL,'信用评级AA级'),
(2,'AA+','02','ast_credit_rating',NULL,'primary','N','0','admin',NOW(),'',NULL,'信用评级AA+级'),
(3,'AAA','03','ast_credit_rating',NULL,'success','Y','0','admin',NOW(),'',NULL,'信用评级AAA级'),
(4,'不涉及','04','ast_credit_rating',NULL,'info','N','0','admin',NOW(),'',NULL,'不涉及信用评级'),
(5,'免评级','05','ast_credit_rating',NULL,'info','N','0','admin',NOW(),'',NULL,'免评级'),
(6,'无评级','06','ast_credit_rating',NULL,'danger','N','0','admin',NOW(),'',NULL,'无评级'),
(7,'A','07','ast_credit_rating',NULL,'warning','N','0','admin',NOW(),'',NULL,'信用评级A级'),
(8,'BBB','08','ast_credit_rating',NULL,'warning','N','0','admin',NOW(),'',NULL,'信用评级BBB级'),
(9,'BBB以下','09','ast_credit_rating',NULL,'danger','N','0','admin',NOW(),'',NULL,'信用评级BBB以下');

-- TB0006 - 资产定义表字典数据

-- 资产小小类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('资产小小类','ast_asset_sub_sub_category','0','admin',NOW(),'',NULL,'资产小小类分类，用于资产定义表等');

-- 资产小小类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'存款','01','ast_asset_sub_sub_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'存款类资产'),
(2,'政府债券','02','ast_asset_sub_sub_category',NULL,'success','N','0','admin',NOW(),'',NULL,'政府债券类资产'),
(3,'中期票据','03','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'中期票据类资产'),
(4,'债券型基金','04','ast_asset_sub_sub_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'债券型基金类资产'),
(5,'公募基金固定收益类专户','05','ast_asset_sub_sub_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'公募基金固定收益类专户'),
(6,'公司债企业债','06','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'公司债企业债类资产'),
(7,'固定收益类保险资产管理产品','07','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'固定收益类保险资产管理产品'),
(8,'货币类保险资产管理产品','08','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'货币类保险资产管理产品'),
(9,'不动产债权投资计划','09','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'不动产债权投资计划'),
(10,'基础设施债权投资计划','10','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'基础设施债权投资计划'),
(11,'信托计划','11','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'信托计划类资产'),
(12,'上市普通股票','12','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'上市普通股票类资产'),
(13,'证券投资基金','13','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'证券投资基金类资产'),
(14,'可转债','14','ast_asset_sub_sub_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'可转债类资产'),
(15,'以自有资金对保险类企业的股权投资','15','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'以自有资金对保险类企业的股权投资'),
(16,'不含保证条款的股权投资计划、私募股权投资基金','16','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'不含保证条款的股权投资计划、私募股权投资基金'),
(17,'权益类和混合类保险资管产品','17','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'权益类和混合类保险资管产品'),
(18,'权益类信托计划','18','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'权益类信托计划'),
(19,'不动产相关金融产品','19','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'不动产相关金融产品'),
(20,'权益类基金专户产品','20','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'权益类基金专户产品'),
(21,'REITS','21','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'REITS类资产'),
(22,'融资回购','22','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'融资回购类资产'),
(23,'活期存款','23','ast_asset_sub_sub_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'活期存款类资产'),
(24,'回购','24','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'回购类资产'),
(25,'负债','25','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'负债类资产'),
(26,'其他','26','ast_asset_sub_sub_category',NULL,'secondary','Y','0','admin',NOW(),'',NULL,'其他类资产'),
(27,'项目资产支持计划','27','ast_asset_sub_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'项目资产支持计划'),
(28,'短期、超短期融资券','28','ast_asset_sub_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'短期、超短期融资券'),
(29,'其他普通未上市股权','29','ast_asset_sub_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'其他普通未上市股权');

-- 境内外标识字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('境内外标识','ast_domestic_foreign','0','admin',NOW(),'',NULL,'境内外标识分类，用于资产定义表等');

-- 境内外标识字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'境内资产','01','ast_domestic_foreign',NULL,'success','Y','0','admin',NOW(),'',NULL,'境内资产'),
(2,'境外资产','02','ast_domestic_foreign',NULL,'warning','N','0','admin',NOW(),'',NULL,'境外资产'),
(3,'不涉及','03','ast_domestic_foreign',NULL,'info','N','0','admin',NOW(),'',NULL,'表示没有境内外这个属性');

-- 五级分类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('五级分类','ast_five_level_classification','0','admin',NOW(),'',NULL,'五级分类标准，用于资产定义表等');

-- 五级分类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'正常类','01','ast_five_level_classification',NULL,'success','Y','0','admin',NOW(),'',NULL,'正常类资产'),
(2,'关注类','02','ast_five_level_classification',NULL,'primary','N','0','admin',NOW(),'',NULL,'关注类资产'),
(3,'次级类','03','ast_five_level_classification',NULL,'warning','N','0','admin',NOW(),'',NULL,'次级类资产'),
(4,'可疑类','04','ast_five_level_classification',NULL,'warning','N','0','admin',NOW(),'',NULL,'可疑类资产'),
(5,'损失类','05','ast_five_level_classification',NULL,'danger','N','0','admin',NOW(),'',NULL,'损失类资产'),
(6,'不良类','06','ast_five_level_classification',NULL,'danger','N','0','admin',NOW(),'',NULL,'不良类资产');

-- 账户名称映射字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('账户名称','ast_account_name_mapping','0','admin',NOW(),'',NULL,'账户名称映射分类，用于账户名称映射表等');

-- 账户名称映射字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'传统账户','01','ast_account_name_mapping',NULL,'primary','Y','0','admin',NOW(),'',NULL,'传统型保险账户'),
(2,'分红账户','02','ast_account_name_mapping',NULL,'success','N','0','admin',NOW(),'',NULL,'分红型保险账户'),
(3,'万能账户','03','ast_account_name_mapping',NULL,'info','N','0','admin',NOW(),'',NULL,'万能型保险账户'),
(4,'独立账户','04','ast_account_name_mapping',NULL,'warning','N','0','admin',NOW(),'',NULL,'独立账户'),
(5,'资本补充债账户','05','ast_account_name_mapping',NULL,'danger','N','0','admin',NOW(),'',NULL,'资本补充债账户'),
(6,'普通账户','06','ast_account_name_mapping',NULL,'default','N','0','admin',NOW(),'',NULL,'普通账户，所有账户的汇总');

-- TB0008 - 资产基础配置表字典数据

-- 固收资产细分类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('固收资产细分类','ast_fixed_income_sub_category','0','admin',NOW(),'',NULL,'固收资产细分类，用于资产基础配置表');

-- 固收资产细分类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'传统固定收益类投资资产','01','ast_fixed_income_sub_category',NULL,'success','Y','0','admin',NOW(),'',NULL,'传统固定收益类投资资产'),
(2,'非标准固定收益类投资资产','02','ast_fixed_income_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'非标准固定收益类投资资产'),
(3,'其他固定收益类金融产品','03','ast_fixed_income_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'其他固定收益类金融产品');

-- 行业统计标识字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('行业统计标识','ast_industry_statistics_flag','0','admin',NOW(),'',NULL,'行业统计标识，用于资产基础配置表');

-- 行业统计标识字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'不考虑','01','ast_industry_statistics_flag',NULL,'info','Y','0','admin',NOW(),'',NULL,'不考虑行业统计'),
(2,'银行','02','ast_industry_statistics_flag',NULL,'primary','N','0','admin',NOW(),'',NULL,'银行行业'),
(3,'非银金融','03','ast_industry_statistics_flag',NULL,'success','N','0','admin',NOW(),'',NULL,'非银金融行业'),
(4,'wind中获取','04','ast_industry_statistics_flag',NULL,'warning','N','0','admin',NOW(),'',NULL,'从wind系统中获取'),
(5,'房地产','05','ast_industry_statistics_flag',NULL,'danger','N','0','admin',NOW(),'',NULL,'房地产行业'),
(6,'建筑装饰','06','ast_industry_statistics_flag',NULL,'default','N','0','admin',NOW(),'',NULL,'建筑装饰行业');

-- 单一资产统计标识字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('单一资产统计标识','ast_single_asset_statistics_flag','0','admin',NOW(),'',NULL,'单一资产统计标识，用于资产基础配置表');

-- 单一资产统计标识字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'不考虑','01','ast_single_asset_statistics_flag',NULL,'info','Y','0','admin',NOW(),'',NULL,'不考虑单一资产统计'),
(2,'考虑','02','ast_single_asset_statistics_flag',NULL,'success','N','0','admin',NOW(),'',NULL,'考虑单一资产统计');

-- 五级分类资产统计标识字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('五级分类资产统计标识','ast_five_level_statistics_flag','0','admin',NOW(),'',NULL,'五级分类资产统计标识，用于资产基础配置表');

-- 五级分类资产统计标识字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'不考虑','01','ast_five_level_statistics_flag',NULL,'info','Y','0','admin',NOW(),'',NULL,'不考虑'),
(2,'债券类','02','ast_five_level_statistics_flag',NULL,'success','N','0','admin',NOW(),'',NULL,'债券类资产'),
(3,'存款类','03','ast_five_level_statistics_flag',NULL,'default','N','0','admin',NOW(),'',NULL,'存款类'),
(4,'固定收益类金融产品','04','ast_five_level_statistics_flag',NULL,'warning','N','0','admin',NOW(),'',NULL,'固定收益类金融产品'),
(5,'未上市企业股权','05','ast_five_level_statistics_flag',NULL,'danger','N','0','admin',NOW(),'',NULL,'未上市企业股权'),
(6,'股权金融产品','06','ast_five_level_statistics_flag',NULL,'primary','N','0','admin',NOW(),'',NULL,'股权金融产品'),
(7,'不动产金融产品','07','ast_five_level_statistics_flag',NULL,'danger','N','0','admin',NOW(),'',NULL,'不动产金融产品');

-- TB0009 - 资产配置状况分类表字典数据

-- 资产配置分类级别字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('资产配置分类级别','ast_category_level','0','admin',NOW(),'',NULL,'资产配置状况分类表的分类级别，用于TB0009表');

-- 资产配置分类级别字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'资产大类','1','ast_category_level',NULL,'primary','N','0','admin',NOW(),'',NULL,'资产大类分类级别'),
(2,'资产一级分类','2','ast_category_level',NULL,'success','N','0','admin',NOW(),'',NULL,'资产一级分类级别'),
(3,'资产二级分类','3','ast_category_level',NULL,'info','N','0','admin',NOW(),'',NULL,'资产二级分类级别'),
(4,'资产三级分类','4','ast_category_level',NULL,'warning','N','0','admin',NOW(),'',NULL,'资产三级分类级别'),
(5,'资产小小类映射','5','ast_category_level',NULL,'default','N','0','admin',NOW(),'',NULL,'资产小小类映射级别');

-- TB0010 - 资产流动性分类及变现系数表字典数据

-- 债券类型字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('债券类型','ast_bond_type','0','admin',NOW(),'',NULL,'债券类型分类，用于资产流动性分类及变现系数表');

-- 债券类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'国债','01','ast_bond_type',NULL,'success','Y','0','admin',NOW(),'',NULL,'国债类债券'),
(2,'政府债','02','ast_bond_type',NULL,'success','N','0','admin',NOW(),'',NULL,'政府债类债券'),
(3,'准政府债','03','ast_bond_type',NULL,'primary','N','0','admin',NOW(),'',NULL,'准政府债类债券'),
(4,'政策性银行金融债','04','ast_bond_type',NULL,'info','N','0','admin',NOW(),'',NULL,'政策性银行金融债'),
(5,'金融企业债','05','ast_bond_type',NULL,'warning','N','0','admin',NOW(),'',NULL,'金融企业债券'),
(6,'非金融企业债','06','ast_bond_type',NULL,'danger','N','0','admin',NOW(),'',NULL,'非金融企业债券');

-- 会计分类类型字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('会计分类类型','ast_accounting_classification','0','admin',NOW(),'',NULL,'会计分类类型，用于资产流动性分类及变现系数表');

-- 会计分类类型字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'交易类','01','ast_accounting_classification',NULL,'danger','N','0','admin',NOW(),'',NULL,'交易类会计分类'),
(2,'可供出售类','02','ast_accounting_classification',NULL,'warning','Y','0','admin',NOW(),'',NULL,'可供出售类会计分类'),
(3,'持有到期类','03','ast_accounting_classification',NULL,'success','N','0','admin',NOW(),'',NULL,'持有到期类会计分类');

-- 资产流动性分类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('资产流动性分类','ast_asset_liquidity_category','0','admin',NOW(),'',NULL,'资产流动性分类，用于资产流动性分类及变现系数表');

-- 资产流动性分类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'现金及流动性管理工具','01','ast_asset_liquidity_category',NULL,'success','Y','0','admin',NOW(),'',NULL,'现金及流动性管理工具'),
(2,'高流动性资产','02','ast_asset_liquidity_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'高流动性资产'),
(3,'中低流动性资产','03','ast_asset_liquidity_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'中低流动性资产'),
(4,'不涉及','04','ast_asset_liquidity_category',NULL,'info','N','0','admin',NOW(),'',NULL,'不涉及流动性分类');

-- TB0012 - 银行分类映射表字典数据

-- 银行分类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('银行分类','ast_bank_classification','0','admin',NOW(),'',NULL,'银行分类，用于银行分类映射表');

-- 银行分类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'国有商业银行','01','ast_bank_classification',NULL,'success','Y','0','admin',NOW(),'',NULL,'国有商业银行'),
(2,'股份制商业银行、邮政储蓄银行','02','ast_bank_classification',NULL,'primary','N','0','admin',NOW(),'',NULL,'股份制商业银行、邮政储蓄银行'),
(3,'城市商业银行及国际信用评级在A级及以上的外资商业银行','03','ast_bank_classification',NULL,'info','N','0','admin',NOW(),'',NULL,'城市商业银行及国际信用评级在A级及以上的外资商业银行'),
(4,'其他境内商业银行和境外银行','04','ast_bank_classification',NULL,'warning','N','0','admin',NOW(),'',NULL,'其他境内商业银行和境外银行'),
(5,'其他存款机构','05','ast_bank_classification',NULL,'danger','N','0','admin',NOW(),'',NULL,'其他存款机构');

-- TB0013 - 付息方式映射表字典数据

-- 付息方式字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('付息方式','ast_payment_method','0','admin',NOW(),'',NULL,'付息方式，用于付息方式映射表');

-- 付息方式字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'到期支付','01','ast_payment_method',NULL,'success','Y','0','admin',NOW(),'',NULL,'到期支付'),
(2,'按年支付','02','ast_payment_method',NULL,'primary','N','0','admin',NOW(),'',NULL,'按年支付'),
(3,'按半年支付','03','ast_payment_method',NULL,'info','N','0','admin',NOW(),'',NULL,'按半年支付'),
(4,'按季支付','04','ast_payment_method',NULL,'warning','N','0','admin',NOW(),'',NULL,'按季支付');

-- TB0014 - 折现曲线配置表字典数据

-- 折现曲线标识字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('折现曲线标识','ast_discount_curve_flag','0','admin',NOW(),'',NULL,'折现曲线标识，用于折现曲线配置表');

-- 折现曲线标识字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'适用到期收益率计算方法','0','ast_discount_curve_flag',NULL,'info','Y','0','admin',NOW(),'',NULL,'适用到期收益率计算方法'),
(2,'国债曲线','1','ast_discount_curve_flag',NULL,'success','N','0','admin',NOW(),'',NULL,'国债曲线'),
(3,'企业债AAA曲线','2','ast_discount_curve_flag',NULL,'primary','N','0','admin',NOW(),'',NULL,'企业债AAA曲线'),
(4,'企业债AA+曲线','3','ast_discount_curve_flag',NULL,'warning','N','0','admin',NOW(),'',NULL,'企业债AA+曲线'),
(5,'企业债AA曲线','4','ast_discount_curve_flag',NULL,'danger','N','0','admin',NOW(),'',NULL,'企业债AA曲线');

-- TB0015 - 固收资产剩余期限资产分类表字典数据

-- 固收资产剩余期限分类字典类型
INSERT INTO sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('固收资产剩余期限分类','ast_fixed_income_term_category','0','admin',NOW(),'',NULL,'固收资产剩余期限分类，用于固收资产剩余期限资产分类表');

-- 固收资产剩余期限分类字典数据
INSERT INTO sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'存款','01','ast_fixed_income_term_category',NULL,'primary','N','0','admin',NOW(),'',NULL,'存款类固收资产'),
(2,'（准）政府债券','02','ast_fixed_income_term_category',NULL,'success','N','0','admin',NOW(),'',NULL,'（准）政府债券类固收资产'),
(3,'金融企业（公司）债券','03','ast_fixed_income_term_category',NULL,'info','N','0','admin',NOW(),'',NULL,'金融企业（公司）债券类固收资产'),
(4,'非金融企业（公司）债券','04','ast_fixed_income_term_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'非金融企业（公司）债券类固收资产'),
(5,'非标准固定收益类投资资产','05','ast_fixed_income_term_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'非标准固定收益类投资资产'),
(6,'其他固定收益类金融产品','06','ast_fixed_income_term_category',NULL,'default','N','0','admin',NOW(),'',NULL,'其他固定收益类金融产品'),
(7,'境外固定收益类投资资产','07','ast_fixed_income_term_category',NULL,'secondary','N','0','admin',NOW(),'',NULL,'境外固定收益类投资资产');
