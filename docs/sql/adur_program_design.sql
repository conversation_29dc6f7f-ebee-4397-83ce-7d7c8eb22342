-- =============================================
-- 资产久期管理模块(ADUR)数据表DDL
-- 数据库：MySQL 8.0
-- 字符集：utf8
-- 生成时间：2025-01-01
-- =============================================

-- TB0001: 万得收益率曲线表
DROP TABLE IF EXISTS `t_adur_wind_yield_curve`;
CREATE TABLE `t_adur_wind_yield_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `curve_name` varchar(50) NOT NULL COMMENT '折现曲线名称',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `date` date NOT NULL COMMENT '日期',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  `term_6` decimal(10,6) DEFAULT 0 COMMENT '期限6',
  `term_7` decimal(10,6) DEFAULT 0 COMMENT '期限7',
  `term_8` decimal(10,6) DEFAULT 0 COMMENT '期限8',
  `term_9` decimal(10,6) DEFAULT 0 COMMENT '期限9',
  `term_10` decimal(10,6) DEFAULT 0 COMMENT '期限10',
  `term_11` decimal(10,6) DEFAULT 0 COMMENT '期限11',
  `term_12` decimal(10,6) DEFAULT 0 COMMENT '期限12',
  `term_13` decimal(10,6) DEFAULT 0 COMMENT '期限13',
  `term_14` decimal(10,6) DEFAULT 0 COMMENT '期限14',
  `term_15` decimal(10,6) DEFAULT 0 COMMENT '期限15',
  `term_16` decimal(10,6) DEFAULT 0 COMMENT '期限16',
  `term_17` decimal(10,6) DEFAULT 0 COMMENT '期限17',
  `term_18` decimal(10,6) DEFAULT 0 COMMENT '期限18',
  `term_19` decimal(10,6) DEFAULT 0 COMMENT '期限19',
  `term_20` decimal(10,6) DEFAULT 0 COMMENT '期限20',
  `term_21` decimal(10,6) DEFAULT 0 COMMENT '期限21',
  `term_22` decimal(10,6) DEFAULT 0 COMMENT '期限22',
  `term_23` decimal(10,6) DEFAULT 0 COMMENT '期限23',
  `term_24` decimal(10,6) DEFAULT 0 COMMENT '期限24',
  `term_25` decimal(10,6) DEFAULT 0 COMMENT '期限25',
  `term_26` decimal(10,6) DEFAULT 0 COMMENT '期限26',
  `term_27` decimal(10,6) DEFAULT 0 COMMENT '期限27',
  `term_28` decimal(10,6) DEFAULT 0 COMMENT '期限28',
  `term_29` decimal(10,6) DEFAULT 0 COMMENT '期限29',
  `term_30` decimal(10,6) DEFAULT 0 COMMENT '期限30',
  `term_31` decimal(10,6) DEFAULT 0 COMMENT '期限31',
  `term_32` decimal(10,6) DEFAULT 0 COMMENT '期限32',
  `term_33` decimal(10,6) DEFAULT 0 COMMENT '期限33',
  `term_34` decimal(10,6) DEFAULT 0 COMMENT '期限34',
  `term_35` decimal(10,6) DEFAULT 0 COMMENT '期限35',
  `term_36` decimal(10,6) DEFAULT 0 COMMENT '期限36',
  `term_37` decimal(10,6) DEFAULT 0 COMMENT '期限37',
  `term_38` decimal(10,6) DEFAULT 0 COMMENT '期限38',
  `term_39` decimal(10,6) DEFAULT 0 COMMENT '期限39',
  `term_40` decimal(10,6) DEFAULT 0 COMMENT '期限40',
  `term_41` decimal(10,6) DEFAULT 0 COMMENT '期限41',
  `term_42` decimal(10,6) DEFAULT 0 COMMENT '期限42',
  `term_43` decimal(10,6) DEFAULT 0 COMMENT '期限43',
  `term_44` decimal(10,6) DEFAULT 0 COMMENT '期限44',
  `term_45` decimal(10,6) DEFAULT 0 COMMENT '期限45',
  `term_46` decimal(10,6) DEFAULT 0 COMMENT '期限46',
  `term_47` decimal(10,6) DEFAULT 0 COMMENT '期限47',
  `term_48` decimal(10,6) DEFAULT 0 COMMENT '期限48',
  `term_49` decimal(10,6) DEFAULT 0 COMMENT '期限49',
  `term_50` decimal(10,6) DEFAULT 0 COMMENT '期限50',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_curve_date` (`curve_name`,`curve_id`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='万得收益率曲线表';

-- TB0002: 关键久期参数表
DROP TABLE IF EXISTS `t_adur_key_duration_parameter`;
CREATE TABLE `t_adur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `key_duration` varchar(20) NOT NULL COMMENT '关键久期',
  `parameter_val_set` mediumtext COMMENT 'JSON格式参数值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_key_duration` (`account_period`, `key_duration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期参数表';

-- TB0003: 久期资产明细表
DROP TABLE IF EXISTS `t_adur_duration_asset_detail`;
CREATE TABLE `t_adur_duration_asset_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `asset_sub_category` varchar(50) NOT NULL COMMENT '资产小小类',
  `holding_face_value` decimal(28,10) DEFAULT 0 COMMENT '持仓面值',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值',
  `book_balance` decimal(28,10) DEFAULT 0 COMMENT '账面余额',
  `book_value` decimal(28,10) DEFAULT 0 COMMENT '账面价值',
  `coupon_rate` decimal(10,6) DEFAULT 0 COMMENT '票面利率',
  `payment_method` varchar(20) NOT NULL COMMENT '付息方式',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `adjusted_value_date` date NOT NULL COMMENT '调整起息日',
  `adjusted_purchase_date` date NOT NULL COMMENT '调整买入日',
  `adjusted_maturity_date` date NOT NULL COMMENT '调整到期日',
  `issue_spread_calc_flag` char(1) NOT NULL COMMENT '发行时点价差计算标识',
  `spread_duration_stat_flag` char(1) DEFAULT '0' COMMENT '利差久期资产统计标识',
  `eval_spread` decimal(10,6) DEFAULT 0 COMMENT '评估时点价差',
  `spread_duration` decimal(10,6) DEFAULT 0 COMMENT '利差久期',
  `book_value_sigma_9` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ9%',
  `book_value_sigma_17` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ17%',
  `book_value_sigma_77` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ77%',
  `issue_present_value` decimal(28,10) DEFAULT 0 COMMENT '发行时点资产现值',
  `issue_spread` decimal(10,6) DEFAULT 0 COMMENT '发行时点价差',
  `eval_present_value` decimal(28,10) DEFAULT 0 COMMENT '评估时点资产现值',
  `eval_maturity_yield` decimal(10,6) DEFAULT 0 COMMENT '评估时点到期收益率',
  `asset_modified_duration` decimal(10,6) DEFAULT 0 COMMENT '资产修正久期',
  `eval_present_value_plus_50bp` decimal(28,10) DEFAULT 0 COMMENT '评估时点资产现值+50bp',
  `eval_present_value_minus_50bp` decimal(28,10) DEFAULT 0 COMMENT '评估时点资产现值-50bp',
  `asset_effective_duration` decimal(10,6) DEFAULT 0 COMMENT '资产有效久期',
  `dv10_1` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0',
  `dv10_2` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5',
  `dv10_3` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1',
  `dv10_4` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2',
  `dv10_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3',
  `dv10_6` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4',
  `dv10_7` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5',
  `dv10_8` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6',
  `dv10_9` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7',
  `dv10_10` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8',
  `dv10_11` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10',
  `dv10_12` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12',
  `dv10_13` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15',
  `dv10_14` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20',
  `dv10_15` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25',
  `dv10_16` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30',
  `dv10_17` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35',
  `dv10_18` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40',
  `dv10_19` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45',
  `dv10_20` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50',
  `dv10_1_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0_上升',
  `dv10_2_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5_上升',
  `dv10_3_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1_上升',
  `dv10_4_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2_上升',
  `dv10_5_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3_上升',
  `dv10_6_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4_上升',
  `dv10_7_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5_上升',
  `dv10_8_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6_上升',
  `dv10_9_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7_上升',
  `dv10_10_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8_上升',
  `dv10_11_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10_上升',
  `dv10_12_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12_上升',
  `dv10_13_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15_上升',
  `dv10_14_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20_上升',
  `dv10_15_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25_上升',
  `dv10_16_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30_上升',
  `dv10_17_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35_上升',
  `dv10_18_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40_上升',
  `dv10_19_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45_上升',
  `dv10_20_up` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50_上升',
  `dv10_1_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0_下降',
  `dv10_2_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5_下降',
  `dv10_3_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1_下降',
  `dv10_4_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2_下降',
  `dv10_5_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3_下降',
  `dv10_6_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4_下降',
  `dv10_7_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5_下降',
  `dv10_8_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6_下降',
  `dv10_9_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7_下降',
  `dv10_10_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8_下降',
  `dv10_11_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10_下降',
  `dv10_12_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12_下降',
  `dv10_13_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15_下降',
  `dv10_14_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20_下降',
  `dv10_15_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25_下降',
  `dv10_16_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30_下降',
  `dv10_17_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35_下降',
  `dv10_18_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40_下降',
  `dv10_19_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45_下降',
  `dv10_20_down` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50_下降',
  `issue_cashflow_set` mediumtext COMMENT '发行时点现金流值集',
  `eval_cashflow_set` mediumtext COMMENT '评估时点现金流值集',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset` (`account_period`,`asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='久期资产明细表';

-- TB0004: 年度折现曲线表
DROP TABLE IF EXISTS `t_adur_annual_discount_curve`;
CREATE TABLE `t_adur_annual_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  `term_6` decimal(10,6) DEFAULT 0 COMMENT '期限6',
  `term_7` decimal(10,6) DEFAULT 0 COMMENT '期限7',
  `term_8` decimal(10,6) DEFAULT 0 COMMENT '期限8',
  `term_9` decimal(10,6) DEFAULT 0 COMMENT '期限9',
  `term_10` decimal(10,6) DEFAULT 0 COMMENT '期限10',
  `term_11` decimal(10,6) DEFAULT 0 COMMENT '期限11',
  `term_12` decimal(10,6) DEFAULT 0 COMMENT '期限12',
  `term_13` decimal(10,6) DEFAULT 0 COMMENT '期限13',
  `term_14` decimal(10,6) DEFAULT 0 COMMENT '期限14',
  `term_15` decimal(10,6) DEFAULT 0 COMMENT '期限15',
  `term_16` decimal(10,6) DEFAULT 0 COMMENT '期限16',
  `term_17` decimal(10,6) DEFAULT 0 COMMENT '期限17',
  `term_18` decimal(10,6) DEFAULT 0 COMMENT '期限18',
  `term_19` decimal(10,6) DEFAULT 0 COMMENT '期限19',
  `term_20` decimal(10,6) DEFAULT 0 COMMENT '期限20',
  `term_21` decimal(10,6) DEFAULT 0 COMMENT '期限21',
  `term_22` decimal(10,6) DEFAULT 0 COMMENT '期限22',
  `term_23` decimal(10,6) DEFAULT 0 COMMENT '期限23',
  `term_24` decimal(10,6) DEFAULT 0 COMMENT '期限24',
  `term_25` decimal(10,6) DEFAULT 0 COMMENT '期限25',
  `term_26` decimal(10,6) DEFAULT 0 COMMENT '期限26',
  `term_27` decimal(10,6) DEFAULT 0 COMMENT '期限27',
  `term_28` decimal(10,6) DEFAULT 0 COMMENT '期限28',
  `term_29` decimal(10,6) DEFAULT 0 COMMENT '期限29',
  `term_30` decimal(10,6) DEFAULT 0 COMMENT '期限30',
  `term_31` decimal(10,6) DEFAULT 0 COMMENT '期限31',
  `term_32` decimal(10,6) DEFAULT 0 COMMENT '期限32',
  `term_33` decimal(10,6) DEFAULT 0 COMMENT '期限33',
  `term_34` decimal(10,6) DEFAULT 0 COMMENT '期限34',
  `term_35` decimal(10,6) DEFAULT 0 COMMENT '期限35',
  `term_36` decimal(10,6) DEFAULT 0 COMMENT '期限36',
  `term_37` decimal(10,6) DEFAULT 0 COMMENT '期限37',
  `term_38` decimal(10,6) DEFAULT 0 COMMENT '期限38',
  `term_39` decimal(10,6) DEFAULT 0 COMMENT '期限39',
  `term_40` decimal(10,6) DEFAULT 0 COMMENT '期限40',
  `term_41` decimal(10,6) DEFAULT 0 COMMENT '期限41',
  `term_42` decimal(10,6) DEFAULT 0 COMMENT '期限42',
  `term_43` decimal(10,6) DEFAULT 0 COMMENT '期限43',
  `term_44` decimal(10,6) DEFAULT 0 COMMENT '期限44',
  `term_45` decimal(10,6) DEFAULT 0 COMMENT '期限45',
  `term_46` decimal(10,6) DEFAULT 0 COMMENT '期限46',
  `term_47` decimal(10,6) DEFAULT 0 COMMENT '期限47',
  `term_48` decimal(10,6) DEFAULT 0 COMMENT '期限48',
  `term_49` decimal(10,6) DEFAULT 0 COMMENT '期限49',
  `term_50` decimal(10,6) DEFAULT 0 COMMENT '期限50',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='年度折现曲线表';

-- TB0005: 月度折现曲线表不含价差
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;
CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_set` mediumtext COMMENT '月度折现曲线利率值集',

  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',

  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_date_type` (`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现曲线表不含价差';

-- =============================================
-- 重要说明：
-- 1. 此表包含完整的term_0到term_600共601个期限字段
-- 2. 完整的DDL文件位于：docs/sql/t_adur_monthly_discount_curve_complete_ddl.sql
-- 3. 期限字段计算逻辑：
--    - 如果折现曲线标识=0 and 日期类型=发行时点：等于"-"
--    - 如果折现曲线标识=0 and 日期类型=评估时点：等于久期资产明细表.到期收益率
--    - 如果折现曲线标识≠0：
--      * 月份为12的整数倍时：在年度折现曲线表中查找对应年份的收益率
--      * 月份不为12的整数倍时：取临近两个整数年的收益率进行线性插值
-- 4. 使用TermFieldUtil工具类进行期限字段的动态访问和计算
-- =============================================

-- TB0006: 月度折现曲线表含价差
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve_with_spread`;
CREATE TABLE `t_adur_monthly_discount_curve_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_rate_with_spread_set` mediumtext COMMENT '月度折现曲线利率含价差值集',

  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',

  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_duration` (`account_period`,`asset_number`,`duration_type`,`basis_point_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_duration_type` (`duration_type`),
  KEY `idx_basis_point_type` (`basis_point_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现曲线表含价差';

-- TB0007: 月度折现因子表含价差
DROP TABLE IF EXISTS `t_adur_monthly_discount_factor_with_spread`;
CREATE TABLE `t_adur_monthly_discount_factor_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `monthly_discount_factor_set` mediumtext COMMENT '月度折现因子表含价差值集',

  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',

  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_duration` (`account_period`,`asset_number`,`duration_type`,`basis_point_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_duration_type` (`duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度折现因子表含价差';

-- =============================================
-- 重要说明：
-- 1. monthly_discount_factor_set字段存储JSON格式的折现因子值集
-- 2. JSON格式：{"0":0.25,"1":0.35...,"600":0.15}
-- 3. 计算逻辑：等于1/(1+月度折现曲线表含价差.期限X)^(月份/12)
-- 4. 其中X为期限编号（0-600），月份为期限编号对应的月份数
-- =============================================

-- TB0008: 关键久期折现曲线表含价差
DROP TABLE IF EXISTS `t_adur_key_duration_curve_with_spread`;
CREATE TABLE `t_adur_key_duration_curve_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) DEFAULT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(10) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `key_duration_curve_with_spread_set` mediumtext COMMENT '关键久期折现曲线表含价差值集',

  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',

  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_key_stress` (`account_period`,`asset_number`,`key_term`,`stress_direction`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_key_term` (`key_term`),
  KEY `idx_stress_direction` (`stress_direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键久期折现曲线表含价差';

-- TB0009: 关键久期折现因子表含价差
DROP TABLE IF EXISTS `t_adur_key_duration_factor_with_spread`;
CREATE TABLE `t_adur_key_duration_factor_with_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) DEFAULT NULL COMMENT '基点类型',
  `key_term` varchar(20) NOT NULL COMMENT '关键期限',
  `stress_direction` varchar(10) NOT NULL COMMENT '压力方向',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `issue_spread` decimal(10,6) DEFAULT 0 COMMENT '发行时点价差',
  `key_duration_factor_with_spread_set` mediumtext COMMENT '关键久期折现因子表含价差值集',

  -- 审计字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',

  -- 主键和索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_key_stress` (`account_period`,`asset_number`,`key_term`,`stress_direction`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_key_term` (`key_term`),
  KEY `idx_stress_direction` (`stress_direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关键久期折现因子表含价差';


-- TB0010: 久期资产结果汇总表
DROP TABLE IF EXISTS `t_adur_duration_asset_summary`;
CREATE TABLE `t_adur_duration_asset_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `market_value` decimal(28,10) DEFAULT 0 COMMENT '市值',
  `book_balance` decimal(28,10) DEFAULT 0 COMMENT '账面余额',
  `book_value` decimal(28,10) DEFAULT 0 COMMENT '账面价值',
  `book_value_sigma_0` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ0%',
  `book_value_sigma_9` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ9%',
  `book_value_sigma_17` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ17%',
  `book_value_sigma_77` decimal(28,10) DEFAULT 0 COMMENT '账面价值σ77%',
  `eval_maturity_yield` decimal(10,6) DEFAULT 0 COMMENT '评估时点到期收益率',
  `eval_present_value` decimal(28,10) DEFAULT 0 COMMENT '评估时点资产现值',
  `asset_modified_duration` decimal(10,6) DEFAULT 0 COMMENT '资产修正久期',
  `asset_effective_duration` decimal(10,6) DEFAULT 0 COMMENT '资产有效久期',
  `dv10_0` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0',
  `dv10_0_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5',
  `dv10_1` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1',
  `dv10_2` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2',
  `dv10_3` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3',
  `dv10_4` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4',
  `dv10_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5',
  `dv10_6` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6',
  `dv10_7` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7',
  `dv10_8` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8',
  `dv10_10` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10',
  `dv10_12` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12',
  `dv10_15` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15',
  `dv10_20` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20',
  `dv10_25` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25',
  `dv10_30` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30',
  `dv10_35` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35',
  `dv10_40` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40',
  `dv10_45` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45',
  `dv10_50` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_period_name` (`account_period`,`account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='久期资产结果汇总表';

-- =============================================
-- DDL生成完成
-- 总计11个表：
-- TB0001: t_base_wind_yield_curve (万得收益率曲线表)
-- TB0002: t_adur_key_duration_parameter (关键久期参数表)
-- TB0003: t_adur_duration_asset_detail (久期资产明细表)
-- TB0004: t_adur_annual_discount_curve (年度折现曲线表)
-- TB0005: t_adur_monthly_discount_curve (月度折现曲线表不含价差)
-- TB0006: t_adur_monthly_discount_curve_with_spread (月度折现曲线表含价差)
-- TB0007: t_adur_monthly_discount_factor_with_spread (月度折现因子表含价差)
-- TB0008: t_adur_key_duration_curve_with_spread (关键久期折现曲线表含价差)
-- TB0009: t_adur_key_duration_factor_with_spread (关键久期折现因子表含价差)
-- TB0010: t_adur_duration_asset_summary (久期资产结果汇总表)
-- =============================================
