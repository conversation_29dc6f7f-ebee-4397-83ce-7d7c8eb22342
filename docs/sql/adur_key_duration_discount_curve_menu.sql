-- =============================================
-- ADUR关键久期折现曲线表含价差菜单SQL
-- 表名：t_adur_key_duration_curve_with_spread
-- 功能：ADUR关键久期折现曲线管理（含价差）
-- 对应表：TB0008 关键久期折现曲线表含价差
-- =============================================

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('ADUR关键久期折现曲线含价差', '2548', '8', 'adurkeydurationdiscountcurve', 'adur/key/duration/discount/curve/index', 1, 0, 'C', '0', '0', 'adur:key:duration:discount:curve:list', '#', 'admin', sysdate(), '', null, 'ADUR关键久期折现曲线含价差菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('关键久期折现曲线含价差导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'adur:key:duration:discount:curve:import',       '#', 'admin', sysdate(), '', null, '');

-- =============================================
-- 菜单权限说明：
-- adur:key:duration:discount:curve:list    - ADUR关键久期折现曲线含价差列表查询
-- adur:key:duration:discount:curve:query   - ADUR关键久期折现曲线含价差详情查询
-- adur:key:duration:discount:curve:add     - ADUR关键久期折现曲线含价差新增
-- adur:key:duration:discount:curve:edit    - ADUR关键久期折现曲线含价差修改
-- adur:key:duration:discount:curve:remove  - ADUR关键久期折现曲线含价差删除
-- adur:key:duration:discount:curve:export  - ADUR关键久期折现曲线含价差导出
-- adur:key:duration:discount:curve:import  - ADUR关键久期折现曲线含价差导入
-- =============================================

-- 注意事项：
-- 1. parent_id 设置为2548（ADUR模块主菜单）
-- 2. order_num 设置为8，表示在ADUR模块中的第8个子菜单
-- 3. path 为 'adurkeydurationdiscountcurve'，对应前端路由
-- 4. component 为 'adur/key/duration/discount/curve/index'，对应Vue组件路径
-- 5. 权限标识统一使用 'adur:key:duration:discount:curve:*' 格式
-- 6. 该表包含601个期限字段（term_0到term_600）和价差相关字段
-- 7. 关键久期折现曲线是基于关键久期参数和基础折现曲线计算得出的
-- 8. 包含久期类型、基点类型、关键期限、压力方向等关键久期特有字段
-- 9. 支持资产级别的关键久期风险管理和计算
-- 10. 唯一约束：account_period + asset_number + key_term + stress_direction
-- =============================================
