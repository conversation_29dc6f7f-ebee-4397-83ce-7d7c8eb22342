package com.ruoyi.adur.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 月度折现因子工具类
 * 用于处理JSON格式的月度折现因子数据和相关计算
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public class MonthlyDiscountFactorUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(MonthlyDiscountFactorUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final MathContext MATH_CONTEXT = new MathContext(10, RoundingMode.HALF_UP);
    
    /**
     * 根据月度折现曲线利率计算折现因子
     * 计算公式：1/(1+利率)^(月份/12)
     * 
     * @param rateJsonStr 月度折现曲线利率JSON字符串
     * @return 月度折现因子JSON字符串
     */
    public static String calculateDiscountFactors(String rateJsonStr) {
        if (rateJsonStr == null || rateJsonStr.trim().isEmpty()) {
            logger.warn("输入的利率JSON字符串为空");
            return "{}";
        }
        
        try {
            JsonNode rateNode = objectMapper.readTree(rateJsonStr);
            ObjectNode factorNode = objectMapper.createObjectNode();
            
            rateNode.fieldNames().forEachRemaining(termStr -> {
                try {
                    Integer term = Integer.parseInt(termStr);
                    BigDecimal rate = new BigDecimal(rateNode.get(termStr).asText());
                    
                    // 计算折现因子：1/(1+利率)^(月份/12)
                    BigDecimal factor = calculateSingleDiscountFactor(rate, term);
                    factorNode.put(termStr, factor);
                    
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或利率失败: {}", termStr);
                }
            });
            
            return objectMapper.writeValueAsString(factorNode);
            
        } catch (JsonProcessingException e) {
            logger.error("计算折现因子失败: {}", rateJsonStr, e);
            return "{}";
        }
    }
    
    /**
     * 计算单个期限的折现因子
     * 
     * @param rate 年利率
     * @param termMonths 期限（月份）
     * @return 折现因子
     */
    public static BigDecimal calculateSingleDiscountFactor(BigDecimal rate, Integer termMonths) {
        if (rate == null || termMonths == null) {
            return BigDecimal.ZERO;
        }
        
        try {
            // 计算 1 + 利率
            BigDecimal onePlusRate = BigDecimal.ONE.add(rate);
            
            // 计算指数：月份/12
            BigDecimal exponent = new BigDecimal(termMonths).divide(new BigDecimal(12), MATH_CONTEXT);
            
            // 计算 (1+利率)^(月份/12)
            double base = onePlusRate.doubleValue();
            double exp = exponent.doubleValue();
            double powered = Math.pow(base, exp);
            
            // 计算 1 / (1+利率)^(月份/12)
            BigDecimal factor = BigDecimal.ONE.divide(new BigDecimal(powered), MATH_CONTEXT);
            
            return factor;
            
        } catch (Exception e) {
            logger.error("计算单个折现因子失败: rate={}, termMonths={}", rate, termMonths, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param factorMap 期限-折现因子映射
     * @return JSON字符串
     */
    public static String mapToJson(Map<Integer, BigDecimal> factorMap) {
        try {
            ObjectNode jsonNode = objectMapper.createObjectNode();
            for (Map.Entry<Integer, BigDecimal> entry : factorMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("转换Map到JSON失败", e);
            return "{}";
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param jsonStr JSON字符串
     * @return 期限-折现因子映射
     */
    public static Map<Integer, BigDecimal> jsonToMap(String jsonStr) {
        Map<Integer, BigDecimal> factorMap = new HashMap<>();
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return factorMap;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                try {
                    Integer term = Integer.parseInt(fieldName);
                    BigDecimal factor = new BigDecimal(jsonNode.get(fieldName).asText());
                    factorMap.put(term, factor);
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或折现因子失败: {}", fieldName);
                }
            });
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return factorMap;
    }
    
    /**
     * 获取指定期限的折现因子
     * 
     * @param jsonStr JSON字符串
     * @param term 期限（月份）
     * @return 折现因子值，如果不存在返回null
     */
    public static BigDecimal getFactor(String jsonStr, Integer term) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode factorNode = jsonNode.get(term.toString());
            if (factorNode != null) {
                return new BigDecimal(factorNode.asText());
            }
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return null;
    }
    
    /**
     * 设置指定期限的折现因子
     * 
     * @param jsonStr 原JSON字符串
     * @param term 期限（月份）
     * @param factor 折现因子值
     * @return 更新后的JSON字符串
     */
    public static String setFactor(String jsonStr, Integer term, BigDecimal factor) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            jsonNode.put(term.toString(), factor);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("设置折现因子失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 批量设置折现因子
     * 
     * @param jsonStr 原JSON字符串
     * @param factorMap 期限-折现因子映射
     * @return 更新后的JSON字符串
     */
    public static String setFactors(String jsonStr, Map<Integer, BigDecimal> factorMap) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            for (Map.Entry<Integer, BigDecimal> entry : factorMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("批量设置折现因子失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 创建完整的折现因子值集（0-600期）
     * 
     * @param rateJsonStr 利率JSON字符串
     * @return 折现因子JSON字符串
     */
    public static String createFullFactorSet(String rateJsonStr) {
        return calculateDiscountFactors(rateJsonStr);
    }
    
    /**
     * 验证JSON格式是否正确
     * 
     * @param jsonStr JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            objectMapper.readTree(jsonStr);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 获取JSON中包含的期限数量
     * 
     * @param jsonStr JSON字符串
     * @return 期限数量
     */
    public static int getTermCount(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return 0;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.size();
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return 0;
        }
    }
    
    /**
     * 计算现值
     * 使用折现因子计算未来现金流的现值
     * 
     * @param factorJsonStr 折现因子JSON字符串
     * @param cashFlowMap 现金流映射（期限->现金流金额）
     * @return 现值
     */
    public static BigDecimal calculatePresentValue(String factorJsonStr, Map<Integer, BigDecimal> cashFlowMap) {
        Map<Integer, BigDecimal> factorMap = jsonToMap(factorJsonStr);
        BigDecimal presentValue = BigDecimal.ZERO;
        
        for (Map.Entry<Integer, BigDecimal> cashFlowEntry : cashFlowMap.entrySet()) {
            Integer term = cashFlowEntry.getKey();
            BigDecimal cashFlow = cashFlowEntry.getValue();
            BigDecimal factor = factorMap.get(term);
            
            if (factor != null && cashFlow != null) {
                BigDecimal pv = cashFlow.multiply(factor, MATH_CONTEXT);
                presentValue = presentValue.add(pv, MATH_CONTEXT);
            }
        }
        
        return presentValue;
    }
}
