# ADUR久期资产结果汇总表字段不匹配问题修复

## 问题描述

用户报告了一个SQL错误：
```
Unknown column 'dv10_9' in 'field list'
```

错误发生在 `t_adur_duration_asset_summary` 表的批量插入操作中，提示 `dv10_9` 字段不存在。

## 根本原因分析

通过对比不同文件中的表结构定义，发现了严重的不一致问题：

### 1. 正确的表结构（adur_program_design.sql）
```sql
CREATE TABLE `t_adur_duration_asset_summary` (
  -- 基础字段...
  `dv10_0` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0',
  `dv10_0_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_0.5',
  `dv10_1` decimal(28,10) DEFAULT 0 COMMENT 'DV10_1',
  `dv10_2` decimal(28,10) DEFAULT 0 COMMENT 'DV10_2',
  `dv10_3` decimal(28,10) DEFAULT 0 COMMENT 'DV10_3',
  `dv10_4` decimal(28,10) DEFAULT 0 COMMENT 'DV10_4',
  `dv10_5` decimal(28,10) DEFAULT 0 COMMENT 'DV10_5',
  `dv10_6` decimal(28,10) DEFAULT 0 COMMENT 'DV10_6',
  `dv10_7` decimal(28,10) DEFAULT 0 COMMENT 'DV10_7',
  `dv10_8` decimal(28,10) DEFAULT 0 COMMENT 'DV10_8',
  `dv10_10` decimal(28,10) DEFAULT 0 COMMENT 'DV10_10',
  `dv10_12` decimal(28,10) DEFAULT 0 COMMENT 'DV10_12',
  `dv10_15` decimal(28,10) DEFAULT 0 COMMENT 'DV10_15',
  `dv10_20` decimal(28,10) DEFAULT 0 COMMENT 'DV10_20',
  `dv10_25` decimal(28,10) DEFAULT 0 COMMENT 'DV10_25',
  `dv10_30` decimal(28,10) DEFAULT 0 COMMENT 'DV10_30',
  `dv10_35` decimal(28,10) DEFAULT 0 COMMENT 'DV10_35',
  `dv10_40` decimal(28,10) DEFAULT 0 COMMENT 'DV10_40',
  `dv10_45` decimal(28,10) DEFAULT 0 COMMENT 'DV10_45',
  `dv10_50` decimal(28,10) DEFAULT 0 COMMENT 'DV10_50'
);
```

**关键特点**：
- 包含 `dv10_0` 和 `dv10_0_5` 字段
- **没有** `dv10_9`, `dv10_11`, `dv10_13`, `dv10_14`, `dv10_16`, `dv10_17`, `dv10_18`, `dv10_19` 字段
- DV10字段对应关键久期期限：0, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 15, 20, 25, 30, 35, 40, 45, 50

### 2. 错误的实现（job模块）

#### job模块Mapper XML（修复前）
```xml
<!-- 错误的字段列表 -->
dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8, dv10_9, dv10_10,
dv10_11, dv10_12, dv10_13, dv10_14, dv10_15, dv10_16, dv10_17, dv10_18, dv10_19, dv10_20
```

#### job模块实体类（修复前）
```java
// 错误的字段定义和注解
@TableField("dv10_1")  // 错误：注释说DV10_0，但注解是dv10_1
private BigDecimal dv101;

@TableField("dv10_9")  // 错误：数据库中不存在dv10_9字段
private BigDecimal dv109;
```

### 3. 正确的实现（app模块）
app模块的实现是正确的，与 `adur_program_design.sql` 中的表结构一致。

## 修复内容

### 1. job模块Mapper XML修复

**文件**: `job/src/main/resources/mapper/adur/AdurDurationAssetSummaryMapper.xml`

#### 修复前（错误）
```xml
<sql id="selectAdurDurationAssetSummaryVo">
    select id, account_period, account_name, market_value, book_balance, book_value,
           book_value_sigma_0, book_value_sigma_9, book_value_sigma_17, book_value_sigma_77,
           eval_maturity_yield, eval_present_value, asset_modified_duration, asset_effective_duration,
           dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8, dv10_9, dv10_10,
           dv10_11, dv10_12, dv10_13, dv10_14, dv10_15, dv10_16, dv10_17, dv10_18, dv10_19, dv10_20,
           create_time, create_by, update_time, update_by, is_del
    from t_adur_duration_asset_summary
</sql>
```

#### 修复后（正确）
```xml
<sql id="selectAdurDurationAssetSummaryVo">
    select id, account_period, account_name, market_value, book_balance, book_value,
           book_value_sigma_0, book_value_sigma_9, book_value_sigma_17, book_value_sigma_77,
           eval_maturity_yield, eval_present_value, asset_modified_duration, asset_effective_duration,
           dv10_0, dv10_0_5, dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8,
           dv10_10, dv10_12, dv10_15, dv10_20, dv10_25, dv10_30, dv10_35, dv10_40, dv10_45, dv10_50,
           create_time, create_by, update_time, update_by, is_del
    from t_adur_duration_asset_summary
</sql>
```

#### 插入语句修复
```xml
<!-- 修复前 -->
<insert id="batchInsertDurationAssetSummary">
    insert into t_adur_duration_asset_summary(
        dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8, dv10_9, dv10_10,
        dv10_11, dv10_12, dv10_13, dv10_14, dv10_15, dv10_16, dv10_17, dv10_18, dv10_19, dv10_20,
        -- ...
    ) values (
        #{item.dv101}, #{item.dv102}, #{item.dv103}, #{item.dv104}, #{item.dv105}, 
        #{item.dv106}, #{item.dv107}, #{item.dv108}, #{item.dv109}, #{item.dv1010},
        #{item.dv1011}, #{item.dv1012}, #{item.dv1013}, #{item.dv1014}, #{item.dv1015}, 
        #{item.dv1016}, #{item.dv1017}, #{item.dv1018}, #{item.dv1019}, #{item.dv1020},
        -- ...
    )
</insert>

<!-- 修复后 -->
<insert id="batchInsertDurationAssetSummary">
    insert into t_adur_duration_asset_summary(
        dv10_0, dv10_0_5, dv10_1, dv10_2, dv10_3, dv10_4, dv10_5, dv10_6, dv10_7, dv10_8,
        dv10_10, dv10_12, dv10_15, dv10_20, dv10_25, dv10_30, dv10_35, dv10_40, dv10_45, dv10_50,
        -- ...
    ) values (
        #{item.dv100}, #{item.dv1005}, #{item.dv101}, #{item.dv102}, #{item.dv103}, 
        #{item.dv104}, #{item.dv105}, #{item.dv106}, #{item.dv107}, #{item.dv108}, 
        #{item.dv1010}, #{item.dv1012}, #{item.dv1015}, #{item.dv1020}, 
        #{item.dv1025}, #{item.dv1030}, #{item.dv1035}, #{item.dv1040}, #{item.dv1045}, #{item.dv1050},
        -- ...
    )
</insert>
```

#### ResultMap修复
```xml
<!-- 修复前 -->
<result property="dv109" column="dv10_9"/>   <!-- 错误：数据库中不存在dv10_9字段 -->
<result property="dv1011" column="dv10_11"/> <!-- 错误：数据库中不存在dv10_11字段 -->

<!-- 修复后 -->
<result property="dv100" column="dv10_0"/>     <!-- 正确：对应数据库中的dv10_0字段 -->
<result property="dv1005" column="dv10_0_5"/>  <!-- 正确：对应数据库中的dv10_0_5字段 -->
```

### 2. job模块实体类修复

**文件**: `job/src/main/java/com/xl/alm/job/adur/entity/AdurDurationAssetSummaryEntity.java`

#### 修复前（错误）
```java
/**
 * DV10_0  // 注释说DV10_0
 */
@TableField("dv10_1")  // 但注解是dv10_1，不匹配
private BigDecimal dv101;

/**
 * DV10_7  // 注释说DV10_7
 */
@TableField("dv10_9")  // 但注解是dv10_9，数据库中不存在此字段
private BigDecimal dv109;
```

#### 修复后（正确）
```java
/**
 * DV10_0
 */
@TableField("dv10_0")  // 正确：注释和注解一致，对应数据库字段
private BigDecimal dv100;

/**
 * DV10_0.5
 */
@TableField("dv10_0_5")  // 正确：对应数据库中的dv10_0_5字段
private BigDecimal dv1005;

/**
 * DV10_1
 */
@TableField("dv10_1")  // 正确：注释和注解一致
private BigDecimal dv101;
```

## DV10字段对应关系

修复后的字段对应关系：

| 关键久期期限 | 数据库字段名 | 实体类字段名 | 说明 |
|-------------|-------------|-------------|------|
| 0 | dv10_0 | dv100 | DV10_0 |
| 0.5 | dv10_0_5 | dv1005 | DV10_0.5 |
| 1 | dv10_1 | dv101 | DV10_1 |
| 2 | dv10_2 | dv102 | DV10_2 |
| 3 | dv10_3 | dv103 | DV10_3 |
| 4 | dv10_4 | dv104 | DV10_4 |
| 5 | dv10_5 | dv105 | DV10_5 |
| 6 | dv10_6 | dv106 | DV10_6 |
| 7 | dv10_7 | dv107 | DV10_7 |
| 8 | dv10_8 | dv108 | DV10_8 |
| 10 | dv10_10 | dv1010 | DV10_10 |
| 12 | dv10_12 | dv1012 | DV10_12 |
| 15 | dv10_15 | dv1015 | DV10_15 |
| 20 | dv10_20 | dv1020 | DV10_20 |
| 25 | dv10_25 | dv1025 | DV10_25 |
| 30 | dv10_30 | dv1030 | DV10_30 |
| 35 | dv10_35 | dv1035 | DV10_35 |
| 40 | dv10_40 | dv1040 | DV10_40 |
| 45 | dv10_45 | dv1045 | DV10_45 |
| 50 | dv10_50 | dv1050 | DV10_50 |

## 验证方法

1. **编译验证**: 确保job模块能够正常编译
2. **SQL验证**: 确保Mapper XML中的SQL语句能够正常执行
3. **功能验证**: 测试久期资产结果汇总功能是否正常工作
4. **数据一致性验证**: 确保app模块和job模块使用相同的表结构

## 影响范围

此次修复主要影响：
- **Job模块**: AdurDurationAssetSummaryEntity、AdurDurationAssetSummaryMapper及相关服务类
- **ADUR功能**: UC0010久期资产结果汇总等相关功能

## 根本问题

这个问题的根本原因是：
1. **缺乏统一的表结构管理**：app模块和job模块使用了不同的表结构定义
2. **代码生成不一致**：可能使用了不同的代码生成工具或模板
3. **缺乏集成测试**：没有及早发现表结构不一致问题

## 预防措施

1. **统一表结构管理**：以 `adur_program_design.sql` 为准，确保所有模块使用相同的表结构
2. **加强代码审查**：确保实体类、Mapper文件与实际表结构保持一致
3. **完善集成测试**：及早发现此类表结构不一致问题
4. **建立DDL变更流程**：表结构变更时，同步更新所有相关代码

### 3. job模块服务实现类修复

**文件**: `job/src/main/java/com/xl/alm/job/adur/service/impl/AdurDurationAssetSummaryCalculationServiceImpl.java`

#### 修复前（错误）
```java
// 编译错误：找不到符号 setDv109
summary.setDv109(dv10Totals[8]);   // DV10_7
summary.setDv1011(dv10Totals[10]); // DV10_10
// ... 其他错误的方法调用
```

#### 修复后（正确）
```java
// 使用正确的方法名，对应修复后的实体类字段
summary.setDv100(dv10Totals[0]);   // DV10_0
summary.setDv1005(dv10Totals[1]);  // DV10_0.5
summary.setDv101(dv10Totals[2]);   // DV10_1
summary.setDv107(dv10Totals[8]);   // DV10_7
summary.setDv108(dv10Totals[9]);   // DV10_8
summary.setDv1010(dv10Totals[10]); // DV10_10
// ... 其他正确的方法调用
```

## 表结构差异说明

### t_adur_duration_asset_detail 表（明细表）
```sql
-- 字段名从dv10_1开始，对应关键久期期限
`dv10_1` COMMENT 'DV10_0',      -- 关键久期0年
`dv10_2` COMMENT 'DV10_0.5',    -- 关键久期0.5年
`dv10_3` COMMENT 'DV10_1',      -- 关键久期1年
...
`dv10_20` COMMENT 'DV10_50'     -- 关键久期50年
```

### t_adur_duration_asset_summary 表（汇总表）
```sql
-- 字段名直接对应关键久期期限
`dv10_0` COMMENT 'DV10_0',      -- 关键久期0年
`dv10_0_5` COMMENT 'DV10_0.5',  -- 关键久期0.5年
`dv10_1` COMMENT 'DV10_1',      -- 关键久期1年
...
`dv10_50` COMMENT 'DV10_50'     -- 关键久期50年
```

### 数据流转逻辑
```
AdurDurationAssetDetailEntity (明细表)
    ↓ sumDV10Values() 方法读取
    ↓ getDv101() → dv10_1字段 → DV10_0数据
    ↓ getDv102() → dv10_2字段 → DV10_0.5数据
    ↓ ...
    ↓ getDv1020() → dv10_20字段 → DV10_50数据
    ↓
BigDecimal[] dv10Totals 汇总数组
    ↓ setDV10Values() 方法写入
    ↓ setDv100() → dv10_0字段 → DV10_0数据
    ↓ setDv1005() → dv10_0_5字段 → DV10_0.5数据
    ↓ ...
    ↓ setDv1050() → dv10_50字段 → DV10_50数据
    ↓
AdurDurationAssetSummaryEntity (汇总表)
```

## 总结

修复后，job模块的久期资产结果汇总表相关代码现在与 `adur_program_design.sql` 中的表结构完全一致，编译错误和SQL错误都应该已经解决。

关键修复点：
1. **实体类字段定义**：与数据库表结构完全匹配
2. **Mapper XML文件**：字段列表和参数映射正确
3. **服务实现类**：方法调用使用正确的字段名
4. **数据流转逻辑**：正确处理两个表之间的字段差异
