package com.ruoyi.adur.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 关键久期折现曲线含价差工具类
 * 用于处理JSON格式的关键久期折现曲线含价差数据和相关计算
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public class KeyDurationCurveWithSpreadUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(KeyDurationCurveWithSpreadUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final MathContext MATH_CONTEXT = new MathContext(10, RoundingMode.HALF_UP);
    
    /**
     * 根据压力方向计算关键久期折现曲线含价差
     * 
     * @param baseCurveJsonStr 基础曲线JSON字符串（来自月度折现曲线表含价差）
     * @param keyParameterJsonStr 关键久期参数JSON字符串
     * @param stressDirection 压力方向（上升/下降）
     * @return 关键久期折现曲线含价差JSON字符串
     */
    public static String calculateKeyDurationCurveWithSpread(String baseCurveJsonStr, 
                                                             String keyParameterJsonStr, 
                                                             String stressDirection) {
        if (baseCurveJsonStr == null || baseCurveJsonStr.trim().isEmpty()) {
            logger.warn("输入的基础曲线JSON字符串为空");
            return "{}";
        }
        
        if (keyParameterJsonStr == null || keyParameterJsonStr.trim().isEmpty()) {
            logger.warn("输入的关键久期参数JSON字符串为空");
            return "{}";
        }
        
        try {
            JsonNode baseCurveNode = objectMapper.readTree(baseCurveJsonStr);
            JsonNode keyParameterNode = objectMapper.readTree(keyParameterJsonStr);
            ObjectNode resultNode = objectMapper.createObjectNode();
            
            baseCurveNode.fieldNames().forEachRemaining(termStr -> {
                try {
                    BigDecimal baseCurveValue = new BigDecimal(baseCurveNode.get(termStr).asText());
                    BigDecimal keyParameterValue = getKeyParameterValue(keyParameterNode, termStr);
                    
                    BigDecimal adjustedValue = calculateAdjustedCurveValue(baseCurveValue, keyParameterValue, stressDirection);
                    resultNode.put(termStr, adjustedValue);
                    
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或曲线值失败: {}", termStr);
                }
            });
            
            return objectMapper.writeValueAsString(resultNode);
            
        } catch (JsonProcessingException e) {
            logger.error("计算关键久期折现曲线含价差失败: baseCurve={}, keyParameter={}", 
                        baseCurveJsonStr, keyParameterJsonStr, e);
            return "{}";
        }
    }
    
    /**
     * 根据压力方向计算调整后的曲线值
     * 
     * @param baseCurveValue 基础曲线值
     * @param keyParameterValue 关键久期参数值
     * @param stressDirection 压力方向
     * @return 调整后的曲线值
     */
    public static BigDecimal calculateAdjustedCurveValue(BigDecimal baseCurveValue, 
                                                         BigDecimal keyParameterValue, 
                                                         String stressDirection) {
        if (baseCurveValue == null) {
            baseCurveValue = BigDecimal.ZERO;
        }
        if (keyParameterValue == null) {
            keyParameterValue = BigDecimal.ZERO;
        }
        
        BigDecimal adjustedValue;
        
        if ("上升".equals(stressDirection)) {
            // 压力方向=上升：基础曲线值 + 关键久期参数值
            adjustedValue = baseCurveValue.add(keyParameterValue, MATH_CONTEXT);
        } else if ("下降".equals(stressDirection)) {
            // 压力方向=下降：基础曲线值 - 关键久期参数值
            adjustedValue = baseCurveValue.subtract(keyParameterValue, MATH_CONTEXT);
        } else {
            logger.warn("未知的压力方向: {}", stressDirection);
            adjustedValue = baseCurveValue;
        }
        
        return adjustedValue;
    }
    
    /**
     * 从关键久期参数JSON中获取指定期限的参数值
     * 
     * @param keyParameterNode 关键久期参数JSON节点
     * @param termStr 期限字符串
     * @return 参数值
     */
    private static BigDecimal getKeyParameterValue(JsonNode keyParameterNode, String termStr) {
        try {
            JsonNode valueNode = keyParameterNode.get(termStr);
            if (valueNode != null) {
                // 如果参数值是对象格式，如 {"date":"2025-01-01","val":0.25}
                if (valueNode.isObject() && valueNode.has("val")) {
                    return new BigDecimal(valueNode.get("val").asText());
                } else {
                    // 如果参数值是直接数值
                    return new BigDecimal(valueNode.asText());
                }
            }
        } catch (NumberFormatException e) {
            logger.warn("解析关键久期参数值失败: term={}", termStr);
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 验证压力方向是否有效
     * 
     * @param stressDirection 压力方向
     * @return 是否有效
     */
    public static boolean isValidStressDirection(String stressDirection) {
        return "上升".equals(stressDirection) || "下降".equals(stressDirection);
    }
    
    /**
     * 验证关键期限是否有效
     * 
     * @param keyTerm 关键期限
     * @return 是否有效
     */
    public static boolean isValidKeyTerm(String keyTerm) {
        String[] validTerms = {"0", "0.5", "1", "2", "3", "4", "5", "6", "7", "8", 
                              "10", "12", "15", "20", "25", "30", "35", "40", "45", "50"};
        
        for (String validTerm : validTerms) {
            if (validTerm.equals(keyTerm)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 将Map转换为JSON字符串
     * 
     * @param curveMap 期限-曲线值映射
     * @return JSON字符串
     */
    public static String mapToJson(Map<Integer, BigDecimal> curveMap) {
        try {
            ObjectNode jsonNode = objectMapper.createObjectNode();
            for (Map.Entry<Integer, BigDecimal> entry : curveMap.entrySet()) {
                jsonNode.put(entry.getKey().toString(), entry.getValue());
            }
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("转换Map到JSON失败", e);
            return "{}";
        }
    }
    
    /**
     * 将JSON字符串转换为Map
     * 
     * @param jsonStr JSON字符串
     * @return 期限-曲线值映射
     */
    public static Map<Integer, BigDecimal> jsonToMap(String jsonStr) {
        Map<Integer, BigDecimal> curveMap = new HashMap<>();
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return curveMap;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            jsonNode.fieldNames().forEachRemaining(fieldName -> {
                try {
                    Integer term = Integer.parseInt(fieldName);
                    BigDecimal curveValue = new BigDecimal(jsonNode.get(fieldName).asText());
                    curveMap.put(term, curveValue);
                } catch (NumberFormatException e) {
                    logger.warn("解析期限或曲线值失败: {}", fieldName);
                }
            });
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return curveMap;
    }
    
    /**
     * 获取指定期限的关键久期折现曲线值
     * 
     * @param jsonStr JSON字符串
     * @param term 期限（月份）
     * @return 曲线值，如果不存在返回null
     */
    public static BigDecimal getCurveValue(String jsonStr, Integer term) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            JsonNode curveNode = jsonNode.get(term.toString());
            if (curveNode != null) {
                return new BigDecimal(curveNode.asText());
            }
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
        }
        
        return null;
    }
    
    /**
     * 设置指定期限的关键久期折现曲线值
     * 
     * @param jsonStr 原JSON字符串
     * @param term 期限（月份）
     * @param curveValue 曲线值
     * @return 更新后的JSON字符串
     */
    public static String setCurveValue(String jsonStr, Integer term, BigDecimal curveValue) {
        try {
            ObjectNode jsonNode;
            if (jsonStr == null || jsonStr.trim().isEmpty()) {
                jsonNode = objectMapper.createObjectNode();
            } else {
                jsonNode = (ObjectNode) objectMapper.readTree(jsonStr);
            }
            
            jsonNode.put(term.toString(), curveValue);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            logger.error("设置关键久期折现曲线值失败", e);
            return jsonStr;
        }
    }
    
    /**
     * 验证JSON格式是否正确
     * 
     * @param jsonStr JSON字符串
     * @return 是否有效
     */
    public static boolean isValidJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return false;
        }
        
        try {
            objectMapper.readTree(jsonStr);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 获取JSON中包含的期限数量
     * 
     * @param jsonStr JSON字符串
     * @return 期限数量
     */
    public static int getTermCount(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return 0;
        }
        
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.size();
        } catch (JsonProcessingException e) {
            logger.error("解析JSON失败: {}", jsonStr, e);
            return 0;
        }
    }
    
    /**
     * 批量计算关键久期折现曲线含价差
     * 
     * @param baseCurveMap 基础曲线映射
     * @param keyParameterMap 关键久期参数映射
     * @param stressDirection 压力方向
     * @return 关键久期折现曲线含价差映射
     */
    public static Map<Integer, BigDecimal> calculateBatchKeyDurationCurve(Map<Integer, BigDecimal> baseCurveMap, 
                                                                          Map<Integer, BigDecimal> keyParameterMap, 
                                                                          String stressDirection) {
        Map<Integer, BigDecimal> resultMap = new HashMap<>();
        
        for (Map.Entry<Integer, BigDecimal> entry : baseCurveMap.entrySet()) {
            Integer term = entry.getKey();
            BigDecimal baseCurveValue = entry.getValue();
            BigDecimal keyParameterValue = keyParameterMap.getOrDefault(term, BigDecimal.ZERO);
            
            BigDecimal adjustedValue = calculateAdjustedCurveValue(baseCurveValue, keyParameterValue, stressDirection);
            resultMap.put(term, adjustedValue);
        }
        
        return resultMap;
    }
}
