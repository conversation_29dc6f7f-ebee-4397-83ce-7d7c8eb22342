-- =============================================
-- 修复月度折现曲线含价差菜单路径问题
-- 问题：菜单配置中的component路径指向已删除的文件
-- 解决：更新菜单配置为正确的路径
-- =============================================

-- 1. 首先查看当前的菜单配置
SELECT menu_id, menu_name, path, component, perms 
FROM sys_menu 
WHERE menu_name LIKE '%月度折现曲线含价差%' 
   OR component LIKE '%monthly/discount/curve%'
   OR perms LIKE '%monthly:discount:curve%';

-- 2. 删除旧的菜单配置（如果存在）
-- 删除旧的按钮权限
DELETE FROM sys_menu WHERE perms LIKE 'adur:issue:monthly:discount:curve:%';

-- 删除旧的主菜单
DELETE FROM sys_menu WHERE component = 'adur/issue/monthly/discount/curve/index';

-- 3. 确保新的菜单配置存在
-- 如果新菜单不存在，则插入
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('ADUR月度折现曲线含价差', '2548', '6', 'adurmonthlydiscountcurvewithspread', 'adur/monthly/discount/curve/with/spread/index', 1, 0, 'C', '0', '0', 'adur:monthly:discount:curve:with:spread:list', '#', 'admin', NOW(), '', NULL, 'ADUR月度折现曲线含价差菜单');

-- 获取新插入的菜单ID
SET @parentId = LAST_INSERT_ID();

-- 如果菜单已存在，获取其ID
SELECT @parentId := menu_id FROM sys_menu WHERE component = 'adur/monthly/discount/curve/with/spread/index' LIMIT 1;

-- 4. 插入新的按钮权限（如果不存在）
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差查询', @parentId, '1', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:query', '#', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差新增', @parentId, '2', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:add', '#', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差修改', @parentId, '3', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差删除', @parentId, '4', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差导出', @parentId, '5', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:export', '#', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('月度折现曲线含价差导入', @parentId, '6', '#', '', 1, 0, 'F', '0', '0', 'adur:monthly:discount:curve:with:spread:import', '#', 'admin', NOW(), '', NULL, '');

-- 5. 验证修复结果
SELECT menu_id, menu_name, path, component, perms 
FROM sys_menu 
WHERE menu_name LIKE '%月度折现曲线含价差%' 
   OR component LIKE '%monthly/discount/curve/with/spread%'
   OR perms LIKE '%monthly:discount:curve:with:spread%'
ORDER BY menu_id;

-- =============================================
-- 执行说明：
-- 1. 运行此SQL前，请先备份sys_menu表
-- 2. 此SQL会删除旧的菜单配置并创建新的配置
-- 3. 执行后需要重新登录系统以刷新菜单缓存
-- 4. 如果问题仍然存在，请检查前端路由配置
-- =============================================

-- 注意事项：
-- 1. 新的路径：'adur/monthly/discount/curve/with/spread/index'
-- 2. 新的权限前缀：'adur:monthly:discount:curve:with:spread:'
-- 3. 新的路由路径：'adurmonthlydiscountcurvewithspread'
-- 4. 确保前端文件存在于正确的路径
-- =============================================
