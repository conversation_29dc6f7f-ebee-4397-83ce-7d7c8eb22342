-- =============================================
-- 修改偿付能力状况表row_number字段类型
-- 将int类型改为varchar类型以支持字符串行次
-- =============================================

-- 修改t_base_solvency_status表的row_number字段类型
ALTER TABLE `t_base_solvency_status` 
MODIFY COLUMN `row_number` varchar(20) NOT NULL COMMENT '行次，表中行的序号';

-- 重新创建索引（如果需要的话）
-- DROP INDEX `idx_row_number` ON `t_base_solvency_status`;
-- CREATE INDEX `idx_row_number` ON `t_base_solvency_status` (`row_number`);
