-- =============================================
-- 月度折现曲线表DDL生成脚本
-- 用于生成包含完整term_0到term_600字段的DDL
-- =============================================

-- 生成完整的CREATE TABLE语句
-- 注意：此脚本用于文档目的，实际执行请使用t_adur_monthly_discount_curve_complete_ddl.sql

/*
-- 基础表结构
DROP TABLE IF EXISTS `t_adur_monthly_discount_curve`;
CREATE TABLE `t_adur_monthly_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
*/

-- 期限字段生成逻辑（伪代码）
-- FOR i = 0 TO 600
--   `term_{i}` decimal(10,6) DEFAULT 0 COMMENT '期限{i}',
-- END FOR

-- 完整字段列表（601个期限字段）：
-- term_0, term_1, term_2, ..., term_600

-- 审计字段和索引
/*
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_asset_date` (`account_period`,`asset_number`,`date_type`),
  KEY `idx_account_period` (`account_period`),
  KEY `idx_asset_number` (`asset_number`),
  KEY `idx_date_type` (`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='月度折现曲线表不含价差';
*/

-- =============================================
-- Java代码生成示例
-- =============================================

-- 生成实体类字段的Java代码
/*
// 期限字段生成
for (int i = 0; i <= 600; i++) {
    System.out.println("/**");
    System.out.println(" * 期限" + i);
    System.out.println(" */");
    System.out.println("@TableField(\"term_" + i + "\")");
    System.out.println("private BigDecimal term" + i + ";");
    System.out.println();
}
*/

-- 生成Mapper XML字段列表
/*
// SELECT字段生成
StringBuilder selectFields = new StringBuilder();
for (int i = 0; i <= 600; i++) {
    if (i > 0) selectFields.append(", ");
    selectFields.append("term_").append(i);
}
System.out.println(selectFields.toString());
*/

-- 生成INSERT字段列表
/*
// INSERT VALUES字段生成
StringBuilder insertValues = new StringBuilder();
for (int i = 0; i <= 600; i++) {
    if (i > 0) insertValues.append(", ");
    insertValues.append("#{item.term").append(i).append("}");
}
System.out.println(insertValues.toString());
*/

-- =============================================
-- 使用说明
-- =============================================

/*
1. 完整DDL文件位置：
   docs/sql/t_adur_monthly_discount_curve_complete_ddl.sql

2. 工具类支持：
   - TermFieldUtil.java：动态访问期限字段
   - 支持批量操作、线性插值、SQL生成等功能

3. 计算逻辑：
   - 折现曲线标识=0 + 发行时点：返回特殊标识
   - 折现曲线标识=0 + 评估时点：使用到期收益率
   - 折现曲线标识≠0：年度曲线插值计算

4. 性能考虑：
   - 使用字段缓存提高反射性能
   - 支持批量设置和获取操作
   - 线性插值算法优化

5. 维护建议：
   - 使用TermFieldUtil工具类而非直接字段访问
   - 定期验证字段完整性
   - 注意内存使用情况（601个BigDecimal字段）
*/

-- =============================================
-- 脚本结束
-- =============================================
