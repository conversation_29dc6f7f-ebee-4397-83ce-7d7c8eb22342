-- 中短存续期产品利差表 (TB0016)
CREATE TABLE IF NOT EXISTS `t_cost_short_term_product_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(20) NOT NULL COMMENT '设计类型，如：传统型',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户名称',
  `new_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '新单规模保费',
  `accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金',
  `investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款',
  `accounting_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化会计投资收益率',
  `comprehensive_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化综合投资收益率',
  `liability_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债资金成本率',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债有效成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='中短存续期产品利差表';
