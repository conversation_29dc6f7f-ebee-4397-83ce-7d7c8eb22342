# ADUR关键久期折现曲线含价差值集为空问题分析

## 问题描述

用户反映关键久期折现曲线表含价差的值集字段目前都是空值，按道理应该有值。

根据设计文档，该字段的计算逻辑如下：

### 计算逻辑
```
格式：{"0":0.25,"1":0.35...,"600":0.15}

值的计算逻辑：
1. 如果压力方向=上升
   等于月度折现曲线表含价差中的term_X + 关键久期参数表.关键参数值集.key

2. 如果压力方向=下降
   等于月度折现曲线表含价差中的term_X - 关键久期参数表.关键参数值集.key
```

### 匹配条件
```
月度折现曲线表含价差匹配条件：
- 月度折现曲线表含价差.曲线细分类=2
- 月度折现曲线表含价差.账户名称=关键久期折现曲线表含价差.账户名称
- 月度折现曲线表含价差.证券代码=关键久期折现曲线表含价差.证券代码

关键久期参数表匹配条件：
- 关键久期参数表.关键期限=关键久期折现曲线表含价差.关键期限
```

## 可能原因分析

### 1. 基础数据缺失

#### 1.1 关键久期参数表数据缺失
- **表名**: `t_adur_key_duration_parameter`
- **关键字段**: `parameter_val_set` (JSON格式参数值集)
- **检查方法**: 
```sql
SELECT account_period, key_duration, 
       CASE WHEN parameter_val_set IS NULL OR parameter_val_set = '' THEN '空' ELSE '有值' END as 参数值集状态
FROM t_adur_key_duration_parameter 
WHERE account_period = '202406';
```

#### 1.2 月度折现曲线含价差数据缺失
- **表名**: `t_adur_monthly_discount_curve_with_spread`
- **关键字段**: `monthly_discount_rate_with_spread_set` (JSON格式期限数据)
- **关键条件**: `curve_sub_category = '2'`
- **检查方法**:
```sql
SELECT account_period, curve_sub_category, account_name, security_code,
       CASE WHEN monthly_discount_rate_with_spread_set IS NULL OR monthly_discount_rate_with_spread_set = '' THEN '空' ELSE '有值' END as 期限数据状态
FROM t_adur_monthly_discount_curve_with_spread 
WHERE account_period = '202406' AND curve_sub_category = '2';
```

### 2. 匹配条件不满足

#### 2.1 曲线细分类不等于2
- 计算逻辑要求使用曲线细分类=2的月度折现曲线含价差数据
- 如果数据库中没有曲线细分类=2的数据，计算将失败

#### 2.2 账户名称或证券代码不匹配
- 关键久期折现曲线表与月度折现曲线含价差表的账户名称、证券代码必须匹配
- 数据不一致会导致无法找到对应的基础数据

#### 2.3 关键期限不匹配
- 关键久期参数表的关键期限必须与关键久期折现曲线表的关键期限匹配
- 支持的关键期限：0, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 15, 20, 25, 30, 35, 40, 45, 50

### 3. JSON数据格式问题

#### 3.1 关键久期参数值集格式错误
- **期望格式**: `{"0":{"val":0.001},"1":{"val":0.002},...,"600":{"val":0.004}}`
- **常见错误**: 
  - 缺少"val"键
  - JSON格式不正确
  - 数值类型错误

#### 3.2 月度折现曲线期限数据格式错误
- **期望格式**: `{"0":0.025,"1":0.026,...,"600":0.030}`
- **常见错误**:
  - JSON格式不正确
  - 期限索引不连续
  - 数值为null

### 4. 计算逻辑异常

#### 4.1 异常处理导致空值
- 当计算过程中发生异常时，代码会设置空的JSON对象 `{}`
- 需要检查日志中的异常信息

#### 4.2 数值计算错误
- 基础利率或参数值为null时，计算结果为null
- null值不会被包含在最终的JSON中

## 解决方案

### 1. 数据准备检查清单

#### 1.1 关键久期参数表数据准备
```sql
-- 检查关键久期参数数据
SELECT * FROM t_adur_key_duration_parameter WHERE account_period = '202406';

-- 如果数据为空，需要先执行相关的数据生成任务
-- 或者手动插入测试数据
```

#### 1.2 月度折现曲线含价差数据准备
```sql
-- 检查月度折现曲线含价差数据（重点检查曲线细分类=2）
SELECT * FROM t_adur_monthly_discount_curve_with_spread 
WHERE account_period = '202406' AND curve_sub_category = '2';

-- 如果数据为空，需要先执行UC0006任务
```

### 2. 调试工具使用

我已经创建了调试工具类 `AdurKeyDurationCurveDebugUtil`，可以帮助分析问题：

```java
// 在服务实现类中已添加调试代码
AdurKeyDurationCurveDebugUtil.debugKeyDurationCurveCalculation(
    accountPeriod, 
    keyDurationParameterList,
    monthlyDiscountCurveWithSpreadList
);
```

### 3. 测试数据准备

我已经更新了测试类 `AdurKeyDurationDiscountCurveWithSpreadTaskTest`，添加了完整的测试数据准备：

- 关键久期参数数据（包含完整的0-600期参数值）
- 月度折现曲线含价差数据（曲线细分类=2）
- 正确的匹配条件设置

### 4. 执行顺序检查

确保按正确的顺序执行ADUR任务：

1. **UC0003**: 生成久期资产明细数据
2. **UC0004**: 计算年度折现曲线
3. **UC0005**: 计算月度折现曲线不含价差
4. **UC0006**: 计算月度折现曲线含价差 ⭐ **必须先执行**
5. **UC0007**: 计算月度折现因子含价差
6. **UC0008**: 计算关键久期折现曲线含价差 ⭐ **当前任务**

### 5. 常见问题排查步骤

1. **检查日志**: 查看是否有异常信息
2. **检查基础数据**: 确认关键久期参数表和月度折现曲线含价差表有数据
3. **检查匹配条件**: 确认曲线细分类=2的数据存在
4. **检查JSON格式**: 确认JSON数据格式正确
5. **运行调试工具**: 使用调试工具分析具体问题
6. **运行测试用例**: 执行测试用例验证计算逻辑

## 预期结果

修复后，关键久期折现曲线含价差值集字段应该包含类似以下格式的数据：

```json
{
  "0": 0.041001,
  "1": 0.041002,
  "12": 0.046001,
  "24": 0.051002,
  ...
  "600": 0.055600
}
```

其中每个期限的值都是通过以下公式计算得出：
- **上升压力**: 月度折现曲线含价差.期限X + 关键久期参数.期限X
- **下降压力**: 月度折现曲线含价差.期限X - 关键久期参数.期限X

## 后续建议

1. **完善监控**: 添加数据质量监控，及时发现空值问题
2. **增强日志**: 在关键计算节点添加详细日志
3. **数据验证**: 在任务执行前验证基础数据的完整性
4. **异常处理**: 改进异常处理逻辑，提供更明确的错误信息
