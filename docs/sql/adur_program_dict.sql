-- =============================================
-- 资产久期管理模块(ADUR)字典数据
-- 基于 adur_program_design.sql DDL生成
-- 数据库: MySQL 8.0
-- 字符集: utf8
-- 生成时间: 2025-01-01
-- =============================================

-- =============================================
-- 字典类型数据
-- =============================================

-- 账户名称字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('账户名称','adur_account_name','0','admin',NOW(),'',NULL,'资产久期管理账户名称');

-- 日期类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('日期类型','adur_date_type','0','admin',NOW(),'',NULL,'资产久期管理日期类型');

-- 久期类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('久期类型','adur_duration_type','0','admin',NOW(),'',NULL,'资产久期管理久期类型');

-- 基点类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('基点类型','adur_basis_point_type','0','admin',NOW(),'',NULL,'资产久期管理基点类型');

-- 压力方向字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('压力方向','adur_stress_direction','0','admin',NOW(),'',NULL,'资产久期管理压力方向');

-- 价差类型字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('价差类型','adur_spread_type','0','admin',NOW(),'',NULL,'资产久期管理价差类型');

-- 曲线细分类字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('曲线细分类','adur_curve_sub_category','0','admin',NOW(),'',NULL,'资产久期管理曲线细分类');

-- 关键期限字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('关键期限','adur_key_term','0','admin',NOW(),'',NULL,'资产久期管理关键期限');

-- 付息方式字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('付息方式','adur_payment_method','0','admin',NOW(),'',NULL,'资产久期管理付息方式');

-- 是否删除字典类型
INSERT INTO alm.sys_dict_type (dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark) VALUES
('是否删除','adur_is_del','0','admin',NOW(),'',NULL,'是否删除标识');

-- =============================================
-- 字典数据
-- =============================================

-- 账户名称字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'传统账户','01','adur_account_name',NULL,'primary','Y','0','admin',NOW(),'',NULL,'传统型保险账户'),
(2,'分红账户','02','adur_account_name',NULL,'success','N','0','admin',NOW(),'',NULL,'分红型保险账户'),
(3,'万能账户','03','adur_account_name',NULL,'info','N','0','admin',NOW(),'',NULL,'万能型保险账户'),
(4,'普通账户','04','adur_account_name',NULL,'warning','N','0','admin',NOW(),'',NULL,'普通账户汇总');

-- 日期类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'发行时点','01','adur_date_type',NULL,'primary','Y','0','admin',NOW(),'',NULL,'资产发行时点'),
(2,'评估时点','02','adur_date_type',NULL,'success','N','0','admin',NOW(),'',NULL,'资产评估时点');

-- 久期类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'修正久期','01','adur_duration_type',NULL,'primary','Y','0','admin',NOW(),'',NULL,'修正久期计算'),
(2,'有效久期','02','adur_duration_type',NULL,'success','N','0','admin',NOW(),'',NULL,'有效久期计算'),
(3,'利差久期','03','adur_duration_type',NULL,'info','N','0','admin',NOW(),'',NULL,'利差久期计算'),
(4,'关键久期','04','adur_duration_type',NULL,'warning','N','0','admin',NOW(),'',NULL,'关键久期计算');

-- 基点类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'0bp','01','adur_basis_point_type',NULL,'primary','Y','0','admin',NOW(),'',NULL,'基准情景'),
(2,'+50bp','02','adur_basis_point_type',NULL,'danger','N','0','admin',NOW(),'',NULL,'上升50个基点'),
(3,'-50bp','03','adur_basis_point_type',NULL,'success','N','0','admin',NOW(),'',NULL,'下降50个基点');

-- 压力方向字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'上升','01','adur_stress_direction',NULL,'danger','Y','0','admin',NOW(),'',NULL,'利率上升压力'),
(2,'下降','02','adur_stress_direction',NULL,'success','N','0','admin',NOW(),'',NULL,'利率下降压力');

-- 价差类型字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'发行时点价差','01','adur_spread_type',NULL,'primary','Y','0','admin',NOW(),'',NULL,'资产发行时点价差'),
(2,'评估时点价差','02','adur_spread_type',NULL,'success','N','0','admin',NOW(),'',NULL,'资产评估时点价差');

-- 曲线细分类字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'修正久期+0bp+发行时点+发行时点价差','1','adur_curve_sub_category',NULL,'primary','Y','0','admin',NOW(),'',NULL,'曲线细分类1'),
(2,'修正久期+0bp+评估时点+发行时点价差','2','adur_curve_sub_category',NULL,'success','N','0','admin',NOW(),'',NULL,'曲线细分类2'),
(3,'有效久期+50bp+评估时点+发行时点价差','3','adur_curve_sub_category',NULL,'warning','N','0','admin',NOW(),'',NULL,'曲线细分类3'),
(4,'有效久期-50bp+评估时点+发行时点价差','4','adur_curve_sub_category',NULL,'info','N','0','admin',NOW(),'',NULL,'曲线细分类4'),
(5,'利差久期+0bp+评估时点+评估时点价差','5','adur_curve_sub_category',NULL,'danger','N','0','admin',NOW(),'',NULL,'曲线细分类5');

-- 关键期限字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'0年','0','adur_key_term',NULL,'primary','Y','0','admin',NOW(),'',NULL,'0年期限'),
(2,'0.5年','0.5','adur_key_term',NULL,'success','N','0','admin',NOW(),'',NULL,'0.5年期限'),
(3,'1年','1','adur_key_term',NULL,'info','N','0','admin',NOW(),'',NULL,'1年期限'),
(4,'2年','2','adur_key_term',NULL,'warning','N','0','admin',NOW(),'',NULL,'2年期限'),
(5,'3年','3','adur_key_term',NULL,'danger','N','0','admin',NOW(),'',NULL,'3年期限'),
(6,'4年','4','adur_key_term',NULL,'primary','N','0','admin',NOW(),'',NULL,'4年期限'),
(7,'5年','5','adur_key_term',NULL,'success','N','0','admin',NOW(),'',NULL,'5年期限'),
(8,'6年','6','adur_key_term',NULL,'info','N','0','admin',NOW(),'',NULL,'6年期限'),
(9,'7年','7','adur_key_term',NULL,'warning','N','0','admin',NOW(),'',NULL,'7年期限'),
(10,'8年','8','adur_key_term',NULL,'danger','N','0','admin',NOW(),'',NULL,'8年期限'),
(11,'10年','10','adur_key_term',NULL,'primary','N','0','admin',NOW(),'',NULL,'10年期限'),
(12,'12年','12','adur_key_term',NULL,'success','N','0','admin',NOW(),'',NULL,'12年期限'),
(13,'15年','15','adur_key_term',NULL,'info','N','0','admin',NOW(),'',NULL,'15年期限'),
(14,'20年','20','adur_key_term',NULL,'warning','N','0','admin',NOW(),'',NULL,'20年期限'),
(15,'25年','25','adur_key_term',NULL,'danger','N','0','admin',NOW(),'',NULL,'25年期限'),
(16,'30年','30','adur_key_term',NULL,'primary','N','0','admin',NOW(),'',NULL,'30年期限'),
(17,'35年','35','adur_key_term',NULL,'success','N','0','admin',NOW(),'',NULL,'35年期限'),
(18,'40年','40','adur_key_term',NULL,'info','N','0','admin',NOW(),'',NULL,'40年期限'),
(19,'45年','45','adur_key_term',NULL,'warning','N','0','admin',NOW(),'',NULL,'45年期限'),
(20,'50年','50','adur_key_term',NULL,'danger','N','0','admin',NOW(),'',NULL,'50年期限');

-- 付息方式字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'年付','01','adur_payment_method',NULL,'primary','Y','0','admin',NOW(),'',NULL,'按年付息'),
(2,'半年付','02','adur_payment_method',NULL,'success','N','0','admin',NOW(),'',NULL,'按半年付息'),
(3,'季付','03','adur_payment_method',NULL,'info','N','0','admin',NOW(),'',NULL,'按季度付息'),
(4,'月付','04','adur_payment_method',NULL,'warning','N','0','admin',NOW(),'',NULL,'按月付息'),
(5,'到期一次性付息','05','adur_payment_method',NULL,'danger','N','0','admin',NOW(),'',NULL,'到期一次性付息'),
(6,'贴现','06','adur_payment_method',NULL,'secondary','N','0','admin',NOW(),'',NULL,'贴现方式');

-- 是否删除字典数据
INSERT INTO alm.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
(1,'否','0','adur_is_del',NULL,'success','Y','0','admin',NOW(),'',NULL,'未删除'),
(2,'是','1','adur_is_del',NULL,'danger','N','0','admin',NOW(),'',NULL,'已删除');

-- =============================================
-- 字典数据生成完成
-- 总计10个字典类型：
-- 1. adur_account_name - 账户名称
-- 2. adur_date_type - 日期类型
-- 3. adur_duration_type - 久期类型
-- 4. adur_basis_point_type - 基点类型
-- 5. adur_stress_direction - 压力方向
-- 6. adur_spread_type - 价差类型
-- 7. adur_curve_sub_category - 曲线细分类
-- 8. adur_key_term - 关键期限
-- 9. adur_payment_method - 付息方式
-- 10. adur_is_del - 是否删除
-- =============================================
