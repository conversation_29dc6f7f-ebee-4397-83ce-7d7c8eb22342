# 月度折现曲线表DDL说明

## 概述

`t_adur_monthly_discount_curve` 表是ADUR模块的核心表之一，包含从 `term_0` 到 `term_600` 共601个期限字段，用于存储0-600个月的折现曲线数据。

## 文件结构

### 1. 完整DDL文件
- **文件位置**: `docs/sql/t_adur_monthly_discount_curve_complete_ddl.sql`
- **内容**: 包含完整的601个期限字段的CREATE TABLE语句
- **用途**: 实际数据库表创建

### 2. 设计文档DDL
- **文件位置**: `docs/sql/adur_program_design.sql`
- **内容**: 简化版本，显示部分字段和完整说明
- **用途**: 文档展示和设计参考

### 3. 生成脚本
- **文件位置**: `docs/sql/generate_monthly_discount_curve_ddl.sql`
- **内容**: DDL生成逻辑和使用说明
- **用途**: 开发参考和维护指导

## 表结构特点

### 基础字段
- `id`: 主键ID
- `account_period`: 账期（YYYYMM格式）
- `date_type`: 日期类型（发行时点/评估时点）
- `date`: 日期
- `asset_number`: 资产编号
- `account_name`: 账户名称
- `asset_name`: 资产名称
- `security_code`: 证券代码
- `curve_id`: 折现曲线标识

### 期限字段
- **数量**: 601个字段（term_0 到 term_600）
- **类型**: `decimal(10,6)`
- **默认值**: 0
- **含义**: 对应0-600个月的折现曲线值

### 审计字段
- `create_time`: 创建时间
- `create_by`: 创建者
- `update_time`: 更新时间
- `update_by`: 更新者
- `is_del`: 删除标识

## 索引设计

### 主键
- `PRIMARY KEY (id)`

### 唯一索引
- `uk_account_asset_date (account_period, asset_number, date_type)`

### 普通索引
- `idx_account_period (account_period)`
- `idx_asset_number (asset_number)`
- `idx_date_type (date_type)`

## 计算逻辑

### 期限字段取值规则

1. **折现曲线标识=0 AND 日期类型=发行时点**
   - 取值：特殊标识（用-1表示"-"）
   - 说明：不涉及具体计算

2. **折现曲线标识=0 AND 日期类型=评估时点**
   - 取值：久期资产明细表.到期收益率
   - 数据源：`t_adur_duration_asset_detail.eval_maturity_yield`

3. **折现曲线标识≠0**
   - **整数年份**（月份为12的整数倍）
     - 取值：年度折现曲线表对应年份的收益率
     - 计算：月份/12 = 年份索引
   - **非整数年份**（月份不为12的整数倍）
     - 取值：线性插值结果
     - 算法：取临近两个整数年的收益率进行线性插值

### 线性插值公式
```
value = lowerValue + (upperValue - lowerValue) * weight
其中：weight = (monthIndex % 12) / 12.0
```

## 代码支持

### 工具类
- **类名**: `TermFieldUtil`
- **位置**: `job/src/main/java/com/xl/alm/job/adur/util/TermFieldUtil.java`
- **功能**:
  - 动态字段访问（避免手写601个getter/setter）
  - 批量操作支持
  - 线性插值计算
  - SQL生成工具
  - 字段缓存机制

### 计算服务
- **类名**: `AdurMonthlyDiscountCurveCalculationServiceImpl`
- **位置**: `job/src/main/java/com/xl/alm/job/adur/service/impl/`
- **功能**:
  - 期限字段计算逻辑实现
  - 批量计算支持
  - 结果验证

### 实体类
- **类名**: `AdurMonthlyDiscountCurveEntity`
- **说明**: 当前版本可能不包含完整的601个字段
- **建议**: 使用TermFieldUtil工具类进行字段访问

## 使用示例

### 1. 设置期限字段值
```java
AdurMonthlyDiscountCurveEntity entity = new AdurMonthlyDiscountCurveEntity();
TermFieldUtil.setTermValue(entity, 12, new BigDecimal("0.05")); // 设置第12个月的值
```

### 2. 获取期限字段值
```java
BigDecimal value = TermFieldUtil.getTermValue(entity, 12); // 获取第12个月的值
```

### 3. 线性插值计算
```java
BigDecimal interpolated = TermFieldUtil.calculateLinearInterpolation(entity, 18.0); // 18个月的插值
```

### 4. 批量操作
```java
BigDecimal[] values = TermFieldUtil.getAllTermValues(entity); // 获取所有601个值
TermFieldUtil.setAllTermValues(entity, values); // 设置所有601个值
```

## 性能考虑

### 内存使用
- 每个实体包含601个BigDecimal字段
- 建议合理控制实体数量，避免内存溢出

### 查询优化
- 使用索引进行查询过滤
- 避免SELECT *，按需选择字段
- 考虑分页查询大量数据

### 计算优化
- 使用字段缓存提高反射性能
- 批量计算减少数据库访问
- 线性插值算法优化

## 维护建议

### 1. 字段完整性
- 定期验证实体类字段完整性
- 使用`TermFieldUtil.validateTermFields()`方法检查

### 2. 数据一致性
- 确保计算逻辑与业务规则一致
- 定期验证计算结果准确性

### 3. 性能监控
- 监控查询性能和内存使用
- 优化慢查询和大数据量操作

### 4. 代码维护
- 优先使用TermFieldUtil工具类
- 避免直接字段访问
- 保持代码和文档同步更新

## 相关文档

- [ADUR程序设计文档](../design/adur_program_design.md)
- [UC0005用例说明](../design/adur_program_design.md#uc0005)
- [TermFieldUtil工具类文档](../../job/src/main/java/com/xl/alm/job/adur/util/TermFieldUtil.java)

---
*最后更新时间: 2025-01-10*
