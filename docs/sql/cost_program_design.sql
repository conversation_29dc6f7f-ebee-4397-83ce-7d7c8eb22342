-- =====================================================
-- 成本管理模块数据库表结构DDL语句
-- 基于文档：docs/design/cost_program_design.md
-- 生成时间：2025-01-27
-- 模块编号：MD0001
-- =====================================================

-- 产品属性表 (TB0001)
CREATE TABLE IF NOT EXISTS `t_base_product_attribute` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `insurance_main_type` varchar(50) NOT NULL COMMENT '险种主类，如：长期寿险',
  `insurance_sub_type` varchar(50) NOT NULL COMMENT '险种细类，如：年金险、两全险、附加两全险等',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，如：传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `reg_mid_id` char(1) NOT NULL DEFAULT 'N' COMMENT '报监管中短标识，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,6) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户，如：分红账户1、万能账户5等',
  `new_business_flag` char(1) DEFAULT 'Y' COMMENT '新业务标识，Y-是，N-否',
  `remark` varchar(500) DEFAULT NULL COMMENT '产品相关补充说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品属性表';

-- 万能平均结算利率表 (TB0002)
CREATE TABLE IF NOT EXISTS `t_base_universal_avg_settlement_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `avg_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点平均结息利率',
  `avg_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末平均结息利率',
  `avg_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末平均结息利率',
  `avg_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末平均结息利率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='万能平均结算利率表';

-- 分红方案表 (TB0003)
CREATE TABLE IF NOT EXISTS `t_base_dividend_fund_cost_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `investment_return_rate` decimal(10,10) DEFAULT 0 COMMENT '投资收益率假设',
  `dividend_ratio` decimal(10,10) DEFAULT 0 COMMENT '分红比例',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分红方案表';

-- 法定准备金明细表 (TB0004)
CREATE TABLE IF NOT EXISTS `t_base_statutory_reserve_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `valid_policy_count` int(11) DEFAULT 0 COMMENT '有效保单件数',
  `accumulated_premium` decimal(28,10) DEFAULT 0 COMMENT '存量累计规模保费',
  `account_value` decimal(28,10) DEFAULT 0 COMMENT '账户价值',
  `statutory_reserve` decimal(28,10) DEFAULT 0 COMMENT '法定/非单位准备金',
  `guaranteed_rate_reserve` decimal(28,10) DEFAULT 0 COMMENT '保证利率准备金',
  `lapsed_policy_value` decimal(28,10) DEFAULT 0 COMMENT '失效单现价',
  `waiver_reserve` decimal(28,10) DEFAULT 0 COMMENT '豁免责任准备金',
  `unmodeled_reserve` decimal(28,10) DEFAULT 0 COMMENT '未建模准备金',
  `persistence_bonus_reserve` decimal(28,10) DEFAULT 0 COMMENT '持续奖准备金',
  `long_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '长期未到期准备金',
  `short_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '短险未到期准备金',
  `unearned_premium_reserve` decimal(28,10) DEFAULT 0 COMMENT '未到期责任准备金',
  `reported_unpaid` decimal(28,10) DEFAULT 0 COMMENT '已报未决赔款',
  `incurred_unreported` decimal(28,10) DEFAULT 0 COMMENT '未报未决赔款',
  `claim_expense_reserve` decimal(28,10) DEFAULT 0 COMMENT '理赔费用准备金',
  `outstanding_claim_reserve` decimal(28,10) DEFAULT 0 COMMENT '未决赔款准备金',
  `total_statutory_reserve` decimal(28,10) DEFAULT 0 COMMENT '法定准备金合计',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='法定准备金明细表';

-- 会计准备金明细表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_base_accounting_reserve_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `valid_policy_count` int(11) DEFAULT 0 COMMENT '有效保单件数',
  `accumulated_premium` decimal(28,10) DEFAULT 0 COMMENT '存量累计规模保费',
  `account_value` decimal(28,10) DEFAULT 0 COMMENT '账户价值',
  `dividend_provision` decimal(28,10) DEFAULT 0 COMMENT '红利预提',
  `best_estimate` decimal(28,10) DEFAULT 0 COMMENT '最优估计',
  `risk_margin` decimal(28,10) DEFAULT 0 COMMENT '风险边际',
  `residual_margin` decimal(28,10) DEFAULT 0 COMMENT '剩余边际',
  `unmodeled_reserve` decimal(28,10) DEFAULT 0 COMMENT '未建模准备金',
  `waiver_reserve` decimal(28,10) DEFAULT 0 COMMENT '豁免准备金',
  `persistence_bonus_reserve` decimal(28,10) DEFAULT 0 COMMENT '持续奖准备金',
  `long_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '长期险未到期准备金',
  `short_term_unearned` decimal(28,10) DEFAULT 0 COMMENT '短险未到期准备金',
  `unearned_premium_reserve` decimal(28,10) DEFAULT 0 COMMENT '未到期责任准备金',
  `reported_unpaid` decimal(28,10) DEFAULT 0 COMMENT '已报未决赔款',
  `incurred_unreported` decimal(28,10) DEFAULT 0 COMMENT '未报未决赔款',
  `claim_expense_reserve` decimal(28,10) DEFAULT 0 COMMENT '理赔费用准备金',
  `outstanding_claim_reserve` decimal(28,10) DEFAULT 0 COMMENT '未决赔款准备金',
  `total_accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金合计',
  `reinsurance_unearned` decimal(28,10) DEFAULT 0 COMMENT '应收分保未到期责任准备金',
  `reinsurance_reported` decimal(28,10) DEFAULT 0 COMMENT '应收分保已报未决',
  `reinsurance_unreported` decimal(28,10) DEFAULT 0 COMMENT '应收分保未报未决',
  `reinsurance_claim_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保未决合计',
  `reinsurance_total` decimal(28,10) DEFAULT 0 COMMENT '应收分保合计',
  `lapsed_policy_value` decimal(28,10) DEFAULT 0 COMMENT '失效保单现价',
  `fractional_month_dividend` decimal(28,10) DEFAULT 0 COMMENT '零头月红利',
  `unpaid_dividend` decimal(28,10) DEFAULT 0 COMMENT '应付未付红利',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会计准备金明细表';

-- 法定准备金预测表 (TB0006)
CREATE TABLE IF NOT EXISTS `t_base_statutory_reserve_forecast` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`business_type`, `accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='法定准备金预测表';

-- 分产品统计表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_cost_product_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '当前法定准备金',
  `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `business_type`, `accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品统计表';

-- 汇总统计表 (TB0008)
CREATE TABLE IF NOT EXISTS `t_cost_summary_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '当前法定准备金',
  `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `business_type`, `accounting_period`, `term_type`, `design_type`, `short_term_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='汇总统计表';

-- =====================================================
-- 成本管理模块字典数据
-- =====================================================

-- 字典类型定义
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('成本管理业务类型', 'cost_business_type', '0', 'admin', sysdate(), '成本管理模块业务类型字典'),
('成本管理长短期标识', 'cost_term_type', '0', 'admin', sysdate(), '成本管理模块长短期标识字典'),
('成本管理设计类型', 'cost_design_type', '0', 'admin', sysdate(), '成本管理模块产品设计类型字典'),
('成本管理中短期标识', 'cost_short_term_flag', '0', 'admin', sysdate(), '成本管理模块是否中短期产品字典'),
('成本管理情景名称', 'cost_scenario_name', '0', 'admin', sysdate(), '成本管理模块情景名称字典'),
('成本管理险种主类', 'cost_insurance_main_type', '0', 'admin', sysdate(), '成本管理模块险种主类字典'),
('成本管理险种细类', 'cost_insurance_sub_type', '0', 'admin', sysdate(), '成本管理模块险种细类字典');

-- 业务类型字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '有效业务', 'EXISTING', 'cost_business_type', '', 'default', 'Y', '0', 'admin', sysdate(), '成本管理业务类型'),
(2, '新业务', 'NEW', 'cost_business_type', '', 'default', 'N', '0', 'admin', sysdate(), '成本管理业务类型');

-- 长短期标识字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '长期', 'L', 'cost_term_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '长短期标识'),
(2, '短期', 'S', 'cost_term_type', '', 'info', 'N', '0', 'admin', sysdate(), '长短期标识');

-- 设计类型字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '传统险', '传统险', 'cost_design_type', '', 'default', 'Y', '0', 'admin', sysdate(), '产品设计类型'),
(2, '分红险', '分红险', 'cost_design_type', '', 'primary', 'N', '0', 'admin', sysdate(), '产品设计类型'),
(3, '万能险', '万能险', 'cost_design_type', '', 'success', 'N', '0', 'admin', sysdate(), '产品设计类型'),
(4, '投连险', '投连险', 'cost_design_type', '', 'warning', 'N', '0', 'admin', sysdate(), '产品设计类型');

-- 是否中短字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '否', 'N', 'cost_short_term_flag', '', 'success', 'Y', '0', 'admin', sysdate(), '是否中短期产品'),
(2, '是', 'Y', 'cost_short_term_flag', '', 'danger', 'N', '0', 'admin', sysdate(), '是否中短期产品');

-- 情景名称字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '基本情景', '基本情景', 'cost_scenario_name', '', 'primary', 'Y', '0', 'admin', sysdate(), '成本管理情景名称'),
(2, '压力情景一', '压力情景一', 'cost_scenario_name', '', 'warning', 'N', '0', 'admin', sysdate(), '成本管理情景名称'),
(3, '压力情景二', '压力情景二', 'cost_scenario_name', '', 'warning', 'N', '0', 'admin', sysdate(), '成本管理情景名称'),
(4, '压力情景三', '压力情景三', 'cost_scenario_name', '', 'danger', 'N', '0', 'admin', sysdate(), '成本管理情景名称');

-- 险种主类字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '长期寿险', '长期寿险', 'cost_insurance_main_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '险种主类'),
(2, '短期寿险', '短期寿险', 'cost_insurance_main_type', '', 'info', 'N', '0', 'admin', sysdate(), '险种主类'),
(3, '健康险', '健康险', 'cost_insurance_main_type', '', 'success', 'N', '0', 'admin', sysdate(), '险种主类'),
(4, '意外险', '意外险', 'cost_insurance_main_type', '', 'warning', 'N', '0', 'admin', sysdate(), '险种主类');

-- 险种细类字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '年金险', '年金险', 'cost_insurance_sub_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '险种细类'),
(2, '两全险', '两全险', 'cost_insurance_sub_type', '', 'primary', 'N', '0', 'admin', sysdate(), '险种细类'),
(3, '附加两全险', '附加两全险', 'cost_insurance_sub_type', '', 'info', 'N', '0', 'admin', sysdate(), '险种细类'),
(4, '终身寿险', '终身寿险', 'cost_insurance_sub_type', '', 'success', 'N', '0', 'admin', sysdate(), '险种细类'),
(5, '定期寿险', '定期寿险', 'cost_insurance_sub_type', '', 'warning', 'N', '0', 'admin', sysdate(), '险种细类');

-- =====================================================
-- 补充剩余表结构 (TB0009-TB0016)
-- =====================================================

-- 分账户汇总表 (TB0009)
CREATE TABLE IF NOT EXISTS `t_cost_account_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `statutory_reserve_t0` decimal(28,10) DEFAULT 0 COMMENT '评估时点法定准备金',
  `statutory_reserve_t1` decimal(28,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(28,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(28,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `accounting_period`, `design_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分账户汇总表';

-- 分产品有效成本率表 (TB0010)
CREATE TABLE IF NOT EXISTS `t_cost_product_effective_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '产品有效成本率',
  `cash_flow_set` text COMMENT 'JSON格式存储现金流数据',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`, `design_type`, `short_term_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品有效成本率表';

-- 分账户有效成本率表 (TB0011)
CREATE TABLE IF NOT EXISTS `t_cost_account_effective_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险、普通账户',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '账户有效成本率',
  `cash_flow_set` text NOT NULL COMMENT 'JSON格式存储现金流数据',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `design_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分账户有效成本率表';

-- 负债现金流表 (TB0012)
CREATE TABLE IF NOT EXISTS `t_dur_liability_cash_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` varchar(20) NOT NULL COMMENT '现金流类型，流入、流出',
  `basis_point_type` varchar(10) DEFAULT NULL COMMENT '基点类型，0bp、+50bp、-50bp',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `cash_flow_set` text NOT NULL COMMENT 'JSON格式存储预测现金流日期、期限、金额',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型，修正久期、有效久期',
  `main_insurance_type` varchar(50) NOT NULL COMMENT '险种主类',
  `detailed_insurance_type` varchar(50) NOT NULL COMMENT '险种细类',
  `design_type` varchar(50) NOT NULL COMMENT '产品设计类型',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `cash_flow_type`, `actuarial_code`, `duration_type`, `design_type`, `short_term_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='负债现金流表';

-- 保费收入明细表 (TB0013)
CREATE TABLE IF NOT EXISTS `t_base_premium_income_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(20) NOT NULL COMMENT '统计期间，格式：YYYYMM',
  `company_code` varchar(20) NOT NULL COMMENT '公司唯一标识',
  `company_name` varchar(50) NOT NULL COMMENT '公司名称',
  `product_code` varchar(20) NOT NULL COMMENT '产品唯一标识',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `channel_code` varchar(20) NOT NULL COMMENT '渠道唯一标识',
  `channel_name` varchar(50) NOT NULL COMMENT '渠道名称',
  `account_code` varchar(20) NOT NULL COMMENT '账户唯一标识',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `current_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(本月)',
  `current_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(本月)',
  `current_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(本月)',
  `current_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(本月)',
  `current_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(本月)',
  `current_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(本月)',
  `current_ul_renewal` decimal(28,10) DEFAULT 0 COMMENT '万能投连-续期(本月)',
  `current_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(本月)',
  `current_ul_total` decimal(28,10) DEFAULT 0 COMMENT '万能投连-合计(本月)',
  `current_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(本月)',
  `current_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(本月)',
  `current_surrender` decimal(28,10) DEFAULT 0 COMMENT '退保金(本月)',
  `current_ul_withdraw` decimal(28,10) DEFAULT 0 COMMENT '万能投连领取(本月)',
  `current_claim` decimal(28,10) DEFAULT 0 COMMENT '赔款支出(本月)',
  `current_medical` decimal(28,10) DEFAULT 0 COMMENT '死伤医疗给付(本月)',
  `current_maturity` decimal(28,10) DEFAULT 0 COMMENT '满期给付(本月)',
  `current_annuity` decimal(28,10) DEFAULT 0 COMMENT '年金给付(本月)',
  `current_ul_claim` decimal(28,10) DEFAULT 0 COMMENT '万能投连-赔款支出(本月)',
  `current_ul_medical` decimal(28,10) DEFAULT 0 COMMENT '万能投连-死伤医疗给付(本月)',
  `current_ul_maturity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-满期给付(本月)',
  `current_ul_annuity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-年金给付(本月)',
  `current_total_claim` decimal(28,10) DEFAULT 0 COMMENT '赔付支出合计(本月)',
  `ytd_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(年累计)',
  `ytd_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(年累计)',
  `ytd_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(年累计)',
  `ytd_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(年累计)',
  `ytd_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(年累计)',
  `ytd_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(年累计)',
  `ytd_ul_renewal` decimal(28,10) DEFAULT 0 COMMENT '万能投连-续期(年累计)',
  `ytd_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(年累计)',
  `ytd_ul_total` decimal(28,10) DEFAULT 0 COMMENT '万能投连-合计(年累计)',
  `ytd_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(年累计)',
  `ytd_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(年累计)',
  `ytd_surrender` decimal(28,10) DEFAULT 0 COMMENT '退保金(年累计)',
  `ytd_ul_withdraw` decimal(28,10) DEFAULT 0 COMMENT '万能投连领取(年累计)',
  `ytd_claim` decimal(28,10) DEFAULT 0 COMMENT '赔款支出(年累计)',
  `ytd_medical` decimal(28,10) DEFAULT 0 COMMENT '死伤医疗给付(年累计)',
  `ytd_maturity` decimal(28,10) DEFAULT 0 COMMENT '满期给付(年累计)',
  `ytd_annuity` decimal(28,10) DEFAULT 0 COMMENT '年金给付(年累计)',
  `ytd_ul_claim` decimal(28,10) DEFAULT 0 COMMENT '万能投连-赔款支出(年累计)',
  `ytd_ul_medical` decimal(28,10) DEFAULT 0 COMMENT '万能投连-死伤医疗给付(年累计)',
  `ytd_ul_maturity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-满期给付(年累计)',
  `ytd_ul_annuity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-年金给付(年累计)',
  `ytd_total_claim` decimal(28,10) DEFAULT 0 COMMENT '赔付支出合计(年累计)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `company_code`, `product_code`, `channel_code`, `account_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='保费收入明细表';

-- 子账户收益率表 (TB0014)
CREATE TABLE IF NOT EXISTS `t_base_sub_account_yield_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(20) NOT NULL COMMENT '统计期间，格式：YYYYMM',
  `account_name` varchar(50) NOT NULL COMMENT '子账户名称',
  `accounting_yield_rate` decimal(10,6) DEFAULT 0 COMMENT '年化会计投资收益率，以百分比表示',
  `comprehensive_yield_rate` decimal(10,6) DEFAULT 0 COMMENT '年化综合投资收益率，以百分比表示',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='子账户收益率表';

-- 分产品保费收入表 (TB0015)
CREATE TABLE IF NOT EXISTS `t_cost_product_premium_income_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(20) NOT NULL COMMENT '统计期间，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(20) NOT NULL COMMENT '设计类型，如：传统险',
  `current_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(本月)',
  `current_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(本月)',
  `current_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(本月)',
  `current_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(本月)',
  `current_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(本月)',
  `current_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(本月)',
  `current_ul_renewal` decimal(28,10) DEFAULT 0 COMMENT '万能投连-续期(本月)',
  `current_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(本月)',
  `current_ul_total` decimal(28,10) DEFAULT 0 COMMENT '万能投连-合计(本月)',
  `current_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(本月)',
  `current_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(本月)',
  `current_surrender` decimal(28,10) DEFAULT 0 COMMENT '退保金(本月)',
  `current_ul_withdraw` decimal(28,10) DEFAULT 0 COMMENT '万能投连领取(本月)',
  `current_claim` decimal(28,10) DEFAULT 0 COMMENT '赔款支出(本月)',
  `current_medical` decimal(28,10) DEFAULT 0 COMMENT '死伤医疗给付(本月)',
  `current_maturity` decimal(28,10) DEFAULT 0 COMMENT '满期给付(本月)',
  `current_annuity` decimal(28,10) DEFAULT 0 COMMENT '年金给付(本月)',
  `current_ul_claim` decimal(28,10) DEFAULT 0 COMMENT '万能投连-赔款支出(本月)',
  `current_ul_medical` decimal(28,10) DEFAULT 0 COMMENT '万能投连-死伤医疗给付(本月)',
  `current_ul_maturity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-满期给付(本月)',
  `current_ul_annuity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-年金给付(本月)',
  `current_total_claim` decimal(28,10) DEFAULT 0 COMMENT '赔付支出合计(本月)',
  `ytd_single_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-趸交(年累计)',
  `ytd_regular_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-期交(年累计)',
  `ytd_renewal_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-续期(年累计)',
  `ytd_total_premium` decimal(28,10) DEFAULT 0 COMMENT '原保费-合计(年累计)',
  `ytd_ul_single` decimal(28,10) DEFAULT 0 COMMENT '万能投连-趸交(年累计)',
  `ytd_ul_regular` decimal(28,10) DEFAULT 0 COMMENT '万能投连-期交(年累计)',
  `ytd_ul_renewal` decimal(28,10) DEFAULT 0 COMMENT '万能投连-续期(年累计)',
  `ytd_ul_initial_fee` decimal(28,10) DEFAULT 0 COMMENT '万能投连-初始费用(年累计)',
  `ytd_ul_total` decimal(28,10) DEFAULT 0 COMMENT '万能投连-合计(年累计)',
  `ytd_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '规模保费合计(年累计)',
  `ytd_investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款余额(年累计)',
  `ytd_surrender` decimal(28,10) DEFAULT 0 COMMENT '退保金(年累计)',
  `ytd_ul_withdraw` decimal(28,10) DEFAULT 0 COMMENT '万能投连领取(年累计)',
  `ytd_claim` decimal(28,10) DEFAULT 0 COMMENT '赔款支出(年累计)',
  `ytd_medical` decimal(28,10) DEFAULT 0 COMMENT '死伤医疗给付(年累计)',
  `ytd_maturity` decimal(28,10) DEFAULT 0 COMMENT '满期给付(年累计)',
  `ytd_annuity` decimal(28,10) DEFAULT 0 COMMENT '年金给付(年累计)',
  `ytd_ul_claim` decimal(28,10) DEFAULT 0 COMMENT '万能投连-赔款支出(年累计)',
  `ytd_ul_medical` decimal(28,10) DEFAULT 0 COMMENT '万能投连-死伤医疗给付(年累计)',
  `ytd_ul_maturity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-满期给付(年累计)',
  `ytd_ul_annuity` decimal(28,10) DEFAULT 0 COMMENT '万能投连-年金给付(年累计)',
  `ytd_total_claim` decimal(28,10) DEFAULT 0 COMMENT '赔付支出合计(年累计)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品保费收入表';

-- 中短存续期产品利差表 (TB0016)
CREATE TABLE IF NOT EXISTS `t_cost_short_term_product_spread` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(20) NOT NULL COMMENT '设计类型，如：传统型',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户名称',
  `new_scale_premium` decimal(28,10) DEFAULT 0 COMMENT '新单规模保费',
  `accounting_reserve` decimal(28,10) DEFAULT 0 COMMENT '会计准备金',
  `investment_balance` decimal(28,10) DEFAULT 0 COMMENT '保户储金及投资款',
  `accounting_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化会计投资收益率',
  `comprehensive_yield_rate` decimal(10,10) DEFAULT 0 COMMENT '年化综合投资收益率',
  `liability_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债资金成本率',
  `effective_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '负债有效成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='中短存续期产品利差表';
