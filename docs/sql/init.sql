-- 初始化数据库表结构
-- 字符集: utf8
-- 数据库: MySQL 8.0

-- 负债现金流表 (TB0001)
CREATE TABLE IF NOT EXISTS `t_dur_liability_cash_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `is_short_term` char(1) NOT NULL DEFAULT 'N' COMMENT '是否为中短期险种',
  `actuarial_code` varchar(20) NOT NULL COMMENT '精算代码',
  `business_code` varchar(20) NOT NULL COMMENT '业务代码',
  `product_name` varchar(50) NOT NULL COMMENT '产品名称',
  `insurance_main_type` varchar(50) NOT NULL COMMENT '险种主类',
  `insurance_sub_type` varchar(50) NOT NULL COMMENT '险种细类',
  `cash_val_set` mediumtext NOT NULL COMMENT '现金流值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `duration_type`, `design_type`, `is_short_term`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='负债现金流表';

-- 折现曲线表 (TB0002)
CREATE TABLE IF NOT EXISTS `t_dur_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `curve_type` char(2) NOT NULL COMMENT '曲线类型,01:中档,02:低档',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `curve_val_set` mediumtext NOT NULL COMMENT '曲线值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `curve_type`, `duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='折现曲线表';

-- 折现因子表 (TB0003)
CREATE TABLE IF NOT EXISTS `t_dur_discount_factor` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `factor_type` char(2) NOT NULL COMMENT '因子类型,01:中档,02:低档',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `factor_val_set` mediumtext NOT NULL COMMENT '因子值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `factor_type`, `duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='折现因子表';

-- 负债现金流汇总表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_dur_liability_cash_flow_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `is_short_term` char(1) NOT NULL DEFAULT 'N' COMMENT '是否为中短期险种',
  `cash_val_set` mediumtext NOT NULL COMMENT '现金流值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `present_cash_val_set` mediumtext NOT NULL COMMENT '现金流现值值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `duration_type`, `design_type`, `is_short_term`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='负债现金流汇总表';

-- 负债久期汇总表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_dur_liability_duration_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `is_short_term` char(1) NOT NULL DEFAULT 'N' COMMENT '是否为中短期险种',
  `duration_val_set` mediumtext NOT NULL COMMENT '久期值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `duration_type`, `design_type`, `is_short_term`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='负债久期汇总表';

-- 分账户负债现金流现值汇总表 (TB0008)
CREATE TABLE IF NOT EXISTS `t_dur_sub_account_liability_present_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `present_cash_val_set` mediumtext NOT NULL COMMENT '现金流现值值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `evaluation_point_present_value` decimal(30,10) DEFAULT NULL COMMENT '评估时点现值,存储值集中0时刻的值,用于最终填报报表',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `bp_type`, `duration_type`, `design_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分账户负债现金流现值汇总表';

-- 分账户负债久期汇总表 (TB0009)
CREATE TABLE IF NOT EXISTS `t_dur_sub_account_liability_duration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `bp_type` char(2) NOT NULL COMMENT '基点类型,01:+50bp,02:-50bp',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',
  `duration_val_set` mediumtext NOT NULL COMMENT '久期值集,分为1272项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1272":{"date":"2025-12-01","val":0.15}}',
  `evaluation_point_duration` decimal(30,10) DEFAULT NULL COMMENT '评估时点久期,存储值集中0时刻的值,用于最终填报报表',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `duration_type`, `design_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分账户负债久期汇总表';

-- 关键久期参数表 (TB0003)
CREATE TABLE IF NOT EXISTS `t_dur_key_duration_parameter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM',
  `key_duration` char(2) NOT NULL COMMENT '关键期限点',
  `parameter_val_set` mediumtext NOT NULL COMMENT '关键久期参数值集,分为1273项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1273":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `key_duration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期参数表';

-- 关键久期折现曲线表 (TB0004)
CREATE TABLE IF NOT EXISTS `t_dur_key_duration_discount_curve` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM',
  `curve_type` char(2) NOT NULL COMMENT '曲线类型,01:中档,02:低档',
  `key_duration` char(2) NOT NULL COMMENT '关键期限点',
  `stress_direction` char(2) NOT NULL COMMENT '压力方向,01:上升,02:下降',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期,03:关键久期',
  `curve_val_set` mediumtext NOT NULL COMMENT '曲线值集,分为1273项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1273":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `curve_type`, `key_duration`, `stress_direction`, `duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期折现曲线表';

-- 关键久期折现因子表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_dur_key_duration_discount_factors` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM',
  `curve_type` char(2) NOT NULL COMMENT '曲线类型,01:中档,02:低档',
  `key_duration` char(2) NOT NULL COMMENT '关键期限点',
  `stress_direction` char(2) NOT NULL COMMENT '压力方向,01:上升,02:下降',
  `duration_type` char(2) NOT NULL COMMENT '久期类型,01:修正久期,02:有效久期,03:关键久期',
  `factor_val_set` mediumtext NOT NULL COMMENT '因子值集,分为1273项,格式{"1":{"date":"2025-01-01","val":0.25},"2":{"date":"2025-01-02","val":0.35},...,"1273":{"date":"2025-12-01","val":0.15}}',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `curve_type`, `key_duration`, `stress_direction`, `duration_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关键久期折现因子表';

-- 分中短负债基点价值DV10表 (TB0006)
CREATE TABLE IF NOT EXISTS `t_dur_liability_dv10_by_duration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `design_type` char(2) NOT NULL COMMENT '设计类型',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否为中短期险种,Y:是,N:否',
  `value_type` char(2) NOT NULL COMMENT '现值类型,01:上升,02:下降,03:净值',
  `term_0` decimal(18,10) DEFAULT 0 COMMENT '0年期限点的DV10现值',
  `term_0_5` decimal(18,10) DEFAULT 0 COMMENT '0.5年期限点的DV10现值',
  `term_1` decimal(18,10) DEFAULT 0 COMMENT '1年期限点的DV10现值',
  `term_2` decimal(18,10) DEFAULT 0 COMMENT '2年期限点的DV10现值',
  `term_3` decimal(18,10) DEFAULT 0 COMMENT '3年期限点的DV10现值',
  `term_4` decimal(18,10) DEFAULT 0 COMMENT '4年期限点的DV10现值',
  `term_5` decimal(18,10) DEFAULT 0 COMMENT '5年期限点的DV10现值',
  `term_6` decimal(18,10) DEFAULT 0 COMMENT '6年期限点的DV10现值',
  `term_7` decimal(18,10) DEFAULT 0 COMMENT '7年期限点的DV10现值',
  `term_8` decimal(18,10) DEFAULT 0 COMMENT '8年期限点的DV10现值',
  `term_10` decimal(18,10) DEFAULT 0 COMMENT '10年期限点的DV10现值',
  `term_12` decimal(18,10) DEFAULT 0 COMMENT '12年期限点的DV10现值',
  `term_15` decimal(18,10) DEFAULT 0 COMMENT '15年期限点的DV10现值',
  `term_20` decimal(18,10) DEFAULT 0 COMMENT '20年期限点的DV10现值',
  `term_25` decimal(18,10) DEFAULT 0 COMMENT '25年期限点的DV10现值',
  `term_30` decimal(18,10) DEFAULT 0 COMMENT '30年期限点的DV10现值',
  `term_35` decimal(18,10) DEFAULT 0 COMMENT '35年期限点的DV10现值',
  `term_40` decimal(18,10) DEFAULT 0 COMMENT '40年期限点的DV10现值',
  `term_45` decimal(18,10) DEFAULT 0 COMMENT '45年期限点的DV10现值',
  `term_50` decimal(18,10) DEFAULT 0 COMMENT '50年期限点的DV10现值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `design_type`, `short_term_flag`, `value_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分中短负债基点价值DV10表';

-- 分账户负债基点价值DV10表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_dur_account_liability_dv10` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_period` varchar(6) NOT NULL COMMENT '账期,格式YYYYMM',
  `cash_flow_type` char(2) NOT NULL COMMENT '现金流类型,01:流入,02:流出',
  `design_type` char(2) NOT NULL COMMENT '设计类型',
  `value_type` char(2) NOT NULL COMMENT '现值类型,01:上升,02:下降,03:净值',
  `term_0` decimal(18,10) DEFAULT 0 COMMENT '0年期限点的DV10现值',
  `term_0_5` decimal(18,10) DEFAULT 0 COMMENT '0.5年期限点的DV10现值',
  `term_1` decimal(18,10) DEFAULT 0 COMMENT '1年期限点的DV10现值',
  `term_2` decimal(18,10) DEFAULT 0 COMMENT '2年期限点的DV10现值',
  `term_3` decimal(18,10) DEFAULT 0 COMMENT '3年期限点的DV10现值',
  `term_4` decimal(18,10) DEFAULT 0 COMMENT '4年期限点的DV10现值',
  `term_5` decimal(18,10) DEFAULT 0 COMMENT '5年期限点的DV10现值',
  `term_6` decimal(18,10) DEFAULT 0 COMMENT '6年期限点的DV10现值',
  `term_7` decimal(18,10) DEFAULT 0 COMMENT '7年期限点的DV10现值',
  `term_8` decimal(18,10) DEFAULT 0 COMMENT '8年期限点的DV10现值',
  `term_10` decimal(18,10) DEFAULT 0 COMMENT '10年期限点的DV10现值',
  `term_12` decimal(18,10) DEFAULT 0 COMMENT '12年期限点的DV10现值',
  `term_15` decimal(18,10) DEFAULT 0 COMMENT '15年期限点的DV10现值',
  `term_20` decimal(18,10) DEFAULT 0 COMMENT '20年期限点的DV10现值',
  `term_25` decimal(18,10) DEFAULT 0 COMMENT '25年期限点的DV10现值',
  `term_30` decimal(18,10) DEFAULT 0 COMMENT '30年期限点的DV10现值',
  `term_35` decimal(18,10) DEFAULT 0 COMMENT '35年期限点的DV10现值',
  `term_40` decimal(18,10) DEFAULT 0 COMMENT '40年期限点的DV10现值',
  `term_45` decimal(18,10) DEFAULT 0 COMMENT '45年期限点的DV10现值',
  `term_50` decimal(18,10) DEFAULT 0 COMMENT '50年期限点的DV10现值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`account_period`, `cash_flow_type`, `design_type`, `value_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分账户负债基点价值DV10表';

-- 最低资本模块表结构

-- 分部门最低资本明细表 (TB0001)
CREATE TABLE IF NOT EXISTS `t_minc_dept_mincap_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `department` varchar(100) NOT NULL COMMENT '统计部门名称',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `account_code` varchar(10) NOT NULL COMMENT '账户编码，对应sys_dict_data表中dict_type=''minc_account''的字典项',
  `amount` decimal(18,10) DEFAULT 0 COMMENT '账户金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `department`, `item_code`, `account_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分部门最低资本明细表';

-- 风险项目金额表 (TB0002)
CREATE TABLE IF NOT EXISTS `t_minc_risk_item_amount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，对应sys_dict_data表中dict_type=''minc_risk_item''的字典项',
  `s05_amount` decimal(30,10) DEFAULT 0 COMMENT 'S05-最低资本表的期末金额',
  `ir05_amount` decimal(30,10) DEFAULT 0 COMMENT 'IR05-寿险业务保险风险表的期末金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='风险项目金额表';
-- 删除唯一索引
ALTER TABLE `t_minc_risk_item_amount` DROP INDEX `idx_unique`;

-- 相关系数表 (TB0004)
CREATE TABLE IF NOT EXISTS `t_minc_correlation_coef` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_code_x` varchar(20) NOT NULL COMMENT '第一个风险项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `item_code_y` varchar(20) NOT NULL COMMENT '第二个风险项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `correlation_value` decimal(5,2) NOT NULL COMMENT '存储相关系数，范围[-1,1]',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_code_x`, `item_code_y`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='相关系数表';

-- 项目定义表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_minc_item_definition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，如：MR001',
  `risk_type` varchar(50) DEFAULT NULL COMMENT '风险类型，如：市场风险、信用风险',
  `capital_item` varchar(100) DEFAULT NULL COMMENT '边际最低资本贡献率表中的项目名称，如：寿险业务保险风险最低资本合计',
  `correlation_item` varchar(50) DEFAULT NULL COMMENT '相关系数表中的项目名称，如：寿险',
  `parent_item_code` varchar(20) DEFAULT NULL COMMENT '指定该项目对应的上层级项目编码',
  `sub_risk_factor_formula` varchar(500) DEFAULT NULL COMMENT '子风险最低资本因子计算公式',
  `company_factor_formula` varchar(500) DEFAULT NULL COMMENT '公司整体最低资本因子计算公式',
  `capital_calculation_formula` varchar(500) DEFAULT NULL COMMENT '最低资本计算公式',
  `status` char(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='项目定义表';

-- 边际最低资本贡献率表 (TB0006)
CREATE TABLE IF NOT EXISTS `t_minc_marginal_capital` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `reinsu_after_amount` decimal(18,2) NOT NULL DEFAULT 0 COMMENT '再保后金额，数值类型，保留2位小数',
  `sub_risk_marginal_factor` decimal(10,4) DEFAULT 0 COMMENT '存储子风险层面边际最低资本贡献因子，百分比格式，如：0.583表示58.3%',
  `company_marginal_factor` decimal(10,4) DEFAULT 0 COMMENT '存储公司层面边际最低资本贡献因子，百分比格式，如：0.583表示58.3%',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='边际最低资本贡献率表';

-- 最低资本明细汇总表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_minc_min_capital_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `account_code` varchar(10) NOT NULL COMMENT '账户编码，对应sys_dict_data表中dict_type=''minc_account''的字典项',
  `amount` decimal(18,10) DEFAULT 0 COMMENT '账户金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_code`, `account_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='最低资本明细汇总表';

-- 市场及信用最低资本表 (TB0008)
CREATE TABLE IF NOT EXISTS `t_minc_min_capital_by_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_code` varchar(20) NOT NULL COMMENT '项目编码，关联项目定义表(t_minc_item_definition)的item_code',
  `account_code` varchar(10) NOT NULL COMMENT '账户编码，对应sys_dict_data表中dict_type=''minc_account''的字典项',
  `amount` decimal(18,10) DEFAULT 0 COMMENT '账户金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_code`, `account_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='市场及信用最低资本表';

-- 利率风险对冲率表 (TB0009)
CREATE TABLE IF NOT EXISTS `t_minc_ir_hedge_ratio` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202306）',
  `item_name` varchar(100) NOT NULL COMMENT '项目名称，如：利率风险资产敏感度、利率风险负债敏感度、利率风险对冲率',
  `account_code` varchar(10) NOT NULL COMMENT '账户编码，对应sys_dict_data表中dict_type=''minc_account''的字典项',
  `sensitivity_rate` decimal(10,4) DEFAULT 0 COMMENT '敏感度或对冲率，百分比格式，如：0.0793表示7.93%',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `item_name`, `account_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='利率风险对冲率表';

-- 成本管理模块表结构

-- 产品属性表 (TB0001)
CREATE TABLE IF NOT EXISTS `t_base_product_attribute` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `insurance_main_type` varchar(50) NOT NULL COMMENT '险种主类，如：长期寿险',
  `insurance_sub_type` varchar(50) NOT NULL COMMENT '险种细类，如：年金险、两全险、附加两全险等',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，如：传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `reg_mid_id` char(1) NOT NULL DEFAULT 'N' COMMENT '报监管中短标识，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,6) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `sub_account` varchar(50) DEFAULT NULL COMMENT '子账户，如：分红账户1、万能账户5等',
  `new_business_flag` char(1) DEFAULT 'Y' COMMENT '新业务标识，Y-是，N-否',
  `remark` varchar(500) DEFAULT NULL COMMENT '产品相关补充说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='产品属性表';

-- 万能平均结算利率表 (TB0002)
CREATE TABLE IF NOT EXISTS `t_base_universal_avg_settlement_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `avg_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点平均结息利率',
  `avg_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末平均结息利率',
  `avg_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末平均结息利率',
  `avg_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末平均结息利率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='万能平均结算利率表';

-- 分红方案表 (TB0003)
CREATE TABLE IF NOT EXISTS `t_base_dividend_fund_cost_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `guaranteed_cost_rate` decimal(10,10) DEFAULT 0 COMMENT '定价保证成本率，以小数形式存储，如0.03表示3%',
  `investment_return_rate` decimal(10,10) DEFAULT 0 COMMENT '投资收益率假设',
  `dividend_ratio` decimal(10,10) DEFAULT 0 COMMENT '分红比例',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '评估时点资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分红方案表';

-- 法定准备金明细表 (TB0004)
CREATE TABLE IF NOT EXISTS `t_base_statutory_reserve_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `valid_policy_count` int(11) DEFAULT 0 COMMENT '有效保单件数',
  `accumulated_premium` decimal(18,10) DEFAULT 0 COMMENT '存量累计规模保费',
  `account_value` decimal(18,10) DEFAULT 0 COMMENT '账户价值',
  `statutory_reserve` decimal(18,10) DEFAULT 0 COMMENT '法定/非单位准备金',
  `guaranteed_rate_reserve` decimal(18,10) DEFAULT 0 COMMENT '保证利率准备金',
  `lapsed_policy_value` decimal(18,10) DEFAULT 0 COMMENT '失效单现价',
  `waiver_reserve` decimal(18,10) DEFAULT 0 COMMENT '豁免责任准备金',
  `unmodeled_reserve` decimal(18,10) DEFAULT 0 COMMENT '未建模准备金',
  `persistence_bonus_reserve` decimal(18,10) DEFAULT 0 COMMENT '持续奖准备金',
  `long_term_unearned` decimal(18,10) DEFAULT 0 COMMENT '长期未到期准备金',
  `short_term_unearned` decimal(18,10) DEFAULT 0 COMMENT '短险未到期准备金',
  `unearned_premium_reserve` decimal(18,10) DEFAULT 0 COMMENT '未到期责任准备金',
  `reported_unpaid` decimal(18,10) DEFAULT 0 COMMENT '已报未决赔款',
  `incurred_unreported` decimal(18,10) DEFAULT 0 COMMENT '未报未决赔款',
  `claim_expense_reserve` decimal(18,10) DEFAULT 0 COMMENT '理赔费用准备金',
  `outstanding_claim_reserve` decimal(18,10) DEFAULT 0 COMMENT '未决赔款准备金',
  `total_statutory_reserve` decimal(18,10) DEFAULT 0 COMMENT '法定准备金合计',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='法定准备金明细表';

-- 会计准备金明细表 (TB0005)
CREATE TABLE IF NOT EXISTS `t_base_accounting_reserve_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `valid_policy_count` int(11) DEFAULT 0 COMMENT '有效保单件数',
  `accumulated_premium` decimal(18,10) DEFAULT 0 COMMENT '存量累计规模保费',
  `account_value` decimal(18,10) DEFAULT 0 COMMENT '账户价值',
  `dividend_provision` decimal(18,10) DEFAULT 0 COMMENT '红利预提',
  `best_estimate` decimal(18,10) DEFAULT 0 COMMENT '最优估计',
  `risk_margin` decimal(18,10) DEFAULT 0 COMMENT '风险边际',
  `residual_margin` decimal(18,10) DEFAULT 0 COMMENT '剩余边际',
  `unmodeled_reserve` decimal(18,10) DEFAULT 0 COMMENT '未建模准备金',
  `waiver_reserve` decimal(18,10) DEFAULT 0 COMMENT '豁免准备金',
  `persistence_bonus_reserve` decimal(18,10) DEFAULT 0 COMMENT '持续奖准备金',
  `long_term_unearned` decimal(18,10) DEFAULT 0 COMMENT '长期险未到期准备金',
  `short_term_unearned` decimal(18,10) DEFAULT 0 COMMENT '短险未到期准备金',
  `unearned_premium_reserve` decimal(18,10) DEFAULT 0 COMMENT '未到期责任准备金',
  `reported_unpaid` decimal(18,10) DEFAULT 0 COMMENT '已报未决赔款',
  `incurred_unreported` decimal(18,10) DEFAULT 0 COMMENT '未报未决赔款',
  `claim_expense_reserve` decimal(18,10) DEFAULT 0 COMMENT '理赔费用准备金',
  `outstanding_claim_reserve` decimal(18,10) DEFAULT 0 COMMENT '未决赔款准备金',
  `total_accounting_reserve` decimal(18,10) DEFAULT 0 COMMENT '会计准备金合计',
  `reinsurance_unearned` decimal(18,10) DEFAULT 0 COMMENT '应收分保未到期责任准备金',
  `reinsurance_reported` decimal(18,10) DEFAULT 0 COMMENT '应收分保已报未决',
  `reinsurance_unreported` decimal(18,10) DEFAULT 0 COMMENT '应收分保未报未决',
  `reinsurance_claim_total` decimal(18,10) DEFAULT 0 COMMENT '应收分保未决合计',
  `reinsurance_total` decimal(18,10) DEFAULT 0 COMMENT '应收分保合计',
  `lapsed_policy_value` decimal(18,10) DEFAULT 0 COMMENT '失效保单现价',
  `fractional_month_dividend` decimal(18,10) DEFAULT 0 COMMENT '零头月红利',
  `unpaid_dividend` decimal(18,10) DEFAULT 0 COMMENT '应付未付红利',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会计准备金明细表';

-- 法定准备金预测表 (TB0006)
CREATE TABLE IF NOT EXISTS `t_base_statutory_reserve_forecast` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t1` decimal(18,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(18,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(18,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`business_type`, `accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='法定准备金预测表';

-- 分产品统计表 (TB0007)
CREATE TABLE IF NOT EXISTS `t_cost_product_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `actuarial_code` varchar(20) NOT NULL COMMENT '产品精算代码，以字母开头',
  `business_code` varchar(20) NOT NULL COMMENT '产品业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品全称',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t0` decimal(18,10) DEFAULT 0 COMMENT '当前法定准备金',
  `statutory_reserve_t1` decimal(18,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(18,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(18,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `business_type`, `accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分产品统计表';

-- 汇总统计表 (TB0008)
CREATE TABLE IF NOT EXISTS `t_cost_summary_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scenario_name` varchar(50) NOT NULL COMMENT '情景名称，如：基本情景、压力情景三',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型，有效业务/新业务',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM（如202506）',
  `term_type` char(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识，L-长期，S-短期',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型，传统险、分红险、万能险、投连险',
  `short_term_flag` char(1) NOT NULL DEFAULT 'N' COMMENT '是否中短，Y-是，N-否',
  `statutory_reserve_t0` decimal(18,10) DEFAULT 0 COMMENT '当前法定准备金',
  `statutory_reserve_t1` decimal(18,10) DEFAULT 0 COMMENT '未来第1年末法定准备金',
  `statutory_reserve_t2` decimal(18,10) DEFAULT 0 COMMENT '未来第2年末法定准备金',
  `statutory_reserve_t3` decimal(18,10) DEFAULT 0 COMMENT '未来第3年末法定准备金',
  `fund_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前资金成本率',
  `fund_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末资金成本率',
  `fund_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末资金成本率',
  `fund_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末资金成本率',
  `guaranteed_cost_rate_t0` decimal(10,10) DEFAULT 0 COMMENT '当前保证成本率',
  `guaranteed_cost_rate_t1` decimal(10,10) DEFAULT 0 COMMENT '未来第1年末保证成本率',
  `guaranteed_cost_rate_t2` decimal(10,10) DEFAULT 0 COMMENT '未来第2年末保证成本率',
  `guaranteed_cost_rate_t3` decimal(10,10) DEFAULT 0 COMMENT '未来第3年末保证成本率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`scenario_name`, `business_type`, `accounting_period`, `term_type`, `design_type`, `short_term_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='汇总统计表';

-- 成本管理模块字典数据
-- 字典类型定义
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES
('成本管理业务类型', 'cost_business_type', '0', 'admin', sysdate(), '成本管理模块业务类型字典'),
('成本管理长短期标识', 'cost_term_type', '0', 'admin', sysdate(), '成本管理模块长短期标识字典'),
('成本管理设计类型', 'cost_design_type', '0', 'admin', sysdate(), '成本管理模块产品设计类型字典'),
('成本管理中短期标识', 'cost_short_term_flag', '0', 'admin', sysdate(), '成本管理模块是否中短期产品字典'),
('成本管理情景名称', 'cost_scenario_name', '0', 'admin', sysdate(), '成本管理模块情景名称字典');

-- 业务类型字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '有效业务', 'EXISTING', 'cost_business_type', '', 'default', 'Y', '0', 'admin', sysdate(), '成本管理业务类型'),
(2, '新业务', 'NEW', 'cost_business_type', '', 'default', 'N', '0', 'admin', sysdate(), '成本管理业务类型');

-- 长短期标识字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '长期', 'L', 'cost_term_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '长短期标识'),
(2, '短期', 'S', 'cost_term_type', '', 'info', 'N', '0', 'admin', sysdate(), '长短期标识');

-- 设计类型字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '传统险', '传统险', 'cost_design_type', '', 'default', 'Y', '0', 'admin', sysdate(), '产品设计类型'),
(2, '分红险', '分红险', 'cost_design_type', '', 'primary', 'N', '0', 'admin', sysdate(), '产品设计类型'),
(3, '万能险', '万能险', 'cost_design_type', '', 'success', 'N', '0', 'admin', sysdate(), '产品设计类型'),
(4, '投连险', '投连险', 'cost_design_type', '', 'warning', 'N', '0', 'admin', sysdate(), '产品设计类型');

-- 是否中短字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '否', 'N', 'cost_short_term_flag', '', 'success', 'Y', '0', 'admin', sysdate(), '是否中短期产品'),
(2, '是', 'Y', 'cost_short_term_flag', '', 'danger', 'N', '0', 'admin', sysdate(), '是否中短期产品');

-- 情景名称字典
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '基本情景', '基本情景', 'cost_scenario_name', '', 'primary', 'Y', '0', 'admin', sysdate(), '成本管理情景名称'),
(2, '压力情景一', '压力情景一', 'cost_scenario_name', '', 'warning', 'N', '0', 'admin', sysdate(), '成本管理情景名称'),
(3, '压力情景二', '压力情景二', 'cost_scenario_name', '', 'warning', 'N', '0', 'admin', sysdate(), '成本管理情景名称'),
(4, '压力情景三', '压力情景三', 'cost_scenario_name', '', 'danger', 'N', '0', 'admin', sysdate(), '成本管理情景名称');
