-- =============================================
-- ADUR月度折现曲线表含价差DDL
-- 表名：t_adur_monthly_discount_curve_with_spread
-- 对应表：TB0006 月度折现曲线表含价差
-- =============================================

DROP TABLE IF EXISTS `t_adur_monthly_discount_curve_with_spread`;

CREATE TABLE `t_adur_monthly_discount_curve_with_spread` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_period` varchar(6) NOT NULL COMMENT '账期',
  `duration_type` varchar(20) NOT NULL COMMENT '久期类型',
  `basis_point_type` varchar(20) NOT NULL COMMENT '基点类型',
  `date_type` varchar(20) NOT NULL COMMENT '日期类型',
  `date` date NOT NULL COMMENT '日期',
  `spread_type` varchar(20) DEFAULT NULL COMMENT '价差类型',
  `spread` decimal(10,6) DEFAULT 0 COMMENT '价差',
  `curve_sub_category` varchar(10) DEFAULT NULL COMMENT '曲线细分类',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `curve_id` varchar(10) NOT NULL COMMENT '折现曲线标识',
  `term_0` decimal(10,6) DEFAULT 0 COMMENT '期限0',
  `term_1` decimal(10,6) DEFAULT 0 COMMENT '期限1',
  `term_2` decimal(10,6) DEFAULT 0 COMMENT '期限2',
  `term_3` decimal(10,6) DEFAULT 0 COMMENT '期限3',
  `term_4` decimal(10,6) DEFAULT 0 COMMENT '期限4',
  `term_5` decimal(10,6) DEFAULT 0 COMMENT '期限5',
  `term_6` decimal(10,6) DEFAULT 0 COMMENT '期限6',
  `term_7` decimal(10,6) DEFAULT 0 COMMENT '期限7',
  `term_8` decimal(10,6) DEFAULT 0 COMMENT '期限8',
  `term_9` decimal(10,6) DEFAULT 0 COMMENT '期限9',
  `term_10` decimal(10,6) DEFAULT 0 COMMENT '期限10',
  -- 注意：此表包含term_0到term_600共601个期限字段，此处仅显示前11个
  -- 完整字段列表：term_0, term_1, term_2, ..., term_600
  `term_600` decimal(10,6) DEFAULT 0 COMMENT '期限600',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_curve` (`account_period`,`asset_number`,`date_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='ADUR月度折现曲线表含价差';

-- =============================================
-- 索引说明：
-- 1. 主键：id
-- 2. 唯一索引：account_period + asset_number + date_type
-- 3. 该表包含601个期限字段（term_0到term_600）
-- 4. 月度折现曲线包含价差信息，用于折现因子计算
-- 5. 支持久期类型、基点类型、价差类型等多维度管理
-- 6. 支持资产级别的折现曲线管理和计算
-- =============================================
