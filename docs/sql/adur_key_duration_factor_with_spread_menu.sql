-- TB0009: ADUR关键久期折现因子表含价差菜单 SQL

-- 获取ADUR模块菜单ID
SELECT @adurMenuId := menu_id FROM sys_menu WHERE menu_name = 'ADUR模块' AND parent_id = 2548;

-- 主菜单 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ADUR关键久期折现因子含价差', @adurMenuId, 9, 'adurKeyDurationFactorWithSpread', 'adur/key/duration/factor/with/spread/index', '', 1, 0, 'C', '0', '0', 'adur:key:duration:factor:with:spread:list', 'tree-table', 'admin', SYSDATE(), '', NULL, 'ADUR关键久期折现因子表含价差菜单');

-- 按钮父菜单ID
SET @parentId = LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name,parent_id,order_num,path,component,query,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('ADUR关键久期折现因子含价差查询', @parentId, 1, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:query', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现因子含价差新增', @parentId, 2, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:add', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现因子含价差修改', @parentId, 3, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:edit', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现因子含价差删除', @parentId, 4, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:remove', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现因子含价差导出', @parentId, 5, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:export', '#', 'admin', SYSDATE(), '', NULL, ''),
('ADUR关键久期折现因子含价差导入', @parentId, 6, '#', '', '', 1, 0, 'F', '0', '0', 'adur:key:duration:factor:with:spread:import', '#', 'admin', SYSDATE(), '', NULL, '');
