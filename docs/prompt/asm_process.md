任务1：参数1="asm_program_design.md",参数2="2.3"，参数三="asm_program_design.sql",将以上参数带入/prompt/1.generate_ddl.md这个文件中，执行这个文件里面的任务

任务2：参数1="asm_program_design.sql",参数2="asm_program_dict.sql",将以上参数带入/prompt/2.generate_dict_data.md这个文件中，执行这个文件里面的任务

任务3：参数1="asm_program_design.sql",参数2="asm_program_dict.sql",参数3=“TB0018”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务4：参数1="asm_program_design.sql",参数2="asm_program_dict.sql",参数3=“TB0019”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务5：参数1="asm_program_design.sql",参数2="4.1.3.3 计算资产规模与偿付能力表(UC0019)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

