任务1：参数1="cft_program_design.md",参数2="2.3"，参数三="cft_program_design.sql",将以上参数带入/prompt/1.generate_ddl.md这个文件中，执行这个文件里面的任务

任务2：参数1="cft_program_design.sql",参数2="cft_program_dict.sql",将以上参数带入/prompt/2.generate_dict_data.md这个文件中，执行这个文件里面的任务

任务3：参数1="cft_program_design.sql",参数2="cft_program_dict.sql",参数3="TB0020"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务4：参数1="cft_program_design.sql",参数2="cft_program_dict.sql",参数3="TB0021"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务5：参数1="cft_program_design.sql",参数2="cft_program_dict.sql",参数3="TB0022"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务6：参数1="cft_program_design.sql",参数2="cft_program_dict.sql",参数3="TB0023"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务7：参数1="cft_program_design.sql",参数2="4.1.3.3 计算ALMCF实际发生数本年累计表(UC0022)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务8：参数1="cft_program_design.sql",参数2="4.1.3.4 计算ALMCF实际发生数本季度累计表(UC0023)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务


