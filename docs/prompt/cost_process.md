任务1：参数1="cost_program_design.md",参数2="2.3"，参数三="cost_program_design.sql",将以上参数带入/prompt/1.generate_ddl.md这个文件中，执行这个文件里面的任务

任务2：参数1="cost_program_design.sql",参数2="cost_program_dict.sql",将以上参数带入/prompt/2.generate_dict_data.md这个文件中，执行这个文件里面的任务

任务3：参数1="cost_program_design.sql",参数2="cost_program_dict.sql",参数3=“TB0013”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务4：参数1="cost_program_design.sql",参数2="cost_program_dict.sql",参数3=“TB0014”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务5：参数1="cost_program_design.sql",参数2="cost_program_dict.sql",参数3=“TB0015”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务6：参数1="cost_program_design.sql",参数2="cost_program_dict.sql",参数3=“TB0016”，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务7：计算分产品保费收入job任务
- 基于${cost_program_design.md}文档${4.1.3.2.3.3 }章节需求生成功能代码
- 代码生成到${job}模块下
- 生成代码过程要认真、细致，不能出现错误
- 如代码已存在，再确认下已有代码是否符合需求
- 使用其他的类或者实体类，需要先检查一下是否存在之前的方法或者字段，不要随意造新方法和字段，优先使用已有的方法和字段，不存在再新建

任务8：计算中短存续期产品利差job任务
- 基于${cost_program_design.md}文档${4.1.3.2.3.4 }章节需求生成功能代码
- 代码生成到${job}模块下
- 生成代码过程要认真、细致，不能出现错误
- 如代码已存在，再确认下已有代码是否符合需求
- 使用其他的类或者实体类，需要先检查一下是否存在之前的方法或者字段，不要随意造新方法和字段，优先使用已有的方法和字段，不存在再新建


任务9：计算中短存续期产品利差job任务
- 基于${cost_program_design.md}文档${4.1.3.2.3.2 计算分账户有效成本率(UC0012)}章节需求生成功能代码
- 代码生成到${job}模块下
- 生成代码过程要认真、细致，不能出现错误
- 如代码已存在，再确认下已有代码是否符合需求
- 使用其他的类或者实体类，需要先检查一下是否存在之前的方法或者字段，不要随意造新方法和字段，优先使用已有的方法和字段，不存在再新建
