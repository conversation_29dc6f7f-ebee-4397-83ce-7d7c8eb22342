# ADUR资产久期管理模块开发任务流程

## 第一阶段：基础架构搭建

任务1：参数1="adur_program_design.md",参数2="2.3"，参数三="adur_program_design_xxx.sql",将以上参数带入/prompt/1.generate_ddl.md这个文件中，执行这个文件里面的任务

任务2：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",将以上参数带入/prompt/2.generate_dict_data.md这个文件中，执行这个文件里面的任务

## 第二阶段：CRUD功能开发

任务3：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0001"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务4：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0002"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务5：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0003"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务6：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0004"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务7：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0005"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务8：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0006"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务9：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0007"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务
 todo 
~~任务10：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0008"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务~~

任务11：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0009"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

任务12：参数1="adur_program_design.sql",参数2="adur_program_dict.sql",参数3="TB0010"，将以上参数带入/prompt/3.generate_crud_function.md规范文件，执行这个文件里面的任务

## 第三阶段：业务逻辑任务开发

任务13：参数1="adur_program_design.sql",参数2="4.1.3.3 生成久期资产明细数据(UC0003)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务14：参数1="adur_program_design.sql",参数2="4.1.3.4 计算年度折现曲线(UC0004)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务15：参数1="adur_program_design.sql",参数2="4.1.3.5 计算月度折现曲线不含价差(UC0005)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务16：参数1="adur_program_design.sql",参数2="4.1.3.6 计算月度折现曲线含价差(UC0006)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务17：参数1="adur_program_design.sql",参数2="4.1.3.7 计算月度折现因子含价差(UC0007)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务18：参数1="adur_program_design.sql",参数2="4.1.3.8 计算关键久期折现曲线含价差(UC0008)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务19：参数1="adur_program_design.sql",参数2="4.1.3.9 计算关键久期折现因子含价差(UC0009)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务20：参数1="adur_program_design.sql",参数2="4.1.3.10 计算久期指标(UC0010)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

任务21：参数1="adur_program_design.sql",参数2="4.1.3.11 计算久期资产汇总(UC0011)" 将以上参数带入/prompt/4.generate_job.md文档，执行这个文件里面的任务

## 第四阶段：菜单权限配置

任务22：参数1="adur_program_design.sql",参数2="adur_menu.sql",将以上参数带入/prompt/5.generate_menu.md文档，执行这个文件里面的任务

## 任务执行说明

### 执行顺序
1. **必须按顺序执行**：任务1-2为基础架构，任务3-12为CRUD功能，任务13-21为业务逻辑，任务22为菜单配置
2. **依赖关系**：后续任务依赖前面任务的输出结果
3. **测试验证**：每个阶段完成后建议进行功能测试

### 关键节点
- **任务1完成后**：数据库表结构创建完成
- **任务12完成后**：所有表的CRUD功能开发完成
- **任务21完成后**：所有业务计算逻辑开发完成
- **任务22完成后**：系统菜单和权限配置完成

### 注意事项
1. UC0007和UC0008的计算逻辑较为复杂，需要特别关注折现因子和关键久期的计算公式
2. 所有计算任务都需要考虑数据质量控制和异常处理
3. 批量数据处理需要考虑性能优化


