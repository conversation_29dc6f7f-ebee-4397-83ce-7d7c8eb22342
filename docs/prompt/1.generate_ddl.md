# 基于设计文档创建表DDL
## 数据库：
- Mysql8.0
- CHARSET=utf8

## 需求描述：
- 请基于docs模块下“design/${参数1}”文档中的 ${参数2}章节生成数据表的DDL
- 以${参数3}文件存储到docs模块下的sql目录下，如果目录和init.sql文件已存在不需要创建
- 所有表的DDL都写到${参数3}中
- 如果表名相同，不要覆盖 ${参数3}文件中已有的DDL
- 金额字段统一使用decimal类型，长度为28位，小数位为10位
- 不要漏掉字段，全部字段都要包含
## 添加公共字段：
- 针对所有表添加以下字段信息
| 字段名      | 数据类型 | 长度  | 允许空 | 是否主键 | 默认值                | 说明                 |
| ----------- | -------- | ----- | ------ | -------- | --------------------- | -------------------- |
| id          | bigint   | 20    | 否     | 是       | 无                    | 主键                 |
| create_time | datetime |       | 否     | 否       | current_timestamp     | 创建时间             |
| create_by | verchar    | 64    | 是     | 否       |                      | 创建者             |
| update_time | datetime |       | 否     | 否       | current_timestamp     | 更新时间             |
| update_by | verchar    | 64    | 是     | 否       |                      | 更新者             |
| is_del      | tinyint  |   1   | 否     | 否       | 0                     | 是否删除，0:否，1:是 |