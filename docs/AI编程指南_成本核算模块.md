# AI编程指南 - 成本核算模块开发

## 文档信息

| 项目名称 | 成本核算模块AI编程指南 |
| -------- | -------------------- |
| 文档版本 | V2.0                 |
| 创建日期 | 2025-01-27           |
| 状态     | 已确认               |

## 1. 插件安装

### 1.1 开发环境要求
- **操作系统**: Windows 11
- **JDK版本**: JDK8
- **IDE**: IntelliJ IDEA 或 Eclipse
- **Node.js**: 14.x 或以上版本
- **数据库**: MySQL 8.0

### 1.2 AI编程插件安装

#### 1.2.1 Augment Code插件
```bash
# 安装Augment Code插件（推荐）
# 1. 访问 https://augmentcode.com
# 2. 下载并安装插件
# 3. 配置API密钥
```

#### 1.2.2 前端开发插件
```json
// package.json 中的关键插件
{
  "dependencies": {
    "vue": "2.6.12",
    "element-ui": "2.15.8",
    "axios": "0.24.0",
    "js-beautify": "1.13.0",
    "form-gen-parser": "^1.0.3"
  },
  "devDependencies": {
    "@vue/cli-service": "^4.5.0",
    "eslint": "^6.7.2",
    "vue-template-compiler": "2.6.12"
  }
}
```

#### 1.2.3 后端开发插件
```xml
<!-- pom.xml 中的关键依赖 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>2.7.18</version>
    </dependency>
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.1</version>
    </dependency>
</dependencies>
```

### 1.3 插件配置
```javascript
// web/src/utils/pluginsConfig.js
const CDN = 'https://lib.baomitu.com/';

export default {
  beautifierUrl: 'https://lib.baomitu.com/js-beautify/1.13.5/beautifier.min.js',
  tinymceUrl: 'https://lib.baomitu.com/tinymce/5.7.0/tinymce.min.js'
};
```

## 2. 模版工程

### 2.1 项目结构
```
alm/
├── app/                    # 后端应用模块
│   ├── src/main/java/
│   │   └── com/xl/alm/app/
│   │       ├── controller/ # 控制器层
│   │       ├── dto/        # 数据传输对象
│   │       ├── entity/     # 数据库实体
│   │       ├── mapper/     # 数据访问层
│   │       ├── query/      # 查询参数对象
│   │       ├── service/    # 业务逻辑接口
│   │       └── util/       # 工具类
│   └── src/main/resources/
│       └── mapper/         # MyBatis映射文件
├── web/                    # 前端Vue工程
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # 公共组件
│   │   ├── utils/         # 工具函数
│   │   └── views/         # 页面组件
│   └── package.json
├── job/                    # 定时任务模块
│   └── src/main/java/
│       └── com/xl/alm/job/
└── docs/                   # 文档目录
    ├── design/            # 设计文档
    ├── prompt/            # 提示词模板
    ├── rule/              # 开发规范
    └── sql/               # SQL脚本
```

### 2.2 技术栈配置

#### 2.2.1 后端技术栈
- **框架**: Spring Boot 2.7.18
- **JDK**: OpenJDK 8
- **ORM**: MyBatis Plus 3.5.1
- **数据库**: MySQL 8.0
- **权限**: Spring Security
- **任务调度**: PowerJob

#### 2.2.2 前端技术栈
- **框架**: Vue 2.6.12
- **UI组件**: Element UI 2.15.8
- **HTTP客户端**: Axios 0.24.0
- **构建工具**: Vue CLI 4.5.0
- **代码规范**: ESLint + Prettier

### 2.3 模版文件结构

本节介绍如何创建和使用代码模版，帮助开发者快速生成标准化的代码结构。

#### 2.3.1 模版文件组织结构

```
docs/template/
├── backend/                    # 后端代码模版
│   ├── Controller.java.tpl    # Controller模版
│   ├── DTO.java.tpl           # DTO模版
│   ├── Entity.java.tpl        # Entity模版
│   ├── Mapper.java.tpl        # Mapper接口模版
│   ├── Mapper.xml.tpl         # MyBatis XML模版
│   ├── Query.java.tpl         # Query参数模版
│   ├── Service.java.tpl       # Service接口模版
│   └── ServiceImpl.java.tpl   # Service实现模版
├── frontend/                   # 前端代码模版
│   ├── index.vue.tpl          # Vue页面模版
│   ├── api.js.tpl             # API接口模版
│   └── form.vue.tpl           # 表单组件模版
└── sql/                        # SQL脚本模版
    ├── ddl.sql.tpl            # DDL建表模版
    ├── dict.sql.tpl           # 字典数据模版
    └── menu.sql.tpl           # 菜单数据模版
```

#### 2.3.2 如何创建模版文件

##### 步骤1: 创建模版目录
```bash
# 在项目根目录下创建模版目录
mkdir -p docs/template/backend
mkdir -p docs/template/frontend
mkdir -p docs/template/sql
```

##### 步骤2: 创建后端模版文件

**Controller模版 (docs/template/backend/Controller.java.tpl)**
```java
package com.xl.alm.app.controller.{{module}};

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.xl.alm.app.dto.{{module}}.{{Entity}}DTO;
import com.xl.alm.app.query.{{module}}.{{Entity}}Query;
import com.xl.alm.app.service.{{module}}.{{Entity}}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * {{tableComment}}Controller
 *
 * <AUTHOR>
 * @date {{datetime}}
 */
@RestController
@RequestMapping("/{{module}}/{{requestMapping}}")
@PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:list')")
public class {{Entity}}Controller extends BaseController {

    @Autowired
    private {{Entity}}Service {{entityName}}Service;

    /**
     * 查询{{tableComment}}列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:list')")
    public Result<TableDataInfo> list({{Entity}}Query query) {
        startPage();
        List<{{Entity}}DTO> list = {{entityName}}Service.selectList(query);
        return Result.success(getDataTable(list));
    }

    /**
     * 新增{{tableComment}}
     */
    @Log(title = "{{tableComment}}", businessType = BusinessType.INSERT)
    @PostMapping
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:add')")
    public Result<Integer> add(@RequestBody {{Entity}}DTO {{entityName}}) {
        return toAjax({{entityName}}Service.insert({{entityName}}));
    }

    /**
     * 修改{{tableComment}}
     */
    @Log(title = "{{tableComment}}", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:edit')")
    public Result<Integer> edit(@RequestBody {{Entity}}DTO {{entityName}}) {
        return toAjax({{entityName}}Service.update({{entityName}}));
    }

    /**
     * 删除{{tableComment}}
     */
    @Log(title = "{{tableComment}}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:remove')")
    public Result<Integer> remove(@PathVariable Long[] ids) {
        return toAjax({{entityName}}Service.deleteByIds(ids));
    }

    /**
     * 导出{{tableComment}}
     */
    @Log(title = "{{tableComment}}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:export')")
    public void export(HttpServletResponse response, {{Entity}}Query query) {
        List<{{Entity}}DTO> list = {{entityName}}Service.selectList(query);
        com.xl.alm.app.util.ExcelUtil.exportExcel(list, "{{tableComment}}数据", {{Entity}}DTO.class, response);
    }

    /**
     * 导入{{tableComment}}
     */
    @Log(title = "{{tableComment}}", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermi('{{module}}:{{requestMapping}}:import')")
    public Result<String> importData(MultipartFile file) throws Exception {
        List<{{Entity}}DTO> list = com.xl.alm.app.util.ExcelUtil.importExcel(file.getInputStream(), {{Entity}}DTO.class);
        String message = {{entityName}}Service.importData(list);
        return Result.success(message);
    }
}
```

**Entity模版 (docs/template/backend/Entity.java.tpl)**
```java
package com.xl.alm.app.entity.{{module}};

import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * {{tableComment}}对象 {{tableName}}
 *
 * <AUTHOR>
 * @date {{datetime}}
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("{{tableName}}")
public class {{Entity}} extends BaseEntity {

    private static final long serialVersionUID = 1L;

    {{#columns}}
    /** {{columnComment}} */
    private {{javaType}} {{javaField}};

    {{/columns}}
}
```

##### 步骤3: 创建前端模版文件

**Vue页面模版 (docs/template/frontend/index.vue.tpl)**
```vue
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      {{#queryColumns}}
      <el-form-item label="{{columnComment}}" prop="{{javaField}}">
        {{#if isSelect}}
        <el-select v-model="queryParams.{{javaField}}" placeholder="请选择{{columnComment}}" clearable style="width: 200px">
          <el-option
            v-for="dict in dict.type.{{dictType}}"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        {{else}}
        <el-input
          v-model="queryParams.{{javaField}}"
          placeholder="请输入{{columnComment}}"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
        {{/if}}
      </el-form-item>
      {{/queryColumns}}
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['{{module}}:{{requestMapping}}:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['{{module}}:{{requestMapping}}:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['{{module}}:{{requestMapping}}:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['{{module}}:{{requestMapping}}:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['{{module}}:{{requestMapping}}:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="{{entityName}}List" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      {{#columns}}
      <el-table-column label="{{columnComment}}" align="center" prop="{{javaField}}" {{#if showOverflowTooltip}}show-overflow-tooltip{{/if}} />
      {{/columns}}
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['{{module}}:{{requestMapping}}:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['{{module}}:{{requestMapping}}:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { list{{Entity}}, get{{Entity}}, del{{Entity}}, add{{Entity}}, update{{Entity}} } from "@/api/{{module}}/{{entityName}}";

export default {
  name: "{{Entity}}",
  dicts: [{{#dictTypes}}'{{.}}'{{#unless @last}}, {{/unless}}{{/dictTypes}}],
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        {{#queryColumns}}
        {{javaField}}: null,
        {{/queryColumns}}
      },
      // 数据列表
      {{entityName}}List: [],
      // 总条数
      total: 0,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 加载层信息
      loading: true
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询{{tableComment}}列表 */
    getList() {
      this.loading = true;
      list{{Entity}}(this.queryParams).then(response => {
        this.{{entityName}}List = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('{{module}}/{{requestMapping}}/export', {
        ...this.queryParams
      }, `{{entityName}}_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
```

#### 2.3.3 如何使用模版文件

##### 方法1: 手动替换变量
1. **复制模版文件**
   ```bash
   cp docs/template/backend/Controller.java.tpl app/src/main/java/com/xl/alm/app/controller/cost/ProductController.java
   ```

2. **替换模版变量**
   - `{{module}}` → `cost`
   - `{{Entity}}` → `Product`
   - `{{entityName}}` → `product`
   - `{{tableComment}}` → `产品信息`
   - `{{requestMapping}}` → `product`

##### 方法2: 使用脚本自动生成
创建模版生成脚本 `scripts/generate-from-template.sh`：
```bash
#!/bin/bash

# 参数定义
MODULE=$1
ENTITY=$2
TABLE_COMMENT=$3
REQUEST_MAPPING=$4

# 变量转换
ENTITY_NAME=$(echo $ENTITY | sed 's/\(.\)/\L\1/')

# 生成Controller
sed "s/{{module}}/$MODULE/g; s/{{Entity}}/$ENTITY/g; s/{{entityName}}/$ENTITY_NAME/g; s/{{tableComment}}/$TABLE_COMMENT/g; s/{{requestMapping}}/$REQUEST_MAPPING/g" \
    docs/template/backend/Controller.java.tpl > \
    app/src/main/java/com/xl/alm/app/controller/$MODULE/${ENTITY}Controller.java

echo "Controller生成完成: ${ENTITY}Controller.java"
```

使用脚本：
```bash
./scripts/generate-from-template.sh cost Product 产品信息 product
```

##### 方法3: 集成到AI提示词中
在提示词模版中引用模版文件：
```markdown
请基于以下模版生成代码：
- Controller模版：docs/template/backend/Controller.java.tpl
- Entity模版：docs/template/backend/Entity.java.tpl
- Vue页面模版：docs/template/frontend/index.vue.tpl

替换以下变量：
- {{module}}: cost
- {{Entity}}: ProductPremiumIncome
- {{tableComment}}: 分产品保费收入
```

#### 2.3.4 模版变量说明

| 变量名 | 说明 | 示例 |
|--------|------|------|
| {{module}} | 模块名（小写） | cost |
| {{Entity}} | 实体类名（首字母大写） | ProductPremiumIncome |
| {{entityName}} | 实体变量名（首字母小写） | productPremiumIncome |
| {{tableComment}} | 表注释 | 分产品保费收入 |
| {{tableName}} | 表名 | t_cost_product_premium_income |
| {{requestMapping}} | 请求路径 | product/premium/income |
| {{author}} | 作者 | system |
| {{datetime}} | 生成时间 | 2025-01-27 |

#### 2.3.5 模版维护和更新

##### 版本控制
```bash
# 将模版文件纳入版本控制
git add docs/template/
git commit -m "添加代码生成模版"
```

##### 模版更新流程
1. **修改模版文件**：根据项目规范更新模版
2. **测试验证**：使用模版生成测试代码
3. **文档更新**：更新使用说明
4. **团队通知**：通知团队成员模版变更

##### 模版质量检查
```bash
# 创建模版检查脚本
#!/bin/bash
echo "检查模版文件完整性..."

# 检查必要的模版文件
templates=(
    "docs/template/backend/Controller.java.tpl"
    "docs/template/backend/Entity.java.tpl"
    "docs/template/frontend/index.vue.tpl"
)

for template in "${templates[@]}"; do
    if [ -f "$template" ]; then
        echo "✓ $template 存在"
    else
        echo "✗ $template 缺失"
    fi
done
```

## 3. 需求设计文档编码

### 3.1 设计文档结构
成本核算模块的需求设计文档遵循标准化结构：

```
docs/design/cost_program_design.md
├── 1. 需求概述
├── 2. 业务架构
│   ├── 2.1 业务模块关系图
│   ├── 2.2 模块列表
│   └── 2.3 数据模型
├── 3. 业务概念与术语
└── 4. 功能需求
    └── 4.1 负债成本率模块
        ├── 4.1.1 功能概要
        ├── 4.1.2 业务总流程
        └── 4.1.3 用例描述
```

### 3.2 表结构设计编码

#### 3.2.1 表名编码规范
```
t_{模块缩写}_{业务名称}
例如：
- t_cost_product_statistics      # 分产品统计表
- t_cost_business_type_summary   # 分业务类型汇总表
- t_cost_account_summary         # 分账户汇总表
```

#### 3.2.2 字段编码规范
```sql
-- 标准字段定义
CREATE TABLE `t_cost_example` (
  -- 系统字段
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',

  -- 业务字段
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式：YYYYMM',
  `actuarial_code` varchar(20) NOT NULL COMMENT '精算代码',
  `business_code` varchar(20) DEFAULT NULL COMMENT '业务代码',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `design_type` varchar(50) NOT NULL COMMENT '设计类型',

  -- 金额字段
  `premium_amount` decimal(28,10) DEFAULT 0 COMMENT '保费金额',
  `reserve_amount` decimal(28,10) DEFAULT 0 COMMENT '准备金金额',

  -- 比率字段
  `cost_rate` decimal(10,6) DEFAULT 0 COMMENT '成本率',
  `yield_rate` decimal(6,10) DEFAULT 0 COMMENT '收益率',

  -- 标识字段
  `short_term_flag` char(1) DEFAULT 'N' COMMENT '是否中短期，Y-是，N-否',
  `new_business_flag` char(1) DEFAULT 'Y' COMMENT '新业务标识，Y-是，N-否',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_code` (`accounting_period`, `actuarial_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='示例表';
```

### 3.3 业务流程编码

#### 3.3.1 用例编码规范
```
UC{序号4位}: {用例名称}
例如：
- UC0001: 导入产品属性数据
- UC0008: 计算分产品统计数据
- UC0011: 计算分产品有效成本率
```

#### 3.3.2 表编号编码规范
```
TB{序号4位}: {表中文名}
例如：
- TB0001: 产品属性表
- TB0007: 分产品统计表
- TB0010: 分产品有效成本率表
```

### 3.4 计算逻辑编码

#### 3.4.1 资金成本率计算逻辑
```
(1) 筛选产品数据
   - 设计类型：传统险、分红险、万能险
   - 长短期标识：长期或L

(2) 确定业务类型
   - 有效业务：长期产品
   - 新业务：长期产品 + 新业务标识为Y

(3) 计算法定准备金
   - T0：有效业务取明细表，新业务为0
   - T1-T3：取预测表对应字段

(4) 计算资金成本率
   - 传统险：取定价保证成本率
   - 分红险：取分红方案表对应字段
   - 万能险：取平均结算利率表对应字段
```

#### 3.4.2 有效成本率计算逻辑
```
(1) 构建现金流值集
   - 第0个元素：账面价值（负值）
   - 第1-1272个元素：业务净现金流

(2) 计算有效成本率
   - 使用IRR方法计算
   - 现金流为0时，有效成本率=0%
```

## 4. 提示词模版介绍

### 4.1 提示词模版体系
成本核算模块提供了完整的提示词模版体系，位于 `docs/prompt/` 目录：

```
docs/prompt/
├── 1.generate_ddl.md           # DDL生成模版
├── 2.generate_dict_data.md     # 字典数据生成模版
├── 3.generate_crud_function.md # CRUD功能生成模版
├── 4.generate_job.md           # Job任务生成模版
└── cost_process.md             # 成本核算流程模版
```

### 4.2 DDL生成模版 (1.generate_ddl.md)

#### 4.2.1 模版参数
```markdown
参数1: 设计文档名称 (如: cost_program_design.md)
参数2: 章节号 (如: 2.3)
参数3: 输出文件名 (如: cost_program_design.sql)
```

#### 4.2.2 模版内容要点
```markdown
## 数据库：
- Mysql8.0
- CHARSET=utf8

## 需求描述：
- 基于设计文档生成DDL
- 金额字段统一使用decimal(28,10)
- 添加标准公共字段
- 不要漏掉任何字段

## 添加公共字段：
| 字段名      | 数据类型 | 长度  | 允许空 | 是否主键 | 默认值                |
| ----------- | -------- | ----- | ------ | -------- | --------------------- |
| id          | bigint   | 20    | 否     | 是       | 无                    |
| create_time | datetime |       | 否     | 否       | current_timestamp     |
| create_by   | varchar  | 64    | 是     | 否       |                       |
| update_time | datetime |       | 否     | 否       | current_timestamp     |
| update_by   | varchar  | 64    | 是     | 否       |                       |
| is_del      | tinyint  | 1     | 否     | 否       | 0                     |
```

### 4.3 字典数据生成模版 (2.generate_dict_data.md)

#### 4.3.1 模版参数
```markdown
参数1: DDL文件名 (如: cost_program_design.sql)
参数2: 输出文件名 (如: cost_program_dict.sql)
```

#### 4.3.2 字典表结构
```sql
-- sys_dict_type表结构
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  -- 其他字段...
);

-- sys_dict_data表结构
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  -- 其他字段...
);
```

### 4.4 CRUD功能生成模版 (3.generate_crud_function.md)

#### 4.4.1 模版参数
```markdown
参数1: DDL文件名 (如: cost_program_design.sql)
参数2: 字典文件名 (如: cost_program_dict.sql)
参数3: 表编号 (如: TB0013)
```

#### 4.4.2 生成功能清单
```markdown
## 必须实现的功能：
- 列表查询、分页、条件筛选
- 新增记录、编辑记录、删除记录、批量删除
- 数据导入、数据导出、模板下载（模板下载链接放在导入弹窗内）
- Excel模版需要中文表头，字段必须是全部的字段，不要漏掉任何业务字段

## 接口规范：
- 前后端接口地址和数据格式必须完全一致
- 导入和导出功能必须使用POST请求方式

## 数据校验：
- 必须根据SQL定义的字段类型/长度在DTO和Query参数中添加对应校验注解
- 必须覆盖查询/新增/编辑所有场景的输入校验
```

#### 4.4.3 后端实现规范（app模块）
```markdown
## 代码位置：
- 所有后端代码必须放在app模块下

## 代码结构：
必须生成以下Java类：
- Controller：处理HTTP请求
- DTO：数据传输对象
- Entity：数据库实体
- Mapper：数据库操作接口和XML
- Query：查询参数对象
- Service：业务逻辑接口
- ServiceImpl：业务逻辑实现

## 权限控制：
- @PreAuthorize注解格式：`@ss.hasPermi('模块名:操作:子操作')`
- 模块名必须与@RequestMapping路径首单词严格对应（全小写）
- 示例：`@PreAuthorize("@ss.hasPermi('cost:product:premium:income:list')")`

## 数据库操作：
- Mapper.xml的insert语句不得包含create_time/update_time/is_del字段

## 数据对象转换：
- 查询场景：Controller和Service层只能接受Query对象
- 非查询场景：Controller和Service层只能接受DTO对象
- Service层与Mapper层之间必须进行数据对象转换：
  - 请求处理：Service层DTO对象必须转为Mapper层的Entity对象
  - 响应处理：Mapper层Entity必须转为Service层DTO对象

## 禁用项：
- 严禁使用com.jd.lightning.common.utils.poi.ExcelUtil
- 严禁使用java.util.List.of()方法，必须替换为(new ArrayList()).add(Object)模式
- Controller层禁用toResult()方法，必须使用toAjax()方法
  - 正确示例：`return toAjax(service.addUser(user));`
  - 错误示例：`return toResult(service.addUser(user));`

## 命名规范：
- 类名必须基于表名生成，示例：
  - 表名：t_cost_product_premium_income（cost为模块名）
  - 类名：ProductPremiumIncome[Controller|DTO|Entity|Mapper|Query|Service|ServiceImpl]
- 生成的DTO、Query、Entity属性名和类型必须严格对应SQL定义的字段名和类型
- 不得添加SQL定义之外的额外属性
- Controller返回类型必须使用com.jd.lightning.common.core.domain.Result
- Controller的@RequestMapping路径使用`模块名+小写类名前缀`，多个单词间用斜杠(/)分隔
- 示例：ProductPremiumIncomeController的@RequestMapping路径为/cost/product/premium/income

## 特别注意事项：
- 学习一下com.xl.alm.app.util.ExcelUtil类里面有几个常用的方法，参数不要对应错误，外部调用里面的方法名字不要写错
```

#### 4.4.4 前端实现规范（web模块）
```markdown
## 代码位置：
- 所有前端代码必须放在web工程下

## 文件结构：
- 必须按照`web/src/views/模块名/小写单词1/小写单词2/index.vue`格式创建目录和文件
- 模块名必须从表名提取，如t_{模块名}_product_premium_income
- 其他单词必须从Controller类名提取
- 示例：ProductPremiumIncomeController → `web/src/views/cost/product/premium/income/index.vue`

## 数据校验：
- 必须对所有输入项进行校验，包括：
  - 列表页查询条件
  - 新增、编辑页的所有输入项
  - 校验规则必须参考DDL语句中的数据类型、格式及长度

## 导出功能实现：
handleExport()函数必须按以下模式实现：
```javascript
handleExport() {
  this.download(
    'cost/product/premium/income/export',  // API端点路径
    {
      ...this.queryParams,  // 查询参数对象
    },
    `product_premium_income_${new Date().getTime()}.xlsx`  // 带时间戳的文件名
  );
}
```

## 页面布局：
- 查询列表页面的查询条件组件（包括输入框、下拉框、日期等）一行最多展示3个，多出的换行展示

## 下拉框实现：
所有下拉框代码必须按以下格式生成：
```vue
<el-form-item label="某个下拉框" prop="${参数名}">
  <el-select
    v-model="queryParams.${参数名}"
    placeholder="点位符"
    clearable
    style="width: ${根据字段实际长度设置宽度}px"
  >
    <el-option
      v-for="dict in dict.type.${字典数据中的dict_type}"
      :key="dict.value"
      :label="dict.label"
      :value="dict.value"
    />
  </el-select>
</el-form-item>
```
```

#### 4.4.5 菜单SQL生成规范
```markdown
## 写入SQL文件：
- 生成的菜单SQL必须写入docs/sql/menu.sql这个文件，禁止写入其他文件

## 格式示例：
vue页面路径：web/src/views/cost/product/premium/income/index --> component: cost/product/premium/income/index，path: productPremiumIncome

```sql
INSERT INTO alm.sys_menu (menu_name,parent_id,order_num,`path`,component,query,route_name,is_frame,is_cache,menu_type,visible,status,perms,icon,create_by,create_time,update_by,update_time,remark) VALUES
('分产品保费收入管理',1,1,'productPremiumIncome','cost/product/premium/income/index','','',1,0,'C','0','0','cost:product:premium:income:list','user','admin','2025-04-10 12:44:11','',NULL,'分产品保费收入管理菜单'),
('分产品保费收入查询',100,1,'','','','',1,0,'F','0','0','cost:product:premium:income:query','#','admin','2025-04-10 12:44:11','',NULL,''),
('分产品保费收入新增',100,2,'','','','',1,0,'F','0','0','cost:product:premium:income:add','#','admin','2025-04-10 12:44:11','',NULL,''),
('分产品保费收入修改',100,3,'','','','',1,0,'F','0','0','cost:product:premium:income:edit','#','admin','2025-04-10 12:44:11','',NULL,''),
('分产品保费收入删除',100,4,'','','','',1,0,'F','0','0','cost:product:premium:income:remove','#','admin','2025-04-10 12:44:11','',NULL,''),
('分产品保费收入导出',100,5,'','','','',1,0,'F','0','0','cost:product:premium:income:export','#','admin','2025-04-10 12:44:11','',NULL,''),
('分产品保费收入导入',100,6,'','','','',1,0,'F','0','0','cost:product:premium:income:import','#','admin','2025-04-10 12:44:11','',NULL,'');
```

## SQL文件名命名规范：
- 字典数据文件名：docs/sql/dict.sql
- 菜单数据文件名：docs/sql/menu.sql
```

#### 4.4.6 执行要求
```markdown
## 代码生成位置：
- 必须严格按照上述规范在指定模块目录下生成代码

## 执行顺序：
- 先生成后端代码，再生成前端代码，最后生成菜单SQL

## 任务拆分：
- 当表字段信息超过30个时，拆分为多个分片任务完成字段补全，保证生成全量字段信息
```

### 4.5 Job任务生成模版 (4.generate_job.md)

#### 4.5.1 模版参数
```markdown
参数1: 设计文档名称
参数2: 具体章节需求
目标模块: job模块
```

#### 4.5.2 Job开发要求
```markdown
- 基于设计文档章节需求生成功能代码
- 代码生成到job模块下
- 生成过程要认真、细致，不能出现错误
- 优先使用已有的方法和字段
- 生成测试代码，真实调用接口
- 实体类引用BaseEntity，避免冲突
```

## 5. 使用插件自动编码

### 5.1 自动编码流程
成本核算模块采用标准化的9步自动编码流程：

```mermaid
flowchart TD
    A[步骤1: 生成DDL] --> B[步骤2: 生成字典数据]
    B --> C[步骤3: 生成TB0013 CRUD]
    C --> D[步骤4: 生成TB0014 CRUD]
    D --> E[步骤5: 生成TB0015 CRUD]
    E --> F[步骤6: 生成TB0016 CRUD]
    F --> G[步骤7: 分产品保费收入Job]
    G --> H[步骤8: 中短存续期产品利差Job]
    H --> I[步骤9: 分账户有效成本率Job]
```

### 5.2 使用AI插件执行任务

#### 5.2.1 步骤1-2: 数据库结构生成
```bash
# 任务1: 生成DDL
AI提示词: "请基于docs/design/cost_program_design.md文档中的2.3章节生成数据表的DDL，
以cost_program_design.sql文件存储到docs/sql目录下"

# 任务2: 生成字典数据
AI提示词: "读取docs/sql/cost_program_design.sql文件中的DDL，
针对有多值的字段生成字典数据的insert语句，
生成的DML写入docs/sql/cost_program_dict.sql文件中"
```

#### 5.2.2 步骤3-6: CRUD功能生成
```bash
# 任务3: TB0013 CRUD
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
参数3="TB0013"
执行文件="docs/prompt/3.generate_crud_function.md"

AI提示词: "依次读取docs/sql/cost_program_design.sql中DDL建表信息(TB0013)，
生成基于数据表定义的完整CRUD功能，包括后端Java代码、前端Vue界面和菜单SQL"

# 任务4-6: 重复执行，分别处理TB0014、TB0015、TB0016
# 参数3分别改为"TB0014"、"TB0015"、"TB0016"
```

#### 5.2.3 步骤7-9: Job任务生成
```bash
# 任务7: 分产品保费收入Job
AI提示词: "基于cost_program_design.md文档4.1.3.2.3.3章节需求生成功能代码，
代码生成到job模块下"

# 任务8: 中短存续期产品利差Job
AI提示词: "基于cost_program_design.md文档4.1.3.2.3.4章节需求生成功能代码"

# 任务9: 分账户有效成本率Job
AI提示词: "基于cost_program_design.md文档4.1.3.2.3.2章节需求生成功能代码"
```

### 5.3 插件使用最佳实践

#### 5.3.1 代码生成前检查
```markdown
## 检查清单：
- [ ] 设计文档是否完整
- [ ] 表结构定义是否清晰
- [ ] 字段类型和长度是否合理
- [ ] 业务逻辑是否明确
- [ ] 计算公式是否准确
```

#### 5.3.2 代码生成后验证
```markdown
## 验证清单：
- [ ] 所有字段都已包含，无遗漏
- [ ] 数据类型与DDL定义一致
- [ ] 权限注解格式正确
- [ ] 对象转换规范执行
- [ ] 前后端接口地址一致
- [ ] Excel导入导出功能完整
```

### 5.4 常见问题处理

#### 5.4.1 字段遗漏问题
```markdown
问题：生成的代码缺少某些字段
解决：
1. 检查设计文档中的字段定义
2. 重新执行DDL生成任务
3. 确认字段在所有相关文件中都已添加
```

#### 5.4.2 数据类型不匹配
```markdown
问题：前后端数据类型不一致
解决：
1. 统一金额字段使用decimal(28,10)
2. 统一比率字段使用decimal(10,6)或decimal(6,10)
3. 统一标识字段使用char(1)
```

#### 5.4.3 权限配置错误
```markdown
问题：权限注解格式不正确
解决：
1. 使用标准格式：@PreAuthorize("@ss.hasPermi('cost:{操作}:{子操作}')")
2. 确保模块名与@RequestMapping路径首单词对应
3. 检查菜单SQL中的权限标识
```

### 5.5 自动化脚本示例

#### 5.5.1 批量生成脚本
```bash
#!/bin/bash
# 成本核算模块自动生成脚本

echo "开始生成成本核算模块代码..."

# 步骤1: 生成DDL
echo "步骤1: 生成DDL"
# AI插件调用DDL生成模版

# 步骤2: 生成字典数据
echo "步骤2: 生成字典数据"
# AI插件调用字典生成模版

# 步骤3-6: 生成CRUD功能
for table in TB0013 TB0014 TB0015 TB0016; do
    echo "生成${table} CRUD功能"
    # AI插件调用CRUD生成模版
done

# 步骤7-9: 生成Job任务
echo "步骤7-9: 生成Job任务"
# AI插件调用Job生成模版

echo "代码生成完成！"
```

#### 5.5.2 质量检查脚本
```bash
#!/bin/bash
# 代码质量检查脚本

echo "开始代码质量检查..."

# 检查DDL文件
if [ -f "docs/sql/cost_program_design.sql" ]; then
    echo "✓ DDL文件存在"
else
    echo "✗ DDL文件缺失"
fi

# 检查字典文件
if [ -f "docs/sql/cost_program_dict.sql" ]; then
    echo "✓ 字典文件存在"
else
    echo "✗ 字典文件缺失"
fi

# 检查后端代码
for module in controller dto entity mapper query service; do
    if [ -d "app/src/main/java/com/xl/alm/app/${module}/cost" ]; then
        echo "✓ ${module}模块存在"
    else
        echo "✗ ${module}模块缺失"
    fi
done

# 检查前端代码
if [ -d "web/src/views/cost" ]; then
    echo "✓ 前端页面存在"
else
    echo "✗ 前端页面缺失"
fi

echo "质量检查完成！"
```

## 6. 附录

### 6.1 完整示例：分产品统计表开发

#### 6.1.1 设计文档片段
```markdown
## TB0007: 分产品统计表 (t_cost_product_statistics)

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 字段规则描述 |
|----------|----------|----------|------|----------|--------------|
| 账期 | accounting_period | varchar | 6 | 否 | 格式：YYYYMM |
| 精算代码 | actuarial_code | varchar | 20 | 否 | 产品精算代码 |
| 业务代码 | business_code | varchar | 20 | 否 | 产品业务代码 |
| 产品名称 | product_name | varchar | 100 | 否 | 产品全称 |
| 设计类型 | design_type | varchar | 50 | 否 | 传统险、分红险、万能险 |
| 业务类型 | business_type | varchar | 20 | 否 | 有效业务、新业务 |
| 情景名称 | scenario_name | varchar | 50 | 否 | 基本情景、压力情景三 |
| 法定准备金T0 | statutory_reserve_t0 | decimal | 28,10 | 是 | 法定准备金T0 |
| 资金成本率T0 | fund_cost_rate_t0 | decimal | 10,6 | 是 | 资金成本率T0 |
| 保证成本率T0 | guaranteed_cost_rate_t0 | decimal | 10,6 | 是 | 保证成本率T0 |
```

#### 6.1.2 AI提示词示例
```
请基于上述表结构生成完整的CRUD功能，包括：

1. 后端代码（app模块）：
   - ProductStatisticsController.java
   - ProductStatisticsDTO.java
   - ProductStatistics.java (Entity)
   - ProductStatisticsMapper.java
   - ProductStatisticsMapper.xml
   - ProductStatisticsQuery.java
   - ProductStatisticsService.java
   - ProductStatisticsServiceImpl.java

2. 前端代码（web模块）：
   - web/src/views/cost/product/statistics/index.vue
   - web/src/api/cost/productStatistics.js

3. 菜单SQL：
   - docs/sql/menu.sql

请确保：
- 所有字段都包含，不要遗漏
- 权限注解格式正确
- 前后端接口地址一致
- 支持Excel导入导出功能
```

#### 6.1.3 生成的代码结构
```
生成文件清单：
├── app/src/main/java/com/xl/alm/app/
│   ├── controller/cost/ProductStatisticsController.java
│   ├── dto/cost/ProductStatisticsDTO.java
│   ├── entity/cost/ProductStatistics.java
│   ├── mapper/cost/ProductStatisticsMapper.java
│   ├── query/cost/ProductStatisticsQuery.java
│   ├── service/cost/ProductStatisticsService.java
│   └── service/impl/cost/ProductStatisticsServiceImpl.java
├── app/src/main/resources/mapper/cost/ProductStatisticsMapper.xml
├── web/src/views/cost/product/statistics/index.vue
├── web/src/api/cost/productStatistics.js
└── docs/sql/cost_product_statistics_menu.sql
```

### 6.2 常用代码片段

#### 6.2.1 Controller标准模板
```java
@RestController
@RequestMapping("/cost/product/premium/income")
@PreAuthorize("@ss.hasPermi('cost:product:premium:income')")
public class ProductPremiumIncomeController extends BaseController {

    @Autowired
    private ProductPremiumIncomeService service;

    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:list')")
    public Result<TableDataInfo> list(ProductPremiumIncomeQuery query) {
        startPage();
        List<ProductPremiumIncomeDTO> list = service.selectList(query);
        return Result.success(getDataTable(list));
    }

    @PostMapping
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:add')")
    public Result<Integer> add(@RequestBody ProductPremiumIncomeDTO dto) {
        return toAjax(service.insert(dto));
    }

    @PutMapping
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:edit')")
    public Result<Integer> edit(@RequestBody ProductPremiumIncomeDTO dto) {
        return toAjax(service.update(dto));
    }

    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:remove')")
    public Result<Integer> remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteByIds(ids));
    }

    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:export')")
    public void export(HttpServletResponse response, ProductPremiumIncomeQuery query) {
        List<ProductPremiumIncomeDTO> list = service.selectList(query);
        // 使用项目标准的ExcelUtil类
        com.xl.alm.app.util.ExcelUtil.exportExcel(list, "分产品保费收入数据", ProductPremiumIncomeDTO.class, response);
    }

    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:import')")
    public Result<String> importData(MultipartFile file) throws Exception {
        // 使用项目标准的ExcelUtil类
        List<ProductPremiumIncomeDTO> list = com.xl.alm.app.util.ExcelUtil.importExcel(file.getInputStream(), ProductPremiumIncomeDTO.class);
        String message = service.importData(list);
        return Result.success(message);
    }
}
```

#### 6.2.2 Vue页面标准模板
```vue
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input v-model="queryParams.accountingPeriod" placeholder="请输入账期" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="精算代码" prop="actuarialCode">
        <el-input v-model="queryParams.actuarialCode" placeholder="请输入精算代码" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select v-model="queryParams.designType" placeholder="请选择设计类型" clearable style="width: 200px">
          <el-option v-for="dict in dict.type.cost_design_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['cost:product:premium:income:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['cost:product:premium:income:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['cost:product:premium:income:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['cost:product:premium:income:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['cost:product:premium:income:import']">导入</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="精算代码" align="center" prop="actuarialCode" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="设计类型" align="center" prop="designType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="新单规模保费" align="center" prop="newScalePremium" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['cost:product:premium:income:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['cost:product:premium:income:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
export default {
  name: "ProductPremiumIncome",
  dicts: ['cost_design_type'],
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        actuarialCode: null,
        designType: null
      },
      // 数据列表
      dataList: [],
      // 总条数
      total: 0,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 加载层信息
      loading: true
    };
  },
  methods: {
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'cost/product/premium/income/export',
        {
          ...this.queryParams,
        },
        `product_premium_income_${new Date().getTime()}.xlsx`
      );
    }
  }
};
</script>
```

### 6.3 开发规范速查表

#### 6.3.1 命名规范
| 类型 | 规范 | 示例 |
|------|------|------|
| 表名 | t_{模块}_{业务名} | t_cost_product_premium_income |
| 类名 | {业务名}Controller/DTO/Entity | ProductPremiumIncomeController |
| 接口路径 | /{模块}/{业务路径} | /cost/product/premium/income |
| 权限标识 | {模块}:{操作}:{子操作} | cost:product:premium:income:list |
| Vue文件路径 | {模块}/{业务路径}/index.vue | cost/product/premium/income/index.vue |

#### 6.3.2 字段类型规范
| 业务类型 | 数据类型 | 长度 | 示例 |
|----------|----------|------|------|
| 金额 | decimal | 28,10 | premium_amount |
| 比率 | decimal | 10,6 或 6,10 | cost_rate |
| 账期 | varchar | 6 | accounting_period |
| 代码 | varchar | 20 | actuarial_code |
| 名称 | varchar | 100 | product_name |
| 标识 | char | 1 | short_term_flag |

---

**注意**: 本指南基于 `cost_process.md` 文档制定，在实际开发中应严格遵循各项规范，确保代码质量和系统稳定性。开发过程中如遇到问题，请参考相关章节的详细说明或联系技术负责人。

## 4. Job任务开发规范

### 4.1 Job任务分类

#### 4.1.1 数据计算任务
- **分产品保费收入计算** (UC0015)
- **中短存续期产品利差计算** (UC0016)  
- **分账户有效成本率计算** (UC0012)

#### 4.1.2 Job开发规范
```java
@Component
@Slf4j
public class ProductPremiumIncomeJob {
    
    @Autowired
    private ProductPremiumIncomeService service;
    
    public void execute(String accountingPeriod) {
        try {
            log.info("开始执行分产品保费收入计算任务，账期：{}", accountingPeriod);
            
            // 1. 数据清理
            service.deleteByAccountingPeriod(accountingPeriod);
            
            // 2. 数据计算
            service.calculateProductPremiumIncome(accountingPeriod);
            
            log.info("分产品保费收入计算任务执行完成");
        } catch (Exception e) {
            log.error("分产品保费收入计算任务执行失败", e);
            throw e;
        }
    }
}
```

### 4.2 计算逻辑规范

#### 4.2.1 有效成本率计算
```java
// 使用IRR方法计算有效成本率
public BigDecimal calculateEffectiveCostRate(List<CashFlow> cashFlows) {
    if (cashFlows.isEmpty()) {
        return BigDecimal.ZERO;
    }
    
    // 构建现金流数组
    double[] values = cashFlows.stream()
        .mapToDouble(cf -> cf.getValue().doubleValue())
        .toArray();
        
    // 使用IRR算法计算
    return BigDecimal.valueOf(IRRCalculator.calculate(values));
}
```

#### 4.2.2 数据聚合规范
```java
// 按维度聚合数据
public void aggregateByDesignType(String accountingPeriod) {
    // 1. 按设计类型分组
    Map<String, List<ProductStatistics>> groupedData = 
        productStatisticsMapper.selectByAccountingPeriod(accountingPeriod)
            .stream()
            .collect(Collectors.groupingBy(ProductStatistics::getDesignType));
    
    // 2. 计算聚合值
    groupedData.forEach((designType, products) -> {
        AccountSummary summary = calculateSummary(products);
        accountSummaryMapper.insert(summary);
    });
}
```

## 5. 质量保证规范

### 5.1 代码检查清单
- [ ] 所有字段都已包含，无遗漏
- [ ] 数据类型与DDL定义一致
- [ ] 权限注解格式正确
- [ ] 对象转换规范执行
- [ ] 异常处理完整
- [ ] 日志记录充分

### 5.2 测试规范
```java
@Test
public void testCalculateProductPremiumIncome() {
    // 准备测试数据
    String accountingPeriod = "202506";
    
    // 执行计算
    productPremiumIncomeJob.execute(accountingPeriod);
    
    // 验证结果
    List<ProductPremiumIncome> results = 
        productPremiumIncomeMapper.selectByAccountingPeriod(accountingPeriod);
    
    assertThat(results).isNotEmpty();
    assertThat(results.get(0).getNewScalePremium()).isNotNull();
}
```

### 5.3 性能优化规范
- **批量操作**: 使用批量插入/更新
- **索引优化**: 为查询字段建立索引
- **内存管理**: 大数据量分批处理
- **事务控制**: 合理控制事务边界

## 6. 部署和维护

### 6.1 部署检查
- [ ] SQL脚本执行无误
- [ ] 菜单权限配置正确
- [ ] 前后端接口联调通过
- [ ] Job任务调度配置完成

### 6.2 监控和日志
- **业务日志**: 记录关键计算步骤
- **性能监控**: 监控Job执行时间
- **异常告警**: 及时发现和处理异常

## 7. 具体任务执行指南

### 7.1 任务1-2: DDL和字典数据生成

#### 7.1.1 执行命令模板
```bash
# 任务1: 生成DDL
参数1="cost_program_design.md"
参数2="2.3"
参数3="cost_program_design.sql"
执行文件="/prompt/1.generate_ddl.md"

# 任务2: 生成字典数据
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
执行文件="/prompt/2.generate_dict_data.md"
```

#### 7.1.2 关键表结构
- **TB0013**: 分产品保费收入表 (`t_cost_product_premium_income`)
- **TB0014**: 子账户收益率表 (`t_cost_sub_account_yield_rate`)
- **TB0015**: 分产品有效成本率表 (`t_cost_product_effective_rate`)
- **TB0016**: 中短存续期产品利差表 (`t_cost_short_term_product_spread`)

### 7.2 任务3-6: CRUD功能生成

#### 7.2.1 执行参数
```bash
# 任务3: TB0013 CRUD
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
参数3="TB0013"
执行文件="docs/prompt/3.generate_crud_function.md"

# 任务4: TB0014 CRUD
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
参数3="TB0014"

# 任务5: TB0015 CRUD
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
参数3="TB0015"

# 任务6: TB0016 CRUD
参数1="cost_program_design.sql"
参数2="cost_program_dict.sql"
参数3="TB0016"
```

#### 7.2.2 生成文件清单
每个表生成以下文件：
- **Controller**: `app/src/main/java/com/xl/alm/app/controller/cost/`
- **DTO**: `app/src/main/java/com/xl/alm/app/dto/cost/`
- **Entity**: `app/src/main/java/com/xl/alm/app/entity/cost/`
- **Mapper**: `app/src/main/java/com/xl/alm/app/mapper/cost/` 和 `app/src/main/resources/mapper/cost/`
- **Query**: `app/src/main/java/com/xl/alm/app/query/cost/`
- **Service**: `app/src/main/java/com/xl/alm/app/service/cost/`
- **ServiceImpl**: `app/src/main/java/com/xl/alm/app/service/impl/cost/`
- **Vue页面**: `web/src/views/cost/`
- **API文件**: `web/src/api/cost/`
- **菜单SQL**: `docs/sql/menu.sql`

#### 7.2.3 代码生成要求
- **必须严格按照规范**：所有代码必须遵循4.4节中的详细规范
- **字段完整性**：确保所有表字段都包含在生成的代码中，不得遗漏
- **数据校验**：根据DDL定义添加相应的校验注解
- **权限控制**：正确配置@PreAuthorize注解
- **对象转换**：Service层与Mapper层之间正确进行对象转换
- **Excel功能**：使用项目标准的com.xl.alm.app.util.ExcelUtil类

### 7.3 任务7-9: Job任务开发

#### 7.3.1 任务7: 分产品保费收入Job
**需求章节**: 4.1.3.2.3.3
**核心逻辑**:
```java
// 计算新单规模保费
BigDecimal newScalePremium =
    ytdSinglePremium.add(ytdRegularPremium)
    .add(ytdUlSingle).add(ytdUlRegular)
    .add(ytdUlInitialFee);

// 从保费收入表获取年度累计数据
PremiumIncome premiumData = premiumIncomeMapper
    .selectByActuarialCode(actuarialCode, accountingPeriod);
```

#### 7.3.2 任务8: 中短存续期产品利差Job
**需求章节**: 4.1.3.2.3.4
**核心逻辑**:
```java
// 筛选报监管中短标识为"是"的产品
List<ProductAttribute> shortTermProducts =
    productAttributeMapper.selectShortTermProducts(accountingPeriod);

// 计算年化投资收益率
BigDecimal yieldRate = getYieldRateByDesignType(designType, subAccount);

// 计算利差 = 年化投资收益率 - 负债资金成本率
BigDecimal spread = yieldRate.subtract(liabilityCostRate);
```

#### 7.3.3 任务9: 分账户有效成本率Job
**需求章节**: 4.1.3.2.3.2
**核心逻辑**:
```java
// 按设计类型汇总现金流
Map<String, List<CashFlow>> cashFlowsByType =
    aggregateCashFlowsByDesignType(accountingPeriod);

// 使用XIRR函数计算有效成本率
for (Map.Entry<String, List<CashFlow>> entry : cashFlowsByType.entrySet()) {
    BigDecimal effectiveRate = XIRRCalculator.calculate(entry.getValue());
    // 保存结果...
}
```

## 8. 常见问题和解决方案

### 8.1 数据类型问题
**问题**: 金额字段精度不够
**解决**: 统一使用 `decimal(28,10)`

**问题**: 比率字段存储格式不一致
**解决**: 小数形式存储，如0.03表示3%

### 8.2 性能优化问题
**问题**: 大数据量计算超时
**解决**:
```java
// 分批处理
int batchSize = 1000;
List<String> actuarialCodes = getAllActuarialCodes();
for (int i = 0; i < actuarialCodes.size(); i += batchSize) {
    List<String> batch = actuarialCodes.subList(i,
        Math.min(i + batchSize, actuarialCodes.size()));
    processBatch(batch);
}
```

### 8.3 数据一致性问题
**问题**: 重复执行任务导致数据重复
**解决**:
```java
// 执行前先物理删除
@Transactional
public void executeJob(String accountingPeriod) {
    // 1. 物理删除旧数据
    mapper.deleteByAccountingPeriod(accountingPeriod);

    // 2. 重新计算插入
    calculateAndInsert(accountingPeriod);
}
```

## 9. 代码模板

### 9.1 Controller模板
```java
@RestController
@RequestMapping("/cost/product/premium/income")
@PreAuthorize("@ss.hasPermi('cost:product:premium:income')")
public class ProductPremiumIncomeController extends BaseController {

    @Autowired
    private ProductPremiumIncomeService service;

    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:list')")
    public Result<TableDataInfo> list(ProductPremiumIncomeQuery query) {
        startPage();
        List<ProductPremiumIncomeDTO> list = service.selectList(query);
        return Result.success(getDataTable(list));
    }

    @PostMapping
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:add')")
    public Result<Integer> add(@RequestBody ProductPremiumIncomeDTO dto) {
        return toAjax(service.insert(dto));
    }

    @PutMapping
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:edit')")
    public Result<Integer> edit(@RequestBody ProductPremiumIncomeDTO dto) {
        return toAjax(service.update(dto));
    }

    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:remove')")
    public Result<Integer> remove(@PathVariable Long[] ids) {
        return toAjax(service.deleteByIds(ids));
    }

    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:export')")
    public void export(HttpServletResponse response, ProductPremiumIncomeQuery query) {
        List<ProductPremiumIncomeDTO> list = service.selectList(query);
        com.xl.alm.app.util.ExcelUtil.exportExcel(list, "分产品保费收入数据", ProductPremiumIncomeDTO.class, response);
    }

    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermi('cost:product:premium:income:import')")
    public Result<String> importData(MultipartFile file) throws Exception {
        List<ProductPremiumIncomeDTO> list = com.xl.alm.app.util.ExcelUtil.importExcel(file.getInputStream(), ProductPremiumIncomeDTO.class);
        String message = service.importData(list);
        return Result.success(message);
    }
}
```

### 9.2 Service模板
```java
@Service
public class ProductPremiumIncomeServiceImpl implements ProductPremiumIncomeService {

    @Autowired
    private ProductPremiumIncomeMapper mapper;

    @Override
    public List<ProductPremiumIncomeDTO> selectList(ProductPremiumIncomeQuery query) {
        // 将Query对象转换为Entity对象进行查询
        List<ProductPremiumIncome> entities = mapper.selectList(query);
        // 将Entity对象转换为DTO对象返回
        return entities.stream()
            .map(entity -> BeanUtils.copyProperties(entity, ProductPremiumIncomeDTO.class))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public int insert(ProductPremiumIncomeDTO dto) {
        // 将DTO对象转换为Entity对象
        ProductPremiumIncome entity = BeanUtils.copyProperties(dto, ProductPremiumIncome.class);
        return mapper.insert(entity);
    }

    @Override
    @Transactional
    public int update(ProductPremiumIncomeDTO dto) {
        // 将DTO对象转换为Entity对象
        ProductPremiumIncome entity = BeanUtils.copyProperties(dto, ProductPremiumIncome.class);
        return mapper.updateById(entity);
    }

    @Override
    @Transactional
    public int deleteByIds(Long[] ids) {
        return mapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    @Transactional
    public String importData(List<ProductPremiumIncomeDTO> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            throw new ServiceException("导入数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductPremiumIncomeDTO dto : dataList) {
            try {
                // 数据校验
                validateData(dto);
                // 将DTO转换为Entity并插入
                ProductPremiumIncome entity = BeanUtils.copyProperties(dto, ProductPremiumIncome.class);
                mapper.insert(entity);
                successNum++;
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账期 " + dto.getAccountingPeriod() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }

        return successMsg.toString();
    }

    private void validateData(ProductPremiumIncomeDTO dto) {
        // 添加数据校验逻辑
        if (StringUtils.isEmpty(dto.getAccountingPeriod())) {
            throw new ServiceException("账期不能为空");
        }
        if (StringUtils.isEmpty(dto.getActuarialCode())) {
            throw new ServiceException("精算代码不能为空");
        }
    }
}
```

### 9.3 Vue页面模板
```vue
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="精算代码" prop="actuarialCode">
        <el-input
          v-model="queryParams.actuarialCode"
          placeholder="请输入精算代码"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设计类型" prop="designType">
        <el-select
          v-model="queryParams.designType"
          placeholder="请选择设计类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.cost_design_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cost:product:premium:income:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cost:product:premium:income:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cost:product:premium:income:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cost:product:premium:income:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['cost:product:premium:income:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="精算代码" align="center" prop="actuarialCode" />
      <el-table-column label="产品名称" align="center" prop="productName" show-overflow-tooltip />
      <el-table-column label="设计类型" align="center" prop="designType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cost_design_type" :value="scope.row.designType"/>
        </template>
      </el-table-column>
      <el-table-column label="新单规模保费" align="center" prop="newScalePremium" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cost:product:premium:income:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cost:product:premium:income:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
        </el-form-item>
        <el-form-item label="精算代码" prop="actuarialCode">
          <el-input v-model="form.actuarialCode" placeholder="请输入精算代码" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="设计类型" prop="designType">
          <el-select v-model="form.designType" placeholder="请选择设计类型">
            <el-option
              v-for="dict in dict.type.cost_design_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
```

---

**注意**: 本指南基于 `cost_process.md` 文档制定，在实际开发中应严格遵循各项规范，确保代码质量和系统稳定性。开发过程中如遇到问题，请参考相关章节的详细说明或联系技术负责人。
