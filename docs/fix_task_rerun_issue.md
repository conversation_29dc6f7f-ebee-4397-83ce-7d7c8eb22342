# 任务重跑数据插入失败问题修复

## 问题描述

在重跑任务时，由于使用逻辑删除而非物理删除，导致数据库中存在已标记删除的历史数据。当重新插入数据时，由于表中存在唯一约束（如`accounting_period + actuarial_code`），会导致插入重复数据失败。

## 涉及的任务

1. **ProductPremiumIncomeDetailService.calculateProductPremiumIncomeDetail** - 分产品保费收入计算任务
2. **ShortTermProductSpreadTask.execute** - 中短存续期产品利差计算任务

## 问题根本原因

### 1. 逻辑删除vs物理删除

**原有实现（逻辑删除）：**
```sql
-- 分产品保费收入表
update t_cost_product_premium_income_detail set is_del = 1, update_time = now() 
where accounting_period = #{accountingPeriod} and is_del = 0

-- 中短存续期产品利差表  
update t_cost_short_term_product_spread set is_del = 1, update_time = now()
where accounting_period = #{accountingPeriod} and is_del = 0
```

**问题：** 数据仍然存在于数据库中，只是标记为删除，重新插入时会违反唯一约束。

### 2. 数据库唯一约束

```sql
-- 分产品保费收入表唯一约束
UNIQUE KEY idx_unique (accounting_period, actuarial_code)

-- 中短存续期产品利差表唯一约束  
UNIQUE KEY idx_unique (accounting_period, actuarial_code)
```

## 修复方案

### 方案一：修改为物理删除（已实施）

将逻辑删除改为物理删除，确保重跑任务时不会有数据冲突：

```sql
-- 分产品保费收入表
delete from t_cost_product_premium_income_detail
where accounting_period = #{accountingPeriod}

-- 中短存续期产品利差表
delete from t_cost_short_term_product_spread 
where accounting_period = #{accountingPeriod}
```

### 方案二：提供灵活的删除选项（已实施）

同时提供物理删除和逻辑删除两种方法，根据业务需要选择：

#### 分产品保费收入表

**Mapper接口新增方法：**
- `physicalDeleteProductPremiumIncomeDetailByPeriod` - 物理删除
- `logicalDeleteProductPremiumIncomeDetailByPeriod` - 逻辑删除

**Service接口新增方法：**
- `physicalDeleteProductPremiumIncomeDetailByPeriod` - 物理删除
- `logicalDeleteProductPremiumIncomeDetailByPeriod` - 逻辑删除

#### 中短存续期产品利差表

**Mapper接口新增方法：**
- `physicalDeleteShortTermProductSpreadByPeriod` - 物理删除
- `logicalDeleteShortTermProductSpreadByPeriod` - 逻辑删除

**Service接口新增方法：**
- `physicalDeleteShortTermProductSpreadByPeriod` - 物理删除
- `logicalDeleteShortTermProductSpreadByPeriod` - 逻辑删除

## 修改的文件列表

### ProductPremiumIncomeDetail相关

1. `job/src/main/resources/mapper/cost/ProductPremiumIncomeDetailMapper.xml`
2. `job/src/main/java/com/xl/alm/job/cost/mapper/ProductPremiumIncomeDetailMapper.java`
3. `job/src/main/java/com/xl/alm/job/cost/service/ProductPremiumIncomeDetailService.java`
4. `job/src/main/java/com/xl/alm/job/cost/service/impl/ProductPremiumIncomeDetailServiceImpl.java`
5. `job/src/test/java/com/xl/alm/job/cost/service/ProductPremiumIncomeDetailServiceTest.java`

### ShortTermProductSpread相关

1. `job/src/main/resources/mapper/cost/ShortTermProductSpreadMapper.xml`
2. `job/src/main/java/com/xl/alm/job/cost/mapper/ShortTermProductSpreadMapper.java`
3. `job/src/main/java/com/xl/alm/job/cost/service/ShortTermProductSpreadService.java`
4. `job/src/main/java/com/xl/alm/job/cost/service/impl/ShortTermProductSpreadServiceImpl.java`
5. `job/src/test/java/com/xl/alm/job/cost/service/ShortTermProductSpreadTaskTest.java`

## 测试验证

### 新增测试用例

1. **testRepeatedCalculation** - 测试重复执行计算功能
   - 验证第一次执行成功
   - 验证第二次执行（重跑）成功
   - 验证两次结果数量一致

2. **testDeleteMethods** - 测试删除方法功能
   - 测试逻辑删除功能
   - 测试物理删除功能
   - 验证删除效果

### 测试执行

```bash
# 测试分产品保费收入
cd job && mvn test -Dtest=ProductPremiumIncomeDetailServiceTest#testRepeatedCalculation

# 测试中短存续期产品利差
cd job && mvn test -Dtest=ShortTermProductSpreadTaskTest#testRepeatedExecution
```

## 使用建议

1. **任务重跑场景**：使用物理删除，确保数据完全清理
2. **数据归档场景**：使用逻辑删除，保留历史数据
3. **生产环境**：建议使用物理删除，避免数据冲突

## 注意事项

1. **数据备份**：物理删除会永久删除数据，建议在执行前做好数据备份
2. **事务管理**：所有删除操作都在事务中执行，确保数据一致性
3. **日志记录**：所有操作都有详细的日志记录，便于问题排查

## 总结

通过将逻辑删除改为物理删除，解决了任务重跑时的数据插入失败问题。同时提供了灵活的删除选项，满足不同业务场景的需求。修复后的代码经过充分测试，确保功能正常且不会影响现有业务逻辑。
