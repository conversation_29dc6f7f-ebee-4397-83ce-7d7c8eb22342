# TB0006月度折现曲线表含价差导出功能增强

## 概述

本文档描述了TB0006月度折现曲线表含价差的Excel导出功能增强，实现了月度折现曲线利率含价差值集JSON字段的平铺展开导出。

## 功能特性

### 1. JSON字段展开
- **月度折现曲线利率含价差值集** (`monthlyDiscountRateWithSpreadSet`) - JSON字段展开为多列
- **简单格式**: 直接的key-value对，无嵌套结构
- **含价差数据**: 基于基础利率加上价差计算得出的最终利率

### 2. 表头格式
- **单行表头**: 直接显示期限序号 (如: "0", "1", "2", "3", ...)
- **简洁明了**: 不需要日期信息，直接显示期限编号

### 3. 数据格式
- **值**: JSON中的含价差利率值，直接显示为数值
- **排序**: 按期限序号自然排序 (0, 1, 2, ..., 600)

## JSON数据格式

### 月度折现曲线含价差利率JSON结构
```json
{
  "0": 0.027459,
  "1": 0.027459,
  "2": 0.027459,
  "3": 0.027459,
  "6": 0.030000,
  "12": 0.033000,
  "24": 0.037000,
  "36": 0.040000,
  "48": 0.043000,
  "60": 0.045000,
  "120": 0.050000,
  "240": 0.055000,
  "360": 0.057000,
  "480": 0.059000,
  "600": 0.060000
}
```

### 字段说明
- **外层键**: 期限序号 (0-600)，表示月份数
- **值**: 对应期限的含价差折现利率，decimal类型
- **计算逻辑**: 基础利率 + 价差 + 基点调整（根据曲线细分类）

## 表结构特点

### 与TB0005的区别
TB0006相比TB0005增加了以下字段：
- **久期类型** (`durationType`) - 修正久期/有效久期/利差久期
- **基点类型** (`basisPointType`) - 0bp/+50bp/-50bp
- **价差类型** (`spreadType`) - 发行时点价差/评估时点价差
- **价差** (`spread`) - 具体的价差数值
- **曲线细分类** (`curveSubCategory`) - 1-5的分类标识

### 曲线细分类说明
- **1**: 修正久期 + 0bp + 发行时点 + 发行时点价差
- **2**: 修正久期 + 0bp + 评估时点 + 发行时点价差
- **3**: 有效久期 + +50bp + 评估时点 + 发行时点价差
- **4**: 有效久期 + -50bp + 评估时点 + 发行时点价差
- **5**: 利差久期 + 0bp + 评估时点 + 评估时点价差

## 实现细节

### 1. 控制器修改
文件: `app/src/main/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadController.java`

```java
@PostMapping("/export")
public void export(HttpServletResponse response, AdurMonthlyDiscountCurveWithSpreadQuery adurMonthlyDiscountCurveWithSpreadQuery) {
    List<AdurMonthlyDiscountCurveWithSpreadDTO> list = adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoList(adurMonthlyDiscountCurveWithSpreadQuery);

    // 转换字典值为中文标签用于导出
    convertDictValueToLabel(list);

    // 使用ValueSetExcelExporter导出，处理monthlyDiscountRateWithSpreadSet字段
    // 将月度折现曲线利率含价差值集JSON字段展开为多列，key作为表头，value作为值
    // 生成带时间戳的中文文件名
    String timestamp = String.valueOf(System.currentTimeMillis());
    String fileName = "月度折现曲线表含价差_" + timestamp;
    ValueSetExcelExporter.exportExcel(list, fileName, response, "monthlyDiscountRateWithSpreadSet");
}
```

### 2. 字典转换支持
TB0006支持更多的字典类型转换：
```java
private void convertDictValueToLabel(List<AdurMonthlyDiscountCurveWithSpreadDTO> list) {
    for (AdurMonthlyDiscountCurveWithSpreadDTO dto : list) {
        // 转换久期类型
        dto.setDurationType(DictConvertUtil.convertValueToLabel(dto.getDurationType(), "adur_duration_type"));
        
        // 转换基点类型
        dto.setBasisPointType(DictConvertUtil.convertValueToLabel(dto.getBasisPointType(), "adur_basis_point_type"));
        
        // 转换日期类型
        dto.setDateType(DictConvertUtil.convertValueToLabel(dto.getDateType(), "adur_date_type"));
        
        // 转换价差类型
        dto.setSpreadType(DictConvertUtil.convertValueToLabel(dto.getSpreadType(), "adur_spread_type"));
        
        // 转换账户名称
        dto.setAccountName(DictConvertUtil.convertValueToLabel(dto.getAccountName(), "adur_account_name"));
    }
}
```

### 3. ValueSetExcelExporter复用
TB0006复用了为TB0005开发的ValueSetExcelExporter功能：
- **简单JSON格式支持**: 支持直接的key-value格式
- **单行表头**: 显示期限序号作为表头
- **字典转换优化**: 避免重复转换

## 导出效果

### Excel表头示例
```
| 账期 | 久期类型 | 基点类型 | 日期类型 | 价差类型 | 价差 | 曲线细分类 | 资产编号 | 账户名称 | ... | 0 | 1 | 2 | 3 | 6 | 12 | 24 | 36 | ... | 600 |
|------|----------|----------|----------|----------|------|------------|----------|----------|-----|---|---|---|---|---|----|----|----|----|-----|
| 202407| 修正久期 | 0bp      | 发行时点 | 发行时点价差 | 0.0015 | 1        | ASSET001 | 传统账户 | ... |0.027459|0.027459|0.027459|0.027459|0.030|0.033|0.037|0.040|...|0.060|
```

### 特点
1. **丰富的维度信息**: 包含久期类型、基点类型、价差类型等多个维度
2. **单行表头**: 期限列直接显示序号，简洁明了
3. **数据展开**: JSON中的每个期限都成为独立的列
4. **字典转换**: 所有字典字段正确显示中文标签
5. **含价差数据**: 显示经过价差调整后的最终利率

## 字典配置

### 相关字典类型
确保以下字典类型在数据库中正确配置：

1. **adur_duration_type** - 久期类型
   - `01` → `修正久期`
   - `02` → `有效久期`
   - `03` → `利差久期`

2. **adur_basis_point_type** - 基点类型
   - `01` → `0bp`
   - `02` → `+50bp`
   - `03` → `-50bp`

3. **adur_date_type** - 日期类型
   - `01` → `发行时点`
   - `02` → `评估时点`

4. **adur_spread_type** - 价差类型
   - `01` → `发行时点价差`
   - `02` → `评估时点价差`

5. **adur_account_name** - 账户名称
   - `01` → `传统账户`
   - `02` → `分红账户`
   - `03` → `万能账户`
   - `04` → `普通账户`

## 测试验证

### 测试文件
`app/src/test/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadControllerExportTest.java`

### 测试用例
1. **月度折现曲线利率含价差值集导出功能测试**: 验证JSON字段展开导出
2. **月度折现曲线含价差JSON格式解析测试**: 验证JSON格式正确性
3. **字典转换功能测试**: 验证多个字典值正确转换
4. **空数据导出测试**: 验证空数据处理

### 运行测试
```bash
cd app
mvn test -Dtest=AdurMonthlyDiscountCurveWithSpreadControllerExportTest
```

## 使用方法

### 1. 前端调用
访问TB0006页面，点击导出按钮

### 2. API调用
```
POST /adur/monthly/discount/curve/with/spread/export
```

### 3. 参数
- 支持所有现有的查询参数
- 导出文件名: "月度折现曲线表含价差_时间戳.xlsx" (如: "月度折现曲线表含价差_1753837847067.xlsx")

## 数据计算逻辑

### 含价差利率计算
TB0006的利率数据基于TB0005的基础利率计算：

```
含价差利率 = 基础利率 + 价差 + 基点调整

其中：
- 基础利率：来自TB0005月度折现曲线表不含价差
- 价差：spread字段的值
- 基点调整：根据基点类型和曲线细分类确定
  - +50bp: +0.005
  - -50bp: -0.005
  - 0bp: 0
```

### 匹配条件
根据曲线细分类确定与TB0005的匹配条件：
- **曲线细分类1/2**: 匹配日期类型、账户名称、证券代码
- **曲线细分类3/4**: 匹配评估时点、账户名称、证券代码，并加减50bp
- **曲线细分类5**: 匹配评估时点、折现曲线标识=1

## 注意事项

1. **JSON格式**: 确保数据库中的JSON格式正确，为简单的key-value对
2. **期限范围**: 支持0-600期限，总共601个可能的列
3. **Excel限制**: 注意Excel列数限制，大量期限数据可能影响性能
4. **字典转换**: 确保所有相关字典数据正确配置
5. **数据依赖**: TB0006的数据计算依赖TB0005的基础数据
6. **曲线细分类**: 理解不同曲线细分类的计算逻辑差异

## 相关文件

### 修改的文件
1. `app/src/main/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadController.java`

### 新增的文件
1. `app/src/test/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadControllerExportTest.java`
2. `docs/tb0006_monthly_discount_curve_with_spread_export_enhancement.md`

### 依赖的文件
1. `app/src/main/java/com/xl/alm/app/dto/AdurMonthlyDiscountCurveWithSpreadDTO.java`
2. `app/src/main/java/com/xl/alm/app/service/AdurMonthlyDiscountCurveWithSpreadService.java`
3. `app/src/main/java/com/xl/alm/app/util/ValueSetExcelExporter.java`
4. `app/src/main/java/com/xl/alm/app/util/DictConvertUtil.java`

## 完成状态

✅ **已完成** - TB0006月度折现曲线表含价差导出功能增强已实现，支持月度折现曲线利率含价差值集的JSON平铺展开导出，包含完整的字典转换支持。
