# 系统功能设计文档

## 1. 业务架构

### 1.1 业务模块关系图

*这里使用mermaid流程图描述各模块间的层级关系*

```mermaid
flowchart TD
    A[资产宽表模块] --> B[资产配置状况模块]
    A[资产宽表模块] --> C[资产信用状况模块]
    D[负债产品模块] --> B[资产配置状况模块]
    B[资产配置状况模块] --> E[资产负债管理分析]
    C[资产信用状况模块] --> E[资产负债管理分析]
```

### 1.2 模块列表

*描述各模块的基础信息（编号、名称、英文名、英文缩写）*
*编号：文档内唯一标识，不同文档可以重复，编号以MD开头+4位数字（从0001开始）*
*名称：模块的中文名称*
*英文名：模块的英文名称，也是缩写的全称*
*英文缩写：用于生成代码时，命名包路径，如，com.xinlong.alm.asm*、com.xinlong.alm.acm*

| 模块编号   | 模块名称     | 模块英文名                      | 英文缩写 |
| ------ | -------- | -------------------------- | ---- |
| MD0001 | 资产配置状况模块 | asset_allocation_status    | asm  |
| MD0002 | 资产信用状况模块 | asset_credit_management    | acm  |

### 1.3 数据模型

#### 1.3.1 资产配置状况模块

*描述资产配置状况模块下的表间关系及表属性信息*

#### 1.3.2 资产信用状况模块

*描述资产信用状况模块下的表间关系及表属性信息*

##### 1.3.1.1 表间关系

*表间关系用mermaid图描述，主要用于梳理关系，使用英文描述，中文字符会报错*

```mermaid
erDiagram
    %% 基础数据源
    t_ast_asset_detail_overall ||--o{ t_asm_fund_utilization_scale : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_asm_fund_utilization_ratio : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_asm_fixed_income_term_dist : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_asm_risk_10day_var : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_fixed_income_credit_rating : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_deposit_interbank_cd : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_fixed_income_rating_term_dist : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_asset_risk_five_level : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_industry_concentration_risk : "provides asset data"
    t_ast_asset_detail_overall ||--o{ t_acm_single_asset_concentration : "provides asset data"

    %% 负债产品模块数据源
    t_liab_balance_sheet ||--o{ t_asm_financing_leverage_ratio : "provides balance sheet data"

    %% 映射关系表
    t_acm_asset_risk_item_mapping ||--o{ t_acm_asset_risk_five_level : "provides mapping"
    t_acm_industry_name_mapping ||--o{ t_acm_industry_concentration_risk : "provides mapping"
```

**核心关系说明：**
- **t_ast_asset_detail_overall** 是核心数据源，为资产配置状况模块和资产信用状况模块提供基础数据
- **t_liab_balance_sheet** 为融资杠杆比例表提供资产负债表数据
- **映射表** (t_acm_asset_risk_item_mapping、t_acm_industry_name_mapping) 提供业务映射关系
- **统计表** 通过汇总、分组、计算等方式从基础数据生成各类风险分析报表
- **导入表** (t_acm_single_legal_entity_risk) 通过Excel导入方式获取外部数据

##### 1.3.1.2 表名字典

*列出资产配置状况模块和资产信用状况模块所有表信息*

**注意：** 这两个模块都特别依赖资产宽表模块中的资产整体明细数据TB0016表作为基础数据源

| 表编号    | 表中文名                | 表英文名                                | 模块   | 备注                       |
| ------ | ------------------- | ----------------------------------- | ---- | ------------------------ |
| TB0001 | 资产整体明细表（引用自资产宽表模块） | t_ast_asset_detail_overall          | ast  | 引用自资产宽表模块，作为两个模块的基础数据源 |
| TB0002 | 资金运用规模表             | t_asm_fund_utilization_scale        | asm  | 资金运用规模统计表                |
| TB0003 | 资金运用比例监管表           | t_asm_fund_utilization_ratio        | asm  | 资金运用比例监管统计表              |
| TB0004 | 固定收益类投资资产剩余期限分布表     | t_asm_fixed_income_term_dist        | asm  | 固定收益类投资资产剩余期限分布统计表        |
| TB0005 | 风险10日VaR值表           | t_asm_risk_10day_var                | asm  | 风险10日VaR值统计表              |
| TB0006 | 资产负债表（引用自负债产品模块）     | t_liab_balance_sheet                | liab | 引用自负债产品模块，存储资产负债表数据       |
| TB0007 | 融资杠杆比例表             | t_asm_financing_leverage_ratio      | asm  | 融资杠杆比例统计表                |
| TB0008 | 固定收益类投资资产信用评级表       | t_acm_fixed_income_credit_rating    | acm  | 固定收益类投资资产信用评级统计表          |
| TB0009 | 存款及同业存单表            | t_acm_deposit_interbank_cd          | acm  | 存款及同业存单统计表               |
| TB0010 | 固定收益类投资资产外部评级剩余期限分布表 | t_acm_fixed_income_rating_term_dist | acm  | 固定收益类投资资产外部评级剩余期限分布统计表    |
| TB0011 | 保险资产风险五级分类状况表        | t_acm_asset_risk_five_level         | acm  | 保险资产风险五级分类状况统计表           |
| TB0012 | 保险资产风险项目映射表          | t_acm_asset_risk_item_mapping       | acm  | 保险资产风险项目与五级分类统计标识的映射关系表   |
| TB0013 | 行业集中度风险表            | t_acm_industry_concentration_risk   | acm  | 行业集中度风险统计表               |
| TB0014 | 行业名称映射表             | t_acm_industry_name_mapping         | acm  | 行业统计标识与行业名称的映射关系表         |
| TB0015 | 单一资产投资集中度风险表         | t_acm_single_asset_concentration    | acm  | 单一资产投资集中度风险统计表            |
| TB0016 | 单一法人主体集中度风险表         | t_acm_single_legal_entity_risk      | acm  | 单一法人主体集中度风险统计表            |

##### 1.3.1.3 表集

*描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_by、update_by、is_del等字段不需要描述，后续生成DDL时会自动补充*
*唯一索引：这里唯一索引是指业务字段单字段或多字段组合的唯一性，如多字段组合的情况，在所有字段唯一索引列设置为"是"*
*说明：描述字段的作用，枚举类型字段格式为描述,value1:label1[,...,valueN:labelN]，这里一定要注意格式，在后续开发步骤中会通过这里的字段枚举描述生成字段数据*

**（1）TB0001 - 资产整体明细表（引用自资产宽表模块）**

*此表引用自资产宽表模块的t_ast_asset_detail_overall表，作为资产配置状况模块和资产信用状况模块的基础数据源。*

*由于TB0001表字段较多，完整的表结构定义请参考资产宽表模块设计文档中的TB0016表定义*

**（2）TB0002 - 资金运用规模表**

*存储资金运用规模统计数据，通过汇总资产整体明细表数据生成*

| 字段名                              | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**            | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **item_name**                    | varchar | 100   | 否   | 是    | 无   | 项目名称                                                                                                                                                                                                                                                                                                                                             |
| **item_classification_level**    | varchar | 5     | 否   | 否    | 无   | 项目分级标识                                                                                                                                                                                                                                                                                                                                           |
| **data_type**                    | varchar | 20    | 否   | 是    | 无   | 数据类型,01:账面余额,02:账面价值                                                                                                                                                                                                                                                                                                                            |
| general_account                  | decimal | 18,2  | 是   | 否    | 0   | 普通账户金额                                                                                                                                                                                                                                                                                                                                           |
| traditional_account              | decimal | 18,2  | 是   | 否    | 0   | 传统账户金额                                                                                                                                                                                                                                                                                                                                           |
| capital_supplement_bond_account  | decimal | 18,2  | 是   | 否    | 0   | 资本补充债账户金额                                                                                                                                                                                                                                                                                                                                        |
| bonus_account                    | decimal | 18,2  | 是   | 否    | 0   | 分红账户金额                                                                                                                                                                                                                                                                                                                                           |
| universal_account                | decimal | 18,2  | 是   | 否    | 0   | 万能账户金额                                                                                                                                                                                                                                                                                                                                           |

**（3）TB0003 - 资金运用比例监管表**

*存储资金运用比例监管数据，用于监控特定资产类型在各账户中的使用情况*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50    | 否   | 是    | 无   | 资产小小类,引用字典ast_asset_sub_sub_category |
| **account_name**           | varchar | 50    | 否   | 是    | 无   | 账户名称,引用字典ast_account_name_mapping |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |
| remark                     | varchar | 500   | 是   | 否    | 无   | 备注                                                                                                                                                                                                                                                                                                                                               |

**（4）TB0004 - 固定收益类投资资产剩余期限分布表**

*存储固定收益类投资资产的剩余期限分布情况，用于分析不同类型固收资产的期限结构*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                        |
| -------------------------- | ------- | ----- | --- | ---- | --- |-----------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                      |
| **domestic_foreign**       | varchar | 20    | 否   | 是    | 无   | 境内外标识,引用字典ast_domestic_foreign                                                                            |
| **fixed_income_term_category** | varchar | 50    | 否   | 是    | 无   | 固收资产剩余期限资产分类,引用字典ast_fixed_income_term_category                                                           |
| **remaining_term_flag**    | varchar | 50    | 否   | 是    | 无   | 剩余期限标识描述,01:1年及以内,02:1-3年（含3年）,03:3-5年(含5年),04:5-7年（含7年）,05:7-10年（含10年）,06:10-15年（含15年）,07:15年以上,08:无明确期限 |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                    |

**（5）TB0005 - 风险10日VaR值表**

*存储风险10日VaR值统计数据，用于分析不同资产类型的风险价值分布*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **domestic_foreign**       | varchar | 20    | 否   | 是    | 无   | 境内外标识,引用字典ast_domestic_foreign                                                                                                                                                                                                                                                                                                                   |
| **item_category**          | varchar | 50    | 否   | 是    | 无   | 项目分类,引用字典ast_asset_sub_sub_category（债券型基金、上市普通股票、证券投资基金等）                                                                                                                                                                                                                                                                                |
| **sample_period**          | varchar | 10    | 否   | 是    | 无   | 样本期限,01:1年,02:3年                                                                                                                                                                                                                                                                                                                                |
| var_value                  | decimal | 18,2  | 是   | 否    | 0   | VAR值                                                                                                                                                                                                                                                                                                                                            |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |
| var_book_value_ratio       | decimal | 10,4  | 是   | 否    | 0   | VAR值与账面价值比率                                                                                                                                                                                                                                                                                                                                      |

**（6）TB0006 - 资产负债表（引用自负债产品模块）**

*此表引用自负债产品模块的t_liab_balance_sheet表，存储资产负债表数据。*

*完整的表结构定义请参考负债产品模块设计文档中的TB0002表定义*

**（7）TB0007 - 融资杠杆比例表**

*存储融资杠杆比例统计数据，用于监控融资杠杆风险*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **item_name**              | varchar | 100   | 否   | 是    | 无   | 项目名称                                                                                                                                                                                                                                                                                                                                             |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |

**（8）TB0008 - 固定收益类投资资产信用评级表**

*存储固定收益类投资资产的信用评级分布情况，用于分析不同信用等级资产的分布*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **domestic_foreign**       | varchar | 20    | 否   | 是    | 无   | 境内外标识,引用字典ast_domestic_foreign                                                                                                                                                                                                                                                                                                                   |
| **fixed_income_term_category** | varchar | 50    | 否   | 是    | 无   | 固收资产剩余期限资产分类,引用字典ast_fixed_income_term_category                                                                                                                                                                                                                                                                                              |
| **credit_rating_category** | varchar | 20    | 否   | 是    | 无   | 信用评级分类,引用字典ast_credit_rating                                                                                                                                                                                                                                                                                                                   |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |

**（9）TB0009 - 存款及同业存单表**

*存储存款及同业存单的银行分类分布情况，用于分析不同银行类型的资产配置*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **asset_sub_sub_category** | varchar | 50    | 否   | 是    | 无   | 资产小小类,引用字典ast_asset_sub_sub_category                                                                                                                                                                                                                                                                                                             |
| **bank_classification**    | varchar | 100   | 否   | 是    | 无   | 银行分类,引用字典ast_bank_classification                                                                                                                                                                                                                                                                                                                 |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |

**（10）TB0010 - 固定收益类投资资产外部评级剩余期限分布表**

*存储固定收益类投资资产的外部评级与剩余期限交叉分布情况，用于分析不同信用等级资产的期限结构*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **domestic_foreign**       | varchar | 20    | 否   | 是    | 无   | 境内外标识,引用字典ast_domestic_foreign                                                                                                                                                                                                                                                                                                                   |
| **credit_rating_category** | varchar | 20    | 否   | 是    | 无   | 信用评级分类,引用字典ast_credit_rating                                                                                                                                                                                                                                                                                                                   |
| **fixed_income_term_category** | varchar | 50    | 否   | 是    | 无   | 固收资产剩余期限资产分类,引用字典ast_fixed_income_term_category                                                                                                                                                                                                                                                                                              |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |

**（11）TB0011 - 保险资产风险五级分类状况表**

*存储保险资产的风险五级分类状况，用于分析不同类型资产的风险分布*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **item_name**              | varchar | 50    | 否   | 是    | 无   | 项目名称,01:权益类,02:固定收益类,03:不动产类,04:其他投资资产                                                                                                                                                                                                                                                                                                      |
| **five_level_statistics_flag** | varchar | 50    | 否   | 是    | 无   | 五级分类资产统计标识,引用字典ast_five_level_statistics_flag                                                                                                                                                                                                                                                                                                 |
| **five_level_classification** | varchar | 20    | 否   | 是    | 无   | 五级分类,引用字典ast_five_level_classification                                                                                                                                                                                                                                                                                                           |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |

**（12）TB0012 - 保险资产风险项目映射表**

*存储保险资产风险项目与五级分类统计标识的映射关系，用于TB0011表的数据生成*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **item_name**              | varchar | 50    | 否   | 是    | 无   | 项目名称,01:权益类,02:固定收益类,03:不动产类,04:其他投资资产                                                                                                                                                                                                                                                                                                      |
| **five_level_statistics_flag** | varchar | 50    | 否   | 是    | 无   | 五级分类资产统计标识,引用字典ast_five_level_statistics_flag                                                                                                                                                                                                                                                                                                 |

**（13）TB0013 - 行业集中度风险表**

*存储行业集中度风险统计数据，用于分析不同行业的资产集中度风险*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **industry_name**          | varchar | 50    | 否   | 是    | 无   | 行业名称                                                                                                                                                                                                                                                                                                                                             |
| industry_statistics_flag   | varchar | 50    | 否   | 否    | 无   | 行业统计标识,引用字典acm_industry_statistics                                                                                                                                                                                                                                                                                                             |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |
| weight_percentage          | decimal | 10,2  | 是   | 否    | 0   | 权重百分比                                                                                                                                                                                                                                                                                                                                            |
| industry_concentration     | decimal | 10,2  | 是   | 否    | 0   | 行业集中度                                                                                                                                                                                                                                                                                                                                            |
| counterparty_ranking       | varchar | 20    | 是   | 否    | 无   | 交易对手排序                                                                                                                                                                                                                                                                                                                                           |

**（14）TB0014 - 行业名称映射表**

*存储行业统计标识与行业名称的映射关系，用于TB0013行业集中度风险表的数据生成*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **industry_name**          | varchar | 50    | 否   | 是    | 无   | 行业名称,01:1农林牧渔,02:2煤炭,03:3基础化工,04:4黑色金属,05:5有色金属,06:6电子元器件,07:7家用电器,08:8食品饮料,09:9纺织服饰,10:10轻工制造,11:11医药生物,12:12公共事业,13:13交通运输,14:14房地产,15:15商贸零售,16:16餐饮旅游,17:17综合,18:18建筑材料,19:19建筑装饰                                                                                                                                                                                                                                                                                                                                      |
| **industry_statistics_flag** | varchar | 20    | 否   | 是    | 无   | 行业统计标识,引用字典acm_industry_statistics                                                                                                                                                                                                                                                                                                             |

**（15）TB0015 - 单一资产投资集中度风险表**

*存储单一资产投资集中度风险统计数据，用于分析单一资产的投资集中度风险*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **alm_asset_name**         | varchar | 100   | 否   | 是    | 无   | ALM资产名称                                                                                                                                                                                                                                                                                                                                          |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |
| single_asset_ranking       | varchar | 10    | 是   | 否    | 无   | 单一资产排序                                                                                                                                                                                                                                                                                                                                           |

**（16）TB0016 - 单一法人主体集中度风险表**

*存储单一法人主体集中度风险统计数据，通过导入方式获取数据*

| 字段名                        | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                               |
| -------------------------- | ------- | ----- | --- | ---- | --- |--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **accounting_period**      | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM（如202406）                                                                                                                                                                                                                                                                                                                             |
| **single_legal_entity_name** | varchar | 100   | 否   | 是    | 无   | 单一法人主体名称                                                                                                                                                                                                                                                                                                                                         |
| book_balance               | decimal | 18,2  | 是   | 否    | 0   | 账面余额金额                                                                                                                                                                                                                                                                                                                                           |
| book_value                 | decimal | 18,2  | 是   | 否    | 0   | 账面价值金额                                                                                                                                                                                                                                                                                                                                           |

### 1.4 用例列表

*列出所有用例场景，用例可理解为用户为完成某个作业目标而执行的一系列操作的集合，包括页面跳转、接口调用。对于批处理场景，是一系列加工处理步骤的集合*

| 用例编号   | 用例名称         | 用例描述 | 模块编号   |
| ------ | ------------ | ---- | ------ |
| UC0001 | 资金运用规模表生成 | 基于资产整体明细表，按照项目分级汇总生成资金运用规模表 | MD0001 |
| UC0002 | 资金运用比例监管表生成 | 基于资产整体明细表，按照资产小小类和账户名称汇总生成资金运用比例监管表 | MD0001 |
| UC0003 | 固定收益类投资资产剩余期限分布表生成 | 基于资产整体明细表，按照境内外、固收资产分类和剩余期限汇总生成期限分布表 | MD0001 |
| UC0004 | 风险10日VaR值表生成 | 基于资产整体明细表，按照境内外、项目分类和样本期限汇总生成风险VaR值表 | MD0001 |
| UC0005 | 资产负债表查询 | 查询资产负债表数据，支持按账期、类别等条件筛选 | MD0001 |
| UC0006 | 融资杠杆比例表生成 | 基于资产负债表数据，按照项目名称汇总生成融资杠杆比例表 | MD0001 |
| UC0007 | 固定收益类投资资产信用评级表生成 | 基于资产整体明细表，按照境内外、固收资产分类和信用评级汇总生成信用评级表 | MD0002 |
| UC0008 | 存款及同业存单表生成 | 基于资产整体明细表，按照资产小小类和银行分类汇总生成存款及同业存单表 | MD0002 |
| UC0009 | 固定收益类投资资产外部评级剩余期限分布表生成 | 基于资产整体明细表，按照境内外、信用评级和固收资产分类汇总生成外部评级期限分布表 | MD0002 |
| UC0010 | 保险资产风险五级分类状况表生成 | 基于资产整体明细表，按照项目名称、五级分类统计标识和五级分类汇总生成风险五级分类表 | MD0002 |
| UC0011 | 保险资产风险项目映射表维护 | 维护保险资产风险项目与五级分类统计标识的映射关系，支持增删改查操作 | MD0002 |
| UC0012 | 行业集中度风险表生成 | 基于资产整体明细表，按照行业统计标识汇总生成行业集中度风险表 | MD0002 |
| UC0013 | 行业名称映射表维护 | 维护行业统计标识与行业名称的映射关系，支持增删改查操作 | MD0002 |
| UC0014 | 单一资产投资集中度风险表生成 | 基于资产整体明细表，按照ALM资产名称汇总生成单一资产投资集中度风险表 | MD0002 |
| UC0015 | 单一法人主体集中度风险表导入 | 通过Excel导入方式维护单一法人主体集中度风险表数据 | MD0002 |

## 2. 业务概念与术语

*定义业务相关的概念和术语，便于理解业务逻辑*

### 2.1 资产配置状况模块术语

- **资金运用规模**：按照不同项目分级统计的资金运用情况
- **项目分级**：根据资产配置状况分类表确定的项目层级标识
- **账面余额**：资产的账面记录金额
- **账面价值**：账面余额扣除减值准备后的净值

### 2.2 资产信用状况模块术语

- **信用评级分布**：按照信用评级等级统计的资产分布情况
- **风险五级分类**：按照风险程度划分的五个等级分类
- **单一资产集中度**：单一资产占总资产的比例情况

## 3. 功能设计

### 3.1 资产配置状况模块功能

#### 3.1.1 资金运用规模表生成

*基于资产整体明细表TB0001，按照项目分级汇总生成资金运用规模表TB0002*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按照账户名称、资产分类层级汇总账面余额或账面价值
3. 关联资产配置状况分类表(t_ast_asset_allocation_category)确定项目分级标识
4. 按照项目分级生成汇总数据

**详细计算规则：**

**普通账户计算：**
- 普通账户 = 传统账户 + 分红账户 + 万能账户

**各账户汇总逻辑：**
- **传统账户：** 汇总整体资产明细表的账面余额或账面价值
  - 根据：账户名称='传统账户' AND 资产一级分类汇总，并关联资产配置状况分类表，看资产一级分类对应的项目所属的分类级别，然后标到项目分级标识
  - 同理：根据账户名称='传统账户' AND 资产二级分类汇总
  - 同理：根据账户名称='传统账户' AND 资产三级分类汇总
- **资本补充债账户：** 同传统账户逻辑
- **分红账户：** 同传统账户逻辑
- **万能账户：** 同传统账户逻辑

**SQL汇总逻辑：**
```sql
SELECT
    accounting_period,
    item_name,
    item_classification_level,
    data_type,
    SUM(CASE WHEN account_name = '传统账户' THEN book_balance ELSE 0 END) as traditional_account,
    SUM(CASE WHEN account_name = '资本补充债账户' THEN book_balance ELSE 0 END) as capital_supplement_bond_account,
    SUM(CASE WHEN account_name = '分红账户' THEN book_balance ELSE 0 END) as bonus_account,
    SUM(CASE WHEN account_name = '万能账户' THEN book_balance ELSE 0 END) as universal_account,
    SUM(CASE WHEN account_name IN ('传统账户','分红账户','万能账户') THEN book_balance ELSE 0 END) as general_account
FROM t_ast_asset_detail_overall t1
JOIN t_ast_asset_allocation_category t2 ON t1.asset_allocation_level1 = t2.category_name
WHERE t1.accounting_period = #{accountingPeriod}
GROUP BY accounting_period, item_name, item_classification_level, data_type
```

#### 3.1.2 资金运用比例监管表生成

*基于资产整体明细表TB0001，按照资产小小类和账户名称汇总生成资金运用比例监管表TB0003*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 筛选特定资产小小类（如REITS）的数据
3. 按照账户名称汇总账面余额
4. 排除投连账户数据
5. 生成资金运用比例监管表数据

**详细计算规则：**

**账面余额计算：**
```sql
SELECT
    accounting_period,
    asset_sub_sub_category,
    account_name,
    SUM(book_balance) as book_balance,
    '在银行间市场、证券交易所市场等国务院同意设立的交易市场交易的不动产类资产' as remark
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category = 'REITS'  -- 筛选REITS资产
    AND account_name <> '投连账户'  -- 排除投连账户
GROUP BY accounting_period, asset_sub_sub_category, account_name
```

**备注字段：**
- 备注默认为："在银行间市场、证券交易所市场等国务院同意设立的交易市场交易的不动产类资产"

#### 3.1.3 固定收益类投资资产剩余期限分布表生成

*基于资产整体明细表TB0001，按照境内外、固收资产分类和剩余期限汇总生成固定收益类投资资产剩余期限分布表TB0004*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按照境内外标识分组
3. 按照固收资产剩余期限资产分类分组
4. 按照剩余期限标识分组
5. 汇总账面余额
6. 排除投连账户数据
7. 生成固定收益类投资资产剩余期限分布表数据

**详细计算规则：**

**账面余额计算：**

**境内资产：**
```sql
SELECT
    accounting_period,
    '境内' as domestic_foreign,
    fixed_income_sub_category,
    remaining_term_flag,
    SUM(book_balance) as book_balance
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND domestic_foreign = '境内'
    AND fixed_income_sub_category IS NOT NULL
    AND remaining_term_flag IS NOT NULL
    AND account_name <> '投连账户'
GROUP BY accounting_period, fixed_income_sub_category, remaining_term_flag
```

**境外资产：**
```sql
SELECT
    accounting_period,
    '境外' as domestic_foreign,
    fixed_income_sub_category,
    remaining_term_flag,
    SUM(book_balance) as book_balance
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND domestic_foreign = '境外'
    AND remaining_term_flag IS NOT NULL
    AND account_name <> '投连账户'
GROUP BY accounting_period, remaining_term_flag
```

#### 3.1.4 风险10日VaR值表生成

*基于资产整体明细表TB0001，按照境内外、项目分类和样本期限汇总生成风险10日VaR值表TB0005*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按照境内外标识分组
3. 按照项目分类（债券型基金、上市普通股票、证券投资基金）分组
4. 按照样本期限（1年、3年）分组
5. 汇总VAR值和账面价值
6. 计算VAR值与账面价值比率
7. 生成风险10日VaR值表数据

**详细计算规则：**

**VaR值计算：**

**债券型基金（1年期）：**
```sql
SELECT
    accounting_period,
    '境内' as domestic_foreign,
    '债券型基金' as item_category,
    '1年' as sample_period,
    SUM(var_1_year) as var_value,
    SUM(book_value) as book_value
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category = '债券型基金'
GROUP BY accounting_period
```

**债券型基金（3年期）：**
```sql
SELECT
    accounting_period,
    '境内' as domestic_foreign,
    '债券型基金' as item_category,
    '3年' as sample_period,
    SUM(var_3_year) as var_value,
    SUM(book_value) as book_value
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category = '债券型基金'
GROUP BY accounting_period
```

**上市普通股票+证券投资基金（境内外分别计算）：**
```sql
-- 境内1年期
SELECT
    accounting_period,
    '境内' as domestic_foreign,
    '上市普通股票+证券投资基金' as item_category,
    '1年' as sample_period,
    SUM(var_1_year) as var_value,
    SUM(book_value) as book_value
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category IN ('上市普通股票', '证券投资基金')
    AND domestic_foreign = '境内'
GROUP BY accounting_period

-- 境外1年期
SELECT
    accounting_period,
    '境外' as domestic_foreign,
    '上市普通股票+证券投资基金' as item_category,
    '1年' as sample_period,
    SUM(var_1_year) as var_value,
    SUM(book_value) as book_value
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category IN ('上市普通股票', '证券投资基金')
    AND domestic_foreign = '境外'
GROUP BY accounting_period
```

**VaR值/账面价值比例计算：**
- VaR值/账面价值 = 风险10日VaR值表.var_value / 风险10日VaR值表.book_value

#### 3.1.5 融资杠杆比例表生成

*基于资产负债表TB0006，按照项目名称汇总生成融资杠杆比例表TB0007*

**业务逻辑：**
1. 从资产负债表(t_liab_balance_sheet)获取基础数据
2. 按照项目名称分组统计
3. 计算本季末融入资金余额（卖出回购金融资产款）
4. 计算上季末债券回购融入资金余额（上季度卖出回购金融资产款）
5. 计算上季末独立账户资金余额（上季度独立账户负债）
6. 生成融资杠杆比例表数据

**详细计算规则：**

**账面价值计算：**

**本季末融入资金余额：**
```sql
SELECT
    #{accountingPeriod} as accounting_period,
    '本季末融入资金余额' as item_name,
    ending_balance as book_value
FROM t_liab_balance_sheet
WHERE accounting_period = #{accountingPeriod}
    AND item_name = '卖出回购金融资产款'
```

**上季末融入资金余额：**
```sql
SELECT
    #{accountingPeriod} as accounting_period,
    '上季末融入资金余额' as item_name,
    ending_balance as book_value
FROM t_liab_balance_sheet
WHERE accounting_period = #{lastQuarterPeriod}  -- 上季度末所属账期
    AND item_name = '卖出回购金融资产款'
```

**上季末独立账户资金余额：**
```sql
SELECT
    #{accountingPeriod} as accounting_period,
    '上季末独立账户资金余额' as item_name,
    ending_balance as book_value
FROM t_liab_balance_sheet
WHERE accounting_period = #{lastQuarterPeriod}  -- 上季度末所属账期
    AND item_name = '独立账户负债'
```

### 3.2 资产信用状况模块功能

#### 3.2.1 固定收益类投资资产信用评级表生成

*基于资产整体明细表TB0001，按照境内外、固收资产分类和信用评级汇总生成固定收益类投资资产信用评级表TB0008*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按照境内外标识分组
3. 按照固收资产剩余期限资产分类分组
4. 按照信用评级分类分组
5. 汇总账面余额
6. 生成固定收益类投资资产信用评级表数据

**详细计算规则：**

**账面余额计算：**

**境内资产：**
```sql
SELECT
    accounting_period,
    '境内' as domestic_foreign,
    fixed_income_sub_category,
    credit_rating_category,
    SUM(book_balance) as book_balance
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND domestic_foreign = '境内'
    AND fixed_income_sub_category IS NOT NULL
    AND credit_rating_category IS NOT NULL
GROUP BY accounting_period, fixed_income_sub_category, credit_rating_category
```

**境外资产：**
```sql
SELECT
    accounting_period,
    '境外' as domestic_foreign,
    fixed_income_sub_category,
    credit_rating_category,
    SUM(book_balance) as book_balance
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND domestic_foreign = '境外'
    AND credit_rating_category IS NOT NULL
GROUP BY accounting_period, credit_rating_category
```

#### 3.2.2 存款及同业存单表生成

*基于资产整体明细表TB0001，按照资产小小类和银行分类汇总生成存款及同业存单表TB0009*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 筛选资产小小类为存款或同业存单的数据
3. 按照银行分类分组统计
4. 汇总账面价值
5. 排除投连账户数据
6. 生成存款及同业存单表数据

**详细计算规则：**

**账面价值计算：**
```sql
SELECT
    accounting_period,
    asset_sub_sub_category,
    bank_classification,
    SUM(book_value) as book_value
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND asset_sub_sub_category IN ('存款', '同业存单')
    AND bank_classification IS NOT NULL
    AND account_name <> '投连账户'
GROUP BY accounting_period, asset_sub_sub_category, bank_classification
```

#### 3.2.3 固定收益类投资资产外部评级剩余期限分布表生成

*基于资产整体明细表TB0001，按照境内外、信用评级和固收资产分类汇总生成固定收益类投资资产外部评级剩余期限分布表TB0010*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按照境内外标识分组
3. 按照信用评级分类分组
4. 按照固收资产剩余期限资产分类分组
5. 汇总账面余额
6. 生成固定收益类投资资产外部评级剩余期限分布表数据

**详细计算规则：**

**账面余额计算：**
```sql
SELECT
    accounting_period,
    domestic_foreign,
    credit_rating_category,
    fixed_income_sub_category,
    SUM(book_balance) as book_balance
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND domestic_foreign IS NOT NULL
    AND credit_rating_category IS NOT NULL
    AND fixed_income_sub_category IS NOT NULL
GROUP BY accounting_period, domestic_foreign, credit_rating_category, fixed_income_sub_category
```

#### 3.2.4 保险资产风险五级分类状况表生成

*基于资产整体明细表TB0001，按照项目名称、五级分类统计标识和五级分类汇总生成保险资产风险五级分类状况表TB0011*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 关联保险资产风险项目映射表(t_acm_asset_risk_item_mapping)确保项目与五级分类统计标识的对应关系正确
3. 按照项目名称分组
4. 按照五级分类资产统计标识分组
5. 按照五级分类分组
6. 汇总账面余额
7. 生成保险资产风险五级分类状况表数据

**详细计算规则：**

**账面余额计算：**
```sql
SELECT
    t1.accounting_period,
    t2.item_name,
    t1.five_level_statistics_flag,
    t1.five_level_classification,
    SUM(t1.book_balance) as book_balance
FROM t_ast_asset_detail_overall t1
JOIN t_acm_asset_risk_item_mapping t2
    ON t1.accounting_period = t2.accounting_period
    AND t1.five_level_statistics_flag = t2.five_level_statistics_flag
WHERE t1.accounting_period = #{accountingPeriod}
    AND t1.five_level_statistics_flag IS NOT NULL
    AND t1.five_level_classification IS NOT NULL
GROUP BY t1.accounting_period, t2.item_name, t1.five_level_statistics_flag, t1.five_level_classification
```

**统一计算规则：**
- 从整体资产明细表汇总账面余额
- 匹配条件：账期相同（整体资产明细表.账期）
- 五级分类资产统计标识相同（整体资产明细表.五级分类资产统计标识）
- 五级分类相同（整体资产明细表.五级分类）
- 通过映射表确保项目与五级分类资产统计标识的对应关系正确

#### 3.2.5 保险资产风险项目映射表维护

*维护保险资产风险项目与五级分类统计标识的映射关系，支持增删改查操作*

**业务逻辑：**
1. 提供保险资产风险项目映射表的维护界面
2. 支持按账期、项目名称查询映射关系
3. 支持添加新的项目与五级分类统计标识的映射关系
4. 支持修改现有映射关系
5. 支持删除不需要的映射关系
6. 映射表数据作为TB0011保险资产风险五级分类状况表生成的基础数据

#### 3.2.6 行业集中度风险表生成

*基于资产整体明细表TB0001，按照行业统计标识汇总生成行业集中度风险表TB0013*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 按资产进行去重汇总（相同资产在不同账户下会有多条记录）
3. 根据行业名称映射表(t_acm_industry_name_mapping)确定行业统计标识对应的行业名称
4. 按行业汇总账面价值
5. 排除投连账户数据
6. 计算权重百分比（当前行业账面价值 ÷ 所有行业账面价值总和）
7. 计算行业集中度（权重的平方 × 10000）
8. 按账面价值降序排名
9. 生成行业集中度风险表数据

**详细计算规则：**

**账面价值计算规则：**
- 资产去重汇总：因为资产整体明细表区分账户，相同资产在不同账户下会有多条记录，需要先按资产进行去重汇总
- 行业汇总：根据映射表确定行业统计标识对应的行业名称，然后汇总整体资产明细表的账面价值
- 排除投连账户：汇总时排除账户名称为"投连账户"的记录

**权重计算规则：**
- 当前行业的账面价值 ÷ 所有行业账面价值的总和（排除投连账户）

**行业集中度计算规则：**
- 权重的平方 × 10000

**交易对手排序规则：**
- 按账面价值降序排名，使用RANK函数

```sql
-- 第一步：资产去重汇总
WITH asset_dedup AS (
    SELECT
        accounting_period,
        alm_asset_name,
        industry_statistics_flag,
        SUM(book_value) as book_value
    FROM t_ast_asset_detail_overall
    WHERE accounting_period = #{accountingPeriod}
        AND account_name <> '投连账户'
        AND industry_statistics_flag IS NOT NULL
    GROUP BY accounting_period, alm_asset_name, industry_statistics_flag
),
-- 第二步：按行业汇总
industry_summary AS (
    SELECT
        t1.accounting_period,
        t2.industry_name,
        t1.industry_statistics_flag,
        SUM(t1.book_value) as book_value
    FROM asset_dedup t1
    JOIN t_acm_industry_name_mapping t2
        ON t1.accounting_period = t2.accounting_period
        AND t1.industry_statistics_flag = t2.industry_statistics_flag
    GROUP BY t1.accounting_period, t2.industry_name, t1.industry_statistics_flag
),
-- 第三步：计算总和用于权重计算
total_value AS (
    SELECT SUM(book_value) as total_book_value
    FROM industry_summary
)
-- 第四步：最终结果
SELECT
    t1.accounting_period,
    t1.industry_name,
    t1.industry_statistics_flag,
    t1.book_value,
    ROUND(t1.book_value / t2.total_book_value * 100, 2) as weight_percentage,
    ROUND(POWER(t1.book_value / t2.total_book_value, 2) * 10000, 2) as industry_concentration,
    RANK() OVER (ORDER BY t1.book_value DESC) as counterparty_ranking
FROM industry_summary t1
CROSS JOIN total_value t2
ORDER BY t1.book_value DESC
```

#### 3.2.7 行业名称映射表维护

*维护行业统计标识与行业名称的映射关系，支持增删改查操作*

**业务逻辑：**
1. 提供行业名称映射表的维护界面
2. 支持按账期、行业名称查询映射关系
3. 支持添加新的行业统计标识与行业名称的映射关系
4. 支持修改现有映射关系
5. 支持删除不需要的映射关系
6. 映射表数据作为TB0013行业集中度风险表生成的基础数据

#### 3.2.8 单一资产投资集中度风险表生成

*基于资产整体明细表TB0001，按照ALM资产名称汇总生成单一资产投资集中度风险表TB0015*

**业务逻辑：**
1. 从资产整体明细表(t_ast_asset_detail_overall)获取基础数据
2. 筛选单一资产统计标识为"考虑"的资产
3. 按ALM资产名称分组汇总
4. 汇总账面余额和账面价值（不考虑账户区分）
5. 排除投连账户数据
6. 按账面余额使用RANK函数进行排序
7. 生成单一资产投资集中度风险表数据

**详细计算规则：**

**ALM资产名称：**
- 整体资产明细表.ALM资产名称
- 筛选条件：整体资产明细表.单一资产统计标识='考虑'

**账面余额计算：**
- 这里也是在汇总前先根据单一资产统计标识为考虑的，然后把证券代码相同的，及同一资产的账面余额相加，就不考虑账户了
- sum(整体资产明细表.账面余额)
- 匹配条件：整体资产明细表.ALM资产名称 AND 整体资产明细表.账户名称<>投连账户

**账面价值计算：**
- 同上，只不过是汇总账面价值

**单一资产排序：**
- 参见公式：对账面余额 RANK函数排序

```sql
SELECT
    accounting_period,
    alm_asset_name,
    SUM(book_balance) as book_balance,
    SUM(book_value) as book_value,
    RANK() OVER (ORDER BY SUM(book_balance) DESC) as single_asset_ranking
FROM t_ast_asset_detail_overall
WHERE accounting_period = #{accountingPeriod}
    AND single_asset_statistics_flag = '考虑'
    AND account_name <> '投连账户'
    AND alm_asset_name IS NOT NULL
GROUP BY accounting_period, alm_asset_name
ORDER BY SUM(book_balance) DESC
```

#### 3.2.9 单一法人主体集中度风险表导入

*通过Excel导入方式维护单一法人主体集中度风险表TB0016数据*

**业务逻辑：**
1. 提供Excel模板下载功能
2. 支持Excel文件上传和数据导入
3. 数据校验：账期格式、法人主体名称唯一性、数值字段格式等
4. 支持数据覆盖导入（按账期清空后重新导入）
5. 导入成功后提供数据预览和确认功能
6. 支持导入历史记录查询



# 附录1

## 接口请求参数类型：

| 类型       | 说明                      | 示例                             |
| -------- | ----------------------- | ------------------------------ |
| string   | 文本类型，需定义长度限制            | "username": "john_doe"         |
| number   | 包括整数和浮点数                | "age": 25                      |
| boolean  | 布尔值（true/false）         | "is_active": true              |
| array    | 数组，需声明元素类型              | "tags": ["urgent", "feature"]  |
| object   | 键值对嵌套结构                 | "address": {"city": "Beijing"} |
| date     | 日期（YYYY-MM-DD）          | "2025-06-19"                   |
| datetime | 时间（YYYY-MM-DD HH:MM:SS） | "2025-06-19 14:30:00"          |
| file     | 文件上传                    | 文件二进制数据                        |

# 附录2

## 1. 功能逻辑表达方法：

### 1.1 面向对象法

**核心原则**：以数据载体为核心构建逻辑描述，确保 AI 可精准解析数据流向

- 必须明确指定数据操作对象（表 / 缓存 / 文件），避免模糊动作词汇
- 采用 "对象 + 字段 + 操作" 的三元组结构，示例：`从TB0001表查询字段A，对字段B执行聚合操作后写入TB0002表`

**【正例】：**
**步骤1.** 现金流汇总
以账期为202412作为条件查询TB0001表，以账期,现金流类型,基点类型,久期类型,设计类型,是否中短期作为汇总字段,针对cash_val_set的value值按对应序号进行加合汇总，所有数据汇总完成后写入TB0002表
**说明：**

- 这里所面向的对象是表TB0001和TB0002，AI会自动在文档中检索表名并转换为英文名
- 可以直接使用字段的中文名称进行描述，AI会自动转为字段英文名生成代码和SQL，但中文名可能作为前缀出现在文档的非字段描述位置，为了避免识别问题，建议使用英文名描述
- 不需要详细描述DTO和Entity赋值逻辑，AI会自动识别赋值并写入对应表

**【反例】：**
**步骤1.** 现金流汇总
请对账期为202412的现金流数据实现汇总
**说明：**

- 没有描述清楚数据来源
- 没有描述清楚数据加工方法
- 没有描述结果数据如何处理

### 1.2 符号引用法

* 前面的面向对象的方法中可以看到，表名使用TB0001和TB0002这样的符号或者叫编号，这种方式是符号引用法。通过符号引用可以减少字数，使逻辑表达更清晰，符号也具有唯一性，不会出现AI引用位置出错的问题，如中文容易出现引用错误。文档中具有符号的对象有模块、表、接口、用例、页面等，AI可以通过符号查找到对应对象的描述信息。

| 对象类型 | 符号格式     | 示例     |
| ---- | -------- | ------ |
| 数据表  | TB+4 位数字 | TB0001 |
| 接口   | IF+4 位数字 | IF0002 |
| 模块   | MD+4 位数字 | MD0003 |
| 用例   | UC+4 位数字 | UC0004 |
| 页面   | PG+4 位数字 | PG0004 |

**符号引用优势**：

- 唯一性：避免中文同名对象歧义（如 "用户表" 可能指表TB0005，也可能是“用户表达意思”的前缀并不表示某张表）
- 轻量化：符号长度固定，提升文档可读性
- 机器可解析：AI 可通过符号直接关联元数据定义

### 1.3 公式表达法

- 对于涉及复杂计算的场景，可以通过变量定义 + 公式方式描述计算逻辑

**【示例】：**

- i ∈ [0,1272]
- 现金流金额[i] = TB0002.cash_val_set[i].value
- 现金流现值[i] = TB0002.present_cash_val_set[i].valueue
- 折现因子[i] = TB0003.factor_val_set[i].value
- 久期值[i] = (∑[j=i+1,1272] 现金流金额[j] \* 折现因子[j-i-1] \* (j-1/12) / (1 + 折现率[j-i-1])) / 现金流现值[i]

**说明：**

- 变量定义需包含数据来源（表名 + 字段路径）
- 公式中特殊符号需使用标准 LaTeX 格式（如∑表示求和）
- 涉及数组操作时需明确索引范围

### 1.4 标签定义法

- 对于跨记录或不同数据集间的计算场景，可以给不同数据集打上标签，再描述数据集间的关联关系，最后把数据集标签带入公式来描述计算过程

**【示例】：**
有效久期计算：
(1) 数据集标注：

- A=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=+50bp)
- B=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=-50bp)
- C=TB0002 (账期 = 202412,duration_type = 修正久期,bp_type=0bp)

(2) 关联规则：

- A/B/C 通过 [cash_flow_type,design_type,is_short_term] 进行JOIN

(3) 计算公式:

- duration_value[i]=(B.present_cash_val_set[i].value-A.present_cash_val_set[i].value)/0.01/C.present_cash_val_set[i].value,i从0开始至1272

### 1.5 SQL表达法

- 对于复杂的表关联场景，建议直接使用SQL方式描述。如果用自然语言，不仅不好表达，反而更耗时间。

### 1.6 分层分步表达法

- 在描述功能逻辑时，因涉及多步骤、多层级，可按以下分层分步方式清晰呈现：

```textile
{主步骤}：{主题描述}
({子步骤编号}) {子步骤主题}
  - {操作项1}：{详细描述}
  - {操作项2}：{详细描述}
    * {子操作1}：{技术细节}
    * {子操作2}：{技术细节}
```

**【示例】：**

```context
步骤 1：搜索请求处理
(1) 前端交互与请求发送
- 用户在电商系统前端搜索栏输入关键词（如商品名称、品类 ），点击搜索按钮后，前端页面封装搜索参数（含关键词、用户筛选条件等 ），通过 HTTP 协议向服务端发送搜索请求。
(2) 网关层请求转发
- 服务端网关接收到前端请求，校验请求合法性（如参数格式、用户身份 token 有效性 ），若合法则根据系统路由规则，将请求转发至商品搜索服务对应的业务模块。
步骤 2：搜索逻辑执行与结果返回
(1) 搜索业务逻辑处理
- 商品搜索服务模块接收请求后，先从缓存（如 Redis ）查询是否有匹配关键词的热门搜索结果缓存，若有且未过期则直接使用；若缓存无有效数据，再访问数据库（如 MySQL ），通过 SQL 语句（结合全文检索插件如 Elasticsearch 协同），根据关键词、筛选条件检索商品数据，进行数据聚合、排序（按销量、价格、新品等规则 ）。
(2) 结果封装与响应
- 搜索服务将处理后的商品数据（含商品基本信息、价格、库存状态等 ），按前端可解析的格式（如 JSON ）封装，通过网关返回给前端；前端接收后，渲染展示搜索结果列表，供用户浏览选择 。
```

## 2. 设计文档编写原则

### 2.1 尽可能使用简洁的文字描述功能逻辑

### 2.2 一句话只描述单一的功能逻辑

### 2.3 按模块间关联性拆分设计文档

### 2.4 通过AI生成90%以上代码

### 2.5 逻辑描述一定要清晰并可实现

## 3. 图表

### 3.1 流程图

**作用：**

* 以可视化方式直观呈现功能逻辑的完整脉络，让复杂逻辑结构清晰可辨

* 支持在反复迭代优化中动态调整逻辑链路，提升方案打磨效率

* 为AI生成代码提供结构化逻辑框架，辅助AI理解业务逻辑层级

**场景：**

* 当功能逻辑涉及多模块交互、分支条件或时序依赖，单纯通过文字描述难以梳理逻辑闭环时，可借助流程图进行具象化表达，同时结合文字详细描述每个节点的具体逻辑

**【示例】：**

```mermaid
flowchart TD
    A[用户登录] --> B{选择课程}
    B -->|免费课程| C[开始学习]
    B -->|付费课程| D{检查会员状态}
    D -->|是会员| C
    D -->|非会员| E[购买课程]
    E --> C

    C --> F{课程完成?}
    F -->|否| G[继续学习]
    G --> C
    F -->|是| H[参加测验]

    H --> I{测验通过?}
    I -->|是| J[获得证书]
    I -->|否| K[复习课程]
    K --> C

    J --> L[分享成绩]
    L --> M[推荐新课程]
    M --> B
```

### 3.2 协作图

**作用：**

* 清晰呈现各状态间的流转关系

**场景：**

* 适合审批状态、业务流转状态等状态描述场景

**【示例】：**

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Processing : Start
Processing --> Success : Success Event
Processing --> Error : Error Event
Error --> Processing : Retry
Error --> Idle : Cancel
Success --> [*]
Idle --> [*]
```