# 需求规格说明书模板

## 文档信息

| 项目名称 | 【需求名称】     |
| -------- |------------|
| 文档版本 | V1.0       |
| 作者     |            |
| 创建日期 | 2025-06-13 |
| 状态     | 已确认        |

## 变更履历

| 版本 | 日期       | 变更描述 | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-05-07 | 初次编写 |  | 通过 |

## 1. 需求概述

### 1.1 需求背景

先忽略

### 1.2 需求目标

先忽略

### 1.3 需求范围

先忽略

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 |      |      |      |
| 业务负责人 |      |      |      |
| 技术负责人 |      |      |      |

## 2. 业务架构

### 2.1 业务模块关系图

无

### 2.2 模块列表

| 模块编号 | 模块名称 | 模块英文名 | 英文缩写 |
| -------- | -------- | ---------- | -------- |
| MD0001   | 现金流测试  | cash flow test | cft  |

### 2.3 数据模型

#### 2.3.1 现金流测试模块
##### 2.3.1.1 表间关系
```mermaid
erDiagram
    %% 已存在表（成本管理模块）
    PRODUCT["产品属性表(t_base_product_attribute)"]

    %% 基础数据表（需要导入）
    TB0001["TB0001: BP现金流量表"]
    TB0002["TB0002: 变量映射表"]
    TB0003["TB0003: 财务预算费用表"]

    %% 计算表（通过加工得出）
    TB0004["TB0004: 精算业管费汇总表"]
    TB0005["TB0005: 拆分比例表"]
    TB0006["TB0006: 财务预算费用拆分表"]
    TB0007["TB0007: 业务现金流预测表"]

    %% 数据流向和关联关系
    PRODUCT --> TB0001 : "产品信息关联"
    TB0002 --> TB0001 : "变量映射"
    TB0001 --> TB0004 : "汇总计算"
    TB0004 --> TB0005 : "比例计算"
    TB0003 --> TB0006 : "费用拆分"
    TB0005 --> TB0006 : "比例应用"
    TB0001 --> TB0007 : "现金流预测"
    TB0006 --> TB0007 : "费用预测"
```

##### 2.3.1.2 表名字典

| 表编号 | 表中文名 | 表英文名 | 备注 |
| ---- | ------ | ---- | ------ |
| TB0001 | BP现金流量表 | t_cft_bp_cash_flow | 存储BP现金流量数据（基础数据表，需要导入） |
| TB0002 | 变量映射表 | t_cft_variable_mapping | 存储现金流变量映射关系（基础数据表，需要导入） |
| TB0003 | 财务预算费用表 | t_cft_financial_budget_expense | 存储财务预算费用数据（基础数据表，需要导入） |
| TB0004 | 精算业管费汇总表 | t_cft_actuarial_expense_summary | 存储精算业管费汇总数据（通过汇总TB0001表得出） |
| TB0005 | 拆分比例表 | t_cft_split_ratio | 存储拆分比例数据（基于TB0004表计算得出） |
| TB0006 | 财务预算费用拆分表 | t_cft_financial_budget_expense_split | 存储财务预算费用拆分数据（基于TB0003和TB0005表计算得出） |
| TB0007 | 业务现金流预测表 | t_cft_business_cash_flow_forecast | 存储业务现金流预测数据（基于TB0001和TB0006表计算得出） |

##### 2.3.1.3 表集

##### （1）TB0001 BP现金流量表 (t_cft_bp_cash_flow)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 如：基本情景、压力情景等 | 是 | 情景标识，对应cft_scenario_name字典 |
| 业务类型 | business_type | varchar | 20 | 否 | text | - | 值域：有效业务、新业务 | 是 | 业务分类，对应cost_business_type字典 |
| 精算代码 | actuarial_code | varchar | 20 | 否 | text | - | 产品精算代码，以字母开头 | 是 | 关联成本管理模块产品属性表(t_base_product_attribute) |
| 业务代码 | business_code | varchar | 20 | 否 | text | - | 产品业务代码 | 否 | 取自成本管理模块产品属性表 |
| 产品名称 | product_name | varchar | 100 | 否 | text | - | 产品全称 | 否 | 取自成本管理模块产品属性表 |
| 设计类型 | design_type | varchar | 50 | 否 | text | - | 传统险、分红险、万能险、投连险 | 否 | 取自成本管理模块产品属性表，对应cost_design_type字典 |
| 变量列表 | variable_list | varchar | 50 | 否 | text | - | 变量代码标识 | 是 | 现金流变量分类 |
| 变量名称 | variable_name | varchar | 100 | 否 | text | - | 变量中文名称 | 否 | 取自变量映射表，匹配字段：variable_list |
| 现金流值集 | cash_flow_value_set | mediumtext | 65535 | 是 | text | - | JSON格式存储现金流数据 | 否 | 现金流数据集合 |

> 注：此表用于存储BP现金流量数据，支持按情景、业务类型、产品和变量维度进行现金流数据管理。产品相关字段（精算代码、业务代码、产品名称、设计类型）关联成本管理模块的产品属性表(t_base_product_attribute)，该表已存在

##### （2）TB0002 变量映射表 (t_cft_variable_mapping)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 变量列表 | variable_list | varchar | 50 | 否 | text | - | 变量代码标识 | 是 | 现金流变量分类 |
| 变量名称 | variable_name | varchar | 100 | 否 | text | - | 变量中文名称 | 否 | 变量描述 |

> 注：此表用于存储现金流变量的映射关系，建立变量代码与变量名称的对应关系

##### （3）TB0003 财务预算费用表 (t_cft_financial_budget_expense)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 如：基本情景、压力情景等 | 是 | 情景标识，对应cft_scenario_name字典 |
| 财务费用类型 | financial_expense_type | varchar | 50 | 否 | text | - | 财务费用分类 | 是 | 费用类型标识，对应cft_financial_expense_type字典 |
| 日期 | date | varchar | 10 | 否 | text | - | 格式：YYYYQX（如2025Q1） | 是 | 季度标识 |
| 金额 | amount | decimal | 18,2 | 是 | number | 0 | 费用金额 | 否 | 财务费用数值 |

> 注：此表用于存储财务预算费用数据，支持按情景、费用类型和季度维度进行费用管理

##### （4）TB0004 精算业管费汇总表 (t_cft_actuarial_expense_summary)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述                                              | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|-----------------------------------------------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412）                                  | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0001表                                     | 是 | 情景标识，对应cft_scenario_name字典 |
| 精算费用类型 | actuarial_expense_type | varchar | 100 | 否 | text | - | 汇总维度，来源于TB0001表的变量名称字段                        | 是 | 精算费用类型标识，对应TB0001表的variable_name字段 |
| 业务类型 | business_type | varchar | 20 | 否 | text | - | 汇总维度，来源于TB0001表                                     | 是 | 业务分类，对应cost_business_type字典 |
| 设计类型 | design_type | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0001表                                     | 是 | 产品设计类型，对应cost_design_type字典 |
| 现金流值集 | cash_flow_value_set | mediumtext | 65535 | 是 | text | - | 存储现金流数据，格式：{"0": {"日期": "2025/01/31", "值": "2100"}} | 否 | 汇总现金流数据 |

> 注：此表用于存储精算业管费汇总数据，通过加工计算得出。计算规则：将TB0001 BP现金流量表按情景名称、精算费用类型、业务类型、设计类型进行分组汇总，精算费用类型取自TB0001表的变量名称字段

##### （5）TB0005 拆分比例表 (t_cft_split_ratio)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0004表 | 是 | 情景标识，对应cft_scenario_name字典 |
| 业务类型 | business_type | varchar | 20 | 是 | text | - | 汇总维度，来源于TB0004表 | 是 | 业务分类，拆分比例1时为空，对应cost_business_type字典 |
| 设计类型 | design_type | varchar | 50 | 是 | text | - | 汇总维度，来源于TB0004表 | 是 | 产品设计类型，拆分比例1时为空，对应cost_design_type字典 |
| 拆分比例类型 | split_ratio_type | varchar | 20 | 否 | text | - | 拆分比例分类：拆分比例1/拆分比例2 | 是 | 拆分比例分类，对应cft_split_ratio_type字典 |
| 拆分比例值集 | split_ratio_value_set | mediumtext | 65535 | 是 | text | - | 存储拆分比例数据，JSON格式 | 否 | 计算得出的拆分比例 |

> 注：此表用于存储拆分比例数据，通过加工计算得出。计算规则基于TB0004精算业管费汇总表数据：
> 1. **拆分比例1**：季度内月度分配比例，当前月份现金流金额/当前季度现金流总金额，匹配条件：情景名称
> 2. **拆分比例2**：月度内账户业务类型分配比例，当前月份某账户业务类型现金流金额/当前月份总现金流金额，匹配条件：情景名称+业务类型+设计类型

##### （6）TB0006 财务预算费用拆分表 (t_cft_financial_budget_expense_split)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0003、TB0005表 | 是 | 情景标识，对应cft_scenario_name字典 |
| 财务费用类型 | financial_expense_type | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0003表 | 是 | 费用类型标识，对应cft_financial_expense_type字典 |
| 业务类型 | business_type | varchar | 20 | 否 | text | - | 汇总维度，来源于TB0005表 | 是 | 业务分类，对应cost_business_type字典 |
| 设计类型 | design_type | varchar | 50 | 否 | text | - | 汇总维度，来源于TB0005表 | 是 | 产品设计类型，对应cost_design_type字典 |
| 现金流值集 | cash_flow_value_set | mediumtext | 65535 | 是 | text | - | 拆分后的费用数据，JSON格式 | 否 | 拆分后的费用数据 |

> 注：此表用于存储财务预算费用拆分数据，通过加工计算得出。计算规则：（TB0003财务预算费用表.金额）×（TB0005拆分比例表.拆分比例1）×（TB0005拆分比例表.拆分比例2）
> - **第一个乘数**：匹配TB0003财务预算费用表.金额，匹配条件：情景名称+财务费用类型+现金流所处季度
> - **第二个乘数**：匹配TB0005拆分比例表.拆分比例值集，匹配条件：情景名称+拆分比例类型=拆分比例1
> - **第三个乘数**：匹配TB0005拆分比例表.拆分比例值集，匹配条件：情景名称+业务类型+设计类型+拆分比例类型=拆分比例2

##### （7）TB0007 业务现金流预测表 (t_cft_business_cash_flow_forecast)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 情景名称 | scenario_name | varchar | 50 | 否 | text | - | 如：基本情景、压力情景等 | 是 | 情景标识，对应cft_scenario_name字典 |
| 设计类型 | design_type | varchar | 50 | 否 | text | - | 传统险、分红险、万能险、投连险 | 是 | 产品设计类型，对应cost_design_type字典 |
| 业务类型 | business_type | varchar | 20 | 否 | text | - | 有效业务、新业务 | 是 | 业务分类，对应cost_business_type字典 |
| 项目 | item | varchar | 50 | 否 | text | - | 现金流项目名称 | 是 | 现金流分类 |
| 未来第一季度 | future_first_quarter | decimal | 18,2 | 是 | number | 0 | 未来第一季度现金流金额 | 否 | 预测数据 |
| 未来第二季度 | future_second_quarter | decimal | 18,2 | 是 | number | 0 | 未来第二季度现金流金额 | 否 | 预测数据 |
| 未来第三季度 | future_third_quarter | decimal | 18,2 | 是 | number | 0 | 未来第三季度现金流金额 | 否 | 预测数据 |
| 未来第四季度 | future_fourth_quarter | decimal | 18,2 | 是 | number | 0 | 未来第四季度现金流金额 | 否 | 预测数据 |
| 未来第二年剩余季度 | future_second_year_remaining_quarters | decimal | 18,2 | 是 | number | 0 | 未来第二年剩余季度现金流金额 | 否 | 预测数据 |
| 未来第三年 | future_third_year | decimal | 18,2 | 是 | number | 0 | 未来第三年现金流金额 | 否 | 预测数据 |

> 注：此表用于存储业务现金流预测数据，通过加工计算得出。计算规则基于TB0001 BP现金流量表和TB0006财务预算费用拆分表数据，按不同项目类型采用不同的计算逻辑：
> 1. **保费收入、满期给付、退保支出、佣金及手续费、红利支出**：直接汇总TB0001表对应变量名称的现金流值集
> 2. **赔付支出**：汇总TB0001表"赔付支出"和"满期给付"变量的现金流值集
> 3. **业务及管理费**：汇总TB0001表"精算保险保障基金及监管费"变量 + TB0006表"财务业管费"类型的现金流值集
> 4. **费用支出**：业务及管理费 + 佣金及手续费 + TB0006表"财务税金及附加"类型的现金流值集
> 5. **再保业务支出净额**：TB0001表"分保保费" - "分保手续费" - "摊回赔付" - "摊回退保金"变量的现金流值集

### 2.4 用例列表

> **说明**：现金流测试模块包含基础数据导入和计算处理功能，通过数据加工生成现金流预测结果。

| 用例编号   | 用例名称             | 用例描述 | 模块编号 |
|--------|------------------| - | ---- |
| UC0001 | 导入BP现金流量数据 | 导入BP现金流量基础数据，写入t_cft_bp_cash_flow表 | MD0001 |
| UC0002 | 导入变量映射数据 | 导入现金流变量映射关系，写入t_cft_variable_mapping表 | MD0001 |
| UC0003 | 导入财务预算费用数据 | 导入财务预算费用数据，写入t_cft_financial_budget_expense表 | MD0001 |
| UC0004 | 计算精算业管费汇总数据 | 根据BP现金流量表按维度汇总，写入t_cft_actuarial_expense_summary表 | MD0001 |
| UC0005 | 计算拆分比例数据 | 根据精算业管费汇总表计算拆分比例，写入t_cft_split_ratio表 | MD0001 |
| UC0006 | 计算财务预算费用拆分数据 | 根据财务预算费用表和拆分比例表计算费用拆分，写入t_cft_financial_budget_expense_split表 | MD0001 |
| UC0007 | 计算业务现金流预测数据 | 根据BP现金流量表和财务预算费用拆分表计算现金流预测，写入t_cft_business_cash_flow_forecast表 | MD0001 |

### 2.5 接口清单

| 接口编号   | 接口名称        | 接口描述 | 模块编号 |
| ------ |-------------| ---- | ---- |
| IF0001 | 导入BP现金流量数据 | 导入BP现金流量基础数据，写入t_cft_bp_cash_flow表 | MD0001 |
| IF0002 | 导入变量映射数据 | 导入现金流变量映射关系，写入t_cft_variable_mapping表 | MD0001 |
| IF0003 | 导入财务预算费用数据 | 导入财务预算费用数据，写入t_cft_financial_budget_expense表 | MD0001 |
| IF0004 | 计算精算业管费汇总数据 | 根据BP现金流量表按维度汇总，写入t_cft_actuarial_expense_summary表 | MD0001 |
| IF0005 | 计算拆分比例数据 | 根据精算业管费汇总表计算拆分比例，写入t_cft_split_ratio表 | MD0001 |
| IF0006 | 计算财务预算费用拆分数据 | 根据财务预算费用表和拆分比例表计算费用拆分，写入t_cft_financial_budget_expense_split表 | MD0001 |
| IF0007 | 计算业务现金流预测数据 | 根据BP现金流量表和财务预算费用拆分表计算现金流预测，写入t_cft_business_cash_flow_forecast表 | MD0001 |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| BP现金流 | Business Plan现金流，业务计划现金流 | 基于业务计划的现金流预测数据 | 包括保费收入、赔付支出、费用支出等各类现金流项目 |
| 现金流变量 | 现金流测试中的各类现金流项目标识 | 用于分类管理不同类型的现金流数据 | 如保费收入、满期给付、退保支出、佣金及手续费等 |
| 情景分析 | 基于不同假设条件的现金流测试场景 | 用于评估不同市场环境下的现金流表现 | 包括基本情景、压力情景等 |
| 财务预算费用 | 公司层面的财务费用预算数据 | 需要按业务维度进行拆分分配的费用 | 包括财务业管费、财务税金及附加等 |
| 拆分比例 | 将财务费用按业务维度分配的比例系数 | 用于将公司层面费用分摊到具体业务 | 分为拆分比例1（按季度）和拆分比例2（按月份） |
| 业务类型 | 现金流测试中的业务分类维度 | 区分不同业务性质的现金流特征 | 包括有效业务（01）、新业务（02） |
| 设计类型 | 保险产品按设计特点的分类 | 用于现金流测试和费用分配的重要维度 | 包括传统险、分红险、万能险、投连险 |

## 4. 功能需求

### 4.1 现金流测试模块

#### 4.1.1 原型图

忽略

#### 4.1.2 接口功能

现金流测试模块提供数据导入和计算处理接口，支持BP现金流量、变量映射、财务预算费用数据的导入，以及各类汇总计算和现金流预测功能。

#### 4.1.3 功能描述

##### 4.1.3.1 现金流测试管理流程

###### 4.1.3.1.1 功能概要
通过数据导入和计算处理，完成现金流测试的管理：基础数据导入、现金流汇总、费用拆分和业务现金流预测。

###### 4.1.3.1.2 业务总流程

```mermaid
flowchart TD
    %% 已存在数据
    A[产品属性表<br/>已在成本管理模块存在]

    %% 基础数据导入
    B[UC0001: 导入BP现金流量数据]
    C[UC0002: 导入变量映射数据]
    D[UC0003: 导入财务预算费用数据]

    %% 第一层计算
    E[UC0004: 计算精算业管费汇总数据]

    %% 第二层计算
    F[UC0005: 计算拆分比例数据]

    %% 第三层计算
    G[UC0006: 计算财务预算费用拆分数据]

    %% 最终预测
    H[UC0007: 计算业务现金流预测数据]

    %% 数据流向
    A --> B
    C --> B
    B --> E
    E --> F
    D --> G
    F --> G
    B --> H
    G --> H
```

###### 4.1.3.1.3 用例描述

> **字典数据说明**：现金流测试模块复用成本管理模块的字典数据，具体映射关系如下：
> - **设计类型**：使用 `cost_design_type` 字典（传统险、分红险、万能险、投连险）
> - **业务类型**：使用 `cost_business_type` 字典（01：有效业务、02：新业务）
> - **情景名称**：需新增 `cft_scenario_name` 字典（基本情景、压力情景等）
> - **财务费用类型**：需新增 `cft_financial_expense_type` 字典（财务业管费、财务税金及附加）
> - **拆分比例类型**：需新增 `cft_split_ratio_type` 字典（拆分比例1、拆分比例2）

###### 4.1.3.1.3.1 导入BP现金流量数据(UC0001)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入BP现金流量数据 |
|   功能描述   | 导入BP现金流量基础数据 |
|    参与者    | 精算管理部人员 |
|    原型图    | PT0001 |
|    关联表    | TB0001 |
|    前置用例    | 产品属性表已存在，变量映射数据已导入 |

**业务流程**：

**步骤1.** 页面访问和模板下载

(1) 用户访问BP现金流量管理页面
(2) 下载Excel导入模板，模板包含以下字段：

| 字段名称 | 字段说明 | 是否必填 | 数据格式 | 示例值 |
|----------|----------|----------|----------|--------|
| 账期 | 业务账期 | 是 | YYYYMM | 202412 |
| 情景名称 | 情景标识 | 是 | 下拉选择 | 基本情景 |
| 业务类型 | 业务分类 | 是 | 下拉选择 | 有效业务 |
| 精算代码 | 产品精算代码 | 是 | 文本 | AC001 |
| 变量列表 | 现金流变量代码 | 是 | 文本 | PREM_IN |
| 现金流值集 | 现金流数据 | 是 | JSON格式 | {"202501":{"日期":"2025/01/31","值":"1000.00"},...} |

**步骤2.** 数据填写和验证

(1) 用户填写Excel模板数据：
   a. 账期：格式YYYYMM，如202412
   b. 情景名称：从下拉列表选择（对应 `cft_scenario_name` 字典）
   c. 业务类型：从下拉列表选择（对应 `cost_business_type` 字典）
   d. 精算代码：必须在产品属性表中存在
   e. 变量列表：必须在变量映射表中存在
   f. 现金流值集：JSON格式，包含月度现金流数据

(2) 现金流值集JSON格式要求：
   a. 格式：{"月份1": {"日期": "YYYY/MM/DD", "值": "金额"}, "月份2": {...}, ...}
   b. 示例：{"202501": {"日期": "2025/01/31", "值": "1000.00"}, "202502": {"日期": "2025/02/28", "值": "1200.00"}}
   c. 月份：格式YYYYMM
   d. 日期：每月最后一天，格式YYYY/MM/DD
   e. 值：数值型，支持小数

**步骤3.** 数据导入和关联验证

(1) 上传Excel文件进行数据导入
(2) 系统进行数据格式验证：
   a. 账期格式验证：必须为6位数字，格式YYYYMM
   b. 情景名称验证：必须在cft_scenario_name字典中存在
   c. 业务类型验证：必须在cost_business_type字典中存在
   d. 现金流值集JSON格式验证：必须为有效JSON格式

(3) 系统进行关联数据验证：
   a. 精算代码验证：
      - 在产品属性表(t_base_product_attribute)中必须存在
      - 自动关联获取：业务代码、产品名称、设计类型
   b. 变量列表验证：
      - 在变量映射表(TB0002)中必须存在
      - 自动关联获取：变量名称

(4) 业务规则验证：
   a. 同一账期内，情景名称+业务类型+精算代码+变量列表组合不能重复
   b. 现金流值集中的月份必须连续且完整
   c. 现金流金额不能为负数（除特殊变量外）

**步骤4.** 数据拼装和入库

(1) 数据拼装逻辑：
   a. 用户导入字段：账期、情景名称、业务类型、精算代码、变量列表、现金流值集
   b. 关联产品属性表字段：业务代码、产品名称、设计类型
   c. 关联变量映射表字段：变量名称

(2) 错误处理：
   a. 格式错误：提示具体的错误行号和错误原因
   b. 关联失败：提示"精算代码[XXX]在产品属性表中不存在"
   c. 重复数据：提示重复的字段组合
   d. 业务规则违反：提示具体的业务规则错误

(3) 导入成功后在列表页显示BP现金流量信息，包含完整的关联数据

###### 4.1.3.1.3.2 导入变量映射数据(UC0002)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入变量映射数据 |
|   功能描述   | 导入现金流变量映射关系 |
|    参与者    | 精算管理部人员 |
|    原型图    | PT0002 |
|    关联表    | TB0002 |
|    前置用例    | |

**业务流程**：

**步骤1.** 页面访问和模板下载

(1) 用户访问变量映射管理页面
(2) 下载Excel导入模板，模板包含以下字段：

| 字段名称 | 字段说明 | 是否必填 | 数据格式 | 示例值 |
|----------|----------|----------|----------|--------|
| 账期 | 业务账期 | 是 | YYYYMM | 202412 |
| 变量列表 | 现金流变量代码 | 是 | 文本 | PREM_IN |
| 变量名称 | 变量中文名称 | 是 | 文本 | 保费收入 |

**步骤2.** 数据填写规范

(1) 账期：格式YYYYMM，如202412
(2) 变量列表：现金流变量代码标识，建议命名规范：
   a. 收入类：PREM_IN（保费收入）、INVEST_IN（投资收入）等
   b. 支出类：CLAIM_OUT（赔付支出）、COMM_OUT（佣金支出）、MGMT_OUT（管理费支出）等
   c. 再保类：REINS_PREM（分保保费）、REINS_COMM（分保手续费）等
   d. 其他类：SURRENDER（退保支出）、DIVIDEND（红利支出）等

(3) 变量名称：对应的中文描述，要求：
   a. 名称简洁明确，便于理解
   b. 与变量代码含义一致
   c. 避免使用特殊字符

**步骤3.** 数据验证和导入

(1) 上传Excel文件进行数据导入
(2) 系统进行数据格式验证：
   a. 账期格式验证：必须为6位数字，格式YYYYMM
   b. 变量列表验证：不能为空，建议使用英文字母和下划线
   c. 变量名称验证：不能为空，长度不超过100个字符

(3) 业务规则验证：
   a. 同一账期内，变量列表不能重复
   b. 变量名称建议唯一，如有重复会给出警告
   c. 变量列表建议使用标准命名规范

(4) 数据一致性检查：
   a. 检查是否有孤立的变量（在变量映射表中存在但在BP现金流量表中未使用）
   b. 检查是否有缺失的变量（在BP现金流量表中使用但在变量映射表中不存在）

**步骤4.** 错误处理和成功确认

(1) 错误处理：
   a. 格式错误：提示具体的错误行号和错误原因
   b. 重复数据：提示"变量列表[XXX]在当前账期已存在"
   c. 命名规范警告：提示建议的命名规范

(2) 导入成功后：
   a. 在列表页显示变量映射信息
   b. 提供变量使用情况统计
   c. 支持变量映射的批量更新和维护

###### 4.1.3.1.3.3 导入财务预算费用数据(UC0003)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入财务预算费用数据 |
|   功能描述   | 导入财务预算费用数据 |
|    参与者    | 财务管理部人员 |
|    原型图    | PT0003 |
|    关联表    | TB0003 |
|    前置用例    | |

**业务流程**：

**步骤1.** 页面访问和模板下载

(1) 用户访问财务预算费用管理页面
(2) 下载Excel导入模板，模板包含以下字段：

| 字段名称 | 字段说明 | 是否必填 | 数据格式 | 示例值 |
|----------|----------|----------|----------|--------|
| 账期 | 业务账期 | 是 | YYYYMM | 202412 |
| 情景名称 | 情景标识 | 是 | 下拉选择 | 基本情景 |
| 财务费用类型 | 费用分类 | 是 | 下拉选择 | 财务业管费 |
| 日期 | 季度标识 | 是 | YYYYQX | 2025Q1 |
| 金额 | 费用金额 | 是 | 数值 | 1000000.00 |

**步骤2.** 数据填写规范

(1) 账期：格式YYYYMM，如202412
(2) 情景名称：从下拉列表选择（对应 `cft_scenario_name` 字典）
   a. 基本情景：正常市场环境下的费用预算
   b. 压力情景：压力测试环境下的费用预算
   c. 其他情景：根据业务需要定义的情景

(3) 财务费用类型：从下拉列表选择（对应 `cft_financial_expense_type` 字典）
   a. 财务业管费：业务及管理费用
   b. 财务税金及附加：税金及附加费用

(4) 日期：季度标识，格式YYYYQX
   a. 格式要求：年份4位+Q+季度1位，如2025Q1、2025Q2、2025Q3、2025Q4
   b. 季度范围：Q1（1-3月）、Q2（4-6月）、Q3（7-9月）、Q4（10-12月）

(5) 金额：费用金额，要求：
   a. 数值型，支持小数，最多保留2位小数
   b. 金额不能为负数
   c. 建议使用万元或千万元为单位

**步骤3.** 数据验证和导入

(1) 上传Excel文件进行数据导入
(2) 系统进行数据格式验证：
   a. 账期格式验证：必须为6位数字，格式YYYYMM
   b. 情景名称验证：必须在cft_scenario_name字典中存在
   c. 财务费用类型验证：必须在cft_financial_expense_type字典中存在
   d. 日期格式验证：必须符合YYYYQX格式，季度范围Q1-Q4
   e. 金额验证：必须为数值型，不能为负数

(3) 业务规则验证：
   a. 同一账期内，情景名称+财务费用类型+日期组合不能重复
   b. 日期的年份应该与账期年份保持逻辑一致性
   c. 金额合理性检查：过大或过小的金额会给出警告

(4) 数据完整性检查：
   a. 检查是否覆盖了所有必要的季度
   b. 检查是否包含了所有情景和费用类型的组合
   c. 提供数据完整性报告

**步骤4.** 错误处理和成功确认

(1) 错误处理：
   a. 格式错误：提示具体的错误行号和错误原因
   b. 重复数据：提示重复的字段组合
   c. 字典值错误：提示"情景名称[XXX]不在字典中"
   d. 业务规则违反：提示具体的业务规则错误

(2) 数据预览和确认：
   a. 导入前提供数据预览功能
   b. 显示导入数据的汇总统计
   c. 用户确认后正式导入

(3) 导入成功后：
   a. 在列表页显示财务预算费用信息
   b. 提供按情景、费用类型、季度的汇总视图
   c. 支持数据的查询、修改和删除操作

###### 4.1.3.1.3.4 计算精算业管费汇总数据(UC0004)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算精算业管费汇总数据 |
|   功能描述   | 根据BP现金流量表按维度汇总计算精算业管费 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0001, TB0004 |
|    前置用例    | UC0001 |

**业务流程**：

**步骤1.** 数据准备

(1) 读取TB0001 BP现金流量表数据：
   a. 读取指定账期的所有BP现金流量数据
   b. 过滤条件：只读取变量列表(variable_list)为"SOL_TOT_EXP"的记录
   c. 验证数据完整性，确保关键字段不为空
   d. 解析现金流值集JSON数据

**步骤2.** 数据分组汇总

(1) 按维度分组：
   a. 分组字段：情景名称、精算费用类型、业务类型、设计类型
   b. 精算费用类型来源于TB0001表的变量名称(variable_name)字段
   c. 精算费用类型取值：来自TB0002变量映射表的变量名称

(2) 现金流值集汇总：
   a. 解析各记录的现金流值集JSON数据
   b. 按月份汇总同组内的现金流金额
   c. 生成汇总后的现金流值集JSON

**步骤3.** 数据验证和入库

(1) 数据验证：
   a. 验证汇总结果的合理性
   b. 验证现金流值集JSON格式正确
   c. 验证分组维度的完整性

(2) 将汇总结果写入TB0004精算业管费汇总表：
   a. 账期：来源于输入参数
   b. 情景名称：来源于分组维度
   c. 精算费用类型：来源于分组维度
   d. 业务类型：来源于分组维度
   e. 设计类型：来源于分组维度
   f. 现金流值集：汇总计算生成的JSON数据

###### 4.1.3.1.3.5 计算拆分比例数据(UC0005)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算拆分比例数据 |
|   功能描述   | 根据精算业管费汇总表计算拆分比例 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0004, TB0005 |
|    前置用例    | UC0004 |

**业务流程**：

**步骤1.** 数据准备

(1) 按账期读取TB0004精算业管费汇总表数据：
   a. 读取指定账期的所有精算业管费汇总数据
   b. 验证数据完整性，确保关键字段不为空
   c. 解析现金流值集JSON数据，提取各季度/月份的现金流金额

**步骤2.** 计算拆分比例1（季度内月度分配比例）

(1) 拆分比例1计算规则：
   a. 匹配条件：精算业管费汇总表.情景名称 = 拆分比例表.情景名称
   b. 计算公式：当前月份现金流金额 / 当前季度现金流总金额
   c. 即：月度费用 / 该月所属季度的三个月费用之和

(2) 具体计算步骤：
   a. 按情景名称分组汇总TB0004表数据
   b. 解析现金流值集JSON，按月份归集现金流金额
   c. 按季度分组：Q1(1-3月)、Q2(4-6月)、Q3(7-9月)、Q4(10-12月)
   d. 计算各月份在其所属季度内的占比：月份现金流金额 / 季度现金流总金额
   e. 生成拆分比例1值集，格式：{"0": {"日期": "2025/01/31", "值": "0.3293"}, "1": {"日期": "2025/02/28", "值": "0.3336"}, ...}

**步骤3.** 计算拆分比例2（月度内账户业务类型分配比例）

(1) 拆分比例2计算规则：
   a. 匹配条件：精算业管费汇总表.情景名称 = 拆分比例表.情景名称 AND 精算业管费汇总表.业务类型 = 拆分比例表.业务类型 AND 精算业管费汇总表.设计类型 = 拆分比例表.设计类型
   b. 计算公式：当前月份某账户业务类型现金流金额 / 当前月份总现金流金额
   c. 即：月度内分账户分业务费用 / 月度费用

(2) 具体计算步骤：
   a. 按情景名称、业务类型、设计类型分组汇总TB0004表数据
   b. 解析现金流值集JSON，按月份归集现金流金额
   c. 计算各月份各账户业务类型占比：月份账户业务类型现金流金额 / 月份总现金流金额
   d. 生成拆分比例2值集，格式：{"0": {"日期": "2025/01/31", "值": "0.6445"}, "1": {"日期": "2025/02/28", "值": "0.6291"}, ...}

**步骤4.** 数据入表

(1) 拆分比例1数据入库：
   a. 情景名称：来源于TB0004表分组维度
   b. 业务类型：NULL（拆分比例1时为空）
   c. 设计类型：NULL（拆分比例1时为空）
   d. 拆分比例类型：拆分比例1（对应cft_split_ratio_type字典）
   e. 拆分比例值集：按季度计算的比例JSON数据

(2) 拆分比例2数据入库：
   a. 情景名称：来源于TB0004表分组维度
   b. 业务类型：来源于TB0004表分组维度
   c. 设计类型：来源于TB0004表分组维度
   d. 拆分比例类型：拆分比例2（对应cft_split_ratio_type字典）
   e. 拆分比例值集：按月份计算的比例JSON数据

(3) 精度控制：
   a. 计算过程保留小数位16位，四舍五入
   b. 最终结果保留小数位8位
   c. 计算过程使用BigDecimal类型保证计算精度
   d. 确保各比例之和等于1.0（允许微小误差）

(4) 将所有计算结果写入TB0005拆分比例表

###### 4.1.3.1.3.6 计算财务预算费用拆分数据(UC0006)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算财务预算费用拆分数据 |
|   功能描述   | 根据财务预算费用表和拆分比例表计算费用拆分 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0003, TB0005, TB0006 |
|    前置用例    | UC0003, UC0005 |

**业务流程**：

**步骤1.** 数据准备

(1) 读取TB0003财务预算费用表数据：
   a. 读取指定账期的所有财务预算费用数据
   b. 按情景名称、财务费用类型、日期（季度）分组
   c. 验证数据完整性，确保关键字段不为空

(2) 读取TB0005拆分比例表数据：
   a. 读取指定账期的所有拆分比例数据
   b. 按拆分比例类型分别加载拆分比例1和拆分比例2数据
   c. 解析拆分比例值集JSON数据

**步骤2.** 三乘数计算规则

(1) 计算公式：
   **最终金额 = 第一个乘数 × 第二个乘数 × 第三个乘数**

(2) 第一个乘数：财务预算费用表.金额
   a. 匹配条件：
      - 财务预算费用表.情景名称 = 财务预算费用拆分表.情景名称
      - 财务预算费用表.财务费用类型 = 财务预算费用拆分表.财务费用类型
      - 财务预算费用表.日期（季度） = 现金流所处的季度
   b. 取值逻辑：直接取财务预算费用表中对应记录的金额字段

(3) 第二个乘数：拆分比例表.拆分比例值集（拆分比例1）
   a. 匹配条件：
      - 财务预算费用拆分表.情景名称 = 拆分比例表.情景名称
      - 拆分比例表.拆分比例类型 = 拆分比例1
   b. 取值逻辑：从拆分比例1的值集JSON中提取对应季度的比例值

(4) 第三个乘数：拆分比例表.拆分比例值集（拆分比例2）
   a. 匹配条件：
      - 财务预算费用拆分表.情景名称 = 拆分比例表.情景名称
      - 财务预算费用拆分表.业务类型 = 拆分比例表.业务类型
      - 财务预算费用拆分表.设计类型 = 拆分比例表.设计类型
      - 拆分比例表.拆分比例类型 = 拆分比例2
   b. 取值逻辑：从拆分比例2的值集JSON中提取对应月份的比例值

**步骤3.** 具体计算过程

(1) 生成计算矩阵：
   a. 按情景名称、财务费用类型、业务类型、设计类型的所有组合生成计算矩阵
   b. 每个组合对应一条财务预算费用拆分记录

(2) 逐条记录计算：
   a. 获取第一个乘数：
      - 根据情景名称、财务费用类型匹配TB0003表
      - 按季度提取对应的金额数据

   b. 获取第二个乘数：
      - 根据情景名称匹配TB0005表中拆分比例类型=拆分比例1的记录
      - 解析拆分比例值集JSON，提取月份比例
      - 格式示例：{"0": {"日期": "2025/01/31", "值": "0.08"}, "1": {"日期": "2025/02/28", "值": "0.09"}, ...}

   c. 获取第三个乘数：
      - 根据情景名称、业务类型、设计类型匹配TB0005表中拆分比例类型=拆分比例2的记录
      - 解析拆分比例值集JSON，提取月份比例
      - 格式示例：{"0": {"日期": "2025/01/31", "值": "0.08"}, "1": {"日期": "2025/02/28", "值": "0.09"}, ...}

(3) 现金流值集计算：
   a. 按月份计算拆分后的费用金额：
      - 确定月份所属季度（如202501属于2025Q1）
      - 计算公式：月份金额 = 季度金额 × 季度比例 × 月份比例

   b. 生成现金流值集JSON：
      - 格式：{"202501": {"日期": "2025/01/31", "值": 计算结果}, "202502": {...}, ...}
      - 日期格式：每月最后一天
      - 值：三乘数计算结果

**步骤4.** 数据入表

(1) 精度控制：
   a. 计算过程保留小数位16位，四舍五入
   b. 最终结果保留小数位10位
   c. 计算过程使用BigDecimal类型保证计算精度

(2) 数据验证：
   a. 验证三个乘数都能正确匹配到数据
   b. 验证计算结果不为负数
   c. 验证现金流值集JSON格式正确

(3) 将计算结果写入TB0006财务预算费用拆分表：
   a. 账期：来源于输入参数
   b. 情景名称：来源于计算矩阵
   c. 财务费用类型：来源于计算矩阵
   d. 业务类型：来源于计算矩阵
   e. 设计类型：来源于计算矩阵
   f. 现金流值集：三乘数计算生成的JSON数据

###### 4.1.3.1.3.7 计算业务现金流预测数据(UC0007)

**功能描述**：
基于BP现金流量表和财务预算费用拆分表数据，计算业务现金流预测数据，包括分设计类型计算和普通账户汇总计算。

**计算范围**：
1. 分设计类型计算：按情景、业务类型、设计类型、项目维度计算
2. 普通账户计算：将传统险、分红险、万能险按情景、业务类型、项目维度汇总

**数据源**：
- TB0001 BP现金流量表
- TB0006 财务预算费用拆分表

**目标表**：
- TB0007 业务现金流预测表

**计算逻辑**：

**第一步：分设计类型计算**

按情景名称、业务类型、设计类型、项目维度计算，生成以下记录：
- 情景名称：基本情景、压力情景
- 业务类型：有效业务、新业务
- 设计类型：传统险、分红险、万能险、投连险
- 项目：保费收入、满期给付、退保支出、佣金及手续费、红利支出、赔付支出、业务及管理费、费用支出、再保业务支出净额

各项目计算规则：

a. 保费收入 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "保费收入"

b. 满期给付 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "满期给付"

c. 退保支出 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "退保支出"

d. 佣金及手续费 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "佣金及手续费"

e. 红利支出 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "红利支出"

f. 赔付支出 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "赔付支出" OR BP现金流量表.变量名称 = "满期给付"

g. 业务及管理费 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "精算保险保障基金及监管费" + sum(财务预算费用拆分表.现金流值集) where 财务预算费用拆分表.财务费用类型 = "财务业管费"

h. 费用支出 = 业务及管理费 + 佣金及手续费 + sum(财务预算费用拆分表.现金流值集) where 财务预算费用拆分表.财务费用类型 = "财务税金及附加"

i. 再保业务支出净额 = sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "分保保费" - sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "分保手续费" - sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "摊回赔付" - sum(BP现金流量表.现金流值集) where BP现金流量表.变量名称 = "摊回退保金"

**第二步：普通账户汇总计算**

基于第一步计算结果，按情景名称、业务类型、项目维度汇总传统险、分红险、万能险的数据：

计算规则：
- 情景名称：基本情景、压力情景
- 业务类型：有效业务、新业务
- 设计类型：普通账户（固定值）
- 项目：保费收入、满期给付、退保支出、佣金及手续费、红利支出、赔付支出、业务及管理费、费用支出、再保业务支出净额

汇总公式：
普通账户各项目金额 = sum(传统险对应项目金额 + 分红险对应项目金额 + 万能险对应项目金额)

其中：
- 未来第一季度 = 传统险未来第一季度 + 分红险未来第一季度 + 万能险未来第一季度
- 未来第二季度 = 传统险未来第二季度 + 分红险未来第二季度 + 万能险未来第二季度
- 未来第三季度 = 传统险未来第三季度 + 分红险未来第三季度 + 万能险未来第三季度
- 未来第四季度 = 传统险未来第四季度 + 分红险未来第四季度 + 万能险未来第四季度
- 未来第二年剩余季度 = 传统险未来第二年剩余季度 + 分红险未来第二年剩余季度 + 万能险未来第二年剩余季度
- 未来第三年 = 传统险未来第三年 + 分红险未来第三年 + 万能险未来第三年

**数据输出**：

第一步输出：2情景 × 2业务类型 × 4设计类型 × 9项目 = 144条记录
第二步输出：2情景 × 2业务类型 × 1普通账户 × 9项目 = 36条记录

总计：180条记录

**字典数据**：
- 情景名称：01-基本情景，02-压力情景
- 业务类型：01-有效业务，02-新业务
- 设计类型：01-传统险，02-分红险，03-万能险，04-投连险，05-普通账户
- 项目：保费收入、满期给付、退保支出、佣金及手续费、红利支出、赔付支出、业务及管理费、费用支出、再保业务支出净额

**计算精度**：
所有金额字段保留2位小数，使用BigDecimal进行精确计算。

**执行顺序**：
1. 先执行分设计类型计算，生成144条基础记录
2. 再执行普通账户汇总计算，生成36条汇总记录
3. 将180条记录批量写入TB0007业务现金流预测表

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算业务现金流预测数据 |
|   功能描述   | 根据BP现金流量表和财务预算费用拆分表计算现金流预测 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0001, TB0006, TB0007 |
|    前置用例    | UC0001, UC0006 |

**业务流程**：

**步骤1.** 数据准备

(1) 读取TB0001 BP现金流量表数据：
   a. 读取指定账期的所有BP现金流量数据
   b. 按情景名称、设计类型、业务类型、变量名称分组
   c. 解析现金流值集JSON数据

(2) 读取TB0006财务预算费用拆分表数据：
   a. 读取指定账期的所有财务预算费用拆分数据
   b. 按情景名称、设计类型、业务类型、财务费用类型分组
   c. 解析现金流值集JSON数据

**步骤2.** 项目计算规则

(1) 生成预测矩阵：
   a. 按情景名称、设计类型、业务类型的所有组合生成预测记录
   b. 每个组合需要计算所有项目的现金流预测值
   c. 项目清单：保费收入、满期给付、退保支出、佣金及手续费、红利支出、赔付支出、业务及管理费、费用支出、再保业务支出净额

**步骤3.** 分项目计算逻辑

**3.1 项目="保费收入"、"满期给付"、"退保支出"、"佣金及手续费"、"红利支出"**

(1) 计算规则：sum(BP现金流量表.现金流值集)
(2) 匹配条件：
   a. BP现金流量表.情景名称 = 业务现金流预测表.情景名称
   b. BP现金流量表.设计类型 = 业务现金流预测表.设计类型
   c. BP现金流量表.业务类型 = 业务现金流预测表.业务类型
   d. BP现金流量表.变量名称 = 业务现金流预测表.项目
(3) 计算步骤：
   a. 根据匹配条件筛选TB0001表记录
   b. 解析现金流值集JSON，按季度汇总现金流金额
   c. 生成各季度预测值

**3.2 项目="赔付支出"**

(1) 计算规则：sum(BP现金流量表.现金流值集)
(2) 匹配条件：
   a. BP现金流量表.情景名称 = 业务现金流预测表.情景名称
   b. BP现金流量表.设计类型 = 业务现金流预测表.设计类型
   c. BP现金流量表.业务类型 = 业务现金流预测表.业务类型
   d. BP现金流量表.变量名称 = "赔付支出" OR BP现金流量表.变量名称 = "赔付支出"
(3) 计算步骤：
   a. 筛选变量名称为"赔付支出"或"满期给付"的记录
   b. 按匹配条件汇总现金流值集
   c. 生成各季度预测值

**3.3 项目="业务及管理费"**

(1) 计算规则：sum(BP现金流量表.现金流值集) + sum(财务预算费用拆分表.现金流值集)
(2) 第一个sum匹配条件：
   a. BP现金流量表.情景名称 = 业务现金流预测表.情景名称
   b. BP现金流量表.设计类型 = 业务现金流预测表.设计类型
   c. BP现金流量表.业务类型 = 业务现金流预测表.业务类型
   d. BP现金流量表.变量名称 = "精算保险保障基金及监管费"
(3) 第二个sum匹配条件：
   a. 财务预算费用拆分表.情景名称 = 业务现金流预测表.情景名称
   b. 财务预算费用拆分表.设计类型 = 业务现金流预测表.设计类型
   c. 财务预算费用拆分表.业务类型 = 业务现金流预测表.业务类型
   d. 财务预算费用拆分表.财务费用类型 = "财务业管费"
(4) 计算步骤：
   a. 分别计算两个sum的现金流值
   b. 按季度将两部分金额相加
   c. 生成各季度预测值

**3.4 项目="费用支出"**

(1) 计算规则：业务现金流预测表.业务及管理费 + 业务现金流预测表.佣金及手续费 + sum(财务预算费用拆分表.现金流值集)
(2) sum匹配条件：
   a. 财务预算费用拆分表.情景名称 = 业务现金流预测表.情景名称
   b. 财务预算费用拆分表.设计类型 = 业务现金流预测表.设计类型
   c. 财务预算费用拆分表.业务类型 = 业务现金流预测表.业务类型
   d. 财务预算费用拆分表.财务费用类型 = "财务税金及附加"
(3) 计算步骤：
   a. 获取已计算的"业务及管理费"项目值
   b. 获取已计算的"佣金及手续费"项目值
   c. 计算"财务税金及附加"的现金流值
   d. 按季度将三部分金额相加
   e. 生成各季度预测值

**3.5 项目="再保业务支出净额"**

(1) 计算规则：sum(分保保费) - sum(分保手续费) - sum(摊回赔付) - sum(摊回退保金)
(2) 各sum的匹配条件（前三个条件相同）：
   a. BP现金流量表.情景名称 = 业务现金流预测表.情景名称
   b. BP现金流量表.设计类型 = 业务现金流预测表.设计类型
   c. BP现金流量表.业务类型 = 业务现金流预测表.业务类型
   d. 第一个sum：BP现金流量表.变量名称 = "分保保费"
   e. 第二个sum：BP现金流量表.变量名称 = "分保手续费"
   f. 第三个sum：BP现金流量表.变量名称 = "摊回赔付"
   g. 第四个sum：BP现金流量表.变量名称 = "摊回退保金"
(3) 计算步骤：
   a. 分别计算四个变量的现金流值
   b. 按公式计算：分保保费 - 分保手续费 - 摊回赔付 - 摊回退保金
   c. 生成各季度预测值

**步骤4.** 季度数据生成

(1) 现金流值集解析：
   a. 解析JSON格式的现金流值集
   b. 按月份归集到对应季度：
      - 未来第一季度：当前账期后1-3个月
      - 未来第二季度：当前账期后4-6个月
      - 未来第三季度：当前账期后7-9个月
      - 未来第四季度：当前账期后10-12个月
      - 未来第二年剩余季度：第二年全年
      - 未来第三年：第三年全年

(2) 季度金额计算：
   a. 按季度汇总月份现金流金额
   b. 处理跨年度的季度归集逻辑
   c. 确保所有季度数据完整

**步骤5.** 数据入表

(1) 精度控制：
   a. 计算过程保留小数位16位，四舍五入
   b. 最终结果保留小数位2位
   c. 计算过程使用BigDecimal类型保证计算精度

(2) 数据验证：
   a. 验证所有项目都能正确计算
   b. 验证季度金额汇总正确
   c. 验证计算结果合理性

(3) 将计算结果写入TB0007业务现金流预测表：
   a. 账期：来源于输入参数
   b. 情景名称：来源于预测矩阵
   c. 设计类型：来源于预测矩阵
   d. 业务类型：来源于预测矩阵
   e. 项目：固定的项目名称
   f. 各季度金额：按计算规则生成的预测值
