# 需求规格说明书模板

## 文档信息

| 项目名称 | 【需求名称】     |
| -------- |------------|
| 文档版本 | V1.0       |
| 作者     |            |
| 创建日期 | 2025-05-19 |
| 状态     | 已确认        |

## 变更履历

| 版本 | 日期       | 变更描述 | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-05-07 | 初次编写 |  | 通过 |

## 1. 需求概述

### 1.1 需求背景

先忽略

### 1.2 需求目标

先忽略

### 1.3 需求范围

先忽略

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 |      |      |      |
| 业务负责人 |      |      |      |
| 技术负责人 |      |      |      |

## 2. 业务架构

### 2.1 业务模块关系图

无

### 2.2 模块列表

| 模块编号 | 模块名称 | 模块英文名 | 英文缩写 |
| -------- | -------- | ---------- | -------- |
| MD0001   | 负债产品信息  | liability products | liab  |

### 2.3 数据模型

#### 2.3.1 负债产品信息模块
##### 2.3.1.1 表间关系
```mermaid
erDiagram
    %% 源数据表（需要导入）
    TB0001["TB0001: 产品属性表"]
    TB0002["TB0002: 资产负债表"]
    TB0003["TB0003: 会计准备金明细表"]

    %% 计算表（通过加工得出）
    TB0004["TB0004: 负债规模明细表"]
    TB0005["TB0005: 负债规模汇总表"]
    TB0006["TB0006: 非寿险负债规模汇总表"]

    %% 数据流向
    TB0001 --> TB0004 : "计算"
    TB0002 --> TB0004 : "计算"
    TB0003 --> TB0004 : "计算"
    TB0004 --> TB0005 : "汇总"
    TB0004 --> TB0006 : "汇总"
```

##### 2.3.1.2 表名字典
| 表编号 | 表中文名 | 表英文名 | 备注 |
| ---- | ------ | ---- | ------ |
| TB0001 | 产品属性表 | t_base_product_attribute | 存储负债产品的属性信息（源数据表，需要导入）**已存在于成本管理模块** |
| TB0002 | 资产负债表 | t_liab_balance_sheet | 存储资产负债表数据（源数据表，需要导入） |
| TB0003 | 会计准备金明细表 | t_base_accounting_reserve_detail | 存储会计准备金明细数据（源数据表，需要导入）**已存在于成本管理模块** |
| TB0004 | 负债规模明细表 | t_liab_scale_detail | 存储负债规模明细数据（通过加工计算得出） |
| TB0005 | 负债规模汇总表 | t_liab_scale_summary | 存储负债规模汇总数据（通过加工计算得出） |
| TB0006 | 非寿险负债规模汇总表 | t_liab_non_life_scale_summary | 存储非寿险负债规模汇总数据（通过加工计算得出） |
| TB0007 | 缴费年期映射表 | t_liab_payment_period_mapping | 存储缴费年期与缴费年期分类的映射关系（源数据表，需要导入） |
| TB0008 | 新单保费汇总表 | t_liab_new_premium_summary | 存储新单保费汇总数据（导入时关联产品属性表拼装数据） |
| TB0009 | ALM新单保费统计表 | t_liab_alm_new_premium_statistics | 存储ALM新单保费统计数据（通过加工计算得出） |
| TB0010 | 三年新业务规划表 | t_liab_three_year_business_plan | 存储三年新业务规划数据（源数据表，需要导入） |

##### 2.3.1.3 表集

##### （1）TB0001 产品属性表 (t_base_product_attribute) **已存在于成本管理模块**

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 账期 | 是 | - |
| 精算代码 | actuarial_code | varchar | 20 | 否 | letter+number | - | 产品精算代码，以字母开头 | 是 | - |
| 业务代码 | business_code | varchar | 20 | 否 | letter+number | - | 产品业务代码 | - | - |
| 产品名称 | product_name | varchar | 100 | 否 | - | - | 产品全称 | - | - |
| 长短期标识 | term_type | char | 1 | 否 | radio | 'L' | L-长期，S-短期 | - | - |
| 险种主类 | insurance_main_type | varchar | 50 | 否 | select | - | 如：长期寿险 | - | - |
| 险种细类 | insurance_sub_type | varchar | 50 | 否 | select | - | 如：年金险、两全险、附加两全险等 | - | - |
| 设计类型 | design_type | varchar | 50 | 否 | select | - | 如：传统险、分红险、万能险、投连险 | - | - |
| 是否中短 | short_term_flag | char | 1 | 否 | radio | 'N' | Y-是，N-否 | - | - |
| 报监管中短标识 | reg_mid_id | char | 1 | 否 | radio | 'N' | Y-是，N-否 | - | - |
| 定价保证成本率 | guaranteed_cost_rate | decimal | 10,6 | 是 | percentage | 0 | 以小数形式存储，如0.03表示3% | - | - |
| 子账户 | sub_account | varchar | 50 | 是 | select | - | 如：分红账户1、万能账户5等 | - | - |
| 新业务标识 | new_business_flag | char | 1 | 是 | radio | 'Y' | Y-是，N-否 | - | - |
| 备注 | remark | varchar | 500 | 是 | - | - | 产品相关补充说明 | - | - |

> 注：此表已存在于成本管理模块，负债产品信息模块直接复用该表


##### （2）TB0002 资产负债表 (t_liab_balance_sheet)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202503） | 是 | 标识业务账期 |
| 类别 | category | varchar | 20 | 否 | select | - | 值域：资产、负债、所有者权益 | 是 | - |
| 项目名称 | item_name | varchar | 100 | 否 | text | - | 资产负债表项目名称 | 是 | - |
| 期末余额 | ending_balance | decimal | 18,10 | 是 | number | 0 | 期末余额金额 | 否 | - |
| 年初余额 | beginning_balance | decimal | 18,10 | 是 | number | 0 | 年初余额金额 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

##### （3）TB0003 会计准备金明细表 (t_base_accounting_reserve_detail) **已存在于成本管理模块**

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 精算代码 | actuarial_code | varchar | 20 | 否 | text | - | 产品精算代码，以字母开头 | 是 | 唯一标识 |
| 业务代码 | business_code | varchar | 20 | 否 | text | - | 产品业务代码 | 否 | - |
| 产品名称 | product_name | varchar | 100 | 否 | text | - | 产品全称 | 否 | - |
| 长短期标识 | term_type | char | 1 | 否 | radio | L | 值域：L-长期，S-短期 | 否 | - |
| 设计类型 | design_type | varchar | 50 | 否 | select | - | 值域：传统险、分红险、万能险、投连险 | 否 | - |
| 是否中短 | short_term_flag | char | 1 | 否 | radio | N | 值域：Y-是，N-否 | 否 | - |
| 有效保单件数 | valid_policy_count | int | 11 | 是 | number | 0 | 有效保单件数 | 否 | - |
| 存量累计规模保费 | accumulated_premium | decimal | 18,10 | 是 | number | 0 | 存量累计规模保费 | 否 | - |
| 账户价值 | account_value | decimal | 18,10 | 是 | number | 0 | 账户价值 | 否 | - |
| 红利预提 | dividend_provision | decimal | 18,10 | 是 | number | 0 | 红利预提 | 否 | - |
| 最优估计 | best_estimate | decimal | 18,10 | 是 | number | 0 | 最优估计 | 否 | - |
| 风险边际 | risk_margin | decimal | 18,10 | 是 | number | 0 | 风险边际 | 否 | - |
| 剩余边际 | residual_margin | decimal | 18,10 | 是 | number | 0 | 剩余边际 | 否 | - |
| 未建模准备金 | unmodeled_reserve | decimal | 18,10 | 是 | number | 0 | 未建模准备金 | 否 | - |
| 豁免准备金 | waiver_reserve | decimal | 18,10 | 是 | number | 0 | 豁免准备金 | 否 | - |
| 持续奖准备金 | persistence_bonus_reserve | decimal | 18,10 | 是 | number | 0 | 持续奖准备金 | 否 | - |
| 长期险未到期 | long_term_unearned | decimal | 18,10 | 是 | number | 0 | 长期险未到期准备金 | 否 | - |
| 短险未到期 | short_term_unearned | decimal | 18,10 | 是 | number | 0 | 短险未到期准备金 | 否 | - |
| 未到期责任准备金 | unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 未到期责任准备金 | 否 | - |
| 已报未决 | reported_unpaid | decimal | 18,10 | 是 | number | 0 | 已报未决赔款 | 否 | - |
| 未报未决 | incurred_unreported | decimal | 18,10 | 是 | number | 0 | 未报未决赔款 | 否 | - |
| 理赔费用准备金 | claim_expense_reserve | decimal | 18,10 | 是 | number | 0 | 理赔费用准备金 | 否 | - |
| 未决赔款准备金 | outstanding_claim_reserve | decimal | 18,10 | 是 | number | 0 | 未决赔款准备金 | 否 | - |
| 会计准备金合计 | total_accounting_reserve | decimal | 18,10 | 是 | number | 0 | 会计准备金合计 | 否 | - |
| 应收分保未到期责任准备金 | reinsurance_unearned | decimal | 18,10 | 是 | number | 0 | 应收分保未到期责任准备金 | 否 | - |
| 应收分保已报未决 | reinsurance_reported | decimal | 18,10 | 是 | number | 0 | 应收分保已报未决 | 否 | - |
| 应收分保未报未决 | reinsurance_unreported | decimal | 18,10 | 是 | number | 0 | 应收分保未报未决 | 否 | - |
| 应收分保未决合计 | reinsurance_claim_total | decimal | 18,10 | 是 | number | 0 | 应收分保未决合计 | 否 | - |
| 应收分保合计 | reinsurance_total | decimal | 18,10 | 是 | number | 0 | 应收分保合计 | 否 | - |
| 失效保单现价 | lapsed_policy_value | decimal | 18,10 | 是 | number | 0 | 失效保单现价 | 否 | - |
| 零头月红利 | fractional_month_dividend | decimal | 18,10 | 是 | number | 0 | 零头月红利 | 否 | - |
| 应付未付红利 | unpaid_dividend | decimal | 18,10 | 是 | number | 0 | 应付未付红利 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表已存在于成本管理模块，负债产品信息模块直接复用该表

##### （4）TB0004 负债规模明细表 (t_liab_scale_detail)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 精算代码 | actuarial_code | varchar | 20 | 否 | text | - | 产品精算代码，以字母开头 | 是 | 取自产品属性表，按设计类型、精算代码排序 |
| 业务代码 | business_code | varchar | 20 | 否 | text | - | 产品业务代码 | 否 | 取自产品属性表 |
| 产品名称 | product_name | varchar | 100 | 否 | text | - | 产品全称 | 否 | 取自产品属性表 |
| 长短期标识 | term_flag | char | 1 | 否 | radio | L | 值域：L-长期，S-短期 | 否 | 取自产品属性表 |
| 险种主类 | insurance_main_type | varchar | 50 | 否 | text | - | 险种主类 | 否 | 取自产品属性表 |
| 险种细类 | insurance_sub_type | varchar | 50 | 否 | text | - | 险种细类 | 否 | 取自产品属性表 |
| 设计类型 | design_type | varchar | 20 | 否 | select | - | 值域：传统险、分红险、万能险、投连险 | 否 | 取自产品属性表 |
| 合理估计负债 | reasonable_liability | decimal | 18,10 | 是 | number | 0 | 合理估计负债 | 否 | 会计准备金明细表计算：最优估计+未建模+豁免准备金+持续奖准备金+失效保单现价 |
| 风险边际 | risk_margin | decimal | 18,10 | 是 | number | 0 | 风险边际 | 否 | 取自会计准备金明细表 |
| 剩余边际 | residual_margin | decimal | 18,10 | 是 | number | 0 | 剩余边际 | 否 | 取自会计准备金明细表 |
| 未决赔款准备金L | outstanding_claim_reserve_l | decimal | 18,10 | 是 | number | 0 | 长期险未决赔款准备金 | 否 | 长短期标识=L时取会计准备金明细表，否则为0 |
| 未到期责任准备金 | unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 未到期责任准备金 | 否 | 取自会计准备金明细表.短险未到期 |
| 未决赔款准备金S | outstanding_claim_reserve_s | decimal | 18,10 | 是 | number | 0 | 短期险未决赔款准备金 | 否 | 长短期标识=S时取会计准备金明细表，否则为0 |
| 万能投连险负债规模 | investment_linked_liability | decimal | 18,10 | 是 | number | 0 | 万能投连险负债规模 | 否 | 设计类型为投连险或万能险时计算：账户价值+会计准备金合计 |
| 应收分保未到期责任准备金 | receivable_unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保未到期责任准备金 | 否 | 险种主类为短期意外险或短期健康险时取值 |
| 应收分保未决赔款准备金 | receivable_outstanding_claim_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保未决赔款准备金 | 否 | 险种主类为短期意外险或短期健康险时取值 |
| 应收分保寿险责任准备金 | receivable_life_insurance_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保寿险责任准备金 | 否 | 险种主类为短期寿险或长期寿险时取值 |
| 应收分保长期健康险责任准备金 | receivable_long_term_health_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保长期健康险责任准备金 | 否 | 险种主类为长期健康险时取值 |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表通过加工计算得出，数据来源于产品属性表和会计准备金明细表，根据不同的业务规则进行计算和汇总

##### （5）TB0005 负债规模汇总表 (t_liab_scale_summary)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 设计类型 | design_type | varchar | 20 | 否 | select | - | 值域：传统险、分红险、万能险、投连险 | 是 | 汇总维度 |
| 合理估计负债 | reasonable_liability | decimal | 18,10 | 是 | number | 0 | 合理估计负债 | 否 | 按设计类型汇总负债规模明细表 |
| 风险边际 | risk_margin | decimal | 18,10 | 是 | number | 0 | 风险边际 | 否 | 按设计类型汇总负债规模明细表 |
| 剩余边际 | residual_margin | decimal | 18,10 | 是 | number | 0 | 剩余边际 | 否 | 按设计类型汇总负债规模明细表 |
| 未决赔款准备金L | outstanding_claim_reserve_l | decimal | 18,10 | 是 | number | 0 | 长期险未决赔款准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 未到期责任准备金 | unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 未到期责任准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 未决赔款准备金S | outstanding_claim_reserve_s | decimal | 18,10 | 是 | number | 0 | 短期险未决赔款准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 万能投连险负债规模 | investment_linked_liability | decimal | 18,10 | 是 | number | 0 | 万能投连险负债规模 | 否 | 按设计类型汇总负债规模明细表 |
| 应收分保未到期责任准备金 | receivable_unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保未到期责任准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 应收分保未决赔款准备金 | receivable_outstanding_claim_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保未决赔款准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 应收分保寿险责任准备金 | receivable_life_insurance_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保寿险责任准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 应收分保长期健康险责任准备金 | receivable_long_term_health_reserve | decimal | 18,10 | 是 | number | 0 | 应收分保长期健康险责任准备金 | 否 | 按设计类型汇总负债规模明细表 |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表通过加工计算得出，按账期和设计类型对负债规模明细表进行汇总，为管理层提供按设计类型分类的负债规模统计数据

##### （6）TB0006 非寿险负债规模汇总表 (t_liab_non_life_scale_summary)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 险种主类 | insurance_main_type | varchar | 50 | 否 | text | - | 值域：短期寿险、短期意外险、短期健康险 | 是 | 汇总维度 |
| 未到期责任准备金 | unearned_premium_reserve | decimal | 18,10 | 是 | number | 0 | 未到期责任准备金 | 否 | 按险种主类汇总负债规模明细表 |
| 未决赔款准备金 | outstanding_claim_reserve | decimal | 18,10 | 是 | number | 0 | 未决赔款准备金 | 否 | 按险种主类汇总负债规模明细表.未决赔款准备金S |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表通过加工计算得出，专门针对非寿险业务（短期寿险、短期意外险、短期健康险）进行负债规模统计，按险种主类对负债规模明细表进行汇总

##### （7）TB0007 缴费年期映射表 (t_liab_payment_period_mapping)
**表描述**：存储缴费年期与缴费年期分类的映射关系，用于新单规模保费计算

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 缴费年期 | payment_period | int | 4 | 否 | number | - | 缴费年期 | 是 | - |
| 缴费年期分类 | payment_period_category | varchar | 50 | 否 | text | - | 缴费年期分类 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表用于新单规模保费计算，建立缴费年期与缴费年期分类的映射关系

##### （8）TB0008 新单保费汇总表 (t_liab_new_premium_summary)

**表英文名**：t_liab_new_premium_summary
**表中文名**：新单保费汇总表
**所属模块**：负债产品信息(MD0001)
**表描述**：存储新单保费汇总数据，用于新单规模保费计算。导入时需要根据业务代码关联产品属性表(TB0001)获取产品信息，并通过缴费年期关联缴费年期映射表(TB0007)获取缴费年期分类，最终拼装成完整的新单保费汇总数据

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 业务代码 | business_code | varchar | 20 | 否 | text | - | 产品业务代码 | 是 | 对接系统，具体需联系谢国平/邓双，前期是导入 |
| 产品名称 | product_name | varchar | 100 | 否 | text | - | 产品全称 | 否 | 取自产品属性表，匹配列：产品名称，匹配字段：业务代码 |
| 设计类型 | design_type | varchar | 50 | 否 | text | - | 设计类型（传统险/分红险/万能险/投连险） | 否 | 取自产品属性表，匹配列：设计类型，匹配字段：业务代码 |
| 是否中短 | short_term_flag | char | 1 | 否 | radio | N | 值域：Y-是，N-否 | 否 | 取自产品属性表，匹配列：是否中短，匹配字段：业务代码 |
| 长短期标识 | term_type | char | 1 | 否 | radio | L | 值域：L-长期，S-短期 | 否 | 取自产品属性表，匹配列：长短期标识，匹配字段：业务代码 |
| 缴费频率 | payment_frequency | varchar | 20 | 否 | text | - | 缴费频率（趸交/期交） | 是 | - |
| 缴费年期 | payment_period | int | 4 | 否 | number | - | 缴费年期 | 是 | - |
| 缴费年期分类 | payment_period_category | varchar | 50 | 否 | text | - | 缴费年期分类 | 否 | 如果缴费频率=趸交，则缴费年期分类=趸交；否则取自缴费年期映射表 |
| 原保费 | original_premium | decimal | 18,10 | 是 | number | 0 | 原保费金额 | 否 | - |
| 初始费用 | initial_fee | decimal | 18,10 | 是 | number | 0 | 初始费用金额 | 否 | - |
| 新单保费合计 | new_premium_total | decimal | 18,10 | 是 | number | 0 | 新单保费合计金额 | 否 | 等于原保费+初始费用 |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表用于新单规模保费计算，不是纯导入表，而是在导入时进行数据关联和拼装：
> 1. **用户导入字段**：账期、业务代码、缴费频率、缴费年期、原保费、初始费用、备注
> 2. **关联产品属性表字段**：通过业务代码匹配获取产品名称、设计类型、是否中短、长短期标识
> 3. **关联缴费年期映射表字段**：如果缴费频率≠趸交，通过缴费年期匹配获取缴费年期分类；如果缴费频率=趸交，则缴费年期分类=趸交
> 4. **系统计算字段**：新单保费合计 = 原保费 + 初始费用

##### （9）TB0009 ALM新单保费统计表 (t_liab_alm_new_premium_statistics)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 6 | 否 | YYYYMM | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 统计类型 | statistics_type | varchar | 50 | 否 | text | - | 统计类型 | 是 | 项目固定：缴费年期、设计类型、设计类型区分中短、业务类别 |
| 统计类型细分 | statistics_sub_type | varchar | 50 | 否 | text | - | 统计类型细分 | 是 | 项目固定 |
| 新单保费合计 | new_premium_total | decimal | 18,10 | 是 | number | 0 | 新单保费合计金额 | 否 | 按统计类型和统计类型细分汇总新单保费汇总表数据 |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表通过加工计算得出，按不同统计维度对新单保费汇总表进行汇总统计，统计逻辑如下：
> 1. 统计类型=缴费年期：按缴费年期分类汇总新单保费合计
> 2. 统计类型=设计类型：按设计类型汇总新单保费合计
> 3. 统计类型=设计类型区分中短：按设计类型且是否中短=是汇总新单保费合计
> 4. 统计类型=业务类别：按长短期标识汇总新单保费合计

##### （10）TB0010 三年新业务规划表 (t_liab_three_year_business_plan)

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 账期 | accounting_period | varchar | 20 | 否 | YYYYMM | - | 格式：YYYYMM（如202303） | 是 | 标识业务账期 |
| 产品账户 | account_type | varchar | 50 | 否 | text | - | 产品账户类型 | 是 | - |
| 未来一年 | first_year | decimal | 16,10 | 是 | number | 0 | 未来第一年规划数据 | 否 | - |
| 未来二年 | second_year | decimal | 16,10 | 是 | number | 0 | 未来第二年规划数据 | 否 | - |
| 未来三年 | third_year | decimal | 16,10 | 是 | number | 0 | 未来第三年规划数据 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | text | - | 备注信息 | 否 | - |

> 注：此表用于存储三年新业务规划数据，支持按产品账户类型进行未来三年的业务规划和预测  

### 2.4 用例列表

> **说明**：产品属性数据和会计准备金明细数据的导入功能已在成本管理模块实现，本模块复用相关功能。

| 用例编号   | 用例名称             | 用例描述 | 模块编号 |
|--------|------------------| - | ---- |
| UC0001 | 导入资产负债表数据 | 导入资产负债表数据，写入t_liab_balance_sheet表 | MD0001 |
| UC0002 | 计算负债规模明细数据 | 根据产品属性表和会计准备金明细表计算负债规模明细，写入t_liab_scale_detail表 | MD0001 |
| UC0003 | 计算负债规模汇总数据 | 根据负债规模明细表按设计类型汇总，写入t_liab_scale_summary表 | MD0001 |
| UC0004 | 计算非寿险负债规模汇总数据 | 根据负债规模明细表按险种主类汇总，写入t_liab_non_life_scale_summary表 | MD0001 |
| UC0005 | 导入缴费年期映射数据 | 导入缴费年期与缴费年期分类的映射关系，写入t_liab_payment_period_mapping表 | MD0001 |
| UC0006 | 导入新单保费汇总数据 | 导入新单保费核心数据，关联产品属性表拼装完整信息，写入t_liab_new_premium_summary表 | MD0001 |
| UC0007 | 计算ALM新单保费统计数据 | 根据新单保费汇总表按不同统计维度汇总，写入t_liab_alm_new_premium_statistics表 | MD0001 |
| UC0008 | 导入三年新业务规划数据 | 导入三年新业务规划数据，写入t_liab_three_year_business_plan表 | MD0001 |

### 2.5 接口清单

| 接口编号   | 接口名称        | 接口描述 | 模块编号 |
| ------ |-------------| ---- | ---- |
| IF0001 | 导入资产负债表数据 | 导入资产负债表数据，写入t_liab_balance_sheet表 | MD0001 |
| IF0002 | 导入缴费年期映射数据 | 导入缴费年期与缴费年期分类的映射关系，写入t_liab_payment_period_mapping表 | MD0001 |
| IF0003 | 导入新单保费汇总数据 | 导入新单保费核心数据，关联产品属性表拼装完整信息，写入t_liab_new_premium_summary表 | MD0001 |
| IF0004 | 导入三年新业务规划数据 | 导入三年新业务规划数据，写入t_liab_three_year_business_plan表 | MD0001 |
| IF0005 | 计算负债规模明细数据 | 根据产品属性表和会计准备金明细表计算负债规模明细，写入t_liab_scale_detail表 | MD0001 |
| IF0006 | 计算负债规模汇总数据 | 根据负债规模明细表按设计类型汇总，写入t_liab_scale_summary表 | MD0001 |
| IF0007 | 计算非寿险负债规模汇总数据 | 根据负债规模明细表按险种主类汇总，写入t_liab_non_life_scale_summary表 | MD0001 |
| IF0008 | 计算ALM新单保费统计数据 | 根据新单保费汇总表按不同统计维度汇总，写入t_liab_alm_new_premium_statistics表 | MD0001 |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 负债规模 | 保险公司承担的各类保险责任的规模 | 反映保险公司负债业务的整体规模和结构 | 包括合理估计负债、风险边际、剩余边际等 |
| 产品属性 | 保险产品的基本特征和分类信息 | 用于产品管理和负债规模计算的基础数据 | 包括设计类型、险种分类、长短期标识等 |
| 会计准备金 | 按照会计准则计提的各类保险责任准备金 | 反映保险公司对未来保险责任的财务准备 | 包括最优估计、风险边际、剩余边际等组成部分 |
| 设计类型 | 保险产品按设计特点的分类 | 用于负债规模统计和管理的重要维度 | 包括传统险、分红险、万能险、投连险 |
| 险种主类 | 保险产品按险种特点的主要分类 | 用于区分不同类型保险业务的负债特征 | 包括长期寿险、短期寿险、短期意外险、短期健康险等 |

## 4. 功能需求

### 4.1 负债产品信息模块

#### 4.1.1 原型图

忽略

#### 4.1.2 接口功能

负债产品信息模块提供数据导入接口，支持产品属性、资产负债表和会计准备金明细数据的导入。

#### 4.1.3 功能描述

##### 4.1.3.1 负债产品信息管理流程

###### 4.1.3.1.1 功能概要
通过数据导入和计算处理，完成负债产品信息的管理：基础数据导入、负债规模计算和汇总统计。

###### 4.1.3.1.2 业务总流程
```mermaid
flowchart TD
    %% 基础数据导入
    A[产品属性数据导入<br/>已在成本管理模块实现]
    B[UC0001: 导入资产负债表数据]
    C[会计准备金明细数据导入<br/>已在成本管理模块实现]
    G[UC0005: 导入缴费年期映射数据]
    H[UC0006: 导入新单保费汇总数据]
    J[UC0008: 导入三年新业务规划数据]

    %% 负债规模计算
    D[UC0002: 计算负债规模明细数据]
    E[UC0003: 计算负债规模汇总数据]
    F[UC0004: 计算非寿险负债规模汇总数据]

    %% 新单保费计算
    I[UC0007: 计算ALM新单保费统计数据]

    %% 计算关系
    A --> D
    C --> D
    D --> E
    D --> F

    %% 新单保费数据流
    A --> H
    G --> H
    H --> I
```

###### 4.1.3.1.3 用例描述

> **字典数据说明**：负债产品信息模块复用成本管理模块的字典数据，具体映射关系如下：
> - **长短期标识**：使用 `cost_term_type` 字典（L-长期，S-短期）
> - **设计类型**：使用 `cost_design_type` 字典（传统险、分红险、万能险、投连险、普通账户）
> - **险种主类**：使用 `cost_insurance_main_type` 字典（长期寿险、短期寿险、短期意外险、短期健康险等）
> - **险种细类**：使用 `cost_insurance_sub_type` 字典（年金险、两全险、终身寿险、定期寿险等）
> - **资产负债表类别**：需新增 `liab_balance_category` 字典（资产、负债、所有者权益）

###### 4.1.3.1.3.1 导入资产负债表数据(UC0001)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入资产负债表数据 |
|   功能描述   | 导入资产负债表基础数据 |
|    参与者    | 财务管理部人员 |
|    原型图    | PT0001 |
|    关联表    | TB0002 |
|    前置用例    | |

**业务流程**：
1. 用户访问资产负债表管理页面
2. 下载Excel导入模板
3. 填写资产负债表数据（账期、类别、项目名称、期末余额、期初余额等）
4. 类别字段使用下拉选择：资产、负债、所有者权益（对应 `liab_balance_category` 字典）
5. 上传Excel文件进行数据导入
6. 系统验证数据格式和业务规则
7. 导入成功后在列表页显示资产负债表信息

###### 4.1.3.1.3.2 计算负债规模明细数据(UC0002)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算负债规模明细数据 |
|   功能描述   | 根据产品属性表和会计准备金明细表计算负债规模明细 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0001, TB0003, TB0004 |
|    前置用例    | 产品属性数据和会计准备金明细数据已导入 |

**业务流程**：

1. 读取产品属性表数据作为主数据源
2. 按精算代码匹配会计准备金明细表数据
3. 根据详细业务规则计算各类负债规模字段
4. 确保产品属性表有多少条数据，负债规模明细表就有多少条数据
5. 将计算结果写入负债规模明细表

**字典数据应用**：

- **长短期标识判断**：使用 `cost_term_type` 字典值（L/S）进行条件计算
- **设计类型分类**：按 `cost_design_type` 字典顺序排序（传统险→分红险→万能险→投连险）
- **险种主类匹配**：根据 `cost_insurance_main_type` 字典值进行应收分保字段计算
- **普通账户标识**：生成设计类型为"普通账户"的汇总记录

**步骤1.** 数据准备

(1) 读取产品属性表(TB0001)数据：
   a. 读取指定账期的所有产品数据
   b. 按设计类型（依次为传统险、分红险、万能险、投连险）、精算代码进行排序

(2) 读取会计准备金明细表(TB0003)数据：
   a. 读取指定账期的所有会计准备金数据
   b. 通过精算代码匹配产品属性表数据

**步骤2.** 计算过程

(1) 以产品属性表为主表，通过精算代码匹配会计准备金明细表数据

(2) 字段计算逻辑：
   a. 基础字段直接取自产品属性表：账期、精算代码、业务代码、产品名称、长短期标识、险种主类、险种细类、设计类型

   b. 合理估计负债 = 最优估计 + 未建模 + 豁免准备金 + 持续奖准备金 + 失效保单现价

   c. 风险边际、剩余边际直接取自会计准备金明细表

   d. 未决赔款准备金L：如果长短期标识=L，取未决赔款准备金；否则为0

   e. 未到期责任准备金：取短险未到期

   f. 未决赔款准备金S：如果长短期标识=S，取未决赔款准备金；否则为0

   g. 万能投连险负债规模：如果设计类型为投连险或万能险，取账户价值+会计准备金合计；否则为0

   h. 应收分保未到期责任准备金：如果险种主类为短期意外险或短期健康险，取应收分保未到期责任准备金；否则为0

   i. 应收分保未决赔款准备金：如果险种主类为短期意外险或短期健康险，取应收分保未决合计；否则为0

   j. 应收分保寿险责任准备金：如果险种主类为短期寿险或长期寿险，取应收分保合计；否则为0

   k. 应收分保长期健康险责任准备金：如果险种主类为长期健康险，取应收分保合计；否则为0

(3) 当产品属性表的产品在会计准备金明细表中匹配不到时，相关字段默认为0

**步骤3.** 普通账户汇总计算

(1) 计算普通账户的未到期责任准备金：
   a. 筛选设计类型为传统险、分红险、万能险的记录
   b. 汇总计算：SUM(传统险、分红险、万能险的未到期责任准备金)

(2) 计算普通账户的未决赔款准备金：
   a. 筛选设计类型为传统险、分红险、万能险的记录
   b. 汇总计算：SUM(传统险、分红险、万能险的未决赔款准备金S)

(3) 生成普通账户汇总记录：
   a. 设计类型标记为"普通账户"
   b. 其他字段按相同规则汇总

**步骤4.** 数据入表

(1) 将计算结果写入TB0004表(负债规模明细表)
(2) 包含各产品明细记录和普通账户汇总记录

###### 4.1.3.1.3.3 计算负债规模汇总数据(UC0003)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算负债规模汇总数据 |
|   功能描述   | 根据负债规模明细表按设计类型汇总 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0004, TB0005 |
|    前置用例    | UC0002 |

**业务流程**：

1. 读取负债规模明细表数据
2. 按账期和设计类型分组汇总各字段
3. 将汇总结果写入负债规模汇总表

**字典数据应用**：

- **设计类型分组**：按 `cost_design_type` 字典值进行分组汇总（传统险、分红险、万能险、投连险、普通账户）
- **排序规则**：按 `cost_design_type` 字典顺序排序输出结果

**步骤1.** 数据准备

(1) 读取负债规模明细表(TB0004)数据：
   a. 读取指定账期的所有负债规模明细数据
   b. 验证数据完整性，确保关键字段不为空

**步骤2.** 汇总计算

(1) 按账期和设计类型分组汇总：
   a. 分组字段：账期(accounting_period)、设计类型(design_type)
   b. 汇总字段：
      - 合理估计负债：SUM(reasonable_liability)
      - 风险边际：SUM(risk_margin)
      - 剩余边际：SUM(residual_margin)
      - 未决赔款准备金L：SUM(outstanding_claim_reserve_l)
      - 未到期责任准备金：SUM(unearned_premium_reserve)
      - 未决赔款准备金S：SUM(outstanding_claim_reserve_s)
      - 万能投连险负债规模：SUM(investment_linked_liability)
      - 应收分保未到期责任准备金：SUM(receivable_unearned_premium_reserve)
      - 应收分保未决赔款准备金：SUM(receivable_outstanding_claim_reserve)
      - 应收分保寿险责任准备金：SUM(receivable_life_insurance_reserve)
      - 应收分保长期健康险责任准备金：SUM(receivable_long_term_health_reserve)

(2) 设计类型排序：
   a. 按设计类型排序：传统险、分红险、万能险、投连险

**步骤3.** 数据入表

(1) 清理目标表数据：删除指定账期的历史汇总数据
(2) 将汇总结果写入TB0005表(负债规模汇总表)

###### 4.1.3.1.3.4 计算非寿险负债规模汇总数据(UC0004)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算非寿险负债规模汇总数据 |
|   功能描述   | 根据负债规模明细表按险种主类汇总 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0004, TB0006 |
|    前置用例    | UC0002 |

**业务流程**：

1. 读取负债规模明细表数据
2. 筛选非寿险业务相关数据
3. 按账期和险种主类分组汇总
4. 将汇总结果写入非寿险负债规模汇总表

**字典数据应用**：

- **险种主类筛选**：使用 `cost_insurance_main_type` 字典值筛选非寿险业务（短期寿险、短期意外险、短期健康险）
- **设计类型识别**：通过 `cost_design_type` 字典值"普通账户"获取汇总基数
- **差额计算逻辑**：短期健康险通过普通账户总额减去短期寿险和短期意外险计算得出

**步骤1.** 数据准备

(1) 读取TB0004表(负债规模明细表)数据：
   a. 读取指定账期的所有负债规模明细数据
   b. 筛选非寿险业务数据：险种主类 IN ('短期寿险', '短期意外险', '短期健康险')
   c. 获取普通账户汇总记录（设计类型='普通账户'）

**步骤2.** 汇总计算

(1) 计算短期寿险和短期意外险的汇总数据：
   a. 短期寿险汇总：
      - 未到期责任准备金：SUM(险种主类='短期寿险'的未到期责任准备金)
      - 未决赔款准备金：SUM(险种主类='短期寿险'的未决赔款准备金S)

   b. 短期意外险汇总：
      - 未到期责任准备金：SUM(险种主类='短期意外险'的未到期责任准备金)
      - 未决赔款准备金：SUM(险种主类='短期意外险'的未决赔款准备金S)

(2) 计算短期健康险的汇总数据（差额计算）：
   a. 短期健康险未到期责任准备金：
      - 普通账户未到期责任准备金 - (短期寿险未到期责任准备金 + 短期意外险未到期责任准备金)

   b. 短期健康险未决赔款准备金：
      - 普通账户未决赔款准备金 - (短期寿险未决赔款准备金 + 短期意外险未决赔款准备金)

**步骤3.** 数据入表

(1) 将汇总结果写入TB0006表(非寿险负债规模汇总表)：
   a. 短期寿险汇总记录
   b. 短期意外险汇总记录
   c. 短期健康险汇总记录（通过差额计算得出）

###### 4.1.3.1.3.5 导入缴费年期映射数据(UC0005)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入缴费年期映射数据 |
|   功能描述   | 导入缴费年期与缴费年期分类的映射关系 |
|    参与者    | 精算管理部人员 |
|    原型图    | PT0002 |
|    关联表    | TB0007 |
|    前置用例    | |

**业务流程**：

1. 用户访问缴费年期映射管理页面
2. 下载Excel导入模板
3. 填写缴费年期映射数据（账期、缴费年期、缴费年期分类等）
4. 上传Excel文件进行数据导入
5. 系统验证数据格式和业务规则
6. 导入成功后在列表页显示缴费年期映射信息

###### 4.1.3.1.3.6 导入新单保费汇总数据(UC0006)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 服务 |
|   用例名称   | 导入新单保费汇总数据 |
|   功能描述   | 导入新单保费核心数据，关联产品属性表拼装完整信息 |
|    参与者    | 精算管理部人员 |
|    原型图    | PT0003 |
|    关联表    | TB0008, TB0001, TB0007 |
|    前置用例    | 产品属性数据已导入，缴费年期映射数据已导入 |

**业务流程**：

1. 用户访问新单保费汇总管理页面
2. 下载Excel导入模板
3. 填写新单保费核心数据（账期、业务代码、缴费频率、缴费年期、原保费、初始费用等）
4. 上传Excel文件进行数据导入
5. 系统进行数据关联和拼装处理
6. 导入成功后在列表页显示完整的新单保费汇总信息

**数据拼装规则**：

**步骤1.** 用户导入字段验证

(1) 必填字段验证：
   a. 账期：格式YYYYMM，如202506
   b. 业务代码：产品业务代码，对接系统获取（前期通过导入）
   c. 缴费频率：趸交/期交
   d. 缴费年期：数值类型
   e. 原保费：金额，可为0
   f. 初始费用：金额，可为0

**步骤2.** 关联产品属性表数据

(1) 通过业务代码匹配产品属性表(TB0001)：
   a. 产品名称：取产品属性表.产品名称
   b. 设计类型：取产品属性表.设计类型（传统险/分红险/万能险/投连险）
   c. 是否中短：取产品属性表.是否中短（Y-是，N-否）
   d. 长短期标识：取产品属性表.长短期标识（L-长期，S-短期）

(2) 业务代码匹配失败处理：
   a. 如果在产品属性表中找不到对应的业务代码，导入失败
   b. 提示错误信息："业务代码[XXX]在产品属性表中不存在"

**步骤3.** 关联缴费年期映射表数据

(1) 缴费年期分类计算规则：
   a. 如果设计类型=投连险或万能险：缴费频率强制赋值为趸交，缴费年期分类=趸交
   b. 如果缴费频率=趸交：缴费年期分类=趸交
   c. 如果缴费频率≠趸交：
      - 通过缴费年期匹配缴费年期映射表(TB0007)
      - 取缴费年期映射表.缴费年期分类
      - 如果匹配失败，导入失败并提示："缴费年期[XXX]在缴费年期映射表中不存在"

**步骤4.** 系统计算字段

(1) 新单保费合计计算：
   a. 新单保费合计 = 原保费 + 初始费用
   b. 计算结果保留10位小数

**步骤5.** 数据验证

(1) 业务规则验证：
   a. 同一账期内，业务代码+缴费频率+缴费年期组合不能重复
   b. 原保费和初始费用不能同时为负数
   c. 新单保费合计不能为负数

**步骤6.** 数据入表

(1) 将拼装完成的数据写入TB0008表(新单保费汇总表)
(2) 包含用户导入字段、关联获取字段和系统计算字段

**导入模板字段**：

| 字段名称 | 字段说明 | 是否必填 | 数据来源 |
|----------|----------|----------|----------|
| 账期 | 格式YYYYMM | 是 | 用户填写 |
| 业务代码 | 产品业务代码 | 是 | 用户填写 |
| 缴费频率 | 趸交/期交 | 是 | 用户填写 |
| 缴费年期 | 缴费年期 | 是 | 用户填写 |
| 原保费 | 原保费金额 | 否 | 用户填写 |
| 初始费用 | 初始费用金额 | 否 | 用户填写 |
| 备注 | 备注信息 | 否 | 用户填写 |

**系统自动填充字段**：

| 字段名称 | 字段说明 | 数据来源 |
|----------|----------|----------|
| 产品名称 | 产品全称 | 产品属性表.产品名称 |
| 设计类型 | 设计类型 | 产品属性表.设计类型 |
| 是否中短 | 是否中短期产品 | 产品属性表.是否中短 |
| 长短期标识 | 长短期标识 | 产品属性表.长短期标识 |
| 缴费年期分类 | 缴费年期分类 | 缴费年期映射表.缴费年期分类或"趸交" |
| 新单保费合计 | 新单保费合计金额 | 原保费+初始费用 |

###### 4.1.3.1.3.7 计算ALM新单保费统计数据(UC0007)

|   用例标识   | 用例描述             |
| :----------: | ------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算ALM新单保费统计数据 |
|   功能描述   | 根据新单保费汇总表按不同统计维度汇总 |
|    参与者    | 系统 |
|    原型图    | |
|    关联表    | TB0008, TB0009 |
|    前置用例    | UC0006 |

**业务流程**：

1. 读取新单保费汇总表数据
2. 按4种固定统计类型进行分组汇总
3. 将统计结果写入ALM新单保费统计表

**统计规则说明**：

**步骤1.** 数据准备

(1) 读取新单保费汇总表(TB0008)数据：
   a. 读取指定账期的所有新单保费汇总数据
   b. 验证数据完整性，确保关键字段不为空

**步骤2.** 统计计算

> **统计类型说明**：系统固定生成4种统计类型，每种统计类型下包含多个统计类型细分

(1) 统计类型=缴费年期：
   a. 按缴费年期分类分组汇总新单保费合计，仅统计长期产品
   b. 匹配条件：新单保费汇总表.缴费年期分类=ALM新单保费统计表.统计类型细分 AND 新单保费汇总表.长短期标识=L
   c. 预期统计类型细分：趸交、3年期及以内、3-5年期（含5年期）、5-10年期（含10年期）、10年期以上

(2) 统计类型=设计类型：
   a. 按设计类型分组汇总新单保费合计
   b. 匹配条件：新单保费汇总表.设计类型=ALM新单保费统计表.统计类型细分
   c. 预期统计类型细分：传统险、分红险、万能险、投连险

(3) 统计类型=设计类型区分中短：
   a. 按设计类型分组汇总新单保费合计，仅统计中短期产品
   b. 匹配条件：新单保费汇总表.设计类型=ALM新单保费统计表.统计类型细分 AND 新单保费汇总表.是否中短=Y
   c. 统计类型细分命名：设计类型+"中短"（如：传统险中短）
   d. 预期统计类型细分：传统险中短、分红险中短、万能险中短、投连险中短

(4) 统计类型=业务类别：
   a. 按长短期标识分组汇总新单保费合计
   b. 匹配条件：新单保费汇总表.长短期标识=ALM新单保费统计表.统计类型细分
   c. 长短期标识映射：L→长期，S→短期
   d. 预期统计类型细分：长期、短期

**步骤3.** 数据入表

(1) 清理目标表数据：删除指定账期的历史统计数据
(2) 将统计结果写入TB0009表(ALM新单保费统计表)
(3) 预期生成记录数：5+4+4+2=15条统计记录

**输出数据示例**：

| 统计类型 | 统计类型细分 | 新单保费合计 |
|----------|--------------|--------------|
| 缴费年期 | 趸交 | 256512.153 |
| 缴费年期 | 3年期及以内 | 256513.153 |
| 缴费年期 | 3-5年期（含5年期） | 256514.153 |
| 缴费年期 | 5-10年期（含10年期） | 256515.153 |
| 缴费年期 | 10年期以上 | 256516.153 |
| 设计类型 | 传统险 | 256517.153 |
| 设计类型 | 分红险 | 256518.153 |
| 设计类型 | 万能险 | 256519.153 |
| 设计类型 | 投连险 | 256520.153 |
| 设计类型区分中短 | 传统险中短 | 256521.153 |
| 设计类型区分中短 | 分红险中短 | 256522.153 |
| 设计类型区分中短 | 万能险中短 | 256523.153 |
| 设计类型区分中短 | 投连险中短 | 256524.153 |
| 业务类别 | 长期 | 256525.153 |
| 业务类别 | 短期 | 256526.153 |
