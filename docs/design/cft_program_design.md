# 现金流量表管理需求规格说明书

## 文档信息

| 项目名称 | 现金流量表管理 |
| -------- | ------------ |
| 文档版本 | V1.0         |
| 作者     |        |
| 创建日期 | 2025-01-20   |
| 状态     | 已确认       |

## 变更履历

| 版本 | 日期       | 变更描述 | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-01-20 | 初次编写 |  | 通过 |

## 1. 需求概述

### 1.1 需求背景

现金流量表管理是保险公司财务管理的重要组成部分，需要对现金流量表基础数据进行管理，并通过现金流项目映射表计算生成ALMCF实际发生数本年累计表，为资产负债管理和现金流测试提供数据支持。

### 1.2 需求目标

建立完整的现金流量表数据管理体系，实现：
1. 现金流量表数据的导入和管理
2. 现金流项目映射表的配置和管理  
3. ALMCF实际发生数本年累计表的自动计算和生成
4. 支持多账户类型的现金流数据管理

### 1.3 需求范围

本需求涵盖现金流量表相关的数据导入、映射配置、计算、查询、导出等功能。

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 |      |      |      |
| 业务负责人 |      |      |      |
| 技术负责人 |      |      |      |

## 2. 业务架构

### 2.1 业务模块关系图

```mermaid
flowchart TD
    A[现金流量表] --> C[ALMCF实际发生数本年累计表]
    B[现金流项目映射表] --> C
    C --> D[资产负债管理]
    C --> E[现金流测试]
```

### 2.2 模块列表

| 模块编号 | 模块名称    | 模块英文名 | 英文缩写 |
| -------- |---------| ---------- | -------- |
| MD0003 | 现金流量表管理    | cash_flow_table_management | cft |

#### 2.2.1 模块说明

**现金流量表管理(MD0003)**：
- 现金流量表(t_base_cash_flow_statement)：管理现金流量表基础数据
- 现金流项目映射表(t_base_cash_flow_item_mapping)：管理现金流项目映射关系
- ALMCF实际发生数本年累计表(t_cft_almcf_actual_ytd)：计算生成ALMCF实际发生数据

### 2.3 数据模型

#### 2.3.1 表间关系

```mermaid
erDiagram
    %% 源数据表（需要直接导入的数据）
    现金流量表 ||--|| 源数据 : "直接导入"
    现金流项目映射表 ||--|| 配置数据 : "手工配置"

    %% 计算流程
    现金流量表 }o--o{ ALMCF实际发生数本年累计表 : "通过映射计算"
    现金流项目映射表 }o--o{ ALMCF实际发生数本年累计表 : "映射关系"
    ALMCF实际发生数本年累计表 }o--o{ ALMCF实际发生数本季度累计表 : "季度计算"
```

#### 2.3.2 表名字典

| 表编号    | 表中文名 | 表英文名                            | 所属模块 | 备注 |
|--------| ------ |---------------------------------| ------ | ------ |
| TB0020 | 现金流量表 | t_base_cash_flow_statement        | MD0003 | 导入数据 |
| TB0021 | 现金流项目映射表 | t_base_cash_flow_item_mapping | MD0003 | 配置数据 |
| TB0022 | ALMCF实际发生数本年累计表 | t_cft_almcf_actual_ytd  | MD0003 | 计算结果 |
| TB0023 | ALMCF实际发生数本季度累计表 | t_cft_almcf_actual_quarterly_cumulative | MD0003 | 计算结果 |

#### 2.3.3 表集

##### 2.3.3.1 现金流量表(TB0020)

**表英文名**：t_base_cash_flow_statement
**表中文名**：现金流量表
**所属模块**：现金流量表管理(MD0003)
**表描述**：存储现金流量表数据，包括公司整体、分红账户、万能账户、投连账户等不同账户类型的现金流数据

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 自增主键 | 是 | 主键 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202506） | 是 | 标识业务账期 |
| 级别类型 | level_type | varchar | 20 | 否 | 文本 | - | 项目级别分类 | 否 | 不使用字典，因为值会很多且动态变化 |
| 项目 | item_name | varchar | 100 | 否 | 文本 | - | 现金流项目名称 | 是 | - |
| 公司整体 | company_overall | decimal | 18,10 | 是 | 数值 | 0 | 公司整体现金流数据 | 否 | - |
| 分红账户 | dividend_account | decimal | 18,10 | 是 | 数值 | 0 | 分红账户现金流数据 | 否 | - |
| 万能账户 | universal_account | decimal | 18,10 | 是 | 数值 | 0 | 万能账户现金流数据 | 否 | - |
| 投连账户 | investment_linked_account | decimal | 18,10 | 是 | 数值 | 0 | 投连账户现金流数据 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | - |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | - |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | - |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | - |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | - |

##### 2.3.3.2 现金流项目映射表(TB0021)

**表英文名**：t_base_cash_flow_item_mapping
**表中文名**：现金流项目映射表
**所属模块**：现金流量表管理(MD0003)
**表描述**：存储现金流量表项目与资负现金流测试项目的映射关系

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 自增主键 | 是 | 主键 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202306） | 是 | 标识业务账期 |
| 现金流量表项目 | cash_flow_item | varchar | 100 | 否 | 文本 | - | 现金流量表对应项目 | 否 | - |
| 资负现金流测试项目 | test_item | varchar | 100 | 否 | 文本 | - | 资负现金流测试对应项目 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | - |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | - |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | - |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | - |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | - |

##### 2.3.3.3 ALMCF实际发生数本年累计表(TB0022)

**表英文名**：t_cft_almcf_actual_ytd
**表中文名**：ALMCF实际发生数本年累计表
**所属模块**：现金流量表管理(MD0003)
**表描述**：存储ALMCF实际发生数本年累计数据，通过现金流量表和映射表计算生成

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 主键，自增长 | 是 | 系统字段 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式YYYYMM | 是 | 标识业务账期 |
| 项目名称 | item_name | varchar | 100 | 否 | 文本 | - | 项目中文名称 | 是 | - |
| 传统账户 | traditional_account | decimal | 20,2 | 是 | 数值 | 0 | 传统账户实际发生金额 | 否 | 等于公司整体-分红账户-万能账户-投连账户 |
| 分红账户 | bonus_account | decimal | 20,2 | 是 | 数值 | 0 | 分红账户实际发生金额 | 否 | 通过映射表计算 |
| 万能账户 | universal_account | decimal | 20,2 | 是 | 数值 | 0 | 万能账户实际发生金额 | 否 | 通过映射表计算 |
| 投连账户 | investment_account | decimal | 20,2 | 是 | 数值 | 0 | 投连账户实际发生金额 | 否 | 通过映射表计算 |
| 普通账户 | ordinary_account | decimal | 20,2 | 是 | 数值 | 0 | 普通账户实际发生金额 | 否 | 等于公司整体-投连账户 |
| 公司整体 | total_account | decimal | 20,2 | 是 | 数值 | 0 | 各账户金额合计 | 否 | 通过映射表计算 |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | 系统字段 |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | 系统字段 |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | 系统字段 |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | 系统字段 |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | 系统字段 |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | 系统字段 |

##### 2.3.3.4 ALMCF实际发生数本季度累计表(TB0023)

**表英文名**：t_cft_almcf_actual_quarterly_cumulative
**表中文名**：ALMCF实际发生数本季度累计表
**所属模块**：现金流量表管理(MD0003)
**表描述**：存储ALMCF实际发生数本季度累计数据，通过ALMCF实际发生数本年累计表计算生成

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 主键，自增长 | 是 | 系统字段 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 项目 | item_name | varchar | 100 | 否 | 文本 | - | 现金流项目名称，如：保费收入、赔付支出等 | 是 | 现金流分类项目 |
| 公司整体 | total_account | decimal | 18,2 | 是 | 数值 | 0 | 公司整体金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.公司整体，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.公司整体 |
| 普通账户 | general_account | decimal | 18,2 | 是 | 数值 | 0 | 普通账户金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.普通账户，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.普通账户 |
| 传统账户 | traditional_account | decimal | 18,2 | 是 | 数值 | 0 | 传统账户金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.传统账户，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.传统账户 |
| 分红账户 | dividend_account | decimal | 18,2 | 是 | 数值 | 0 | 分红账户金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.分红账户，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.分红账户 |
| 万能账户 | universal_account | decimal | 18,2 | 是 | 数值 | 0 | 万能账户金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.万能账户，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.万能账户 |
| 投连账户 | investment_account | decimal | 18,2 | 是 | 数值 | 0 | 投连账户金额 | 否 | 1.非1月/2月/3月：取自ALMCF实际发生数表本年累计表.投连账户，本账期数-上一季度月对应的账期数；2.1月/2月/3月：直接取ALMCF实际发生数表本年累计表.投连账户 |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | 系统字段 |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | 系统字段 |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | 系统字段 |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | 系统字段 |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | 系统字段 |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | 系统字段 |

### 2.4 用例列表

| 用例编号   | 用例名称         | 用例描述                   | 模块编号 |
|--------|--------------|------------------------| ---- |
| UC0020 | 导入现金流量表数据     | 导入现金流量表数据，写入TB0020表     | MD0003 |
| UC0021 | 管理现金流项目映射 | 管理现金流项目映射关系，写入TB0021表 | MD0003 |
| UC0022 | 计算ALMCF实际发生数本年累计表  | 计算ALMCF实际发生数本年累计表，写入TB0022表  | MD0003 |
| UC0023 | 计算ALMCF实际发生数本季度累计表  | 计算ALMCF实际发生数本季度累计表，写入TB0023表  | MD0003 |

### 2.5 接口清单

| 接口编号   | 接口名称        | 接口描述 | 模块编号 |
| ------ |-------------| ---- | ---- |
| IF0020 | 导入现金流量表数据   | 导入现金流量表数据，写入TB0020表 | MD0003 |
| IF0021 | 管理现金流项目映射    | 管理现金流项目映射关系，写入TB0021表 | MD0003 |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 现金流量表 | 反映企业在特定期间现金和现金等价物流入和流出情况的财务报表 | 显示公司经营、投资、筹资活动产生的现金流量 | 按不同账户类型分别统计 |
| 现金流项目映射 | 现金流量表项目与资负现金流测试项目的对应关系 | 用于将现金流量表数据转换为ALMCF格式 | 需要手工配置维护 |
| ALMCF | Asset Liability Management Cash Flow，资产负债管理现金流 | 用于资产负债管理的现金流数据格式 | 按不同账户类型分别计算 |
| 分红账户 | 分红保险产品对应的账户 | 具有分红功能的保险产品现金流 | 包含红利支出等特有项目 |
| 万能账户 | 万能保险产品对应的账户 | 具有投资功能的保险产品现金流 | 不包含红利支出 |
| 投连账户 | 投资连结保险产品对应的账户 | 投资风险由客户承担的保险产品现金流 | 不包含红利支出 |

## 4. 功能需求

### 4.1 现金流量表管理模块

#### 4.1.1 功能概要

现金流量表管理模块主要实现以下功能：
1. 导入基础数据（现金流量表）
2. 配置现金流项目映射关系
3. 计算ALMCF实际发生数本年累计表
4. 支持不同账户类型的现金流数据管理
5. 提供查询和导出功能

#### 4.1.2 业务总流程

```mermaid
flowchart TD
    %% 源数据表导入
    A[UC0020: 导入现金流量表数据]
    B[UC0021: 管理现金流项目映射]

    %% 计算过程
    C[UC0022: 计算ALMCF实际发生数本年累计表]

    %% 数据流向关系
    A --> C
    B --> C
```

#### 4.1.3 用例描述

##### 4.1.3.1 导入现金流量表数据(UC0020)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 导入现金流量表数据                |
|   功能描述   | 导入现金流量表数据 |
|    参与者    | 财务部人员                     |
|    原型图    | PT0020                                |
|    关联表    | TB0020                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击现金流量表菜单,跳转至现金流量表列表页] --> B[点击导入按钮,弹出导入窗口页]
    B --> C[下载Excel模板]
    C --> D[基于Excel模板填写待导入数据]
    D --> E[点击导入按钮,选择对应Excel文件点确认]
    E --> F[页面调用IF0020接口上传Excel内容]
    F --> G[导入完成后,列表页显示导入记录信息]
```

##### 4.1.3.2 管理现金流项目映射(UC0021)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 管理现金流项目映射                |
|   功能描述   | 管理现金流项目映射关系 |
|    参与者    | 精算部人员                     |
|    原型图    | PT0021                                |
|    关联表    | TB0021                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击现金流项目映射菜单,跳转至映射列表页] --> B[点击新增按钮,弹出新增窗口页]
    B --> C[填写现金流量表项目和资负现金流测试项目映射关系]
    C --> D[点击确认按钮保存映射关系]
    D --> E[页面调用IF0021接口保存映射数据]
    E --> F[保存完成后,列表页显示映射记录信息]
```

##### 4.1.3.3 计算ALMCF实际发生数本年累计表(UC0022)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算ALMCF实际发生数本年累计表                |
|   功能描述   | 计算ALMCF实际发生数本年累计表 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0020,TB0021,TB0022                        |
|    前置用例    | UC0020,UC0021                       |

**步骤1.** 数据准备

(1) 读取现金流量表数据(TB0020)
(2) 读取现金流项目映射表数据(TB0021)
(3) 确定计算账期

**步骤2.** 数据计算

根据不同项目类型进行计算：

(1) 对于项目="保费收入"、"减：赔付支出"、"其中：满期给付"、"减：退保支出"、"减：红利支出"、"其中：业务及管理费"、"佣金及手续费"、"减：再保业务支出净额"：
   a. 通过现金流项目映射表转换为现金流量表中的项目
   b. 分红账户：查询现金流量表.分红账户，匹配字段：现金流量表.项目
   c. 万能账户：查询现金流量表.万能账户，匹配字段：现金流量表.项目
   d. 投连账户：查询现金流量表.投连账户，匹配字段：现金流量表.项目
   e. 公司整体：查询现金流量表.公司整体，匹配字段：现金流量表.项目

(2) 对于项目="1.3其他业务现金流"：
   a. 通过现金流项目映射表转换为现金流量表中的项目
   b. 分红账户：查询现金流量表.分红账户*(-1)，匹配字段：现金流量表.项目
   c. 万能账户：查询现金流量表.万能账户*(-1)，匹配字段：现金流量表.项目
   d. 投连账户：查询现金流量表.投连账户*(-1)，匹配字段：现金流量表.项目
   e. 公司整体：查询现金流量表.公司整体*(-1)，匹配字段：现金流量表.项目

(3) 对于项目="减：股利支出"：
   a. 分红账户：赋值为0
   b. 万能账户：赋值为0
   c. 投连账户：赋值为0

(4) 对于项目="减：费用支出"：
   a. 各账户金额 = "其中：业务及管理费" + "佣金及手续费"

(5) 计算衍生字段：
   a. 传统账户 = 公司整体 - 分红账户 - 万能账户 - 投连账户
   b. 普通账户 = 公司整体 - 投连账户

**步骤3.** 数据入表

(1) 将计算结果按照ALMCF实际发生数本年累计表(TB0022)的结构组织数据
(2) 写入TB0022表

##### 4.1.3.4 计算ALMCF实际发生数本季度累计表(UC0023)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算ALMCF实际发生数本季度累计表                |
|   功能描述   | 计算ALMCF实际发生数本季度累计表 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0022,TB0023                        |
|    前置用例    | UC0022                       |

**步骤1.** 数据准备

(1) 读取ALMCF实际发生数本年累计表数据(TB0022)
(2) 确定计算账期

**步骤2.** 数据计算

根据账期月份进行不同的计算逻辑：

(1) 对于1月/2月/3月（第一季度）：
   a. 直接取ALMCF实际发生数本年累计表对应账期的数据
   b. 公司整体 = ALMCF实际发生数本年累计表.公司整体
   c. 普通账户 = ALMCF实际发生数本年累计表.普通账户
   d. 传统账户 = ALMCF实际发生数本年累计表.传统账户
   e. 分红账户 = ALMCF实际发生数本年累计表.分红账户
   f. 万能账户 = ALMCF实际发生数本年累计表.万能账户
   g. 投连账户 = ALMCF实际发生数本年累计表.投连账户

(2) 对于非1月/2月/3月（第二、三、四季度）：
   a. 计算上一季度末月份的账期（如当前为6月，则上一季度末为3月）
   b. 获取本账期的ALMCF实际发生数本年累计表数据
   c. 获取上一季度末账期的ALMCF实际发生数本年累计表数据
   d. 公司整体 = 本账期.公司整体 - 上一季度末账期.公司整体
   e. 普通账户 = 本账期.普通账户 - 上一季度末账期.普通账户
   f. 传统账户 = 本账期.传统账户 - 上一季度末账期.传统账户
   g. 分红账户 = 本账期.分红账户 - 上一季度末账期.分红账户
   h. 万能账户 = 本账期.万能账户 - 上一季度末账期.万能账户
   i. 投连账户 = 本账期.投连账户 - 上一季度末账期.投连账户

**步骤3.** 数据入表

(1) 将计算结果按照ALMCF实际发生数本季度累计表(TB0023)的结构组织数据
(2) 写入TB0023表

## 5. 非功能需求

### 5.1 性能需求

- 数据导入响应时间：单次导入不超过30秒
- 计算处理时间：ALMCF实际发生数本年累计表计算不超过60秒
- 计算处理时间：ALMCF实际发生数本季度累计表计算不超过30秒
- 并发用户数：支持10个用户同时操作

### 5.2 安全需求

- 数据访问权限控制：按角色控制数据访问权限
- 数据备份：重要数据需要定期备份
- 操作日志：记录所有数据变更操作

### 5.3 可用性需求

- 系统可用性：99.5%
- 数据准确性：100%
- 界面友好性：操作简单直观

## 6. 约束条件

### 6.1 技术约束

- 开发语言：Java 8
- 前端框架：Vue 2.6.12
- 数据库：MySQL 5.7+
- 操作系统：Windows 11

### 6.2 业务约束

- 账期格式：必须为YYYYMM格式
- 数据完整性：导入数据必须完整，不允许关键字段为空
- 映射关系：现金流项目映射关系必须完整配置

## 7. 验收标准

### 7.1 功能验收

- 现金流量表数据导入功能正常
- 现金流项目映射管理功能正常
- ALMCF实际发生数本年累计表计算功能正常
- ALMCF实际发生数本季度累计表计算功能正常
- 计算结果准确性验证通过

### 7.2 性能验收

- 导入性能满足要求
- 计算性能满足要求
- 并发性能满足要求

### 7.3 安全验收

- 权限控制功能正常
- 数据安全措施有效
- 操作审计功能完整
