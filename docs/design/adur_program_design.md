# 资产久期管理模块需求规格说明书

## 文档信息

| 项目名称 | 资产久期管理模块 |
| -------- | ------------ |
| 文档版本 | V1.0         |
| 作者     | 系统设计师   |
| 创建日期 | 2025-01-01   |
| 状态     | 已确认       |

## 变更履历

| 版本 | 日期       | 变更描述 | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-01-01 | 初次编写 | 系统设计师 | 通过 |

## 1. 需求概述

### 1.1 需求背景

资产久期管理模块是资产负债管理系统的重要组成部分，主要用于计算和管理固定收益资产的久期风险指标。该模块通过导入万得收益率曲线数据和关键久期参数，结合整体资产明细信息，计算各类久期指标，包括修正久期、有效久期、利差久期等，为资产负债匹配和风险管理提供数据支持。

### 1.2 需求目标

1. 建立完整的资产久期计算体系，支持多种久期类型的计算
2. 实现收益率曲线的多层次管理，包括年度、月度、含价差等不同维度
3. 提供关键久期风险测量功能，支持DV10等风险指标计算
4. 建立折现因子计算体系，支持不同情景下的现值计算
5. 为资产负债管理决策提供准确的久期风险数据

### 1.3 需求范围

本模块涵盖以下功能范围：
- 万得收益率曲线数据导入和管理
- 关键久期参数配置和管理
- 久期资产明细数据处理
- 年度和月度折现曲线计算
- 折现因子计算
- 各类久期指标计算（修正久期、有效久期、利差久期）
- 关键久期风险指标计算（DV10系列）

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 | 资产负债管理部 |      | 业务需求确认 |
| 业务负责人 | 精算部 |      | 久期计算逻辑确认 |
| 技术负责人 | 信息技术部 |      | 技术方案设计和实现 |

## 2. 业务架构

### 2.1 业务模块关系图

```mermaid
graph TD
    A[万得收益率曲线管理] --> B[年度折现曲线计算]
    B --> C[月度折现曲线计算]
    C --> D[月度折现曲线含价差计算]
    D --> E[月度折现因子计算]
    F[关键久期参数管理] --> G[关键久期折现曲线计算]
    G --> H[关键久期折现因子计算]
    I[久期资产明细管理] --> J[久期指标计算]
    E --> J
    H --> J
```

### 2.2 模块列表

| 模块编号 | 模块名称    | 模块英文名 | 英文缩写 |
| -------- |---------| ---------- | -------- |
| MD0001 | 资产久期管理    | asset_duration_management | adur |

#### 2.2.1 模块说明

**资产久期管理(MD0001)**：
- 万得收益率曲线表(t_base_wind_yield_curve)：管理万得收益率曲线基础数据
- 关键久期参数表(t_adur_key_duration_parameter)：管理关键久期计算参数
- 久期资产明细表(t_adur_duration_asset_detail)：管理久期资产明细和计算结果
- 年度折现曲线表(t_adur_annual_discount_curve)：存储年度折现曲线数据
- 月度折现曲线表不含价差(t_adur_monthly_discount_curve)：存储月度折现曲线基础数据
- 月度折现曲线表含价差(t_adur_monthly_discount_curve_with_spread)：存储含价差的月度折现曲线
- 月度折现因子表含价差(t_adur_monthly_discount_factor_with_spread)：存储折现因子计算结果
- 关键久期折现曲线表含价差(t_adur_key_duration_curve_with_spread)：存储关键久期折现曲线
- 关键久期折现因子表含价差(t_adur_key_duration_factor_with_spread)：存储关键久期折现因子

### 2.3 数据模型

#### 2.3.1 表间关系

```mermaid
erDiagram
    %% 基础数据表
    万得收益率曲线表 ||--|| 基础数据 : "直接导入"
    关键久期参数表 ||--|| 基础数据 : "直接导入"
    久期资产明细表 ||--|| 整体资产明细表 : "筛选导入"
    
    %% 折现曲线计算流程
    万得收益率曲线表 }o--o{ 年度折现曲线表 : "TB0001→TB0003"
    年度折现曲线表 }o--o{ 月度折现曲线表不含价差 : "TB0003→TB0004"
    月度折现曲线表不含价差 }o--o{ 月度折现曲线表含价差 : "TB0004→TB0006"
    月度折现曲线表含价差 }o--o{ 月度折现因子表含价差 : "TB0006→TB0007"
    
    %% 关键久期计算流程
    关键久期参数表 }o--o{ 关键久期折现曲线表含价差 : "TB0002→TB0008"
    关键久期折现曲线表含价差 }o--o{ 关键久期折现因子表含价差 : "TB0008→TB0009"

    %% 久期指标计算
    久期资产明细表 }o--o{ 月度折现因子表含价差 : "计算久期指标"
    久期资产明细表 }o--o{ 关键久期折现因子表含价差 : "计算DV10指标"
```

#### 2.3.2 表名字典

| 表编号    | 表中文名 | 表英文名                                 | 所属模块 | 备注 |
|--------| ------ |--------------------------------------| ------ | ------ |
| TB0001 | 万得收益率曲线表 | t_base_wind_yield_curve              | MD0001 | 基础收益率数据 |
| TB0002 | 关键久期参数表 | t_adur_key_duration_parameter        | MD0001 | 关键久期参数计算 |
| TB0003 | 久期资产明细表 | t_adur_duration_asset_detail         | MD0001 | 久期资产明细和计算结果 |
| TB0004 | 年度折现曲线表 | t_adur_annual_discount_curve         | MD0001 | 年度折现曲线数据 |
| TB0005 | 月度折现曲线表不含价差 | t_adur_monthly_discount_curve        | MD0001 | 月度折现曲线基础数据 |
| TB0006 | 月度折现曲线表含价差 | t_adur_monthly_discount_curve_with_spread | MD0001 | 含价差月度折现曲线 |
| TB0007 | 月度折现因子表含价差 | t_adur_monthly_discount_factor_with_spread | MD0001 | 月度折现因子 |
| TB0008 | 关键久期折现曲线表含价差 | t_adur_key_duration_curve_with_spread | MD0001 | 关键久期折现曲线 |
| TB0009 | 关键久期折现因子表含价差 | t_adur_key_duration_factor_with_spread | MD0001 | 关键久期折现因子 |
| TB0010 | 久期资产结果汇总表 | t_adur_duration_asset_summary        | MD0001 | 久期资产汇总结果 |

#### 2.3.3 表集

##### 2.3.3.1 万得收益率曲线表(TB0001)

**表英文名**：t_base_wind_yield_curve
**表中文名**：万得收益率曲线表
**所属模块**：资产久期管理(MD0001)
**表描述**：存储万得收益率曲线基础数据，包含不同期限的收益率信息

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 折现曲线名称 | curve_name | varchar | 50 | 否 | text | - | 导入，项目固定 | 是 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 导入，项目固定 | 是 | - |
| 日期 | date | date | - | 否 | date | - | 导入，项目固定 | 是 | - |
| 期限0 | term_0 | decimal | 10,6 | 是 | number | 0 | 来源于wind数据 | 否 | - |
| 期限1 | term_1 | decimal | 10,6 | 是 | number | 0 | 来源于wind数据 | 否 | - |
| ... | ... | ... | ... | ... | ... | ... | 继续到期限50 | ... | ... |
| 期限50 | term_50 | decimal | 10,6 | 是 | number | 0 | 来源于wind数据 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

##### 2.3.3.2 关键久期参数表(TB0002)

**表英文名**：t_adur_key_duration_parameter
**表中文名**：关键久期参数表
**所属模块**：资产久期管理(MD0001)
**表描述**：存储关键久期计算参数，JSON格式存储0-600期的参数值

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 关键期限 | key_term | varchar | 10 | 否 | text | - | 如：0、0.5、1、2等 | 是 | - |
| 参数值集 | parameter_val_set | text | 65535 | 否 | json | - | JSON格式存储0-600期参数 | 否 | 格式：{"0":{"date":"2025-01-01","val":0.25}} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

##### 2.3.3.3 久期资产明细表(TB0003)

**表英文名**：t_adur_duration_asset_detail
**表中文名**：久期资产明细表
**所属模块**：资产久期管理(MD0001)
**表描述**：存储久期资产明细信息和各类久期计算结果

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 取自整体资产明细表 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 资产小小类 | asset_sub_category | varchar | 50 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 持仓面值 | holding_face_value | decimal | 18,2 | 是 | number | 0 | 取自整体资产明细表 | 否 | - |
| 市值 | market_value | decimal | 18,2 | 是 | number | 0 | 取自整体资产明细表 | 否 | - |
| 账面余额 | book_balance | decimal | 18,2 | 是 | number | 0 | 取自整体资产明细表 | 否 | - |
| 账面价值 | book_value | decimal | 18,2 | 是 | number | 0 | 取自整体资产明细表 | 否 | - |
| 票面利率 | coupon_rate | decimal | 10,6 | 是 | number | 0 | 取自整体资产明细表 | 否 | - |
| 付息方式 | payment_method | varchar | 20 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 调整起息日 | adjusted_value_date | date | - | 否 | date | - | 取自整体资产明细表 | 否 | - |
| 调整买入日 | adjusted_purchase_date | date | - | 否 | date | - | 取自整体资产明细表 | 否 | - |
| 调整到期日 | adjusted_maturity_date | date | - | 否 | date | - | 取自整体资产明细表 | 否 | - |
| 发行时点价差计算标识 | issue_spread_calc_flag | char | 1 | 否 | text | - | 取自整体资产明细表 | 否 | - |
| 利差久期资产统计标识 | spread_duration_stat_flag | char | 1 | 是 | text | 0 | 取自整体资产明细表 | 否 | - |
| 评估时点价差 | eval_spread | decimal | 10,6 | 是 | number | 0 | 通过goseek方法计算 | 否 | - |
| 利差久期 | spread_duration | decimal | 10,6 | 是 | number | 0 | 利差久期计算结果 | 否 | - |
| 账面价值σ9% | book_value_sigma_9 | decimal | 18,2 | 是 | number | 0 | 账面价值*(1-9%*评估时点利差*利差久期) | 否 | - |
| 账面价值σ17% | book_value_sigma_17 | decimal | 18,2 | 是 | number | 0 | 账面价值*(1-17%*评估时点利差*利差久期) | 否 | - |
| 账面价值σ77% | book_value_sigma_77 | decimal | 18,2 | 是 | number | 0 | 账面价值*(1-77%*评估时点利差*利差久期) | 否 | - |
| 发行时点资产现值 | issue_present_value | decimal | 18,2 | 是 | number | 0 | 等于持仓面值 | 否 | - |
| 发行时点价差 | issue_spread | decimal | 10,6 | 是 | number | 0 | 通过goseek方法计算 | 否 | - |
| 评估时点资产现值 | eval_present_value | decimal | 18,2 | 是 | number | 0 | 现金流折现计算 | 否 | - |
| 评估时点到期收益率 | eval_maturity_yield | decimal | 10,6 | 是 | number | 0 | 使用xirr函数计算 | 否 | - |
| 资产修正久期 | asset_modified_duration | decimal | 10,6 | 是 | number | 0 | 修正久期计算结果 | 否 | - |
| 评估时点资产现值+50bp | eval_present_value_plus_50bp | decimal | 18,2 | 是 | number | 0 | +50bp情景现值 | 否 | - |
| 评估时点资产现值-50bp | eval_present_value_minus_50bp | decimal | 18,2 | 是 | number | 0 | -50bp情景现值 | 否 | - |
| 资产有效久期 | asset_effective_duration | decimal | 10,6 | 是 | number | 0 | 有效久期计算结果 | 否 | - |
| DV10_0 | dv10_1 | decimal | 18,2 | 是 | number | 0 | 关键久期0年风险值 | 否 | - |
| DV10_0.5 | dv10_2 | decimal | 18,2 | 是 | number | 0 | 关键久期0.5年风险值 | 否 | - |
| DV10_1 | dv10_3 | decimal | 18,2 | 是 | number | 0 | 关键久期1年风险值 | 否 | - |
| DV10_2 | dv10_4 | decimal | 18,2 | 是 | number | 0 | 关键久期2年风险值 | 否 | - |
| DV10_3 | dv10_5 | decimal | 18,2 | 是 | number | 0 | 关键久期3年风险值 | 否 | - |
| DV10_4 | dv10_6 | decimal | 18,2 | 是 | number | 0 | 关键久期4年风险值 | 否 | - |
| DV10_5 | dv10_7 | decimal | 18,2 | 是 | number | 0 | 关键久期5年风险值 | 否 | - |
| DV10_6 | dv10_8 | decimal | 18,2 | 是 | number | 0 | 关键久期6年风险值 | 否 | - |
| DV10_7 | dv10_9 | decimal | 18,2 | 是 | number | 0 | 关键久期7年风险值 | 否 | - |
| DV10_8 | dv10_10 | decimal | 18,2 | 是 | number | 0 | 关键久期8年风险值 | 否 | - |
| DV10_10 | dv10_11 | decimal | 18,2 | 是 | number | 0 | 关键久期10年风险值 | 否 | - |
| DV10_12 | dv10_12 | decimal | 18,2 | 是 | number | 0 | 关键久期12年风险值 | 否 | - |
| DV10_15 | dv10_13 | decimal | 18,2 | 是 | number | 0 | 关键久期15年风险值 | 否 | - |
| DV10_20 | dv10_14 | decimal | 18,2 | 是 | number | 0 | 关键久期20年风险值 | 否 | - |
| DV10_25 | dv10_15 | decimal | 18,2 | 是 | number | 0 | 关键久期25年风险值 | 否 | - |
| DV10_30 | dv10_16 | decimal | 18,2 | 是 | number | 0 | 关键久期30年风险值 | 否 | - |
| DV10_35 | dv10_17 | decimal | 18,2 | 是 | number | 0 | 关键久期35年风险值 | 否 | - |
| DV10_40 | dv10_18 | decimal | 18,2 | 是 | number | 0 | 关键久期40年风险值 | 否 | - |
| DV10_45 | dv10_19 | decimal | 18,2 | 是 | number | 0 | 关键久期45年风险值 | 否 | - |
| DV10_50 | dv10_20 | decimal | 18,2 | 是 | number | 0 | 关键久期50年风险值 | 否 | - |
| DV10_0_上升 | dv10_1_up | decimal | 18,2 | 是 | number | 0 | 关键久期0年上升情景现值 | 否 | - |
| DV10_0.5_上升 | dv10_2_up | decimal | 18,2 | 是 | number | 0 | 关键久期0.5年上升情景现值 | 否 | - |
| DV10_1_上升 | dv10_3_up | decimal | 18,2 | 是 | number | 0 | 关键久期1年上升情景现值 | 否 | - |
| DV10_2_上升 | dv10_4_up | decimal | 18,2 | 是 | number | 0 | 关键久期2年上升情景现值 | 否 | - |
| DV10_3_上升 | dv10_5_up | decimal | 18,2 | 是 | number | 0 | 关键久期3年上升情景现值 | 否 | - |
| DV10_4_上升 | dv10_6_up | decimal | 18,2 | 是 | number | 0 | 关键久期4年上升情景现值 | 否 | - |
| DV10_5_上升 | dv10_7_up | decimal | 18,2 | 是 | number | 0 | 关键久期5年上升情景现值 | 否 | - |
| DV10_6_上升 | dv10_8_up | decimal | 18,2 | 是 | number | 0 | 关键久期6年上升情景现值 | 否 | - |
| DV10_7_上升 | dv10_9_up | decimal | 18,2 | 是 | number | 0 | 关键久期7年上升情景现值 | 否 | - |
| DV10_8_上升 | dv10_10_up | decimal | 18,2 | 是 | number | 0 | 关键久期8年上升情景现值 | 否 | - |
| DV10_10_上升 | dv10_11_up | decimal | 18,2 | 是 | number | 0 | 关键久期10年上升情景现值 | 否 | - |
| DV10_12_上升 | dv10_12_up | decimal | 18,2 | 是 | number | 0 | 关键久期12年上升情景现值 | 否 | - |
| DV10_15_上升 | dv10_13_up | decimal | 18,2 | 是 | number | 0 | 关键久期15年上升情景现值 | 否 | - |
| DV10_20_上升 | dv10_14_up | decimal | 18,2 | 是 | number | 0 | 关键久期20年上升情景现值 | 否 | - |
| DV10_25_上升 | dv10_15_up | decimal | 18,2 | 是 | number | 0 | 关键久期25年上升情景现值 | 否 | - |
| DV10_30_上升 | dv10_16_up | decimal | 18,2 | 是 | number | 0 | 关键久期30年上升情景现值 | 否 | - |
| DV10_35_上升 | dv10_17_up | decimal | 18,2 | 是 | number | 0 | 关键久期35年上升情景现值 | 否 | - |
| DV10_40_上升 | dv10_18_up | decimal | 18,2 | 是 | number | 0 | 关键久期40年上升情景现值 | 否 | - |
| DV10_45_上升 | dv10_19_up | decimal | 18,2 | 是 | number | 0 | 关键久期45年上升情景现值 | 否 | - |
| DV10_50_上升 | dv10_20_up | decimal | 18,2 | 是 | number | 0 | 关键久期50年上升情景现值 | 否 | - |
| DV10_0_下降 | dv10_1_down | decimal | 18,2 | 是 | number | 0 | 关键久期0年下降情景现值 | 否 | - |
| DV10_0.5_下降 | dv10_2_down | decimal | 18,2 | 是 | number | 0 | 关键久期0.5年下降情景现值 | 否 | - |
| DV10_1_下降 | dv10_3_down | decimal | 18,2 | 是 | number | 0 | 关键久期1年下降情景现值 | 否 | - |
| DV10_2_下降 | dv10_4_down | decimal | 18,2 | 是 | number | 0 | 关键久期2年下降情景现值 | 否 | - |
| DV10_3_下降 | dv10_5_down | decimal | 18,2 | 是 | number | 0 | 关键久期3年下降情景现值 | 否 | - |
| DV10_4_下降 | dv10_6_down | decimal | 18,2 | 是 | number | 0 | 关键久期4年下降情景现值 | 否 | - |
| DV10_5_下降 | dv10_7_down | decimal | 18,2 | 是 | number | 0 | 关键久期5年下降情景现值 | 否 | - |
| DV10_6_下降 | dv10_8_down | decimal | 18,2 | 是 | number | 0 | 关键久期6年下降情景现值 | 否 | - |
| DV10_7_下降 | dv10_9_down | decimal | 18,2 | 是 | number | 0 | 关键久期7年下降情景现值 | 否 | - |
| DV10_8_下降 | dv10_10_down | decimal | 18,2 | 是 | number | 0 | 关键久期8年下降情景现值 | 否 | - |
| DV10_10_下降 | dv10_11_down | decimal | 18,2 | 是 | number | 0 | 关键久期10年下降情景现值 | 否 | - |
| DV10_12_下降 | dv10_12_down | decimal | 18,2 | 是 | number | 0 | 关键久期12年下降情景现值 | 否 | - |
| DV10_15_下降 | dv10_13_down | decimal | 18,2 | 是 | number | 0 | 关键久期15年下降情景现值 | 否 | - |
| DV10_20_下降 | dv10_14_down | decimal | 18,2 | 是 | number | 0 | 关键久期20年下降情景现值 | 否 | - |
| DV10_25_下降 | dv10_15_down | decimal | 18,2 | 是 | number | 0 | 关键久期25年下降情景现值 | 否 | - |
| DV10_30_下降 | dv10_16_down | decimal | 18,2 | 是 | number | 0 | 关键久期30年下降情景现值 | 否 | - |
| DV10_35_下降 | dv10_17_down | decimal | 18,2 | 是 | number | 0 | 关键久期35年下降情景现值 | 否 | - |
| DV10_40_下降 | dv10_18_down | decimal | 18,2 | 是 | number | 0 | 关键久期40年下降情景现值 | 否 | - |
| DV10_45_下降 | dv10_19_down | decimal | 18,2 | 是 | number | 0 | 关键久期45年下降情景现值 | 否 | - |
| DV10_50_下降 | dv10_20_down | decimal | 18,2 | 是 | number | 0 | 关键久期50年下降情景现值 | 否 | - |
| 发行时点现金流值集 | issue_cashflow_set | mediumtext | - | 是 | json | - | JSON格式现金流数据 | 否 | - |
| 评估时点现金流值集 | eval_cashflow_set | mediumtext | - | 是 | json | - | JSON格式现金流数据 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

##### 2.3.3.4 月度折现曲线表不含价差(TB0005)

**表英文名**：t_adur_monthly_discount_curve
**表中文名**：月度折现曲线表不含价差
**所属模块**：资产久期管理(MD0001)
**表描述**：存储月度折现曲线基础数据，包含0-600期的折现曲线利率值集

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 日期类型 | date_type | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 日期 | date | date | - | 否 | date | - | 同年度折现曲线表 | 否 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 月度折现曲线利率值集 | monthly_discount_rate_set | text | 65535 | 是 | json | - | JSON格式存储0-600期利率值 | 否 | 格式：{"0":0.25,"1":0.35...,"600":0.15} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

**月度折现曲线利率值集计算逻辑**：

1. **如果折现曲线标识=0 AND 日期类型=发行时点**
   - 等于"-"（在JSON中用特殊值表示）

2. **如果折现曲线标识=0 AND 日期类型=评估时点**
   - 等于久期资产明细表.到期收益率

3. **如果折现曲线标识<>0**
   - 月份为12的整数倍时（0/12/24/36/48/……/600）：
     - 计算对应年份数：月份/12
     - 在年度折现曲线表中查找期限为（月份/12）年的收益率
   - 月份不为12的整数倍时：
     - 取临近两个整数年的收益率，根据所在月度进行线性插值

##### 2.3.3.5 月度折现曲线表含价差(TB0006)

**表英文名**：t_adur_monthly_discount_curve_with_spread
**表中文名**：月度折现曲线表含价差
**所属模块**：资产久期管理(MD0001)
**表描述**：存储含价差的月度折现曲线数据，用于折现因子计算

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 久期类型 | duration_type | varchar | 20 | 否 | text | - | 取值为：修正久期、有效久期、利差久期 | 是 | - |
| 基点类型 | basis_point_type | varchar | 20 | 否 | text | - | 取值为：0bp、+50bp、-50bp | 是 | - |
| 日期类型 | date_type | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 日期 | date | date | - | 否 | date | - | 同年度折现曲线表 | 否 | - |
| 价差类型 | spread_type | varchar | 20 | 是 | text | - | 取值为：发行时点价差、评估时点价差 | 否 | - |
| 价差 | spread | decimal | 10,6 | 是 | number | 0 | 根据价差类型从久期资产明细表获取 | 否 | - |
| 曲线细分类 | curve_sub_category | varchar | 10 | 是 | text | - | 根据久期类型、基点类型等条件确定 | 否 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 同年度折现曲线表 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 根据久期类型确定 | 否 | - |
| 月度折现曲线利率含价差值集 | monthly_discount_rate_with_spread_set | text | 65535 | 是 | json | - | JSON格式存储0-600期含价差利率值 | 否 | 格式：{"0":0.25,"1":0.35...,"600":0.15} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

**字段详细规则说明**：

**1. 价差字段计算规则**：
- 如果价差类型=发行时点价差：等于久期资产明细表.发行时点价差
  - 匹配条件：证券代码相同 AND 账户名称相同
- 如果价差类型=评估时点价差：等于久期资产明细表.评估时点价差
  - 匹配条件：证券代码相同 AND 账户名称相同

**2. 曲线细分类确定规则**：
- 曲线细分类=1：久期类型=修正久期 AND 基点类型=0bp AND 日期类型=发行时点 AND 价差类型=发行时点价差
- 曲线细分类=2：久期类型=修正久期 AND 基点类型=0bp AND 日期类型=评估时点 AND 价差类型=发行时点价差
- 曲线细分类=3：久期类型=有效久期 AND 基点类型=+50bp AND 日期类型=评估时点 AND 价差类型=发行时点价差
- 曲线细分类=4：久期类型=有效久期 AND 基点类型=-50bp AND 日期类型=评估时点 AND 价差类型=发行时点价差
- 曲线细分类=5：久期类型=利差久期 AND 基点类型=0bp AND 日期类型=评估时点 AND 价差类型=评估时点价差

**3. 折现曲线标识确定规则**：
- 如果久期类型=修正久期 OR 有效久期：同年度折现曲线表
- 如果久期类型=利差久期：赋值为1

**4. 月度折现曲线利率含价差值集计算规则**：
- 曲线细分类=1：月度折现曲线表不含价差.期限X + 价差
  - 匹配条件：日期类型=发行时点 AND 账户名称相同 AND 证券代码相同
- 曲线细分类=2：月度折现曲线表不含价差.期限X + 价差
  - 匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
- 曲线细分类=3：月度折现曲线表不含价差.期限X + 价差 + 0.5%
  - 匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
- 曲线细分类=4：月度折现曲线表不含价差.期限X + 价差 - 0.5%
  - 匹配条件：日期类型=评估时点 AND 账户名称相同 AND 证券代码相同
- 曲线细分类=5：月度折现曲线表不含价差.期限X + 价差
  - 匹配条件：日期类型=评估时点 AND 折现曲线标识=1（可匹配多条，取其中一条）

##### 2.3.3.6 月度折现因子表含价差(TB0007)

**表英文名**：t_adur_monthly_discount_factor_with_spread
**表中文名**：月度折现因子表含价差
**所属模块**：资产久期管理(MD0001)
**表描述**：存储月度折现因子计算结果，包含价差信息，用于久期指标计算

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 久期类型 | duration_type | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 是 | - |
| 基点类型 | basis_point_type | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 是 | - |
| 日期类型 | date_type | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 日期 | date | date | - | 否 | date | - | 同月度折现曲线表含价差表 | 否 | - |
| 价差类型 | spread_type | varchar | 20 | 是 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 价差 | spread | decimal | 10,6 | 是 | number | 0 | 同月度折现曲线表含价差表 | 否 | - |
| 曲线细分类 | curve_sub_category | varchar | 10 | 是 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 月度折现因子表含价差值集 | monthly_discount_factor_set | text | 65535 | 是 | json | - | JSON格式存储0-600期折现因子值 | 否 | 格式：{"0":0.25,"1":0.35...,"600":0.15} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

**月度折现因子表含价差值集计算逻辑**：

计算公式：等于 1/(1+月度折现曲线表含价差.期限X)^(月份/12)

其中：
- X 为期限编号（0-600）
- 月份为期限编号对应的月份数
- 月度折现曲线表含价差.期限X 为对应期限的折现曲线利率值

##### 2.3.3.7 关键久期折现曲线表含价差(TB0008)

**表英文名**：t_adur_key_duration_curve_with_spread
**表中文名**：关键久期折现曲线表含价差
**所属模块**：资产久期管理(MD0001)
**表描述**：存储关键久期折现曲线数据，包含价差信息，用于关键久期风险计算

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 久期类型 | duration_type | varchar | 20 | 否 | text | - | 取值为：关键久期 | 是 | - |
| 基点类型 | basis_point_type | varchar | 20 | 是 | text | - | 取值为：0bp | 否 | - |
| 关键期限 | key_term | varchar | 20 | 否 | text | - | 0/0.5/1/2/3…… | 是 | - |
| 压力方向 | stress_direction | varchar | 10 | 否 | text | - | 上升/下降 | 否 | - |
| 日期类型 | date_type | varchar | 20 | 否 | text | - | 取值为：评估时点，项目固定 | 否 | - |
| 日期 | date | date | - | 否 | date | - | 等于账期（转换为所在月的最后一天） | 否 | - |
| 价差类型 | spread_type | varchar | 20 | 是 | text | - | 取值为：发行时点价差 | 否 | - |
| 价差 | spread | decimal | 10,6 | 是 | number | 0 | 同月度折现曲线表含价差表 | 否 | - |
| 曲线细分类 | curve_sub_category | varchar | 10 | 是 | text | - | 取值为：2 | 否 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 同月度折现曲线表含价差表 | 否 | - |
| 关键久期折现曲线表含价差值集 | key_duration_curve_with_spread_set | text | 65535 | 是 | json | - | JSON格式存储0-600期关键久期折现曲线值 | 否 | 格式：{"0":0.25,"1":0.35...,"600":0.15} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

**关键久期折现曲线表含价差值集计算逻辑**：

**1. 压力方向=上升**：
- 计算公式：月度折现曲线表含价差.期限X + 关键久期参数表.关键参数值集.key
- 其中X为期限编号（0-600）

**2. 压力方向=下降**：
- 计算公式：月度折现曲线表含价差.期限X - 关键久期参数表.关键参数值集.key
- 其中X为期限编号（0-600）

**3. 月度折现曲线表含价差匹配条件**：
- 曲线细分类=2
- 账户名称相同
- 证券代码相同

**4. 关键久期参数表匹配条件**：
- 关键期限相同

**5. 字段固定取值**：
- 久期类型：关键久期
- 基点类型：0bp
- 日期类型：评估时点
- 价差类型：发行时点价差
- 曲线细分类：2

##### 2.3.3.8 关键久期折现因子表含价差(TB0009)

**表英文名**：t_adur_key_duration_factor_with_spread
**表中文名**：关键久期折现因子表含价差
**所属模块**：资产久期管理(MD0001)
**表描述**：存储关键久期折现因子计算结果，用于DV10风险指标计算

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 久期类型 | duration_type | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 是 | - |
| 基点类型 | basis_point_type | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 关键期限 | key_term | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 是 | - |
| 压力方向 | stress_direction | varchar | 10 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 日期类型 | date_type | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 日期 | date | date | - | 否 | date | - | 同关键久期折现曲线表含价差 | 否 | - |
| 价差类型 | spread_type | varchar | 20 | 是 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 价差 | spread | decimal | 10,6 | 是 | number | 0 | 同关键久期折现曲线表含价差 | 否 | - |
| 曲线细分类 | curve_sub_category | varchar | 10 | 是 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 资产编号 | asset_number | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 资产名称 | asset_name | varchar | 100 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 证券代码 | security_code | varchar | 20 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 折现曲线标识 | curve_id | varchar | 10 | 否 | text | - | 同关键久期折现曲线表含价差 | 否 | - |
| 发行时点价差 | issue_spread | decimal | 10,6 | 是 | number | 0 | 同关键久期折现曲线表含价差 | 否 | - |
| 关键久期折现因子表含价差值集 | key_duration_factor_with_spread_set | text | 65535 | 是 | json | - | JSON格式存储0-600期关键久期折现因子值 | 否 | 格式：{"0":0.25,"1":0.35...,"600":0.15} |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

**关键久期折现因子表含价差值集计算逻辑**：

计算公式：等于 1/(1+关键久期折现曲线表含价差.期限X)^(月份/12)

其中：
- X 为期限编号（0-600）
- 月份为期限编号对应的月份数
- 关键久期折现曲线表含价差.期限X 为对应期限的关键久期折现曲线利率值

##### 2.3.3.9 久期资产结果汇总表(TB0010)

**表英文名**：t_adur_duration_asset_summary
**表中文名**：久期资产结果汇总表
**所属模块**：资产久期管理(MD0001)
**表描述**：按账户汇总久期资产的各项指标，包括市值、久期、DV10等风险指标

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| 主键ID | id | bigint | 20 | 否 | auto_increment | - | 自增主键 | PK | - |
| 账期 | account_period | varchar | 6 | 否 | text | - | 格式：YYYYMM | 是 | - |
| 账户名称 | account_name | varchar | 50 | 否 | text | - | 取值：传统账户/分红账户/万能账户/普通账户 | 是 | - |
| 市值 | market_value | decimal | 18,2 | 是 | number | 0 | 按账户汇总的市值 | 否 | - |
| 账面余额 | book_balance | decimal | 18,2 | 是 | number | 0 | 按账户汇总的账面余额 | 否 | - |
| 账面价值 | book_value | decimal | 18,2 | 是 | number | 0 | 按账户汇总的账面价值 | 否 | - |
| 账面价值σ0% | book_value_sigma_0 | decimal | 18,2 | 是 | number | 0 | 利差久期资产统计标识=1的账面价值汇总 | 否 | - |
| 账面价值σ9% | book_value_sigma_9 | decimal | 18,2 | 是 | number | 0 | 利差久期资产统计标识=1的账面价值σ9%汇总 | 否 | - |
| 账面价值σ17% | book_value_sigma_17 | decimal | 18,2 | 是 | number | 0 | 利差久期资产统计标识=1的账面价值σ17%汇总 | 否 | - |
| 账面价值σ77% | book_value_sigma_77 | decimal | 18,2 | 是 | number | 0 | 利差久期资产统计标识=1的账面价值σ77%汇总 | 否 | - |
| 评估时点到期收益率 | eval_maturity_yield | decimal | 10,6 | 是 | number | 0 | 按账面价值加权平均的到期收益率 | 否 | - |
| 评估时点资产现值 | eval_present_value | decimal | 18,2 | 是 | number | 0 | 按账户汇总的评估时点资产现值 | 否 | - |
| 资产修正久期 | asset_modified_duration | decimal | 10,6 | 是 | number | 0 | 按评估时点资产现值加权平均的修正久期 | 否 | - |
| 资产有效久期 | asset_effective_duration | decimal | 10,6 | 是 | number | 0 | 按评估时点资产现值加权平均的有效久期 | 否 | - |
| DV10_0 | dv10_0 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_0风险值 | 否 | - |
| DV10_0.5 | dv10_0_5 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_0.5风险值 | 否 | - |
| DV10_1 | dv10_1 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_1风险值 | 否 | - |
| DV10_2 | dv10_2 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_2风险值 | 否 | - |
| DV10_3 | dv10_3 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_3风险值 | 否 | - |
| DV10_4 | dv10_4 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_4风险值 | 否 | - |
| DV10_5 | dv10_5 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_5风险值 | 否 | - |
| DV10_6 | dv10_6 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_6风险值 | 否 | - |
| DV10_7 | dv10_7 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_7风险值 | 否 | - |
| DV10_8 | dv10_8 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_8风险值 | 否 | - |
| DV10_10 | dv10_10 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_10风险值 | 否 | - |
| DV10_12 | dv10_12 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_12风险值 | 否 | - |
| DV10_15 | dv10_15 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_15风险值 | 否 | - |
| DV10_20 | dv10_20 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_20风险值 | 否 | - |
| DV10_25 | dv10_25 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_25风险值 | 否 | - |
| DV10_30 | dv10_30 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_30风险值 | 否 | - |
| DV10_35 | dv10_35 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_35风险值 | 否 | - |
| DV10_40 | dv10_40 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_40风险值 | 否 | - |
| DV10_45 | dv10_45 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_45风险值 | 否 | - |
| DV10_50 | dv10_50 | decimal | 18,2 | 是 | number | 0 | 按账户汇总的DV10_50风险值 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | - | - | 创建者 | - | - |
| 创建时间 | create_time | datetime | - | 是 | - | - | 创建时间 | - | - |
| 更新者 | update_by | varchar | 64 | 是 | - | - | 更新者 | - | - |
| 更新时间 | update_time | datetime | - | 是 | - | - | 更新时间 | - | - |
| 是否删除 | is_del | tinyint | 1 | 否 | - | 0 | 是否删除，0:否，1:是 | - | - |

| 用例编号   | 用例名称         | 用例描述                   | 模块编号 |
|--------|--------------|------------------------| ---- |
| UC0001 | 导入万得收益率曲线数据 | 导入万得收益率曲线数据，写入TB0001表 | MD0001 |
| UC0002 | 导入关键久期参数数据 | 导入关键久期参数数据，写入TB0002表 | MD0001 |
| UC0003 | 生成久期资产明细数据 | 从整体资产明细表筛选和计算久期资产数据，写入TB0003表 | MD0001 |
| UC0004 | 计算年度折现曲线 | 基于万得收益率曲线计算年度折现曲线，写入TB0004表 | MD0001 |
| UC0005 | 计算月度折现曲线不含价差 | 基于年度折现曲线插值计算月度折现曲线，写入TB0005表 | MD0001 |
| UC0006 | 计算月度折现曲线含价差 | 基于月度折现曲线和价差计算含价差曲线，写入TB0006表 | MD0001 |
| UC0007 | 计算月度折现因子 | 基于月度折现曲线含价差计算折现因子，写入TB0007表 | MD0001 |
| UC0008 | 计算关键久期折现曲线 | 基于关键久期参数计算关键久期折现曲线，写入TB0008表 | MD0001 |
| UC0009 | 计算关键久期折现因子 | 基于关键久期折现曲线计算折现因子，写入TB0009表 | MD0001 |
| UC0010 | 计算久期指标 | 计算各类久期指标和DV10风险指标，更新TB0003表 | MD0001 |
| UC0011 | 计算久期资产汇总 | 按账户汇总久期资产各项指标，写入TB0010表 | MD0001 |

### 2.5 接口清单

| 接口编号   | 接口名称        | 接口描述 | 模块编号 |
| ------ |-------------| ---- | ---- |
| IF0001 | 导入万得收益率曲线数据 | 导入万得收益率曲线数据，写入TB0001表 | MD0001 |
| IF0002 | 导入关键久期参数数据 | 导入关键久期参数数据，写入TB0002表 | MD0001 |


## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 修正久期 | Modified Duration | 衡量债券价格对收益率变化敏感性的指标 | 用于利率风险管理 |
| 有效久期 | Effective Duration | 考虑期权特征的久期指标 | 适用于含权债券 |
| 利差久期 | Spread Duration | 衡量债券价格对信用利差变化敏感性的指标 | 用于信用风险管理 |
| DV10 | Dollar Value of 10bp | 收益率曲线变动10个基点时的价值变化 | 关键久期风险指标 |
| 折现因子 | Discount Factor | 将未来现金流折现到现值的系数 | 现值计算基础 |
| 万得收益率曲线 | Wind Yield Curve | 万得金融终端提供的收益率曲线数据 | 基础市场数据 |
| 关键久期 | Key Rate Duration | 特定期限点收益率变化对债券价格的影响 | 精细化风险管理 |

## 4. 功能需求

### 4.1 资产久期管理模块

#### 4.1.1 功能概要

资产久期管理模块主要实现以下功能：
1. 导入基础数据（万得收益率曲线、关键久期参数、久期资产明细）
2. 计算多层次折现曲线（年度、月度、含价差）
3. 计算折现因子
4. 计算各类久期指标（修正久期、有效久期、利差久期）
5. 计算关键久期风险指标（DV10系列）
6. 提供久期风险分析和报告

#### 4.1.2 业务总流程

```mermaid
flowchart TD
    %% 基础数据导入
    A[UC0001: 导入万得收益率曲线数据]
    B[UC0002: 导入关键久期参数数据]
    C[UC0003: 生成久期资产明细数据]

    %% 折现曲线计算流程
    D[UC0004: 计算年度折现曲线]
    E[UC0005: 计算月度折现曲线不含价差]
    F[UC0006: 计算月度折现曲线含价差]
    G[UC0007: 计算月度折现因子含价差]

    %% 关键久期计算流程
    H[UC0008: 计算关键久期折现曲线含价差]
    I[UC0009: 计算关键久期折现因子含价差]

    %% 久期指标计算
    J[UC0010: 计算久期指标]
    K[UC0011: 计算久期资产汇总]

    %% 数据流向关系
    A --> D
    D --> E
    E --> F
    F --> G

    B --> H
    F --> H
    H --> I

    C --> J
    G --> J
    I --> J
    J --> K
```

#### 4.1.3 用例描述

##### 4.1.3.1 导入万得收益率曲线数据(UC0001)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 导入万得收益率曲线数据                |
|   功能描述   | 导入万得收益率曲线数据 |
|    参与者    | 资产管理部人员                     |
|    原型图    | PT0001                                |
|    关联表    | TB0001                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击万得收益率曲线菜单,跳转至列表页] --> B[点击导入按钮,弹出导入窗口页]
    B --> C[下载Excel模板]
    C --> D[基于Excel模板填写待导入数据]
    D --> E[点击导入按钮,选择对应Excel文件点确认]
    E --> F[页面调用IF0001接口上传Excel内容]
    F --> G[导入完成后,列表页显示导入记录信息]
```

##### 4.1.3.2 导入关键久期参数数据(UC0002)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 导入关键久期参数数据                |
|   功能描述   | 导入关键久期参数数据 |
|    参与者    | 资产管理部人员                     |
|    原型图    | PT0002                                |
|    关联表    | TB0002                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击关键久期参数菜单,跳转至列表页] --> B[点击导入按钮,弹出导入窗口页]
    B --> C[下载Excel模板]
    C --> D[基于Excel模板填写待导入数据]
    D --> E[点击导入按钮,选择对应Excel文件点确认]
    E --> F[页面调用IF0002接口上传Excel内容]
    F --> G[导入完成后,列表页显示导入记录信息]
```

##### 4.1.3.3 生成久期资产明细数据(UC0003)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 生成久期资产明细数据                |
|   功能描述   | 从整体资产明细表筛选和生成久期资产基础数据，包含复杂的金融计算逻辑 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | t_ast_asset_detail_overall,TB0003,TB0006,TB0007                        |
|    前置用例    | 需要TB0006,TB0007表数据支持复杂计算                        |

**步骤1.** 筛选基础资产数据

(1) 从整体资产明细表(t_ast_asset_detail_overall)中筛选数据
(2) 筛选条件：可计算现金流固收资产标识(calculable_cashflow_flag)=1的资产
(3) 按账期进行数据筛选

**步骤2.** 提取基础字段（流程节点1）

(1) 直接从整体资产明细表提取以下字段：
   - 账期(account_period)
   - 资产编号(asset_number) - 唯一索引字段
   - 账户名称(account_name)
   - 资产名称(asset_name)
   - 证券代码(security_code)
   - 资产小小类(asset_sub_category)
   - 持仓面值(holding_face_value)
   - 市值(market_value)
   - 账面余额(book_balance)
   - 账面价值(book_value)
   - 票面利率(coupon_rate)
   - 付息方式(payment_method)
   - 折现曲线标识(curve_id)
   - 调整起息日(adjusted_value_date)
   - 调整买入日(adjusted_purchase_date)
   - 调整到期日(adjusted_maturity_date)
   - 发行时点价差计算标识(issue_spread_calc_flag)
   - 利差久期资产统计标识(spread_duration_stat_flag)

**步骤3.** 计算发行时点相关字段（流程节点2）

(1) 发行时点资产现值：等于持仓面值
(2) 发行时点现金流值集：基于债券资产未来现金流计算逻辑生成JSON格式数据
(3) 评估时点现金流值集：基于债券资产未来现金流计算逻辑生成JSON格式数据

**现金流计算逻辑详述**：

**3.1 核心参数与概念**
- 付息方式分类：
  - 0：到期一次性支付（本金+利息）
  - 1：按年支付利息（每年固定月份付息）
  - 2：按半年支付利息（每半年固定月份付息）
  - 4：按季支付利息（每季度固定月份付息）

- 关键日期：
  - 调整起息日：实际计息起始日
  - 调整到期日：债券实际到期日
  - 预测日期：需计算现金流的未来日期

**3.2 付息月份计算逻辑**
- 到期支付（01）：付息月份一 = 调整到期日的月份
- 按年支付（02）：付息月份一 = 调整起息日的月份
- 按半年支付（03）：
  - 付息月份一 = 调整起息日月份 +6（若超过12则减12）
  - 付息月份二 = 调整起息日的月份
- 按季支付（04）：
  - 付息月份一 = 调整起息日月份 +3（若超过12则减12）
  - 付息月份二 = 调整起息日月份 +6（若超过12则减12）
  - 付息月份三 = 调整起息日月份 +9（若超过12则减12）
  - 付息月份四 = 调整起息日的月份

**3.3 未来现金流计算规则**
- 仅处理"可计算现金流固收资产标识"=1且"剩余期限分类"≠1的资产
- 仅计算调整起息日之后、调整到期日之前的现金流
- 根据付息方式和总月数是否为付息周期整数倍进行分类计算
- 到期日处理本金偿还，付息日处理利息支付

**3.4 JSON格式规范**
```json
{
  "asset_number": "资产编号",
  "calculation_date": "计算基准日期",
  "cash_flows": [
    {
      "period": 1,
      "date": "2025-01-31",
      "amount": 1000.00,
      "type": "interest"
    },
    {
      "period": 12,
      "date": "2025-12-31",
      "amount": 101000.00,
      "type": "principal_and_interest"
    }
  ],
  "total_periods": 12,
  "payment_frequency": 1
}
```

**步骤4.** 计算发行时点价差（流程节点5）

(1) 发行时点价差计算逻辑：
   - 如果折现曲线标识=0，赋值为0
   - 如果折现曲线标识≠0，使用goseek方法计算
   - 计算方法：使得[发行时点现金流值集向量*月度折现因子表含价差中的折现因子向量]=发行时点资产现值
   - 匹配条件：曲线细分类=1 AND 账户名称=账户名称 AND 证券代码=证券代码

**步骤5.** 计算评估时点相关字段（流程节点8）

(1) 评估时点价差计算：
   - 如果利差久期资产统计标识=0，赋值为0
   - 如果利差久期资产统计标识≠0，使用goseek方法计算
   - 计算方法：使得[评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量]=市值
   - 匹配条件：曲线细分类=5 AND 账户名称=账户名称 AND 证券代码=证券代码

(2) 利差久期计算：
   - 公式：[评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量*1/(1+月度折现曲线表含价差中的折现曲线向量)*月份向量/12]/评估时点资产现值
   - 折现因子匹配条件：曲线细分类=5 AND 账户名称=账户名称 AND 证券代码=证券代码
   - 折现曲线匹配条件：曲线细分类=5 AND 账户名称=账户名称 AND 证券代码=证券代码

(3) 账面价值σ系列计算：
   - 账面价值σ=9%：账面价值*(1-9%*评估时点价差*利差久期)
   - 账面价值σ=17%：账面价值*(1-17%*评估时点价差*利差久期)
   - 账面价值σ=77%：账面价值*(1-77%*评估时点价差*利差久期)

(4) 评估时点资产现值计算：
   - 如果折现曲线标识=0，等于市值
   - 如果折现曲线标识≠0，等于评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量
   - 匹配条件：曲线细分类=2 AND 账户名称=账户名称 AND 证券代码=证券代码

(5) 评估时点到期收益率：使用XIRR函数计算，求得R使得评估时点现金流值集的折现值等于账面价值

(6) 资产修正久期计算：
   - 公式：[评估时点现金流值集向量*月度折现因子表含价差中的折现因子向量*1/(1+月度折现曲线表含价差中的折现曲线向量)*月份向量/12]/评估时点资产现值
   - 折现因子匹配条件：曲线细分类=2 AND 账户名称=账户名称 AND 证券代码=证券代码
   - 折现曲线匹配条件：曲线细分类=2 AND 账户名称=账户名称 AND 证券代码=证券代码

(7) 评估时点资产现值±50bp计算：
   - +50bp：使用曲线细分类=3的折现因子
   - -50bp：使用曲线细分类=4的折现因子
   - 匹配条件：账户名称=账户名称 AND 证券代码=证券代码

(8) 资产有效久期：等于-(评估时点资产现值+50bp-评估时点资产现值-50bp)/评估时点资产现值/0.01

**步骤6.** 计算DV10风险指标（流程节点8）

(1) DV10上升/下降情景计算：
   - 公式：评估时点现金流值集向量*关键久期折现因子表含价差中的折现因子向量
   - 匹配条件：压力方向=(上升/下降) AND 关键期限=对应期限 AND 账户名称=账户名称 AND 证券代码=证券代码

(2) DV10基础指标计算：
   - 公式：-(DV10_X_上升-DV10_X_下降)/2
   - 适用于所有20个关键期限：0, 0.5, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 15, 20, 25, 30, 35, 40, 45, 50年

**步骤7.** 数据入表

(1) 将筛选和计算的结果写入TB0003表(久期资产明细表)
(2) 先删除原有账期数据，再批量插入新数据
(3) 复杂计算字段如无法立即计算，初始化为默认值，在UC0010中完成详细计算

##### 4.1.3.4 计算年度折现曲线(UC0004)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算年度折现曲线                |
|   功能描述   | 基于万得收益率曲线计算年度折现曲线 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0001,TB0003,TB0004                        |
|    前置用例    | UC0001,UC0003                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0003表(久期资产明细表)，获取资产基本信息
(2) 按账期读取TB0001表(万得收益率曲线表)，构建收益率曲线HashMap

**步骤2.** 计算年度折现曲线

(1) 确定日期类型和日期
   a. 发行时点：日期等于久期资产明细表.调整起息日
   b. 评估时点：日期等于账期转换为所在月的最后一天

(2) 计算各期限收益率
   a. 如果折现曲线标识=0 and 日期类型=发行时点：赋值为"-"
   b. 如果折现曲线标识=0 and 日期类型=评估时点：等于久期资产明细表.到期收益率
   c. 如果折现曲线标识<>0：
      - 从万得收益率曲线表中查找对应的收益率数据
      - 匹配条件：折现曲线标识和日期
      - 将收益率除以100转换为小数形式

**步骤3.** 数据入表

(1) 将计算结果按照年度折现曲线表(TB0004)的结构组织数据
(2) 写入TB0004表

##### 4.1.3.5 计算月度折现曲线不含价差(UC0005)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算月度折现曲线不含价差                |
|   功能描述   | 基于年度折现曲线插值计算月度折现曲线 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0004,TB0005                        |
|    前置用例    | UC0004                       |

**步骤1.** 加载年度折现曲线数据

(1) 按账期读取TB0004表(年度折现曲线表)，构建年度收益率HashMap

**步骤2.** 计算月度折现曲线

(1) 对于每个资产和日期类型组合，计算0-600个月的收益率
(2) 计算规则：
   a. 如果折现曲线标识=0 and 日期类型=发行时点：赋值为"-"
   b. 如果折现曲线标识=0 and 日期类型=评估时点：等于久期资产明细表.到期收益率
   c. 如果折现曲线标识<>0：
      - 月份为12的整数倍时：直接取年度折现曲线表中对应年份的收益率
      - 月份不为12的整数倍时：取临近两个整数年的收益率进行线性插值

**步骤3.** 数据入表

(1) 将计算结果按照月度折现曲线表不含价差(TB0005)的结构组织数据
(2) 写入TB0005表

##### 4.1.3.6 计算月度折现曲线含价差(UC0006)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算月度折现曲线含价差                |
|   功能描述   | 基于月度折现曲线和价差计算含价差曲线 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0003,TB0005,TB0006                        |
|    前置用例    | UC0005                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0005表(月度折现曲线表不含价差)，构建基础收益率HashMap
(2) 按账期读取TB0003表(久期资产明细表)，获取价差信息

**步骤2.** 计算含价差折现曲线

(1) 确定曲线细分类
   a. 曲线细分类=1：修正久期 and 0bp and 发行时点 and 发行时点价差
   b. 曲线细分类=2：修正久期 and 0bp and 评估时点 and 发行时点价差
   c. 曲线细分类=3：有效久期 and +50bp and 评估时点 and 发行时点价差
   d. 曲线细分类=4：有效久期 and -50bp and 评估时点 and 发行时点价差
   e. 曲线细分类=5：利差久期 and 0bp and 评估时点 and 评估时点价差

(2) 计算各期限含价差收益率
   a. 曲线细分类=1：月度折现曲线表不含价差.期限+发行时点价差
   b. 曲线细分类=2：月度折现曲线表不含价差.期限+发行时点价差
   c. 曲线细分类=3：月度折现曲线表不含价差.期限+发行时点价差+0.5%
   d. 曲线细分类=4：月度折现曲线表不含价差.期限+发行时点价差-0.5%
   e. 曲线细分类=5：月度折现曲线表不含价差.期限+评估时点价差

**步骤3.** 数据入表

(1) 将计算结果按照月度折现曲线表含价差(TB0006)的结构组织数据
(2) 写入TB0006表

##### 4.1.3.7 计算月度折现因子含价差(UC0007)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算月度折现因子含价差                |
|   功能描述   | 基于月度折现曲线含价差计算折现因子 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0006,TB0007                        |
|    前置用例    | UC0006                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0006表(月度折现曲线表含价差)，获取含价差的收益率曲线数据
(2) 构建收益率曲线HashMap，按照账期、久期类型、基点类型、日期类型、日期、价差类型、价差、曲线细分类、资产编号等维度进行索引

**步骤2.** 计算折现因子

(1) 对于每个资产和情景组合，计算0-600期的折现因子
(2) 计算公式：期限X的折现因子 = 1/(1+月度折现曲线表含价差.期限X)^(X/12)
   其中X为月份数，范围从0到600

**步骤3.** 组织输出数据

(1) 继承月度折现曲线表含价差的所有维度字段：
   - 账期(account_period)
   - 久期类型(duration_type)
   - 基点类型(basis_point_type)
   - 日期类型(date_type)
   - 日期(date)
   - 价差类型(spread_type)
   - 价差(spread)
   - 曲线细分类(curve_sub_category)
   - 资产编号(asset_number)
   - 账户名称(account_name)
   - 资产名称(asset_name)
   - 证券代码(security_code)
   - 折现曲线标识(curve_id)

(2) 计算结果字段：
   - 期限0到期限600：对应的折现因子值

**步骤4.** 数据入表

(1) 将计算结果按照月度折现因子表含价差(TB0007)的结构组织数据
(2) 写入TB0007表

##### 4.1.3.8 计算关键久期折现曲线含价差(UC0008)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算关键久期折现曲线含价差                |
|   功能描述   | 基于关键久期参数和月度折现曲线含价差计算关键久期折现曲线 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0002,TB0006,TB0008                        |
|    前置用例    | UC0002,UC0006                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0002表(关键久期参数表)，获取关键久期参数值集
(2) 按账期读取TB0006表(月度折现曲线表含价差)，构建基础收益率HashMap
(3) 筛选条件：月度折现曲线表含价差.曲线细分类=2

**步骤2.** 确定基础维度信息

(1) 账期：继承输入账期
(2) 久期类型：固定为"关键久期"
(3) 基点类型：固定为"0bp"
(4) 关键期限：从关键久期参数表中获取，取值为0、0.5、1、2、3、4、5、6、7、8、10、12、15、20、25、30、35、40、45、50
(5) 压力方向：分别计算"上升"和"下降"两种情景
(6) 日期类型：固定为"评估时点"
(7) 日期：将账期转换为所在月的最后一天
(8) 价差类型：固定为"发行时点价差"
(9) 曲线细分类：固定为"2"

**步骤3.** 继承资产维度信息

(1) 从月度折现曲线表含价差(曲线细分类=2)中继承以下字段：
   - 价差(spread)
   - 资产编号(asset_number)
   - 账户名称(account_name)
   - 资产名称(asset_name)
   - 证券代码(security_code)
   - 折现曲线标识(curve_id)

**步骤4.** 计算关键久期折现曲线

(1) 获取关键久期参数
   a. 根据关键期限从关键久期参数表中获取对应的参数值集(parameter_val_set)
   b. 解析JSON格式的参数值集，获取0-600期的参数值

(2) 计算各期限的关键久期折现曲线值
   a. 如果压力方向=上升：
      期限X = 月度折现曲线表含价差.期限X + 关键久期参数表.参数值集[X]
   b. 如果压力方向=下降：
      期限X = 月度折现曲线表含价差.期限X - 关键久期参数表.参数值集[X]

(3) 匹配条件说明
   a. 月度折现曲线表含价差匹配条件：
      - 曲线细分类=2
      - 账户名称=关键久期折现曲线表含价差.账户名称
      - 证券代码=关键久期折现曲线表含价差.证券代码
   b. 关键久期参数表匹配条件：
      - 关键期限=关键久期折现曲线表含价差.关键期限

**步骤5.** 数据入表

(1) 将计算结果按照关键久期折现曲线表含价差(TB0008)的结构组织数据
(2) 为每个资产、关键期限、压力方向组合生成一条记录
(3) 写入TB0008表

##### 4.1.3.9 计算关键久期折现因子含价差(UC0009)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算关键久期折现因子含价差                |
|   功能描述   | 基于关键久期折现曲线含价差计算折现因子 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0008,TB0009                        |
|    前置用例    | UC0008                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0008表(关键久期折现曲线表含价差)，获取关键久期折现曲线数据
(2) 构建关键久期折现曲线HashMap，按照账期、久期类型、基点类型、关键期限、压力方向、日期类型、日期、价差类型、价差、曲线细分类、资产编号等维度进行索引

**步骤2.** 继承维度字段

(1) 完全继承关键久期折现曲线表含价差(TB0008)的所有维度字段：
   - 账期(account_period)：6位字符，格式YYYYMM，唯一索引
   - 久期类型(duration_type)：20位字符，同关键久期折现曲线表含价差，唯一索引
   - 基点类型(basis_point_type)：20位字符，同关键久期折现曲线表含价差
   - 关键期限(key_term)：20位字符，同关键久期折现曲线表含价差，唯一索引
   - 压力方向(stress_direction)：10位字符，同关键久期折现曲线表含价差
   - 日期类型(date_type)：同关键久期折现曲线表含价差
   - 日期(date)：10位日期，同关键久期折现曲线表含价差
   - 价差类型(spread_type)：同关键久期折现曲线表含价差
   - 价差(spread)：同关键久期折现曲线表含价差
   - 曲线细分类(curve_sub_category)：同关键久期折现曲线表含价差
   - 资产编号(asset_number)：20位字符，同关键久期折现曲线表含价差，唯一索引
   - 账户名称(account_name)：50位字符，同关键久期折现曲线表含价差
   - 资产名称(asset_name)：100位字符，同关键久期折现曲线表含价差
   - 证券代码(security_code)：20位字符，同关键久期折现曲线表含价差
   - 折现曲线标识(curve_id)：10位字符，同关键久期折现曲线表含价差
   - 发行时点价差(issue_spread)：10,6位小数，可空，缺省值0，同关键久期折现曲线表含价差

**步骤3.** 计算关键久期折现因子

(1) 对于每个资产、关键期限、压力方向组合，计算0-600期的折现因子
(2) 计算公式：期限X的折现因子 = 1/(1+关键久期折现曲线表含价差.期限X)^(X/12)
   其中X为月份数，范围从0到600

(3) 具体计算逻辑：
   - 期限0(term_0)：1/(1+关键久期折现曲线表含价差.期限0)^(0/12) = 1/(1+关键久期折现曲线表含价差.期限0)^0 = 1
   - 期限1(term_1)：1/(1+关键久期折现曲线表含价差.期限1)^(1/12)
   - 期限2(term_2)：1/(1+关键久期折现曲线表含价差.期限2)^(2/12)
   - ...
   - 期限X(term_X)：1/(1+关键久期折现曲线表含价差.期限X)^(X/12)
   - ...
   - 期限600(term_600)：1/(1+关键久期折现曲线表含价差.期限600)^(600/12) = 1/(1+关键久期折现曲线表含价差.期限600)^50

(4) 字段属性：
   - 数据类型：decimal(10,6)
   - 是否可空：是
   - 缺省值：0

**步骤4.** 数据质量控制

(1) 验证计算结果的合理性：
   - 折现因子值应在0到1之间
   - 期限越长，折现因子值应越小
   - 检查是否存在异常值或空值

(2) 处理异常情况：
   - 如果关键久期折现曲线表含价差中某期限值为空或异常，对应折现因子设为0
   - 如果计算结果超出合理范围，记录异常日志

**步骤5.** 输出字段完整性检查

(1) 确保所有必填字段完整性：
   - 账期(account_period)：6位，非空，唯一索引组成部分
   - 久期类型(duration_type)：20位，非空，唯一索引组成部分
   - 基点类型(basis_point_type)：20位，非空
   - 关键期限(key_term)：20位，非空，唯一索引组成部分
   - 压力方向(stress_direction)：10位，非空
   - 日期类型(date_type)：非空
   - 日期(date)：10位，非空
   - 资产编号(asset_number)：20位，非空，唯一索引组成部分
   - 账户名称(account_name)：50位，非空
   - 资产名称(asset_name)：100位，非空
   - 证券代码(security_code)：20位，非空
   - 折现曲线标识(curve_id)：10位，非空

(2) 可空字段处理：
   - 价差类型(spread_type)：可空，继承自TB0008
   - 价差(spread)：可空，继承自TB0008
   - 曲线细分类(curve_sub_category)：可空，继承自TB0008
   - 发行时点价差(issue_spread)：decimal(10,6)，可空，缺省值0
   - 期限0到期限600：decimal(10,6)，可空，缺省值0

**步骤6.** 数据入表

(1) 将计算结果按照关键久期折现因子表含价差(TB0009)的结构组织数据
(2) 确保唯一索引字段的完整性：账期+久期类型+关键期限+资产编号
(3) 批量写入TB0009表，提高数据处理效率
(4) 记录处理日志，包括处理记录数、异常记录数等统计信息

##### 4.1.3.10 计算久期指标(UC0010)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算久期指标                |
|   功能描述   | 计算各类久期指标和DV10风险指标 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0003,TB0007,TB0009                        |
|    前置用例    | UC0003,UC0007,UC0009                       |

**步骤1.** 加载基础数据

(1) 按账期读取TB0003表(久期资产明细表)，获取资产现金流数据
(2) 按账期读取TB0007表(月度折现因子表含价差)，构建折现因子数据集合
(3) 按账期读取TB0009表(关键久期折现因子表含价差)，构建关键久期折现因子数据集合

**数据匹配策略**：
- 不预先构建HashMap，而是在计算时根据具体匹配条件从数据集合中查找
- 避免因复合键构建不当导致的数据覆盖或匹配错误问题
- 确保每个资产的每个计算场景都能找到唯一对应的折现因子

**步骤2.** 计算价差

(1) 发行时点价差计算
   a. 如果折现曲线标识=0：赋值为0
   b. 如果折现曲线标识<>0：使用goseek方法计算，使得发行时点现金流值集向量*月度折现因子向量=发行时点资产现值

(2) 评估时点价差计算
   a. 如果利差久期资产统计标识=0：赋值为0
   b. 如果利差久期资产统计标识<>0：使用goseek方法计算，使得评估时点现金流值集向量*月度折现因子向量=市值

**步骤3.** 计算现值

(1) 评估时点资产现值
   a. 如果折现曲线标识=0：等于市值
   b. 如果折现曲线标识<>0：等于评估时点现金流值集向量*月度折现因子向量

   **折现因子匹配条件**：
   - 月度折现因子表含价差.曲线细分类=2
   - 月度折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 月度折现因子表含价差.证券代码=久期资产明细表.证券代码

(2) 评估时点资产现值+50bp：等于评估时点现金流值集向量*月度折现因子向量

   **折现因子匹配条件**：
   - 月度折现因子表含价差.曲线细分类=3
   - 月度折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 月度折现因子表含价差.证券代码=久期资产明细表.证券代码

(3) 评估时点资产现值-50bp：等于评估时点现金流值集向量*月度折现因子向量

   **折现因子匹配条件**：
   - 月度折现因子表含价差.曲线细分类=4
   - 月度折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 月度折现因子表含价差.证券代码=久期资产明细表.证券代码

**步骤4.** 计算久期指标

(1) 资产修正久期 = [评估时点现金流值集向量*月度折现因子向量*1/(1+月度折现曲线向量)*月份向量/12]/评估时点资产现值

   **折现因子匹配条件**：
   - 月度折现因子表含价差.曲线细分类=2
   - 月度折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 月度折现因子表含价差.证券代码=久期资产明细表.证券代码

   **折现曲线匹配条件**：
   - 月度折现曲线表含价差.曲线细分类=2
   - 月度折现曲线表含价差.账户名称=久期资产明细表.账户名称
   - 月度折现曲线表含价差.证券代码=久期资产明细表.证券代码

(2) 资产有效久期 = -(评估时点资产现值+50bp-评估时点资产现值-50bp)/评估时点资产现值/0.01

(3) 利差久期 = [评估时点现金流值集向量*月度折现因子向量*1/(1+月度折现曲线向量)*月份向量/12]/评估时点资产现值（曲线细分类=5）

**步骤5.** 计算DV10指标

(1) 计算各关键期限的上升和下降情景现值
   a. DV10_X_上升 = 评估时点现金流值集向量*关键久期折现因子向量

   **关键久期折现因子匹配条件**：
   - 关键久期折现因子表含价差.压力方向=上升
   - 关键久期折现因子表含价差.关键期限=X（对应的关键期限值）
   - 关键久期折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 关键久期折现因子表含价差.证券代码=久期资产明细表.证券代码

   b. DV10_X_下降 = 评估时点现金流值集向量*关键久期折现因子向量

   **关键久期折现因子匹配条件**：
   - 关键久期折现因子表含价差.压力方向=下降
   - 关键久期折现因子表含价差.关键期限=X（对应的关键期限值）
   - 关键久期折现因子表含价差.账户名称=久期资产明细表.账户名称
   - 关键久期折现因子表含价差.证券代码=久期资产明细表.证券代码

(2) 计算DV10风险值
   a. DV10_X = -(DV10_X_上升-DV10_X_下降)/2

**步骤6.** 计算其他指标

(1) 评估时点到期收益率：使用XirrCalculator工具类计算XIRR，求得R使得评估时点现金流值集的折现值等于账面价值
   - 构建现金流数据：第一个现金流为负的账面价值（投资成本），后续为各期现金流
   - 构建日期数据：基准日期加上对应的月份数
   - 调用XirrCalculator.Newtons_method()方法进行计算

(2) 账面价值σ系列：
   a. 账面价值σ=9% = 账面价值*(1-9%*评估时点利差*利差久期)
   b. 账面价值σ=17% = 账面价值*(1-17%*评估时点利差*利差久期)
   c. 账面价值σ=77% = 账面价值*(1-77%*评估时点利差*利差久期)

**步骤7.** 数据入表

(1) 将计算结果更新到TB0003表(久期资产明细表)

**关键技术改进**：

**折现因子匹配逻辑优化**：
- **问题**：原先使用资产编号作为唯一匹配键，导致同一资产的不同曲线细分类、账户名称、证券代码组合的折现因子数据被覆盖
- **解决方案**：改为使用精确匹配条件从数据集合中查找折现因子
  - 月度折现因子匹配：账户名称 + 证券代码 + 曲线细分类
  - 关键久期折现因子匹配：账户名称 + 证券代码 + 关键期限 + 压力方向
- **优势**：确保每个资产的每个计算场景都能找到唯一对应的折现因子，避免数据匹配错误

**XIRR计算优化**：
- **改进**：使用专业的XirrCalculator工具类替代简化的牛顿法实现
- **优势**：提高计算精度和稳定性，支持复杂的现金流场景

##### 4.1.3.11 计算久期资产汇总(UC0011)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算久期资产汇总                |
|   功能描述   | 按账户汇总久期资产各项指标 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0003,TB0010                        |
|    前置用例    | UC0010                       |

**步骤1.** 加载久期资产明细数据

(1) 按账期读取TB0003表(久期资产明细表)，获取所有资产的计算结果

**步骤2.** 按账户汇总计算

(1) 确定账户类型
   a. 传统账户、分红账户、万能账户：直接按账户名称汇总
   b. 普通账户：汇总传统账户、分红账户、万能账户的数据

(2) 计算汇总指标
   a. 市值 = SUM(久期资产明细表.市值)
   b. 账面余额 = SUM(久期资产明细表.账面余额)
   c. 账面价值 = SUM(久期资产明细表.账面价值)
   d. 账面价值σ系列 = SUM(对应字段)，仅统计利差久期资产统计标识=1的资产

(3) 计算加权平均指标
   a. 评估时点到期收益率 = SUM(账面价值*评估时点到期收益率)/SUM(账面价值)
   b. 资产修正久期 = SUM(评估时点资产现值*资产修正久期)/SUM(评估时点资产现值)
   c. 资产有效久期 = SUM(评估时点资产现值*资产有效久期)/SUM(评估时点资产现值)

(4) 计算DV10风险指标
   a. DV10_X = SUM(久期资产明细表.DV10_X)，X为各关键期限点

**步骤3.** 数据入表

(1) 将计算结果按照久期资产结果汇总表(TB0010)的结构组织数据
(2) 写入TB0010表

## 5. 模版文件结构

### 5.1 万得收益率曲线导入模版

**文件名**：万得收益率曲线导入模版.xlsx

**操作指导**：
1. 用户需要从万得金融终端导出收益率曲线数据
2. 按照模版格式整理数据，确保折现曲线名称、标识、日期格式正确
3. 期限0-50字段填写对应期限的收益率数据（以百分比形式）
4. 上传前请检查数据完整性和格式正确性

**字段说明**：
- 折现曲线名称：如"中债国债收益率曲线"
- 折现曲线标识：数字标识，如"1"、"2"等
- 日期：格式为YYYY-MM-DD
- 期限0-50：对应0年到50年的收益率数据

### 5.2 关键久期参数导入模版

**文件名**：关键久期参数导入模版.xlsx

**操作指导**：
1. 用户需要配置各关键期限点的参数值
2. 参数值集字段需要按JSON格式填写0-600期的数据
3. 确保JSON格式正确，包含日期和数值信息
4. 关键期限字段填写如0、0.5、1、2等标准期限点

**字段说明**：
- 关键期限：标准期限点，如0、0.5、1、2、3、4、5、6、7、8、10、12、15、20、25、30、35、40、45、50
- 参数值集：JSON格式，如{"0":{"date":"2025-01-01","val":0.25},"1":{"date":"2025-01-02","val":0.35}}



## 6. 非功能性需求

### 6.1 性能需求

1. **数据处理能力**：系统应能处理10万条以上的资产记录
2. **计算性能**：久期指标计算应在30分钟内完成
3. **并发处理**：支持多用户同时进行数据导入和查询操作
4. **响应时间**：页面响应时间不超过3秒

### 6.2 可靠性需求

1. **数据准确性**：计算结果准确率达到99.9%以上
2. **系统稳定性**：系统可用性达到99.5%以上
3. **错误处理**：提供完善的错误处理和异常恢复机制
4. **数据备份**：定期备份重要数据，确保数据安全

### 6.3 安全性需求

1. **访问控制**：基于角色的访问控制，确保数据安全
2. **数据加密**：敏感数据传输和存储加密
3. **审计日志**：记录用户操作日志，支持审计追踪
4. **权限管理**：细粒度的功能权限控制

### 6.4 可维护性需求

1. **代码规范**：遵循编码规范，提高代码可读性
2. **文档完整**：提供完整的技术文档和用户手册
3. **模块化设计**：采用模块化设计，便于维护和扩展
4. **版本管理**：建立完善的版本管理机制

## 7. 数据库脚本文件

### 7.1 DDL脚本文件

#### 7.1.1 月度折现曲线表不含价差(TB0005)
- **最新版本**：`docs/sql/t_adur_monthly_discount_curve_new_ddl.sql`
- **说明**：基于最新字段要求，使用JSON格式存储月度折现曲线利率值集
- **特点**：简化表结构，提高存储效率，支持灵活的期限数据管理

#### 7.1.2 月度折现曲线表含价差(TB0006)
- **最新版本**：`docs/sql/t_adur_monthly_discount_curve_with_spread_new_ddl.sql`
- **说明**：基于最新字段要求，使用JSON格式存储月度折现曲线利率含价差值集
- **特点**：支持复杂的价差计算逻辑和曲线细分类管理

#### 7.1.3 月度折现因子表含价差(TB0007)
- **最新版本**：`docs/sql/t_adur_monthly_discount_factor_with_spread_ddl.sql`
- **说明**：基于最新字段要求，使用JSON格式存储月度折现因子表含价差值集
- **特点**：简化表结构，支持复杂的折现因子计算逻辑

#### 7.1.4 关键久期折现曲线表含价差(TB0008)
- **最新版本**：`docs/sql/t_adur_key_duration_curve_with_spread_new_ddl.sql`
- **说明**：基于最新字段要求，使用JSON格式存储关键久期折现曲线表含价差值集
- **特点**：支持压力测试计算和关键久期风险分析

#### 7.1.5 关键久期折现因子表含价差(TB0009)
- **最新版本**：`docs/sql/t_adur_key_duration_factor_with_spread_new_ddl.sql`
- **说明**：基于最新字段要求，使用JSON格式存储关键久期折现因子表含价差值集
- **特点**：支持DV10风险指标计算和关键久期敏感性分析

#### 7.1.6 历史版本文件
- **完整版本**：`docs/sql/t_adur_monthly_discount_curve_complete_ddl.sql`
- **说明**：包含term_0到term_600共601个独立字段的版本
- **用途**：历史参考和数据迁移

#### 7.1.7 生成工具
- **生成脚本**：`docs/sql/generate_monthly_discount_curve_ddl.sql`
- **说明文档**：`docs/sql/README_monthly_discount_curve.md`

### 7.2 数据迁移脚本

如需从旧版本迁移到新版本，请参考新DDL文件中的数据迁移说明部分。

### 7.3 Java工具类

#### 7.3.1 月度折现曲线工具类
- **文件位置**：`docs/sql/MonthlyDiscountRateUtil.java`
- **主要功能**：
  - JSON格式数据的读写操作
  - 期限利率的获取和设置
  - 线性插值计算
  - 数据格式验证

#### 7.3.2 月度折现曲线含价差工具类
- **文件位置**：`docs/sql/MonthlyDiscountCurveWithSpreadUtil.java`
- **主要功能**：
  - 含价差利率计算
  - 曲线细分类确定
  - 基点调整计算
  - 折现曲线标识确定

#### 7.3.3 月度折现因子工具类
- **文件位置**：`docs/sql/MonthlyDiscountFactorUtil.java`
- **主要功能**：
  - 折现因子计算
  - 现值计算
  - JSON数据操作
  - 数学计算支持

#### 7.3.4 关键久期折现曲线含价差工具类
- **文件位置**：`docs/sql/KeyDurationCurveWithSpreadUtil.java`
- **主要功能**：
  - 关键久期折现曲线计算
  - 压力测试计算
  - 关键期限验证
  - 压力方向处理

#### 7.3.5 关键久期折现因子含价差工具类
- **文件位置**：`docs/sql/KeyDurationFactorWithSpreadUtil.java`
- **主要功能**：
  - 关键久期折现因子计算
  - DV10风险指标计算
  - 期限敏感性分析
  - 现金流折现计算

#### 7.3.6 工具类使用示例

**月度折现曲线工具类示例**：
```java
// 创建完整的利率值集
String rateSet = MonthlyDiscountRateUtil.createFullRateSet(new BigDecimal("0.025"));

// 获取特定期限的利率
BigDecimal rate12 = MonthlyDiscountRateUtil.getRate(rateSet, 12);

// 设置特定期限的利率
String updatedRateSet = MonthlyDiscountRateUtil.setRate(rateSet, 12, new BigDecimal("0.030"));

// 线性插值计算
BigDecimal interpolatedRate = MonthlyDiscountRateUtil.linearInterpolation(rateSet, 18.5);
```

**月度折现曲线含价差工具类示例**：
```java
// 计算含价差利率
String baseRateJson = "{\"0\":0.025,\"12\":0.030,\"24\":0.035}";
BigDecimal spread = new BigDecimal("0.001");
String curveSubCategory = "3"; // 有效久期+50bp
String rateWithSpread = MonthlyDiscountCurveWithSpreadUtil.calculateRateWithSpread(
    baseRateJson, spread, curveSubCategory);

// 确定曲线细分类
String subCategory = MonthlyDiscountCurveWithSpreadUtil.determineCurveSubCategory(
    "修正久期", "0bp", "评估时点", "发行时点价差");

// 确定折现曲线标识
String curveId = MonthlyDiscountCurveWithSpreadUtil.determineCurveId("利差久期", "2");
```

**月度折现因子工具类示例**：
```java
// 基于利率计算折现因子
String factorSet = MonthlyDiscountFactorUtil.calculateDiscountFactors(rateWithSpread);

// 获取特定期限的折现因子
BigDecimal factor12 = MonthlyDiscountFactorUtil.getFactor(factorSet, 12);

// 计算现值
Map<Integer, BigDecimal> cashFlowMap = new HashMap<>();
cashFlowMap.put(12, new BigDecimal("1000"));
cashFlowMap.put(24, new BigDecimal("1000"));
BigDecimal presentValue = MonthlyDiscountFactorUtil.calculatePresentValue(factorSet, cashFlowMap);
```

**关键久期折现曲线含价差工具类示例**：
```java
// 计算关键久期折现曲线含价差
String baseCurveJson = "{\"0\":0.025,\"12\":0.030,\"24\":0.035}";
String keyParameterJson = "{\"0\":0.001,\"12\":0.002,\"24\":0.003}";
String stressDirection = "上升";
String keyDurationCurve = KeyDurationCurveWithSpreadUtil.calculateKeyDurationCurveWithSpread(
    baseCurveJson, keyParameterJson, stressDirection);

// 验证关键期限和压力方向
boolean isValidTerm = KeyDurationCurveWithSpreadUtil.isValidKeyTerm("1");
boolean isValidDirection = KeyDurationCurveWithSpreadUtil.isValidStressDirection("上升");

// 获取特定期限的关键久期折现曲线值
BigDecimal curveValue12 = KeyDurationCurveWithSpreadUtil.getCurveValue(keyDurationCurve, 12);
```

**关键久期折现因子含价差工具类示例**：
```java
// 基于关键久期折现曲线计算关键久期折现因子
String keyDurationCurveJson = "{\"0\":0.025,\"12\":0.030,\"24\":0.035}";
String keyDurationFactor = KeyDurationFactorWithSpreadUtil.calculateKeyDurationFactorWithSpread(keyDurationCurveJson);

// 获取特定期限的关键久期折现因子
BigDecimal factor12 = KeyDurationFactorWithSpreadUtil.getFactor(keyDurationFactor, 12);

// 计算DV10风险指标
Map<Integer, BigDecimal> cashFlowMap = new HashMap<>();
cashFlowMap.put(12, new BigDecimal("1000"));
cashFlowMap.put(24, new BigDecimal("1000"));
BigDecimal dv10 = KeyDurationFactorWithSpreadUtil.calculateDV10(keyDurationFactor, cashFlowMap, "1");

// 验证JSON格式
boolean isValid = KeyDurationFactorWithSpreadUtil.isValidJson(keyDurationFactor);
```

### 7.4 数据迁移脚本

- **迁移脚本**：`docs/sql/migrate_monthly_discount_curve.sql`
- **说明**：提供从旧版本到新版本的完整迁移方案
- **包含内容**：数据备份、格式转换、验证查询、回滚脚本

### 7.5 使用建议

1. **新项目**：直接使用 `t_adur_monthly_discount_curve_new_ddl.sql`
2. **现有项目**：评估迁移成本，可选择保持现有结构或迁移到新版本
3. **性能考虑**：JSON格式在查询特定期限时需要使用JSON函数，可能影响查询性能
4. **存储优化**：新版本显著减少了表字段数量，提高了存储效率
5. **开发效率**：使用提供的Java工具类可以简化JSON数据的操作
