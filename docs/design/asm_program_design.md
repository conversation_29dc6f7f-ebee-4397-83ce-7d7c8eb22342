# 资产规模与偿付能力需求规格说明书

## 文档信息

| 项目名称 | 资产规模与偿付能力管理 |
| -------- | ------------ |
| 文档版本 | V1.0         |
| 作者     |        |
| 创建日期 | 2025-01-20   |
| 状态     | 已确认       |

## 变更履历

| 版本 | 日期       | 变更描述 | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-01-20 | 初次编写 |  | 通过 |

## 1. 需求概述

### 1.1 需求背景

资产规模与偿付能力管理是保险公司风险管理的重要组成部分，需要对资产负债表、偿付能力状况表等基础数据进行管理，并计算生成资产规模与偿付能力表，为监管报告和经营决策提供数据支持。

### 1.2 需求目标

建立完整的资产规模与偿付能力数据管理体系，实现：
1. 资产负债表数据的导入和管理
2. 偿付能力状况表数据的导入和管理  
3. 资产规模与偿付能力表的自动计算和生成
4. 支持审计前后不同账期的数据管理

### 1.3 需求范围

本需求涵盖资产规模与偿付能力相关的数据导入、计算、查询、导出等功能。

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 |      |      |      |
| 业务负责人 |      |      |      |
| 技术负责人 |      |      |      |

## 2. 业务架构

### 2.1 业务模块关系图

```mermaid
flowchart TD
    A[资产负债表] --> C[资产规模与偿付能力表]
    B[偿付能力状况表] --> C
    C --> D[监管报告]
    C --> E[经营分析]
```

### 2.2 模块列表

| 模块编号 | 模块名称    | 模块英文名 | 英文缩写 |
| -------- |---------| ---------- | -------- |
| MD0002 | 资产规模与偿付能力管理    | asset_solvency_management | asm |

#### 2.2.1 模块说明

**资产规模与偿付能力管理(MD0002)**：
- 资产负债表(t_liab_balance_sheet)：管理资产负债表数据
- 偿付能力状况表(t_base_solvency_status)：管理偿付能力状况数据
- 资产规模与偿付能力表(t_ass_asset_scale_solvency)：计算生成资产规模与偿付能力数据

### 2.3 数据模型

#### 2.3.1 表间关系

```mermaid
erDiagram
    %% 源数据表（需要直接导入的数据）
    资产负债表 ||--|| 源数据 : "直接导入"
    偿付能力状况表 ||--|| 源数据 : "直接导入"

    %% 计算流程
    资产负债表 }o--o{ 资产规模与偿付能力表 : "TB0017→TB0019"
    偿付能力状况表 }o--o{ 资产规模与偿付能力表 : "TB0018→TB0019"
```

#### 2.3.2 表名字典

| 表编号    | 表中文名 | 表英文名                            | 所属模块 | 备注 |
|--------| ------ |---------------------------------| ------ | ------ |
| TB0017 | 资产负债表 | t_liab_balance_sheet        | MD0002 | 导入数据 |
| TB0018 | 偿付能力状况表 | t_base_solvency_status | MD0002 | 导入数据 |
| TB0019 | 资产规模与偿付能力表 | t_ass_asset_scale_solvency  | MD0002 | 计算结果 |

#### 2.3.3 表集

##### 2.3.3.1 资产负债表(TB0017)

**表英文名**：t_liab_balance_sheet
**表中文名**：资产负债表
**所属模块**：资产规模与偿付能力管理(MD0002)
**表描述**：存储资产负债表数据，包括资产、负债、所有者权益等项目的期末余额和年初余额

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 自增主键 | 是 | 主键 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202412） | 是 | 标识业务账期 |
| 类别 | category | varchar | 20 | 否 | 文本 | - | 值域：资产、负债、所有者权益 | 是 | - |
| 项目名称 | item_name | varchar | 100 | 否 | 文本 | - | 资产负债表项目名称 | 是 | - |
| 期末余额 | ending_balance | decimal | 18,10 | 是 | 数值 | 0 | 期末余额金额 | 否 | - |
| 年初余额 | beginning_balance | decimal | 18,10 | 是 | 数值 | 0 | 年初余额金额 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | - | 否 | - |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | - | 否 | - |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | - | 否 | - |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | - | 否 | - |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | - | 否 | - |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | - |

##### 2.3.3.2 偿付能力状况表(TB0018)

**表英文名**：t_base_solvency_status
**表中文名**：偿付能力状况表
**所属模块**：资产规模与偿付能力管理(MD0002)
**表描述**：存储S01-偿付能力状况表数据，包括各项偿付能力指标

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 自增主键 | 是 | 主键 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202503） | 是 | 标识业务账期 |
| 行次 | row_number | int | 11 | 否 | 数值 | - | 表中行的序号 | 否 | - |
| 项目 | item_name | varchar | 100 | 否 | 文本 | - | 偿付能力指标项目名称 | 是 | - |
| 期末数 | ending_balance | decimal | 20,10 | 是 | 数值 | 0 | 期末余额 | 否 | - |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | - |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | - |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | - |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | - |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | - |

##### 2.3.3.3 资产规模与偿付能力表(TB0019)

**表英文名**：t_ass_asset_scale_solvency
**表中文名**：资产规模与偿付能力表
**所属模块**：资产规模与偿付能力管理(MD0002)
**表描述**：存储资产规模与偿付能力计算结果，通过资产负债表和偿付能力状况表数据计算生成

**字段列表**：

| 字段名称 | 字段代码 | 数据类型 | 长度 | 是否可空 | 输入格式 | 缺省值 | 字段规则描述 | 唯一索引 | 备注 |
|----------|----------|----------|------|----------|----------|--------|--------------|----------|------|
| ID | id | bigint | 20 | 否 | 数值 | - | 自增主键 | 是 | 主键 |
| 账期 | accounting_period | varchar | 6 | 否 | 文本 | - | 格式：YYYYMM（如202503） | 是 | 标识业务账期 |
| 项目 | item_name | varchar | 100 | 否 | 文本 | - | 指标项目名称 | 是 | 项目内容固定 |
| 上年末 | last_year_end | decimal | 100,10 | 是 | 数值 | 0 | 上年末数据 | 否 | 计算字段 |
| 上季末 | last_quarter_end | decimal | 100,10 | 是 | 数值 | 0 | 上季末数据 | 否 | 计算字段 |
| 本季末 | current_quarter_end | decimal | 100,10 | 是 | 数值 | 0 | 本季末数据 | 否 | 计算字段 |
| 创建者 | create_by | varchar | 64 | 是 | 文本 | - | 创建人员 | 否 | - |
| 创建时间 | create_time | datetime | - | 是 | 日期时间 | - | 创建时间 | 否 | - |
| 更新者 | update_by | varchar | 64 | 是 | 文本 | - | 更新人员 | 否 | - |
| 更新时间 | update_time | datetime | - | 是 | 日期时间 | - | 更新时间 | 否 | - |
| 备注 | remark | varchar | 500 | 是 | 文本 | - | 备注信息 | 否 | - |
| 是否删除 | is_del | tinyint | 1 | 否 | 数值 | 0 | 0:否，1:是 | 否 | - |

### 2.4 用例列表

| 用例编号   | 用例名称         | 用例描述                   | 模块编号 |
|--------|--------------|------------------------| ---- |
| UC0017 | 导入资产负债表数据     | 导入资产负债表数据，写入TB0017表     | MD0002 |
| UC0018 | 导入偿付能力状况表数据 | 导入偿付能力状况表数据，写入TB0018表 | MD0002 |
| UC0019 | 计算资产规模与偿付能力表  | 计算资产规模与偿付能力表，写入TB0019表  | MD0002 |

### 2.5 接口清单

| 接口编号   | 接口名称        | 接口描述 | 模块编号 |
| ------ |-------------| ---- | ---- |
| IF0017 | 导入资产负债表数据   | 导入资产负债表数据，写入TB0017表 | MD0002 |
| IF0018 | 导入偿付能力状况表数据    | 导入偿付能力状况表数据，写入TB0018表 | MD0002 |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 资产负债表 | 反映企业在特定时点财务状况的会计报表 | 显示公司资产、负债和所有者权益的财务状况 | 审计前后数据可能不同 |
| 偿付能力状况表 | 反映保险公司偿付能力充足率等指标的监管报表 | 监管部门评估保险公司风险承受能力的重要依据 | S01表格式 |
| 核心资本 | 核心一级资本和核心二级资本的合计 | 保险公司最稳定的资本来源 | 用于计算偿付能力充足率 |

## 4. 功能需求

### 4.1 资产规模与偿付能力管理模块

#### 4.1.1 功能概要

资产规模与偿付能力管理模块主要实现以下功能：
1. 导入基础数据（资产负债表、偿付能力状况表）
2. 计算资产规模与偿付能力表
3. 支持不同账期数据的选择和计算
4. 提供查询和导出功能

#### 4.1.2 业务总流程

```mermaid
flowchart TD
    %% 源数据表导入
    A[UC0017: 导入资产负债表数据]
    B[UC0018: 导入偿付能力状况表数据]

    %% 计算过程
    C[UC0019: 计算资产规模与偿付能力表]

    %% 数据流向关系
    A --> C
    B --> C
```

#### 4.1.3 用例描述

##### 4.1.3.1 导入资产负债表数据(UC0017)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 导入资产负债表数据                |
|   功能描述   | 导入资产负债表数据 |
|    参与者    | 财务部人员                     |
|    原型图    | PT0017                                |
|    关联表    | TB0017                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击资产负债表菜单,跳转至资产负债表列表页] --> B[点击导入按钮,弹出导入窗口页]
    B --> C[下载Excel模板]
    C --> D[基于Excel模板填写待导入数据]
    D --> E[点击导入按钮,选择对应Excel文件点确认]
    E --> F[页面调用IF0017接口上传Excel内容]
    F --> G[导入完成后,列表页显示导入记录信息]
```

##### 4.1.3.2 导入偿付能力状况表数据(UC0018)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 服务 |
|   用例名称   | 导入偿付能力状况表数据                |
|   功能描述   | 导入偿付能力状况表数据 |
|    参与者    | 精算部人员                     |
|    原型图    | PT0018                                |
|    关联表    | TB0018                        |
|    前置用例    |                        |

```mermaid
flowchart TD
    A[用户点击偿付能力状况表菜单,跳转至偿付能力状况表列表页] --> B[点击导入按钮,弹出导入窗口页]
    B --> C[下载Excel模板]
    C --> D[基于Excel模板填写待导入数据]
    D --> E[点击导入按钮,选择对应Excel文件点确认]
    E --> F[页面调用IF0018接口上传Excel内容]
    F --> G[导入完成后,列表页显示导入记录信息]
```

##### 4.1.3.3 计算资产规模与偿付能力表(UC0019)

|   用例标识   | 用例描述             |
| :----------: | ------------------------------------ |
| 类型 | 批处理 |
|   用例名称   | 计算资产规模与偿付能力表                |
|   功能描述   | 计算资产规模与偿付能力表 |
|    参与者    | 系统                     |
|    原型图    |                                |
|    关联表    | TB0017,TB0018,TB0019                        |
|    前置用例    | UC0017,UC0018                       |

**步骤1.** 账期选择


(1) job任务根据计算需要自动从其他表获取这些账期的数据
   a. 上年末账期：用于获取上年末数据
   b. 上季末账期：用于获取上季末数据
   c. 本季末账期：用于获取本季末数据

**步骤2.** 数据计算

(1) 总资产计算
   a. 查询条件：资产负债表.类别='资产'
   b. 计算公式：sum(资产负债表.期末余额)
   c. 分别计算上年末、上季末、本季末三个时点的数据

(2) 净资产计算
   a. 查询条件：资产负债表.类别='所有者权益'
   b. 计算公式：sum(资产负债表.期末余额)
   c. 分别计算上年末、上季末、本季末三个时点的数据

(3) 核心资本计算
   a. 查询条件：S01-偿付能力状况表.项目 in ('核心一级资本','核心二级资本')
   b. 计算公式：sum(S01-偿付能力状况表.期末数)
   c. 分别计算上年末、上季末、本季末三个时点的数据

(4) 核心偿付能力充足率计算
   a. 查询条件：S01-偿付能力状况表.项目='核心偿付能力充足率'
   b. 取值：S01-偿付能力状况表.期末数
   c. 分别获取上年末、上季末、本季末三个时点的数据

(5) 综合偿付能力充足率计算
   a. 查询条件：S01-偿付能力状况表.项目='综合偿付能力充足率（%）'
   b. 取值：S01-偿付能力状况表.期末数
   c. 分别获取上年末、上季末、本季末三个时点的数据

**步骤3.** 数据入表

(1) 将计算结果按照资产规模与偿付能力表(TB0019)的结构组织数据
(2) 项目包括：总资产、净资产、核心资本、核心偿付能力充足率（%）、综合偿付能力充足率（%）
(3) 写入TB0019表

## 5. 非功能需求

### 5.1 性能需求

- 数据导入响应时间：单次导入不超过30秒
- 计算处理时间：资产规模与偿付能力表计算不超过60秒
- 并发用户数：支持10个用户同时操作

### 5.2 安全需求

- 数据访问权限控制：按角色控制数据访问权限
- 数据备份：重要数据需要定期备份
- 操作日志：记录所有数据变更操作

### 5.3 可用性需求

- 系统可用性：99.5%
- 数据准确性：100%
- 界面友好性：操作简单直观

## 6. 约束条件

### 6.1 技术约束

- 开发语言：Java 8
- 前端框架：Vue 2.6.12
- 数据库：MySQL 5.7+
- 操作系统：Windows 11

### 6.2 业务约束

- 账期格式：必须为YYYYMM格式
- 数据完整性：导入数据必须完整，不允许关键字段为空
- 审计要求：支持审计前后数据的区分管理

## 7. 验收标准

### 7.1 功能验收

- 资产负债表数据导入功能正常
- 偿付能力状况表数据导入功能正常
- 资产规模与偿付能力表计算功能正常
- 计算结果准确性验证通过

### 7.2 性能验收

- 导入性能满足要求
- 计算性能满足要求
- 并发性能满足要求

### 7.3 安全验收

- 权限控制功能正常
- 数据安全措施有效
- 操作审计功能完整
