# TB0005月度折现曲线表导出功能增强

## 概述

本文档描述了TB0005月度折现曲线表不含价差的Excel导出功能增强，实现了月度折现曲线利率值集JSON字段的平铺展开导出。

## 功能特性

### 1. JSON字段展开
- **月度折现曲线利率值集** (`monthlyDiscountRateSet`) - JSON字段展开为多列
- **简单格式**: 直接的key-value对，无嵌套结构

### 2. 表头格式
- **单行表头**: 直接显示期限序号 (如: "0", "1", "2", "3", ...)
- **简洁明了**: 不需要日期信息，直接显示期限编号

### 3. 数据格式
- **值**: JSON中的利率值，直接显示为数值
- **排序**: 按期限序号自然排序 (0, 1, 2, ..., 600)

## JSON数据格式

### 月度折现曲线利率JSON结构
```json
{
  "0": 0.022459,
  "1": 0.022459,
  "2": 0.022459,
  "3": 0.022459,
  "6": 0.025000,
  "12": 0.028000,
  "24": 0.032000,
  "36": 0.035000,
  "48": 0.038000,
  "60": 0.040000,
  "120": 0.045000,
  "240": 0.050000,
  "360": 0.052000,
  "480": 0.054000,
  "600": 0.055000
}
```

### 字段说明
- **外层键**: 期限序号 (0-600)，表示月份数
- **值**: 对应期限的折现利率，decimal类型

## 实现细节

### 1. 控制器修改
文件: `app/src/main/java/com/xl/alm/app/controller/MonthlyDiscountCurveController.java`

```java
@PostMapping("/export")
public void export(HttpServletResponse response, MonthlyDiscountCurveQuery monthlyDiscountCurveQuery) {
    List<MonthlyDiscountCurveDTO> list = monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoList(monthlyDiscountCurveQuery);

    // 转换字典值为中文标签用于导出
    convertDictValueToLabel(list);

    // 使用ValueSetExcelExporter导出，处理monthlyDiscountRateSet字段
    // 将月度折现曲线利率值集JSON字段展开为多列，key作为表头，value作为值
    ValueSetExcelExporter.exportExcel(list, "月度折现曲线数据", response, "monthlyDiscountRateSet");
}
```

### 2. ValueSetExcelExporter增强
文件: `app/src/main/java/com/xl/alm/app/util/ValueSetExcelExporter.java`

#### 主要增强点:
1. **简单JSON格式支持**: 支持直接的key-value格式，无嵌套对象
2. **单行表头**: 对于简单格式，只显示期限序号作为表头
3. **字典转换优化**: 避免重复字典转换，由控制器统一处理

#### 关键逻辑修改:
```java
// 检查是否为嵌套对象格式（如现金流数据）
if (value instanceof JSONObject) {
    // 处理复杂格式（现金流数据）
    JSONObject itemData = (JSONObject) value;
    // 获取日期信息...
} else {
    // 简单key-value格式（如月度折现曲线数据），使用序号作为表头
    dateMap.put(index, "期限" + index);
}

// 表头生成逻辑
if (entry.getValue().startsWith("期限")) {
    // 简单格式：只有一行表头，显示序号
    head.add(entry.getKey().toString());
    head.add(entry.getKey().toString());
} else {
    // 复杂格式：双行表头
    head.add(fieldDisplayName + "-" + entry.getKey());
    head.add(entry.getValue());
}

// JSON解析逻辑
if (valueObj instanceof JSONObject) {
    // 处理嵌套对象格式
    JSONObject itemData = (JSONObject) valueObj;
    // 获取值字段...
} else {
    // 简单key-value格式
    value = new BigDecimal(valueObj.toString());
}
```

## 导出效果

### Excel表头示例
```
| 账期 | 日期类型 | 资产编号 | 账户名称 | ... | 0 | 1 | 2 | 3 | 6 | 12 | 24 | 36 | ... | 600 |
|------|----------|----------|----------|-----|---|---|---|---|---|----|----|----|----|-----|
| 202407| 发行时点 | ASSET001 | 账户A    | ... |0.022459|0.022459|0.022459|0.022459|0.025|0.028|0.032|0.035|...|0.055|
```

### 特点
1. **单行表头**: 直接显示期限序号，简洁明了
2. **数据展开**: JSON中的每个期限都成为独立的列
3. **字典转换**: 日期类型、账户名称等字段正确显示中文标签
4. **数值精度**: 利率值保持原有精度

## 字典转换修复

### 问题分析
原来的实现中存在重复字典转换的问题：
1. 控制器中调用`convertDictValueToLabel(list)`进行字典转换
2. ValueSetExcelExporter中又进行了一次字典转换
3. 导致已转换的中文标签被再次转换，可能失败

### 解决方案
```java
// 修改前：ValueSetExcelExporter中的重复转换
if (excel != null && StringUtils.isNotEmpty(excel.dictType()) && value != null) {
    value = convertDictValue(excel.dictType(), String.valueOf(value));
}

// 修改后：移除重复转换
// 注意：字典转换应该在控制器中完成，这里不再进行字典转换
// 避免重复转换导致的问题
rowData.add(value);
```

## 测试验证

### 测试文件
`app/src/test/java/com/xl/alm/app/controller/MonthlyDiscountCurveControllerExportTest.java`

### 测试用例
1. **月度折现曲线利率值集导出功能测试**: 验证JSON字段展开导出
2. **月度折现曲线JSON格式解析测试**: 验证JSON格式正确性
3. **字典转换功能测试**: 验证字典值正确转换
4. **空数据导出测试**: 验证空数据处理

### 运行测试
```bash
cd app
mvn test -Dtest=MonthlyDiscountCurveControllerExportTest
```

## 使用方法

### 1. 前端调用
访问TB0005页面，点击导出按钮

### 2. API调用
```
POST /adur/monthly/discount/curve/export
```

### 3. 参数
- 支持所有现有的查询参数
- 导出文件名: "月度折现曲线数据.xlsx"

## 字典配置确认

### 相关字典类型
确保以下字典类型在数据库中正确配置：
- `adur_date_type` - 日期类型
  - `01` → `发行时点`
  - `02` → `评估时点`
- `adur_account_name` - 账户名称

### 字典数据检查
如果导出时字典字段显示为空，请检查：
1. 字典数据是否已导入：执行`docs/sql/adur_program_dict.sql`
2. 数据库中的实际字段值是否为正确的字典值（如"01"、"02"）
3. 字典服务是否正常工作

## 注意事项

1. **JSON格式**: 确保数据库中的JSON格式正确，为简单的key-value对
2. **期限范围**: 支持0-600期限，总共601个可能的列
3. **Excel限制**: 注意Excel列数限制，大量期限数据可能影响性能
4. **字典转换**: 确保字典数据正确配置，避免显示为空

## 相关文件

### 修改的文件
1. `app/src/main/java/com/xl/alm/app/controller/MonthlyDiscountCurveController.java`
2. `app/src/main/java/com/xl/alm/app/util/ValueSetExcelExporter.java`

### 新增的文件
1. `app/src/test/java/com/xl/alm/app/controller/MonthlyDiscountCurveControllerExportTest.java`
2. `docs/tb0005_monthly_discount_curve_export_enhancement.md`

### 依赖的文件
1. `app/src/main/java/com/xl/alm/app/dto/MonthlyDiscountCurveDTO.java`
2. `app/src/main/java/com/xl/alm/app/service/MonthlyDiscountCurveService.java`
3. `app/src/main/java/com/xl/alm/app/util/DictConvertUtil.java`

## 完成状态

✅ **已完成** - TB0005月度折现曲线表导出功能增强已实现，支持月度折现曲线利率值集的JSON平铺展开导出，并修复了字典转换问题。
